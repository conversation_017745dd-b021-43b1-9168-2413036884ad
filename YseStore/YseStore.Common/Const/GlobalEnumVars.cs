using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Common.Const
{
    public class GlobalEnumVars
    {
        /// <summary>
        /// 订单编号类型大全
        /// </summary>
        public enum SerialNumberType
        {
            [Description("订单编号")]
            订单编号 = 1,
            [Description("支付单编号")]
            支付单编号 = 2,
            [Description("商品编号")]
            商品编号 = 3,
            [Description("货品编号")]
            货品编号 = 4,
            [Description("售后单编号")]
            售后单编号 = 5,
            [Description("退款单编号")]
            退款单编号 = 6,
            [Description("退货单编号")]
            退货单编号 = 7,
            [Description("发货单编号")]
            发货单编号 = 8,
            [Description("提货单号")]
            提货单号 = 9,
            [Description("服务订单编号")]
            服务订单编号 = 10,
            [Description("服务券兑换码")]
            服务券兑换码 = 11,
            [Description("推荐码")]
            推荐码 = 12,
        }
    }
}
