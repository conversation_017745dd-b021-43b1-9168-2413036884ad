using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Common.Helper
{
    public class OrderNumberGenerator
    {
        private static readonly Random _random = new Random();

        public static string Generate()
        {
            // 第一部分：0-9的随机数（1位）
            string part1 = _random.Next(0, 10).ToString();

            // 第二部分：当前时间后4位（使用高精度时间戳）
            string timePart = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            string part2 = timePart.Substring(timePart.Length - 4);

            // 第三部分：10-99的随机数（2位，补零）
            string part3 = _random.Next(10, 100).ToString("D2");

            // 组合并返回前7位（确保总长度）
            return (part1 + part2 + part3).Substring(0, 7);
        }
    }
}
