using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Common;

public class OrderExportMenuHelper
{
    public static readonly Dictionary<string, string> AllMenuItems = new()
    {
        {"A", "0"}, {"B", "0"}, {"C", "0"}, {"D", "0"}, {"E", "0"}, {"F", "0"},{"G", "0"},
        {"H", "0"}, {"I", "0"}, {"J", "0"}, {"K", "0"}, {"L", "0"}, {"M", "0"},
        {"N", "0"}, {"O", "0"}, {"P", "0"}, {"Q", "0"}, {"R", "0"},
        {"S", "0"}, {"T", "0"}, {"U", "0"}, {"V", "0"}, {"W", "0"},
        {"X", "0"}, {"Y", "0"}, {"Z", "0"}, {"AA", "0"}, {"AB", "0"},
        {"AM", "0"}, {"AO", "0"}, {"AP", "0"}, {"AC", "0"}, {"AD", "0"},
        {"AE", "0"}, {"AF", "0"}, {"AG", "0"}, {"AH", "0"}, {"AI", "0"}, {"AJ", "0"}, {"AQ", "0"},{"AR", "0"},
        {"AK", "0"}, {"AL", "0"}, {"AN", "0"}, {"AU", "0"}, {"AT", "0"},{"AS", "0"}, {"AZ", "0"},
        {"BA", "0"}, {"BB", "0"}, {"BD", "0"}, {"BC", "0"}
    };


    public static Dictionary<string, string> AllMenuItemsText()
    {
        var merged = new Dictionary<string, string>();
        var nameCounts = new Dictionary<string, int>();

        // 添加AllMenuItems到合并列表（确保基础字段被处理）
        foreach (var dict in new[]
        {
        AllMenuItemsTextOrder,
        AllMenuItemsTextPro,
        AllMenuItemsTextPay,
        AllMenuItemsTextCus
    })
        {
            foreach (var kvp in dict)
            {
                if (merged.ContainsKey(kvp.Key)) continue;

                var baseName = kvp.Value;

                var count = nameCounts.ContainsKey(baseName) ? nameCounts[baseName] + 1 : 1;
                nameCounts[baseName] = count;

                merged[kvp.Key] = count > 1 ? $"{baseName}_{count}" : baseName;
            }
        }
        return merged;
    }
    public static readonly Dictionary<string, string> AllMenuItemsTextCus = new()
    {
            { "P", "收货姓名" },
            { "Q", "收货街道" },
            { "R", "收货寓所" },
            { "S", "收货国家" },
            { "T", "收货省份" },

            { "U", "收货城市" },
            { "V", "收货邮编" },
            { "W", "收货电话" },
            { "X", "账单姓名" },
            { "Y", "账单街道" },

            { "Z", "账单寓所" },
            { "AA", "账单国家" },
            { "AB", "账单省份" },
            { "AC", "账单城市" },
            { "AD", "账单邮编" },

            { "AE", "账单电话" },
            { "AR", "客户备注" },
            { "BB", "税号" },
            { "BC", "附加信息" },

    };
    public static readonly Dictionary<string, string> AllMenuItemsTextPay = new()
    {
            { "M", "付款方式" },
            { "AN", "付款时间" },
            { "AS", "支付流水号" }
    };
    public static readonly Dictionary<string, string> AllMenuItemsTextPro = new()
    {
            { "E", "产品总价" },
            { "AH", "产品名称" },
            { "AI", "产品单价" },
            { "AJ", "产品数量" },
            { "AQ", "产品发货状态" },

            { "AK", "产品SKU" },
            { "AL", "产品属性" },
            { "AU", "产品成本价" }

    };
    public static readonly Dictionary<string, string> AllMenuItemsTextOrder = new()
    {
            { "A", "订单ID" },
            { "C", "订单号" },
            { "D", "邮箱" },
            { "B", "付款状态" },
            { "F", "运费" },

            { "G", "订单来源" },
            { "H", "订单总额" },
            { "I", "重量" },
            { "J", "发货状态" },
            { "K", "订单状态" },

            { "L", "配送信息" },
            { "N", "创建时间" },
            { "O", "优惠券" },
            { "AM", "税费" },
            { "AO", "手续费" },

            { "AP", "折扣" },
            { "AF", "运单号" },
            { "AG", "备注" },
            { "AT", "物流商" },
            { "AZ", "退款原因" },

            { "BA", "取消原因" },
            { "BD", "积分" }

    };
    public static readonly Dictionary<string, string[]> FieldRules = new()
    {
#region 订单信息
        { "A", new[] { "OrderId" } },
        { "B", new[] { "PaymentStatus" } },
        { "C", new[] { "OId" } },
        { "D", new[] { "Email" } },
        { "F", new[] { "OrderSymbol", "ShippingPrice" } },

        {"G", new[] { "PaymentMethod" } },
        {"H", new[] { "OrderSymbol", "OrderSum" } },
        {"I", new[] { "TotalWeight" } },
        {"J", new[] { "ShippingStatus" } },
        {"K", new[] { "OrderStatus" } },

        {"L",  new[] { "ordersListExplodes.ShippingExpress" } },
        {"N",  new[] { "OrderTime" } },
        {"O",  new[] { "OrderSymbol", "CouponPrice" } },
        {"AM", new[] { "OrderSymbol", "TaxCost" } },
        {"AO", new[] { "OrderSymbol", "Commission" } },

        { "AP", new[] { "OrderSymbol", "DiscountPrice" } },
        { "AF", new[] { "ordersListExplodes.TrackingNumber" } },
        { "AG", new[] { "ordersRemarkLog" } },
        { "AT", new[] { "ordersListExplodes.Carrier" } },
        { "AZ", new[] { "ordersReason" } },

        { "BA", new[] { "CancelReason" } },
        { "BD", new[] { "Points" } },
        #endregion

#region 产品信息
        { "E",  new[] { "ProductPrice" } },
        { "AH", new[] { "ordersListExplodes.Name" } },
        { "AI", new[] {  "OrderSymbol","ordersListExplodes.Price" } },
        { "AJ", new[] { "ordersListExplodes.Qty" } },//
        { "AQ", new[] { "ordersListExplodes.Status" } },//

        { "AK", new[] { "ordersListExplodes.SKU" } },
        { "AL", new[] { "ordersListExplodes.Property" } },
        { "AU", new[] { "OrderSymbol", "ordersListExplodes.PropertyPrice" } },
        #endregion

#region 付款方式
        { "M",  new[] { "PaymentMethod" } },
        { "AN", new[] { "PayTime" } },
        { "AS", new[] { "PaymentId" } },
#endregion


        { "P", new[] { "ShippingFirstName", "ShippingLastName" } },
        { "Q", new[] { "ShippingAddressLine1" } },
        { "R", new[] { "ShippingAddressLine2" } },
        { "S", new[] { "ShippingCountry" } },
        { "T", new[] { "ShippingState" } },

        { "U", new[] { "ShippingCity" } },
        { "V", new[] { "ShippingZipCode" } },
        { "W", new[] { "ShippingCountryCode", "ShippingPhoneNumber" } },
        { "X", new[] { "BillFirstName", "BillLastName" } },
        { "Y", new[] { "BillAddressLine1" } },

        { "Z",  new[] { "BillAddressLine2" } },
        { "AA", new[] { "BillCountry" } },
        { "AB", new[] { "BillState" } },
        { "AC", new[] { "BillCity" } },
        { "AD", new[] { "BillZipCode" } },

        { "AE", new[] { "BillCountryCode", "BillPhoneNumber" } },
        { "AR", new[] { "userRemarkLog" } },
        //从客户信息中获取
        { "BB", new[] { "userRemarkLog" } },
        { "BC", new[] { "userRemarkLog" } }
    };


}
