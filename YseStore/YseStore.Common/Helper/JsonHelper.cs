using Fluid.Values;
using Fluid;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Text.Json;
using System.Threading.Tasks;

namespace YseStore.Common.Helper
{
    public class JsonHelper
    {
        public static class JsonParseFilter
        {
            public static ValueTask<FluidValue> JsonParse(FluidValue input, FilterArguments _, TemplateContext context)
            {
                var parsedValue = JNode.Parse(input.ToStringValue());
                if (parsedValue.GetValueKind() == JsonValueKind.Array)
                {
                    return ValueTask.FromResult(FluidValue.Create(parsedValue, context.Options));
                }
                if (parsedValue.GetValueKind() == JsonValueKind.Object)
                {
                    return ValueTask.FromResult(FluidValue.Create(parsedValue, context.Options));
                }
                return ValueTask.FromResult<FluidValue>(new ObjectValue(parsedValue));
            }
        }
    }
}
