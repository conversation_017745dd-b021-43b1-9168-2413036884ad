using YseStore.IService.Products;
using YseStore.Model.RequestModels.Products;
using Entitys;
using SqlSugar;
using YseStore.Common.Helper;
using YseStore.IService;
using YseStore.Model.Utils;
using YseStore.Ext;
using System.Security.Claims;
using Newtonsoft.Json;
using YseStore.IService.Store;
using Microsoft.Extensions.Logging;

namespace YseStore.Controllers
{
    public class ShopController : BaseController
    {
        private readonly IProductCategoryService _productCategoryService;
        private readonly IProductFrontService _productFrontService;
        private readonly ICaptchaService _captchaService;
        private readonly ISqlSugarClient _db;
        private readonly IProductSeoService _productSeoService;
        private readonly ICategorySeoService _categorySeoService;
        private readonly IConfigService _configService;
        private readonly ILogger<ShopController> _logger;
        private readonly IProductRecommendService _productRecommendService;

        public ShopController(IProductCategoryService productCategoryService, IProductFrontService productFrontService,
            ICategorySeoService categorySeoService,
            ICaptchaService captchaService, ISqlSugarClient db, IProductSeoService productSeoService,
            IConfigService configService, IMenuService menuService, ILogger<ShopController> logger,
            IProductRecommendService productRecommendService) : base(menuService)
        {
            _productCategoryService = productCategoryService;
            _productFrontService = productFrontService;
            _categorySeoService = categorySeoService;
            _captchaService = captchaService;
            _db = db;
            _productSeoService = productSeoService;
            _configService = configService;
            _logger = logger;
            _productRecommendService = productRecommendService;
        }

        /// <summary>
        /// 产品首页
        /// </summary>
        /// <param name="categoryUrl"></param>
        /// <returns></returns>
        [Route("/collections/{*categoryUrl:minlength(1)}")]
        [Route("/collections")]
        public async Task<IActionResult> Index(string categoryUrl = "")
        {
            try
            {
                var page = Request.GetQueryString("page").ObjToInt();
                if (page <= 0) page = 1; // 确保页码至少为1

                var keyword = Request.GetQueryString("keyword") ?? "";
                var includeSoldOut = Request.GetQueryString("includeSoldOut").ObjToBool();
                var sortBy = Request.GetQueryString("sortBy") ?? "Default";
                // 创建分类查询请求模型
                var categoryQueryRequest = new ProductCategoryQueryRequest
                {
                    PageIndex = 1,
                    PageSize = 50, // 获取足够多的顶级分类
                    Keyword = "",
                    IncludeSoldOut = includeSoldOut
                };

                // 从服务中获取分类列表数据
                var categoryResult = await _productCategoryService.GetAllProductCategories(categoryQueryRequest);
                string categoryId = "";
                // 检查产品URL参数
                if (!string.IsNullOrEmpty(categoryUrl))
                {
                    // return RedirectToAction("Index");
                    var urlAsync = await _categorySeoService.GetCategoryIdByPageUrlAsync(categoryUrl);
                    categoryId = urlAsync.ToString();
                }

                // 创建产品查询请求模型
                var productQueryRequest = new ProductFrontendQueryRequest
                {
                    PageIndex = page,
                    PageSize = 12,
                    Keyword = keyword,
                    CategoryId = categoryId,
                    IncludeSoldOut = includeSoldOut,
                    SortBy = sortBy
                };

                // 获取当前用户ID
                int currentUserId = 0;
                if (User.Identity.IsAuthenticated)
                {
                    int.TryParse(User.FindFirstValue(ClaimTypes.NameIdentifier), out currentUserId);
                }

                // 从服务中获取产品列表数据
                var productResult =
                    await _productFrontService.GetFrontendProductList(productQueryRequest, CurrentCurrency,
                        currentUserId);


                // 获取所有分类数据，而不仅仅是顶级分类
                var allCategories = categoryResult.data.ToList();
                var topCategories = allCategories.Where(x => x.UId.Equals("0,")).ToList();

                // 构建分类树 - 使用所有分类数据
                var categoryTree = CategoryTreeBuilder.BuildCategoryTreeGeneric(allCategories);

                // 转换为字符串键的字典，以便Liquid模板可以访问
                var categoryTreeForLiquid = new Dictionary<string, List<products_category>>();
                foreach (var kvp in categoryTree)
                {
                    categoryTreeForLiquid[kvp.Key.ToString()] = kvp.Value;
                }

                // 获取分类描述信息
                products_category_description categoryTopDescription = null;
                products_category_description categoryBottomDescription = null;

                if (!string.IsNullOrEmpty(categoryId) && int.TryParse(categoryId, out int cateIdInt))
                {
                    try
                    {
                        // 直接从已获取的分类数据中查找当前分类，避免重复查询
                        var currentCategory = allCategories.FirstOrDefault(c => c.CateId == cateIdInt);
                        if (currentCategory != null)
                        {
                            // 直接查询分类描述表
                            var descriptions = await _db.Queryable<products_category_description>()
                                .Where(d => d.CateId == cateIdInt)
                                .ToListAsync();

                            if (descriptions != null && descriptions.Any())
                            {
                                // PositionType: 0=内容-top, 3=内容-bottom
                                categoryTopDescription = descriptions.FirstOrDefault(d => d.PositionType == 0);
                                categoryBottomDescription = descriptions.FirstOrDefault(d => d.PositionType == 3);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "获取分类描述信息失败，CategoryId: {CategoryId}", categoryId);
                    }
                }

                // 获取推荐产品数据
                var recommendRequest = new ProductRecommendFrontendRequest
                {
                    CurrentPage = "goods", // 产品详情页
                    ShowType = "auto",
                    CurrentCurrency = CurrentCurrency,
                    UserId = CurrentUserId,
                    CategoryId = (!string.IsNullOrEmpty(categoryId) && int.TryParse(categoryId, out int categoryIdInt)) ? categoryIdInt : 0 // 安全转换产品分类ID
                };
                var recommendProducts = await _productRecommendService.GetFrontendRecommendProducts(recommendRequest);
                // 获取导航菜单数据
                var menuData = await GetNavigationMenusAsync();

                // 创建视图模型，合并导航菜单数据
                var model = new
                {
                    //总分类列表
                    Categories = topCategories,
                    RecommendProducts = recommendProducts,
                    //项目分类树
                    CategoryTree = categoryTreeForLiquid,
                    Products = productResult.data,
                    CurrentPage = page,
                    TotalPages = (int)Math.Ceiling(productResult.dataCount / (double)productQueryRequest.PageSize),
                    TotalItems = productResult.dataCount,
                    Keyword = keyword,
                    CategoryUrl = categoryUrl,
                    CategoryId = categoryId,
                    SortBy = sortBy,
                    IncludeSoldOut = includeSoldOut,
                    ShowingStart = (page - 1) * productQueryRequest.PageSize + 1,
                    ShowingEnd = Math.Min(page * productQueryRequest.PageSize, productResult.dataCount),
                    // 添加导航菜单数据
                    NavMenus = ((dynamic)menuData).NavMenus,
                    FooterNavMenus = ((dynamic)menuData).FooterNavMenus,
                    // 添加分类描述数据
                    CategoryTopDescription = categoryTopDescription,
                    CategoryBottomDescription = categoryBottomDescription
                };
                // 返回视图和模型
                return View(model);
            }
            catch (Exception ex)
            {
                // 记录异常以便调试
                Console.WriteLine($"获取商店页面数据时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");

                // 返回空模型
                return View();
            }
        }

        /// <summary>
        /// 产品列表页
        /// </summary>
        /// <returns></returns>
        [Route("/collections/ShopList")]
        public async Task<IActionResult> ShopList()
        {
            try
            {
                var page = Request.GetQueryString("page").ObjToInt();
                if (page <= 0) page = 1; // 确保页码至少为1

                var keyword = Request.GetQueryString("keyword") ?? "";
                var includeSoldOut = Request.GetQueryString("includeSoldOut").ObjToBool();
                var categoryUrl = Request.GetQueryString("categoryUrl") ?? "";
                var sortBy = Request.GetQueryString("sortBy") ?? "Default";
                // 创建分类查询请求模型
                var categoryQueryRequest = new ProductCategoryQueryRequest
                {
                    PageIndex = 1,
                    PageSize = 50, // 获取足够多的顶级分类
                    Keyword = "",
                    IncludeSoldOut = includeSoldOut
                };

                // 从服务中获取分类列表数据
                var categoryResult = await _productCategoryService.GetAllProductCategories(categoryQueryRequest);
                string categoryId = "";
                // 检查产品URL参数
                if (!string.IsNullOrEmpty(categoryUrl))
                {
                    var urlAsync = await _categorySeoService.GetCategoryIdByPageUrlAsync(categoryUrl);
                    categoryId = urlAsync.ToString();
                }

                // 创建产品查询请求模型
                var productQueryRequest = new ProductFrontendQueryRequest
                {
                    PageIndex = page,
                    PageSize = 12,
                    Keyword = keyword,
                    CategoryId = categoryId,
                    IncludeSoldOut = includeSoldOut,
                    SortBy = sortBy
                };

                // 获取当前用户ID
                int currentUserId = 0;
                if (User.Identity.IsAuthenticated)
                {
                    int.TryParse(User.FindFirstValue(ClaimTypes.NameIdentifier), out currentUserId);
                }

                // 从服务中获取产品列表数据
                var productResult =
                    await _productFrontService.GetFrontendProductList(productQueryRequest, CurrentCurrency,
                        currentUserId);

                // 获取所有分类数据，而不仅仅是顶级分类
                var allCategories = categoryResult.data.ToList();
                var topCategories = allCategories.Where(x => x.UId.Equals("0,")).ToList();

                // 构建分类树 - 使用所有分类数据
                var categoryTree = CategoryTreeBuilder.BuildCategoryTreeGeneric(allCategories);

                // 转换为字符串键的字典，以便Liquid模板可以访问
                var categoryTreeForLiquid = new Dictionary<string, List<products_category>>();
                foreach (var kvp in categoryTree)
                {
                    categoryTreeForLiquid[kvp.Key.ToString()] = kvp.Value;
                }

                // 获取推荐产品数据
                int currentCategoryId = 0;
                if (!string.IsNullOrEmpty(categoryId))
                {
                    int.TryParse(categoryId, out currentCategoryId);
                }

                var listRecommendRequest = new ProductRecommendFrontendRequest
                {
                    CurrentPage = "list", // 产品列表页
                    ShowType = "auto", //
                    CurrentCurrency = CurrentCurrency,
                    UserId = currentUserId,
                    CategoryId = currentCategoryId // 传递当前分类ID
                };
                var recommendProducts =
                    await _productRecommendService.GetFrontendRecommendProducts(listRecommendRequest);

                // 获取导航菜单数据
                var menuData = await GetNavigationMenusAsync();

                // 创建视图模型，合并导航菜单数据
                var model = new
                {
                    //总分类列表
                    Categories = topCategories,
                    //项目分类树
                    CategoryTree = categoryTreeForLiquid,
                    Products = productResult.data,
                    CurrentPage = page,
                    TotalPages = (int)Math.Ceiling(productResult.dataCount / (double)productQueryRequest.PageSize),
                    TotalItems = productResult.dataCount,
                    Keyword = keyword,
                    CategoryUrl = categoryUrl,
                    SortBy = sortBy,
                    IncludeSoldOut = includeSoldOut,
                    ShowingStart = (page - 1) * productQueryRequest.PageSize + 1,
                    ShowingEnd = Math.Min(page * productQueryRequest.PageSize, productResult.dataCount),
                    RecommendProducts = recommendProducts, // 添加推荐产品数据
                    // 添加导航菜单数据
                    NavMenus = ((dynamic)menuData).NavMenus,
                    FooterNavMenus = ((dynamic)menuData).FooterNavMenus
                };
                // 返回视图和模型
                return View(model);
            }
            catch (Exception ex)
            {
                // 记录异常以便调试
                Console.WriteLine($"获取商店页面数据时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");

                // 返回空模型
                return View();
            }
        }

        /// <summary>
        /// 获取产品快速预览数据
        /// </summary>
        /// <param name="productId">产品ID</param>
        /// <returns>JSON格式的产品快速预览数据</returns>
        [HttpGet]
        public async Task<IActionResult> QuickView(int productId)
        {
            try
            {
                if (productId <= 0)
                {
                    return Json(new { success = false, message = "Invalid product ID" });
                }

                var product = await _productFrontService.GetProductQuickView(productId, CurrentCurrency, CurrentUserId);

                if (product == null)
                {
                    return Json(new { success = false, message = "Product not found" });
                }

                return Json(new { success = true, data = product });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取产品快速预览数据时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                return Json(new { success = false, message = "An error occurred while fetching product data" });
            }
        }

        [HttpPost]
        [Route("/collections/search")]
        public IActionResult Search([FromForm] string keywords)
        {
            if (string.IsNullOrEmpty(keywords))
            {
                // 返回空内容，阻止提交
                return NoContent();
            }

            var Result = keywords;
            return View();
        }

        public IActionResult SingleOverview()
        {
            return View();
        }
        public IActionResult ProductDescription()
        {
            return View();
        }
        

        //[Route("/products/Single")]
        //public IActionResult SingleUrl()
        //{
        //    return View();
        //}
        /// <summary>
        /// 产品详情页
        /// </summary>
        /// <param name="productUrl">产品URL</param>
        /// <returns></returns>
        [Route("/products/{*productUrl:minlength(1)}")]
        public async Task<IActionResult> Single(string? productUrl)
        {
            try
            {
                // 检查产品URL参数
                if (string.IsNullOrWhiteSpace(productUrl))
                {
                    return RedirectToAction("Index");
                }

                // 通过ProductSeoService根据URL获取产品ID
                int productId = await _productSeoService.GetProductIdByUrlAsync(productUrl);
                if (productId <= 0)
                {
                    return RedirectToAction("Index");
                }

                // 获取产品详情
                var product = await _productFrontService.GetProductDetail(productId, CurrentCurrency);

                if (product == null)
                {
                    return RedirectToAction("Index");
                }

                // 获取stickyCart配置
                var stickyCartConfigJson = await _configService.GetConfigValueByGroup("products_show", "add_to_cart");

                string addToCartFlow = "0";
                string[] floatingButton = new string[] { };
                string pc = "0";
                string pcPosition = "top";
                string mobile = "0";
                string mobilePosition = "down";

                if (!string.IsNullOrEmpty(stickyCartConfigJson))
                {
                    try
                    {
                        var configData = JsonConvert.DeserializeObject<dynamic>(stickyCartConfigJson);
                        addToCartFlow = configData?.AddToCartFlow?.ToString() ?? "0"; //0:禁用，1：启用
                        floatingButton = configData?.FloatingButton?.ToObject<string[]>() ?? new string[] { }; //展示的按钮
                        pc = configData?.Pc?.ToString() ?? "0"; //0：不显示，1：显示
                        pcPosition = configData?.PcPosition?.ToString() ?? "top";
                        mobile = configData?.Mobile?.ToString() ?? "0"; //0：不显示，1：显示
                        mobilePosition = configData?.MobilePosition?.ToString() ?? "down";
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"解析stickyCart配置时出错: {ex.Message}");
                    }
                }

                var stickyCartConfig = new
                {
                    AddToCartFlow = addToCartFlow,
                    FloatingButton = floatingButton,
                    Pc = pc,
                    PcPosition = pcPosition,
                    Mobile = mobile,
                    MobilePosition = mobilePosition
                };

                // 获取产品切换面板信息
                var productSwitches = await GetProductSwitchesAsync(productId);

                // 获取产品分类ID（通过ProductFrontService获取）
                int categoryId = 0;
                try
                {
                    // 使用ProductFrontService的GetRelatedProducts方法来获取分类信息
                    // 这里我们暂时设置为0，因为推荐系统可以处理没有分类ID的情况
                    categoryId = 0;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"获取产品分类ID时出错: {ex.Message}");
                }

                // 获取推荐产品数据
                var recommendRequest = new ProductRecommendFrontendRequest
                {
                    CurrentPage = "goods", // 产品详情页
                    ShowType = "auto",
                    CurrentCurrency = CurrentCurrency,
                    UserId = CurrentUserId,
                    CategoryId = categoryId, // 传递产品分类ID
                    ProductId = productId // 传递当前产品ID
                };
                var recommendProducts = await _productRecommendService.GetFrontendRecommendProducts(recommendRequest);

                // 创建视图模型，导航菜单数据将通过HTMX加载
                var model = new
                {
                    Product = product,
                    ProductSwitches = productSwitches,
                    StickyCartConfig = stickyCartConfig,
                    RecommendProducts = recommendProducts // 添加推荐产品数据
                };

                return View(model);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取产品详情页面数据时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// 验证产品评论验证码
        /// </summary>
        /// <param name="request">验证码验证请求</param>
        /// <returns>验证结果</returns>
        [HttpPost("api/product/review/verify-captcha")]
        public async Task<IActionResult> VerifyProductReviewCaptcha([FromBody] CaptchaVerifyRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.CaptchaVerifyParam))
                {
                    return Json(new { success = false, message = "验证码参数不能为空" });
                }

                // 使用验证码服务验证
                var verifyResult = await _captchaService.VerifyCaptchaAsync(request.CaptchaVerifyParam);

                return Json(new
                {
                    success = verifyResult.Success,
                    message = verifyResult.Message,
                    code = verifyResult.Code
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"验证验证码时出错: {ex.Message}");
                return Json(new { success = false, message = "验证码验证失败，请重试" });
            }
        }

        /// <summary>
        /// 提交产品评论
        /// </summary>
        /// <param name="request">评论请求对象</param>
        /// <returns>JSON结果</returns>
        [HttpPost("api/product/review/submit")]
        public async Task<IActionResult> SubmitProductReview([FromBody] ProductReviewRequest request)
        {
            try
            {
                // 参数验证
                if (request.ProductId <= 0)
                {
                    return Json(new { success = false, message = "The product ID is invalid" });
                }

                if (string.IsNullOrWhiteSpace(request.Name))
                {
                    return Json(new { success = false, message = "Please enter your name" });
                }

                if (string.IsNullOrWhiteSpace(request.Content))
                {
                    return Json(new { success = false, message = "Please enter your comment" });
                }

                if (request.Rating <= 0 || request.Rating > 5)
                {
                    return Json(new { success = false, message = "Please select a valid rating (1-5)" });
                }

                // 验证验证码
                if (string.IsNullOrEmpty(request.CaptchaVerifyParam))
                {
                    return Json(new { success = false, message = "Please complete the verification code" });
                }

                // 创建评论对象
                var review = new products_review
                {
                    ProId = request.ProductId,
                    CustomerName = request.Name,
                    Title = request.Subject,
                    Content = request.Content,
                    Rating = request.Rating,
                    AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    Audit = false, // 默认需要审核
                    IsRead = false
                    // PicPath_0 = string.Empty,
                    // PicPath_1 = string.Empty,
                    // PicPath_2 = string.Empty,
                    // Source = string.Empty,
                    // Assess = string.Empty
                };

                // 保存评论
                var reviewId = await _db.Insertable(review).ExecuteReturnIdentityAsync();

                if (reviewId > 0)
                {
                    return Json(new
                    {
                        success = true,
                        message =
                            "The review was submitted successfully, thank you for participating! After approval, it will be displayed on the product page",
                        data = reviewId
                    });
                }
                else
                {
                    return Json(new { success = false, message = "Comment submission failed, please try again later" });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"提交产品评论时出错: {ex.Message}");
                return Json(new { success = false, message = "System error, please try again later" });
            }
        }

        /// <summary>
        /// 提交评论回复
        /// </summary>
        /// <param name="reviewId">评论ID</param>
        /// <param name="request">回复请求对象</param>
        /// <returns>JSON结果</returns>
        [HttpPost("api/product/review/{reviewId}/reply")]
        public async Task<IActionResult> SubmitProductReviewReply(int reviewId,
            [FromBody] ProductReviewReplyRequest request)
        {
            try
            {
                // 参数验证
                if (reviewId <= 0)
                {
                    return Json(new { success = false, message = "The comment ID is invalid" });
                }

                if (string.IsNullOrWhiteSpace(request.Name))
                {
                    return Json(new { success = false, message = "Please enter your name" });
                }

                if (string.IsNullOrWhiteSpace(request.Content))
                {
                    return Json(new { success = false, message = "Please enter your reply" });
                }

                // 验证验证码
                if (string.IsNullOrEmpty(request.CaptchaVerifyParam))
                {
                    return Json(new { success = false, message = "Please complete the verification code" });
                }

                // 验证评论是否存在
                var review = await _db.Queryable<products_review>()
                    .FirstAsync(r => r.RId == reviewId);

                if (review == null)
                {
                    return Json(new { success = false, message = "Comments are non-existent" });
                }

                // 创建回复对象
                var reply = new products_review_reply
                {
                    RId = reviewId,
                    ResponderName = request.Name,
                    Content = request.Content,
                    AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    Identity = request.IsAdmin ? "admin" : "user",
                    ReplyToId = request.ReplyToId,
                    ReplyToName = request.ReplyToName
                };

                // 保存回复
                var replyId = await _db.Insertable(reply).ExecuteReturnIdentityAsync();

                if (replyId > 0)
                {
                    // 更新评论的回复ID
                    review.ReId = replyId;
                    await _db.Updateable(review).UpdateColumns(r => new { r.ReId }).ExecuteCommandAsync();

                    return Json(new
                    {
                        success = true, message = "The reply was submitted successfully, thank you for participating!",
                        data = replyId
                    });
                }
                else
                {
                    return Json(
                        new { success = false, message = "The reply submission failed, please try again later" });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "System error, please try again later" });
            }
        }

        /// <summary>
        /// 获取适用于指定产品的切换面板信息
        /// </summary>
        /// <param name="productId">产品ID</param>
        /// <returns>适用的切换面板列表</returns>
        private async Task<List<dynamic>> GetProductSwitchesAsync(int productId)
        {
            try
            {
                // 一次性获取所有面板和内容数据
                var allSwitches = await _db.Queryable<products_switch>()
                    .OrderBy(s => s.MyOrder)
                    .ToListAsync();

                var allContents = await _db.Queryable<products_switch_content>()
                    .ToListAsync();

                // 获取产品所属的分类ID列表，用于Categories类型的判断
                var productCategoryIds = await _db.Queryable<products_category_relate>()
                    .Where(r => r.ProId == productId)
                    .Select(r => r.CateId)
                    .ToListAsync();

                var result = new List<dynamic>();

                foreach (var sw in allSwitches)
                {
                    var switchContents = allContents.Where(c => c.SId == sw.SId).ToList();

                    // 判断面板是否适用于当前产品
                    if (!IsSwitchApplicableToProduct(sw, switchContents, productId, productCategoryIds))
                    {
                        continue;
                    }

                    // 构建适用的内容字符串
                    var contentString = BuildSwitchContentString(sw, switchContents, productId, productCategoryIds);

                    // 创建面板对象
                    var switchWithContent = new
                    {
                        SId = sw.SId,
                        Name = sw.Name,
                        Apply = sw.Apply,
                        ApplyCollect = sw.ApplyCollect,
                        DataId = sw.DataId,
                        Identity = sw.Identity,
                        MyOrder = sw.MyOrder,
                        Content = contentString
                    };

                    result.Add(switchWithContent);
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取产品切换面板时出错: {ex.Message}");
                return new List<dynamic>();
            }
        }

        /// <summary>
        /// 判断切换面板是否适用于指定产品
        /// </summary>
        /// <param name="sw">切换面板</param>
        /// <param name="contents">面板内容列表</param>
        /// <param name="productId">产品ID</param>
        /// <param name="productCategoryIds">产品所属分类ID列表</param>
        /// <returns>是否适用</returns>
        private bool IsSwitchApplicableToProduct(products_switch sw, List<products_switch_content> contents,
            int productId, List<int> productCategoryIds)
        {
            switch (sw.Apply)
            {
                case "Website":
                    return true;

                case "Products":
                    return contents.Any(content => IsContentApplicableToProduct(content, productId));

                case "Categories":
                    return contents.Any(content => IsContentApplicableToCategories(content, productCategoryIds));

                default:
                    return false;
            }
        }

        /// <summary>
        /// 构建切换面板的内容字符串
        /// </summary>
        /// <param name="sw">切换面板</param>
        /// <param name="contents">面板内容列表</param>
        /// <param name="productId">产品ID</param>
        /// <param name="productCategoryIds">产品所属分类ID列表</param>
        /// <returns>内容字符串</returns>
        private string BuildSwitchContentString(products_switch sw, List<products_switch_content> contents,
            int productId, List<int> productCategoryIds)
        {
            var applicableContents = new List<string>();

            foreach (var content in contents)
            {
                bool includeContent = sw.Apply switch
                {
                    "Website" => true,
                    "Products" => IsContentApplicableToProduct(content, productId),
                    "Categories" => IsContentApplicableToCategories(content, productCategoryIds),
                    _ => false
                };

                if (includeContent && !string.IsNullOrEmpty(content.Content))
                {
                    applicableContents.Add(content.Content);
                }
            }

            return string.Join("<br/><br/>", applicableContents);
        }

        /// <summary>
        /// 判断内容是否适用于指定产品
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="productId">产品ID</param>
        /// <returns>是否适用</returns>
        private bool IsContentApplicableToProduct(products_switch_content content, int productId)
        {
            if (string.IsNullOrEmpty(content.ApplyID))
            {
                return false;
            }

            var targetId = $"|{productId}|";
            return content.ApplyID.Contains(targetId);
        }

        /// <summary>
        /// 判断内容是否适用于指定分类
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="productCategoryIds">产品所属分类ID列表</param>
        /// <returns>是否适用</returns>
        private bool IsContentApplicableToCategories(products_switch_content content, List<int> productCategoryIds)
        {
            if (string.IsNullOrEmpty(content.ApplyID) || !productCategoryIds.Any())
            {
                return false;
            }

            try
            {
                var contentCategoryIds = content.ApplyID.Trim('|')
                    .Split('|', StringSplitOptions.RemoveEmptyEntries)
                    .Select(int.Parse)
                    .ToList();

                return contentCategoryIds.Any(categoryId => productCategoryIds.Contains(categoryId));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析分类ID时出错: {ex.Message}");
                return false;
            }
        }
    }
}