using Aop.Api.Domain;
using Castle.Components.DictionaryAdapter.Xml;
using Entitys;
using FluentEmail.Core;
using InitQ.Cache;
using MailKit;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Localization;
using NetTaste;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using Org.BouncyCastle.Ocsp;
using SendGrid;
using SendGrid.Helpers.Mail;
using SqlSugar;
using System;
using System.ComponentModel;
using System.Diagnostics.PerformanceData;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.Common.Const;
using YseStore.Common.Helper;
using YseStore.Ext;
using YseStore.IService;
using YseStore.IService.Customer;
using YseStore.IService.Email;
using YseStore.IService.Order;
using YseStore.IService.Products;
using YseStore.IService.Sales;
using YseStore.IService.Shopping;
using YseStore.IService.SiteSystem;
using YseStore.IService.Store;
using YseStore.IService.User;
using YseStore.Model;
using YseStore.Model.Entities.Blog;
using YseStore.Model.Enums;
using YseStore.Model.FromBody;
using YseStore.Model.RequestModels;
using YseStore.Model.RequestModels.Blog;
using YseStore.Model.RequestModels.Products;
using YseStore.Model.Response.Products;
using YseStore.Model.Response.Sales;
using YseStore.Model.Response.User;
using YseStore.Model.VM;
using YseStore.Model.VM.Country;
using YseStore.Model.VM.Order;
using YseStore.Model.VM.Payment;
using YseStore.Model.VM.Payment.Payoneer;
using YseStore.Model.VM.Payment.Paypal;
using YseStore.Model.VM.User;
using YseStore.Service;
using YseStore.Service.Order;
using ZXing;
using static System.Runtime.InteropServices.JavaScript.JSType;
using MemberInfo = System.Reflection.MemberInfo;

namespace YseStore.Controllers
{
    public class AccountController : BaseController
    {
        private static readonly Regex NameRegex = new Regex(@"^[a-zA-Z0-9]+$", RegexOptions.Compiled);

        private readonly IUserService _userService;
        private readonly IStringLocalizer<AccountController> T;

        private readonly ICustomerListService _customerListService;
        private readonly IProductService _productService;
        private readonly ISystemEmailTplServices _emailTplService;
        private readonly ICaching _caching;
        private readonly IUserOperationLogServices _userOperationLogServices;
        private readonly ISalesCouponService _salesCouponService;
        private readonly IOrderConsumerService _orderConsumerService;
        private readonly ICommonService _commonService;
        private readonly ISettingBasisService _settingBasisService;
        private readonly IToolService _toolService;
        private readonly IUserAddressBookServices _userAddressBookServices;
        private readonly IShoppingCartServices _shoppingCartServices;
        private readonly ICurrencyService _currencyService;
        private readonly ILanguageServices _languageServices;
        private readonly IUserLabelCollectionServices _userLabelCollectionServices;
        private readonly ISalesAreasService _salesAreasService;
        private readonly IFlashSaleService _flashSaleService;


        public AccountController(
            IUserService userService,
            IStringLocalizer<AccountController> localizer,
            ICustomerListService customerListService,
            IProductService productService, ISystemEmailTplServices systemEmailTplServices, ICaching caching,
            IUserOperationLogServices userOperationLogServices, ISalesCouponService salesCouponService,
            IOrderConsumerService orderConsumerService, ICommonService commonService, ISettingBasisService settingBasisService,
            IToolService toolService, IUserAddressBookServices userAddressBookServices, IShoppingCartServices shoppingCartServices, ICurrencyService currencyService, ILanguageServices languageServices, IUserLabelCollectionServices userLabelCollectionServices, ISalesAreasService salesAreasService, IFlashSaleService flashSaleService)
        {
            _userService = userService;
            T = localizer;
            _customerListService = customerListService;
            _productService = productService;
            _emailTplService = systemEmailTplServices;
            _caching = caching;
            _userOperationLogServices = userOperationLogServices;
            _salesCouponService = salesCouponService;
            _orderConsumerService = orderConsumerService;
            _commonService = commonService;
            _settingBasisService = settingBasisService;
            _toolService = toolService;
            _userAddressBookServices = userAddressBookServices;
            _shoppingCartServices = shoppingCartServices;
            _currencyService = currencyService;
            _languageServices = languageServices;
            _userLabelCollectionServices = userLabelCollectionServices;
            _salesAreasService = salesAreasService;
            _flashSaleService = flashSaleService;
        }



        public async Task<IActionResult> MyOrders(int page = 1, string keyword = "", int OrderStatus = 0)
        {
            // 设置每页显示的数量
            int pageSize = 20;

            // 确保页码有效
            if (page < 1) page = 1;

            try
            {
                // 获取当前用户ID
                if (!int.TryParse(User.FindFirstValue(ClaimTypes.NameIdentifier), out int userId))
                {
                    // 未登录或ID无效，重定向到登录页
                    return RedirectToAction("Login");
                }
                var result = await _orderConsumerService.QueryAsync(userId, keyword, OrderStatus, page, pageSize);
                List<StatusItem> statusList = GetAllStatusInfo();
                // 创建视图模型
                var model = new
                {
                    MyOrders = result,
                    CurrentPage = page,
                    TotalPages = (int)Math.Ceiling(result.TotalCount / (double)pageSize),
                    Keyword = keyword,
                    OrderStatus = OrderStatus,
                    statusList = statusList
                };
                // 直接将模型传递给视图
                return View(model);
            }
            catch (Exception ex)
            {
                // 记录异常以便调试
                Console.WriteLine($"获取列表时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                // 直接将模型传递给视图
                return View();
            }
        }
        // 获取枚举的Description特性值
        public static string GetDescription(Enum value)
        {
            Type type = value.GetType();
            MemberInfo[] memInfo = type.GetMember(value.ToString());
            if (memInfo != null && memInfo.Length > 0)
            {
                object[] attrs = memInfo[0].GetCustomAttributes(typeof(DescriptionAttribute), false);
                if (attrs != null && attrs.Length > 0)
                {
                    return ((DescriptionAttribute)attrs[0]).Description;
                }
            }
            return value.ToString();
        }

        // 生成所有枚举项的字典列表
        public class StatusItem
        {
            public string Key { get; set; }
            public string Value { get; set; }
        }

        public static List<StatusItem> GetAllStatusInfo()
        {
            var list = new List<StatusItem>();
            foreach (OrderStatusEnum status in Enum.GetValues(typeof(OrderStatusEnum)))
            {
                string description = GetDescription(status);
                string value = ((int)status).ToString();
                list.Add(new StatusItem { Key = description, Value = value });
            }
            return list;
        }
        //public IActionResult OrderDetail()
        //{
        //    return View();
        //}
        public async Task<IActionResult> OrderDetail(int id = 0)
        {
            try
            {
                if (id <= 0)
                {
                    return RedirectToAction("MyOrders");
                }
                // 获取当前用户ID
                if (!int.TryParse(User.FindFirstValue(ClaimTypes.NameIdentifier), out int userId))
                {
                    // 未登录或ID无效，重定向到登录页
                    return RedirectToAction("Login");
                }
                var OrderDetailModel = await _orderConsumerService.GetOrderDetailByOrderIdAsync(id, userId);
                // 产品数量-产品价格-积分价格-运费-手续费-订单总价格-货币符号-支付方式
                var OrdersAmount = await _orderConsumerService.GetOrdersAmount(id, userId);
                var OrderRefund = await _orderConsumerService.GetOrderRefundByOrderIdAsync(id);
                var ordersProductsLists = await _orderConsumerService.GetOrders_Products_Lists(id);
                var OrderPackage = await _orderConsumerService.GetPackageAsync(id);
                // 处理包裹列表
                var ValidPackages = OrderPackage
                     .Where(p => p.ParentId != 0 && p.ProInfo != "[]")
                     .ToList();

                if (!ValidPackages.Any())
                {
                    ValidPackages = OrderPackage
                        .Where(p => p.ParentId == 0 && p.ProInfo != "[]")
                        .ToList();
                }


                #region
                var ordersLog = await _orderConsumerService.GetOrdersLogByOrderIdAsync(id, userId);
                var currentOrderStatus = OrderDetailModel.OrderStatus;
                // 构建状态步骤列表
                var statusSteps = new List<OrderStatusStep>();
                //var statusTimeDict = ordersLog.ToDictionary(log => log.OrderStatus, log => log.AccTime);
                // 或取最后一个出现的状态时间（假设按时间排序）
                var statusTimeDict = ordersLog
                    .OrderByDescending(log => log.AccTime) // 按时间倒序
                    .GroupBy(log => log.OrderStatus)
                    .ToDictionary(g => g.Key, g => g.First().AccTime);

                foreach (OrderStatusEnum status in Enum.GetValues(typeof(OrderStatusEnum)))
                {
                    int k = (int)status;
                    string v = status.ToString();
                    // 业务逻辑处理（对应PHP条件）
                    if (currentOrderStatus == 3)
                    { // 支付失败
                        if (!statusTimeDict.ContainsKey(k)) continue;
                        if (k > 3) break;
                    }
                    else if (currentOrderStatus == 7)
                    { // 订单取消
                        if (k == 3 || k == 6 || (!statusTimeDict.ContainsKey(k) && k != 7)) continue;
                    }
                    else
                    {
                        if (k == 3 || k == 7) continue;
                    }

                    if (k == 2 && !statusTimeDict.ContainsKey(k)) continue;

                    statusSteps.Add(new OrderStatusStep
                    {
                        Status = k,
                        StatusName = "user.account.OrderStatusAry_" + k,
                        StatusTime = statusTimeDict.ContainsKey(k) ? DateTimeHelper.ConvertToBeijingTime(statusTimeDict[k].Value) : null,
                        IconClass = k.ToString() // 状态到图标的映射字典
                    });
                }


                #endregion



                // 创建视图模型
                var model = new
                {
                    OrderDetailModel,
                    ordersProductsLists,
                    OrderPackage = ValidPackages,
                    ProductCount = OrdersAmount.Item1,
                    TotalProductPrice = OrdersAmount.Item2,
                    points = OrdersAmount.Item3,
                    shippingFee = OrdersAmount.Item4,
                    commission = OrdersAmount.Item5,
                    OrderSum = OrdersAmount.Item6,
                    OrderSymbol = OrdersAmount.Item7,
                    paymentMethod = OrdersAmount.Item8,
                    DiscountPrice = OrdersAmount.Item9,
                    CouponPrice = OrdersAmount.Item10,
                    TaxPrice = OrdersAmount.Item11,
                    OIdBase64 = OrderDetailModel.OId.ToBase64(),
                    OrderRefundAmount = OrderRefund?.Amount ?? 0,
                    ShippingAddress = OrderDetailModel.ShippingAddressLine2 + "," + OrderDetailModel.ShippingAddressLine1 + ","
                    + OrderDetailModel.ShippingCity + "," +
                    OrderDetailModel.ShippingState + "," + OrderDetailModel.ShippingCountry,
                    order_status_steps = statusSteps,
                    current_order_status = currentOrderStatus
                };

                // 直接将模型传递给视图
                return View(model);
            }
            catch (Exception ex)
            {
                // 记录异常以便调试
                Console.WriteLine($"获取详情时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");

                return RedirectToAction("MyOrders");
            }
        }

        public class OrderStatusStep
        {
            public int Status { get; set; }
            public string StatusName { get; set; }
            public DateTime? StatusTime { get; set; }
            public string IconClass { get; set; }
        }


        //public async Task<(string)> sdfsf(int id) {
        //    var OrderPackage = await _orderConsumerService.GetPackageAsync(id);
        //    var ordersProductsLists = await _orderConsumerService.GetOrders_Products_Lists(id);


        //    // 处理包裹列表
        //   var ValidPackages = OrderPackage
        //        .Where(p => p.ParentId != 0 && p.ProInfo != "[]")
        //        .ToList();

        //    if (!ValidPackages.Any())
        //    {
        //        ValidPackages = OrderPackage
        //            .Where(p => p.ParentId == 0 && p.ProInfo != "[]")
        //            .ToList();
        //    }

        //    // 预处理包裹数据
        //    foreach (var package in ValidPackages)
        //    {
        //        var proInfo = JsonConvert.DeserializeObject<Dictionary<string, int>>(package.ProInfo);

        //        var ProductIds = proInfo.Keys.Select(int.Parse).ToList();

        //        // 处理物流信息
        //        if (!string.IsNullOrEmpty(package.Carrier))
        //        {
        //            var carrier = JObject.Parse(package.Carrier);
        //            package.CarrierName = carrier["name"]?.ToString();
        //            package.CarrierKey = carrier["key"]?.ToString();
        //        }
        //    }

        //    // 过滤产品列表
        //    var allProductIds = ValidPackages.SelectMany(p => p.ProductIds).Distinct();
        //    var OrderProducts = ordersProductsLists
        //        .Where(p => allProductIds.Contains(p.LId))
        //        .ToList();

        //}




        public async Task<IActionResult> MyInbox()
        {
            try
            {

                // 获取当前用户ID
                if (!int.TryParse(User.FindFirstValue(ClaimTypes.NameIdentifier), out int userId))
                {
                    // 未登录或ID无效，重定向到登录页
                    return RedirectToAction("Login");
                }
                var UserModel = await _orderConsumerService.GetUserByUserIdAsync(userId);
                var result = await _orderConsumerService.GetordersLogListByUserId(userId);

                // 创建视图模型
                var model = new
                {
                    UserModel,
                    OrdersLog = result,
                    userId = userId
                };
                // 直接将模型传递给视图
                return View(model);
            }
            catch (Exception ex)
            {
                // 记录异常以便调试
                Console.WriteLine($"获取列表时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                return RedirectToAction("MyOrders");
            }
        }
        public async Task<IActionResult> MyInboxURl()
        {
            try
            {

                // 获取当前用户ID
                if (!int.TryParse(User.FindFirstValue(ClaimTypes.NameIdentifier), out int userId))
                {
                    // 未登录或ID无效，重定向到登录页
                    return RedirectToAction("Login");
                }
                var UserModel = await _orderConsumerService.GetUserByUserIdAsync(userId);
                var result = await _orderConsumerService.GetordersLogListByUserId(userId);

                // 创建视图模型
                var model = new
                {
                    UserModel,
                    OrdersLog = result,
                    userId = userId
                };
                // 直接将模型传递给视图
                return View(model);
            }
            catch (Exception ex)
            {
                // 记录异常以便调试
                Console.WriteLine($"获取列表时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                return RedirectToAction("MyOrders");
            }
        }
        public async Task<IActionResult> MyContact(int id = 0)
        {


            try
            {
                if (id <= 0)
                {
                    return RedirectToAction("MyOrders");
                }
                // 获取当前用户ID
                if (!int.TryParse(User.FindFirstValue(ClaimTypes.NameIdentifier), out int userId))
                {
                    // 未登录或ID无效，重定向到登录页
                    return RedirectToAction("Login");
                }
                var OrderDetailModel = await _orderConsumerService.GetOrderDetailByOrderIdAsync(id, userId);
                var result = await _orderConsumerService.GetordersLogListByOId(OrderDetailModel.OId);

                // 创建视图模型
                var model = new
                {
                    OrderDetailModel,
                    OrdersLog = result,
                    OrderTime = DateTimeHelper.ConvertToBeijingTime(Convert.ToInt32(OrderDetailModel.OrderTime)).ToString("MMM d, yyyy")
                };
                // 直接将模型传递给视图
                return View(model);
            }
            catch (Exception ex)
            {
                // 记录异常以便调试
                Console.WriteLine($"获取列表时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                return RedirectToAction("MyOrders");
            }
        }
        [Authorize]
        public async Task<IActionResult> AccountSettings()
        {
            if (CurrentUserId == 0)
            {
                return Redirect("/account/signin");
            }

            var UserData = await _orderConsumerService.GetUserByUserIdAsync(CurrentUserId);

            // 创建视图模型
            var model = new
            {
                UserData
            };


            return View(model);
        }
        /// <summary>
        /// 帐户设置--修改用户信息
        /// </summary>
        /// <returns></returns>
        [HttpPost("api/account/comment/SaveUser")]
        [Authorize]
        public async Task<IActionResult> OnSaveUser()
        {
            if (CurrentUserId == 0)
            {
                return Redirect("/account/signin");
            }
            var entity = await _orderConsumerService.GetUserByUserIdAsync(CurrentUserId);
            entity.FirstName = Request.Form["FirstName"].ToString();
            entity.LastName = Request.Form["LastName"].ToString();
            entity.Email = Request.Form["Email"].ToString().Trim();
            entity.NickName = Request.Form["NickName"].ToString();
            var b = await _orderConsumerService.UpdateUserDataByUserId(entity, CurrentUserId);
            var jm = new WebApiCallBack();
            if (b)
            {

                jm.status = true;
                jm.msg = T["web.global.submit_success"].Value;//"设置成功";
            }
            else
            {
                jm.status = false;
                jm.msg = T["web.global.set_error"].Value;//"设置失败";
            }
            return Json(jm);
        }
        /// <summary>
        /// 帐户设置--修改用户密码
        /// </summary>
        /// <returns></returns>
        [HttpPost("api/account/comment/SavePwd")]
        [Authorize]
        public async Task<IActionResult> OnSavePwd()
        {
            if (CurrentUserId == 0)
            {
                return Redirect("/account/signin");
            }
            var jm = new WebApiCallBack();
            var existing_pwd = Request.Form["existing_pwd"].ToString();
            var password = Request.Form["password"].ToString();
            var confirm_pwd = Request.Form["confirm_pwd"].ToString();
            var entity = await _orderConsumerService.GetUserByUserIdAsync(CurrentUserId);
            var existing_pwdTwo = _userService.Password(existing_pwd);
            if (entity.Password != existing_pwdTwo)
            {
                jm.status = false;
                jm.msg = T["user.global.existing_pwd"].Value + T["user.forgot.matchDesajuste"].Value;//"现用密码错误";
                return Json(jm);
            }
            if (password != confirm_pwd)
            {
                jm.status = false;
                jm.msg = T["user.global.password"].Value + T["user.forgot.matchDesajuste"].Value;//"密码错误";
                return Json(jm);
            }
            entity.Password = password;
            var b = await _orderConsumerService.UpdateUserDataByUserId(entity, CurrentUserId);

            if (b)
            {

                jm.status = true;
                jm.msg = T["web.global.submit_success"].Value;//"设置成功";
            }
            else
            {
                jm.status = false;
                jm.msg = T["web.global.set_error"].Value;//"设置失败";
            }
            return Json(jm);
        }



        #region 收货地址
        /// <summary>
        /// 收货地址详情
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> Addaddress(int id = 0, int Billing = 0)
        {
            if (CurrentUserId == 0)
            {
                return Redirect("/account/signin");
            }
            var IsProvince = false;
            var address = await _userAddressBookServices.QueryByClauseAsync(it => it.AId == id && it.UserId == CurrentUserId);
            var CountryList = await _userAddressBookServices.GetCountryData();
            var ProvinceList = await _userAddressBookServices.GetCountry_statesData();
            if (ProvinceList != null && ProvinceList.Count > 0)
            {
                IsProvince = true;
            }
            if (id > 0 && address != null)
            {
                if (address.SId > 0)
                {
                    IsProvince = true;
                    ProvinceList = await _userAddressBookServices.GetCountry_statesDataByCId(Convert.ToInt32(address.CId));
                }
            }
            //var OrderCountryJson = System.Text.Json.JsonSerializer.Serialize(OrderCountryResponseList);
            // 创建视图模型
            var model = new
            {
                address,
                CountryList,
                ProvinceList,
                IsProvince,
                AId = id,
                Billing
            };


            return View(model);
        }

        /// <summary>
        /// 保存收货地址
        /// </summary>
        /// <returns></returns>
        [HttpPost("api/account/comment/SaveAddress")]
        [Authorize]
        public async Task<IActionResult> OnSaveAddress()
        {

            user_address_book entity = new user_address_book();
            entity.CId = Convert.ToInt32(Request.Form["CId"]);
            if (!string.IsNullOrEmpty(Request.Form["SId"].ToString()))
            {
                entity.SId = Convert.ToInt32(Request.Form["SId"]);
            }

            entity.FirstName = Request.Form["FirstName"].ToString();
            entity.LastName = Request.Form["LastName"].ToString();
            entity.PhoneNumber = Request.Form["PhoneNumber"].ToString();
            entity.AddressLine1 = Request.Form["AddressLine1"].ToString();
            entity.AddressLine2 = Request.Form["AddressLine2"].ToString();
            entity.City = Request.Form["City"].ToString();
            entity.ZipCode = Request.Form["ZipCode"].ToString();
            entity.TaxCode = Request.Form["TaxCode"].ToString();
            entity.AId = Convert.ToInt32(Request.Form["AId"]);
            entity.IsBillingAddress = Request.Form["Billing"] == "1" ? true : false;
            var CountryData = await _userAddressBookServices.GetCountryDataByCId(Convert.ToInt32(entity.CId));
            entity.CountryCode = CountryData?.Code ?? 0;
            if (entity.SId != null && entity.SId > 0)
            {
                var CountrystatesData = await _userAddressBookServices.GetCountry_statesDataBySId(Convert.ToInt32(entity.SId));
                entity.State = CountrystatesData?.States ?? "";
            }
            else
            {
                entity.State = "";
            }

            //[FromBody] user_address_book entity
            var jm = new WebApiCallBack();
            if (CurrentUserId == 0)
            {
                return Redirect("/account/signin");
            }

            if (entity.AId > 0)
            {
                if (entity.IsDefault == false)
                {
                    if (await _userAddressBookServices.GetCountAsync(p => p.UserId == entity.UserId && p.IsDefault == true && p.AId != entity.AId) == 0)
                    {
                        entity.IsDefault = true;
                    }

                }

                var address = await _userAddressBookServices.QueryById(entity.AId);
                if (address == null || address.UserId != CurrentUserId)
                {
                    jm.status = false;
                    jm.msg = T["web.global.no_data"].Value;//沒有数据
                    return Json(jm);
                }
                address.FirstName = entity.FirstName;
                address.LastName = entity.LastName;
                address.AddressLine1 = entity.AddressLine1;
                address.AddressLine2 = entity.AddressLine2;
                address.City = entity.City;
                address.State = entity.State;
                address.CountryCode = entity.CountryCode;
                address.ZipCode = entity.ZipCode;
                address.PhoneNumber = entity.PhoneNumber;
                address.IsBillingAddress = entity.IsBillingAddress;
                //address.IsDefault = false;
                //address.AdditionalInfoData = "[]";
                //address.AdditionalInfoName = "[]";
                address.SId = entity.SId;
                address.CId = entity.CId;
                //address.CodeOption = 0;
                address.TaxCode = entity.TaxCode;


                var update = await _userAddressBookServices.Update(address);
                if (update)
                {
                    if (address.IsDefault)
                    {
                        //如果设置为默认地址，则将其他地址的默认标志设置为false
                        await _userAddressBookServices.UpdateAsync(p => new user_address_book { IsDefault = false },
                            p => p.UserId == CurrentUserId && p.AId != address.AId);
                    }

                    jm.status = true;
                    jm.msg = T["web.global.submit_success"].Value;//"设置成功";
                }
                else
                {
                    jm.status = false;
                    jm.msg = T["web.global.set_error"].Value;//"设置失败";
                }


            }
            else
            {
                entity.IsBillingAddress = entity.IsBillingAddress;
                entity.CodeOption = 0;
                entity.AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                entity.UserId = CurrentUserId;
                if (entity.IsDefault == false)
                {
                    if (await _userAddressBookServices.GetCountAsync(p => p.UserId == entity.UserId && p.IsDefault == true) == 0)
                    {
                        entity.IsDefault = true;
                    }
                }

                var add = await _userAddressBookServices.AddWithIntId(entity);
                if (add > 0)
                {
                    if (entity.IsDefault)
                    {
                        //如果设置为默认地址，则将其他地址的默认标志设置为false
                        await _userAddressBookServices.UpdateAsync(p => new user_address_book { IsDefault = false },
                            p => p.UserId == CurrentUserId && p.AId != entity.AId);
                    }

                    jm.status = true;
                    jm.msg = T["web.global.submit_success"].Value;//"设置成功";
                }
                else
                {
                    jm.status = false;
                    jm.msg = T["web.global.set_error"].Value;//"设置失败";
                }

            }


            return Json(jm);
        }


        /// <summary>
        /// 根据国家id获取省份
        /// </summary>
        /// <returns></returns>
        [HttpPost("api/account/comment/CountryCId")]
        public async Task<IActionResult> GetCountryCId()
        {
            try
            {
                var IsProvince = false;
                var CId = Convert.ToInt32(Request.Form["CId"]);
                var result = await _userAddressBookServices.GetCountry_statesDataByCId(CId);
                if (result != null && result.Count > 0)
                {
                    IsProvince = true;
                }
                return Json(new { success = true, isProvince = IsProvince, message = result });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据国家id获取省份时出错: {ex.Message}");
                return Json(new { success = false, message = new List<country_states_iso>() });
            }
        }


        /// <summary>
        /// 收货地址--设置默认地址
        /// </summary>
        /// <returns></returns>
        [HttpPost("api/account/comment/SetDefaultAddress")]
        [Authorize]
        public async Task<IActionResult> OnSetDefaultAddress()
        {
            var jm = new WebApiCallBack();
            if (CurrentUserId == 0)
            {
                return Redirect("/account/signin");
            }
            var aId = Convert.ToInt32(Request.Form["addressId"]);
            var isDefault = Request.Form["isDefault"] == "true" ? true : false;
            if (aId <= 0)
            {
                jm.status = false;
                jm.msg = T["web.global.no_data"];//没有数据
                return Json(jm);
            }

            var b = await _userAddressBookServices.SetDefaultAddress(CurrentUserId, aId);


            if (b)
            {

                jm.status = true;
                jm.msg = T["web.global.submit_success"].Value;//"设置成功";
            }
            else
            {
                jm.status = false;
                jm.msg = T["web.global.set_error"].Value;//"设置失败";
            }
            return Json(jm);


        }


        /// <summary>
        /// 删除收货地址
        /// </summary>
        /// <returns></returns>
        [HttpPost("api/account/comment/DeleteAddress")]
        [Authorize]
        public async Task<IActionResult> OnDeleteAddress()
        {
            var jm = new WebApiCallBack();
            if (CurrentUserId == 0)
            {
                return Redirect("/account/signin");
            }
            var aId = Convert.ToInt32(Request.Form["AId"]);
            if (aId <= 0)
            {
                jm.status = false;
                jm.msg = T["web.global.no_data"];//没有数据
                return Json(jm);
            }
            var address = await _userAddressBookServices.QueryById(aId);
            if (address == null || address.UserId != CurrentUserId)
            {
                jm.status = false;
                jm.msg = T["web.global.no_data"];//没有数据
                return Json(jm);
            }
            var del = await _userAddressBookServices.DeleteById(aId);
            if (del)
            {
                //如果只有一个地址了，默认将最后一个剩余的地址设置为默认。
                if (await _userAddressBookServices.GetCountAsync(p => p.UserId == CurrentUserId) == 1)
                {
                    var lastAddress = await _userAddressBookServices.QueryByClauseAsync(p => p.UserId == CurrentUserId);
                    if (lastAddress != null)
                    {
                        lastAddress.IsDefault = true;
                        await _userAddressBookServices.Update(lastAddress);
                    }
                }


                jm.status = true;
                jm.msg = T["web.global.delete_success"];//删除成功
            }
            else
            {
                jm.status = false;
                jm.msg = T["web.global.delete_error"];//删除失败
            }
            return Json(jm);


        }



        /// <summary>
        /// 获取收货地址列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> MyAddress()
        {
            if (CurrentUserId == 0)
            {

                return Redirect("/account/signin");
            }
            var addressList = await _userAddressBookServices.Query(it => it.UserId == CurrentUserId);
            addressList = addressList.OrderByDescending(it => it.AId).ToList();
            var countryDataList = await _userAddressBookServices.GetCountryDataList();
            if (addressList != null && addressList.Count > 0)
            {
                foreach (var item in addressList)
                {
                    var CountryData = countryDataList.Where(x => x.CId == item.CId).First();
                    item.Country = CountryData?.Country ?? "";
                }
            }
            var model = new
            {
                ShippingAddress = addressList.Where(x => x.IsBillingAddress == false).ToList(),
                BillingAddress = addressList.Where(x => x.IsBillingAddress == true).ToList()
            };
            return View(model);
        }
        #endregion

        public async Task<IActionResult> MyCoupon(int status = 0)
        {
            try
            {
                // 获取当前用户ID
                if (!int.TryParse(User.FindFirstValue(ClaimTypes.NameIdentifier), out int userId))
                {
                    // 未登录或ID无效，重定向到登录页
                    return RedirectToAction("Login");
                }

                //获取我的优惠券列表
                //获取用户币种
                var currency = await _currencyService.GetCurrency(CurrentCurrency);

                //获取后台币种
                var defaultCurrency = await _currencyService.GetManageDefaultCurrency();
                var list = await _salesCouponService.QueryMyCouponAsync(userId, status);
                if (list.Any())
                {
                    foreach (var item in list)
                    {
                        var ruleBuilder = new StringBuilder();

                        // 使用要求条件
                        if (item.ConditionType == ConditionTypeEnum.MinAmount)
                        {
                            var convertprice = _currencyService.ShowPriceFormat(item.ConditionPrice, currency, defaultCurrency);
                            ruleBuilder.Append($"For orders over{currency.Symbol}{convertprice.Item1}，");
                        }
                        else
                        {
                            ruleBuilder.Append($"Order more than {item.ConditionQty} products");
                        }

                        // 优惠类型逻辑
                        if (item.CouponType == CouponTypeEnum.Discount) // 折扣券
                        {
                            ruleBuilder.Append($"{100 - item.CouponTypeValue} % off");
                            if (item.IsMaxAmount && item.MaxAmount > 0) // 如果有最高抵扣金额
                            {
                                var convertprice = _currencyService.ShowPriceFormat(item.MaxAmount, currency, defaultCurrency);
                                ruleBuilder.Append($"，Maximum Discount Price: {convertprice.Item1}元");
                            }
                        }
                        else // 现金券
                        {
                            var convertprice = _currencyService.ShowPriceFormat(item.CouponTypeValue, currency, defaultCurrency);
                            ruleBuilder.Append($"{currency.Symbol}{convertprice.Item1}off");
                        }
                        item.Rules = ruleBuilder.ToString();
                    }
                }
                ViewBag.IsEmpty = !list.Any();
                return View(list);
            }
            catch (Exception ex)
            {
                // 记录错误日志
                Console.WriteLine($"获取我的优惠券列表时出错: {ex.Message}");
                ViewBag.IsEmpty = true;
                ViewBag.ErrorMessage = "无法加载我的优惠券列表，请稍后再试";
                return View(new List<MyCouponResponse>());
            }
        }

        //public IActionResult MyOrders()
        //{
        //    return View();
        //}

        //public IActionResult OrderDetail()
        //{
        //    return View();
        //}

        /// <summary>
        /// 提交回复
        /// </summary>
        /// <param name="reviewId">评论ID</param>
        /// <param name="request">回复请求对象</param>
        /// <returns>JSON结果</returns>
        [HttpPost("api/account/comment/userreply")]
        public async Task<IActionResult> SubmitUserReply()
        {
            try
            {
                var MsgPicPath = "";
                var MsgVideoPath = "";
                var Content = Request.Form["Content"];
                var MId = Convert.ToInt32(Request.Form["MId"]);
                var OId = Request.Form["OId"];
                var VideoPath = Request.Form["VideoPath"];
                var picPath = Request.Form["PicPath"].ToString();
                if (!string.IsNullOrEmpty(picPath))
                {
                    MsgPicPath = await MsgPicPathVideoPath(picPath);
                }
                if (!string.IsNullOrEmpty(VideoPath))
                {
                    MsgVideoPath = await MsgPicPathVideoPath(VideoPath);
                }

                //Task<UserMessageMessageOrdersResponse> AddordersLogList(string Message, string MsgPicPath, string MsgVideoPath, int MId)
                var result = await _orderConsumerService.AddordersLogList(Content, MsgPicPath, MsgVideoPath, MId);
                if (result != null)
                {
                    return Json(new { success = true, message = "回复提交成功，感谢您的参与！" });
                }
                else
                {
                    return Json(new { success = false, message = "回复提交失败，请稍后再试" });
                }


                //if (replyId > 0)
                //{
                //    return Json(new { success = true, message = "回复提交成功，感谢您的参与！", data = replyId });
                //}
                //else
                //{
                //    return Json(new { success = false, message = "回复提交失败，请稍后再试" });
                //}

            }
            catch (Exception ex)
            {
                Console.WriteLine($"提交回复时出错: {ex.Message}");
                return Json(new { success = false, message = "系统错误，请稍后再试" });
            }
        }
        /// <summary>
        /// 提交回复
        /// </summary>
        /// <param name="reviewId">评论ID</param>
        /// <param name="request">回复请求对象</param>
        /// <returns>JSON结果</returns>
        [HttpPost("api/account/comment/userreplyInbox")]
        public async Task<IActionResult> SubmituserreplyInbox()
        {
            try
            {
                var MsgPicPath = "";
                var MsgVideoPath = "";
                var Content = Request.Form["Content"];
                var MId = Request.Form["MId"].ObjToInt();
                var userId = Request.Form["userId"].ObjToInt();
                var VideoPath = Request.Form["VideoPath"];
                var picPath = Request.Form["PicPath"].ToString();
                if (!string.IsNullOrEmpty(picPath))
                {
                    MsgPicPath = await MsgPicPathVideoPath(picPath);
                }
                if (!string.IsNullOrEmpty(VideoPath))
                {
                    MsgVideoPath = await MsgPicPathVideoPath(VideoPath);
                }

                //Task<UserMessageMessageOrdersResponse> AddordersLogList(string Message, string MsgPicPath, string MsgVideoPath, int MId)
                var result = await _orderConsumerService.AddMyInboxLogList(Content, MsgPicPath, MsgVideoPath, MId, userId);
                if (result != null)
                {
                    return Json(new { success = true, message = "回复提交成功，感谢您的参与！" });
                }
                else
                {
                    return Json(new { success = false, message = "回复提交失败，请稍后再试" });
                }


                //if (replyId > 0)
                //{
                //    return Json(new { success = true, message = "回复提交成功，感谢您的参与！", data = replyId });
                //}
                //else
                //{
                //    return Json(new { success = false, message = "回复提交失败，请稍后再试" });
                //}

            }
            catch (Exception ex)
            {
                Console.WriteLine($"提交回复时出错: {ex.Message}");
                return Json(new { success = false, message = "系统错误，请稍后再试" });
            }
        }

        /// <summary>
        /// 确认收货
        /// </summary>
        /// <returns></returns>
        [HttpPost("api/account/comment/receiving")]
        public async Task<IActionResult> SubmitOrderReceiving()
        {
            try
            {
                var OrderId = Convert.ToInt32(Request.Form["OrderId"]);
                var result = await _orderConsumerService.OrdersReceivingAction(OrderId);

                if (result)
                {
                    return Json(new { success = true, message = "确认收货成功，感谢您的参与！" });
                }
                else
                {
                    return Json(new { success = false, message = "确认收货失败，请稍后再试" });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"提交确认收货时出错: {ex.Message}");
                return Json(new { success = false, message = "系统错误，请稍后再试" });
            }
        }
        /// <summary>
        /// 取消订单
        /// </summary>
        /// <returns></returns>
        [HttpPost("api/account/comment/cancelorder")]
        public async Task<IActionResult> SubmitUsercancelorder()
        {
            try
            {
                var OrderId = Convert.ToInt32(Request.Form["OrderId"]);
                var result = await _orderConsumerService.OrdersCancelAction(OrderId);

                if (result)
                {
                    return Json(new { success = true, message = "取消订单成功，感谢您的参与！" });
                }
                else
                {
                    return Json(new { success = false, message = "取消订单失败，请稍后再试" });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"提交取消订单时出错: {ex.Message}");
                return Json(new { success = false, message = "系统错误，请稍后再试" });
            }
        }

        public async Task<string> MsgPicPathVideoPath(string picPath)
        {
            var MsgPicPath = "";
            // 分割Base64数据
            string[] parts = picPath.Split(new[] { ',' }, 2);
            if (parts.Length != 2)
                return "无效的图片格式";

            string base64Data = parts[1];
            string mimeType = parts[0].Split(new[] { ';' }, 2)[0].Split(':')[1]; // 提取MIME类型（如image/jpeg）
            string fileExtension = mimeType.Split('/')[1]; // 提取扩展名（如jpeg）
            string fileName = $"{Guid.NewGuid()}.{fileExtension}"; // 生成唯一文件名

            byte[] bytes = Convert.FromBase64String(base64Data);

            // 创建IFormFile实例
            var formFile = new Models.MemoryFormFile(bytes, fileName, mimeType);

            // 模拟表单文件集合（假设原逻辑使用Filedata参数）
            List<IFormFile> files = new List<IFormFile> { formFile };

            var filesStorageOptions = await _settingBasisService.GetFilesStorageOptions();
            var maxSize = 1024 * 1024 * filesStorageOptions.MaxSize;

            if (files.Count == 0)
                return "未选择任何文件";

            var file = files[0];
            string fileExt = Path.GetExtension(file.FileName).ToLowerInvariant();

            // 验证文件大小
            if (file.Length > maxSize)
            {
                string msg = $"上传文件大小超过限制，最大允许上传{filesStorageOptions.MaxSize}M";
                return msg;
            }

            // 验证文件扩展名
            if (string.IsNullOrEmpty(fileExt) ||
                !filesStorageOptions.FileTypes.Split(',')
                    .Select(t => t.Trim().ToLower())
                    .Contains(fileExt.Substring(1).ToLower()))
            {
                string msg = $"不允许的文件类型，请上传：{filesStorageOptions.FileTypes}";
                return msg;
            }

            // 执行上传逻辑
            object fileObj;
            if (filesStorageOptions.StorageType == Consts.FilesStorageType_LocalStorage)
            {
                fileObj = await _toolService.UpLoadFileForLocalStorage(filesStorageOptions, fileExt, file);
                string jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(fileObj);
                dynamic jsonObj = Newtonsoft.Json.JsonConvert.DeserializeObject(jsonString);
                MsgPicPath = jsonObj.filePath;
            }
            else if (filesStorageOptions.StorageType == Consts.FilesStorageType_AliYunOSS)
            {
                fileObj = await _toolService.UpLoadFileForAliYunOSS(filesStorageOptions, fileExt, file);
                string jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(fileObj);
                dynamic jsonObj = Newtonsoft.Json.JsonConvert.DeserializeObject(jsonString);
                MsgPicPath = jsonObj.filePath;
            }
            else
            {
                return "不支持的存储类型";
            }
            return MsgPicPath;

        }


        public IActionResult MyPoints()
        {
            return View();
        }

        public IActionResult PointsRedeem()
        {
            return View();
        }




        [HttpGet]
        [Authorize]
        public async Task<IActionResult> MyProfile()
        {
            if (CurrentUserId == 0)
            {
                return Redirect("/account/signin");
            }
            var UserData = await _orderConsumerService.GetUserByUserIdAsync(CurrentUserId);

            var BillingAddress = await _userAddressBookServices.QueryByClauseAsync(it => it.IsBillingAddress == true && it.UserId == CurrentUserId);
            var address = await _userAddressBookServices.QueryByClauseAsync(it => it.IsBillingAddress == false && it.IsDefault == true && it.UserId == CurrentUserId);
            var countryDataList = await _userAddressBookServices.GetCountryDataList();
            if (BillingAddress != null)
            {
                var CountryData = countryDataList.Where(x => x.CId == BillingAddress.CId).First();
                BillingAddress.Country = CountryData?.Country ?? "";
            }
            if (address != null)
            {
                var CountryData = countryDataList.Where(x => x.CId == address.CId).First();
                address.Country = CountryData?.Country ?? "";
            }

            // 创建视图模型
            var model = new
            {
                UserData,
                BillingAddress,
                address
            };


            return View(model);
        }

        public IActionResult MyReview()
        {
            return View();
        }

        public IActionResult ReviewDetail()
        {
            return View();
        }

        [Authorize]
        public async Task<IActionResult> MyWishlist()
        {
            try
            {
                // 获取当前用户ID
                if (!int.TryParse(User.FindFirstValue(ClaimTypes.NameIdentifier), out int userId))
                {
                    // 未登录或ID无效，重定向到登录页
                    return RedirectToAction("Login");
                }

                // 查询用户收藏列表
                var favoriteList = await _customerListService.GetFavoriteProducts(userId);

                if (!favoriteList.Any())
                {
                    // 如果收藏列表为空，显示空视图
                    ViewBag.IsEmpty = true;
                    return View(new List<WishlistViewModel>());
                }

                // 获取币种信息
                var userCurrency = await _currencyService.GetCurrency(CurrentCurrency);
                var manageCurrency = await _currencyService.GetManageDefaultCurrency();

                // 构建完整的收藏列表视图模型
                var wishlistItems = new List<WishlistViewModel>();

                foreach (var favorite in favoriteList)
                {
                    // 获取产品详情
                    var product = await _productService.QueryById(favorite.ProId ?? 0);

                    if (product != null)
                    {
                        // 判断产品类型标签
                        string typeLabel = "Hot"; // 默认设置为 "Hot" 用于测试标签显示
                        if (product.SoldOut == true)
                        {
                            typeLabel = "Out Of Stock";
                        }

                        // 查询产品库存信息
                        var totalStock = await _productService.Db.Queryable<products_selected_attribute_combination>()
                            .Where(c => c.ProId == product.ProId)
                            .SumAsync(c => c.Stock);

                        bool isInStock = totalStock > 0;

                        // 价格处理逻辑 - 参考ProductFrontService
                        decimal? Price = product.Price_1;
                        decimal? OriginalPrice = product.Price_0;

                        // 获取促销价格信息
                        decimal promotionPrice = 0;
                        string promotionPriceFormat = null;
                        try
                        {
                            // 处理组合产品价格
                            if (product.IsCombination == 2)
                            {
                                var combinations = await _productService.Db.Queryable<products_selected_attribute_combination>()
                                    .Where(c => c.ProId == product.ProId && c.VariantsId != "")
                                    .FirstAsync();
                                if (combinations != null)
                                {
                                    Price += combinations.Price;
                                    OriginalPrice += combinations.OldPrice;
                                }
                            }

                            // 计算促销价格
                            var flashSaleResult = await _flashSaleService.FlashSaleCalculation(
                                product,
                                Price ?? 0,
                                OriginalPrice ?? 0,
                                "", // variantsId
                                userId
                            );

                            if (flashSaleResult != null && flashSaleResult.IsUsed == 1 && flashSaleResult.Price > 0)
                            {
                                promotionPrice = flashSaleResult.Price;
                                var promotionPriceResult = _currencyService.ShowPriceFormat(promotionPrice, userCurrency, manageCurrency);
                                promotionPriceFormat = promotionPriceResult.Item2;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取产品{product.ProId}促销价格失败: {ex.Message}");
                            // 出错时促销价格保持为0
                        }

                        // 格式化价格
                        string priceFormat = "";
                        string originalPriceFormat = "";

                        // 正确的价格显示逻辑：原价始终不变，促销价作用在售价上
                        if (promotionPrice > 0)
                        {
                            // 有促销价时：现价位置显示促销价
                            priceFormat = promotionPriceFormat;

                            // 原价位置显示真正的原价（Price_0对应的OriginalPrice）
                            if (OriginalPrice > 0)
                            {
                                var originalPriceResult = _currencyService.ShowPriceFormat(OriginalPrice.Value, userCurrency, manageCurrency);
                                originalPriceFormat = originalPriceResult.Item2;
                            }
                        }
                        else
                        {
                            // 没有促销价时：正常显示现价和原价
                            // 格式化销售价格
                            if (Price > 0)
                            {
                                var priceResult = _currencyService.ShowPriceFormat(Price.Value, userCurrency, manageCurrency);
                                priceFormat = priceResult.Item2;
                            }

                            // 如果原价大于售价，显示原价作为划线价
                            if (OriginalPrice.HasValue && OriginalPrice > 0 && Price.HasValue && OriginalPrice > Price)
                            {
                                var originalPriceResult = _currencyService.ShowPriceFormat(OriginalPrice.Value, userCurrency, manageCurrency);
                                originalPriceFormat = originalPriceResult.Item2;
                            }
                        }

                        // 构建收藏项视图模型
                        wishlistItems.Add(new WishlistViewModel
                        {
                            FavoriteId = favorite.FId,
                            ProductId = product.ProId,
                            ProductName = product.Name_en,
                            PageUrl = product.PageUrl ?? "",
                            PicPath = favorite.PicPath_0 ?? product.PicPath_0,
                            Price = Price ?? 0,
                            OriginalPrice = OriginalPrice,
                            PriceFormat = priceFormat,
                            OriginalPriceFormat = originalPriceFormat,
                            PromotionPrice = promotionPrice,
                            PromotionPriceFormat = promotionPriceFormat,
                            Rating = product.Rating ?? 0,
                            IsInStock = isInStock,
                            TypeLabel = typeLabel
                        });
                    }
                }

                ViewBag.IsEmpty = !wishlistItems.Any();
                return View(wishlistItems);
            }
            catch (Exception ex)
            {
                // 记录错误日志
                Console.WriteLine($"获取收藏列表时出错: {ex.Message}");
                ViewBag.IsEmpty = true;
                ViewBag.ErrorMessage = "无法加载收藏列表，请稍后再试";
                return View(new List<WishlistViewModel>());
            }
        }

        public IActionResult PaymentMethods()
        {
            return View();
        }

        public IActionResult AddPayment()
        {
            return View();
        }

        /// <summary>
        /// 登录页
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        [Route("/account/login")]
        [Route("/account/signin")]
        [Route("/SignIn")]
        [Route("/login")]
        public IActionResult SignIn()
        {
            //var ipcountry = _commonService.GetAliyunCountryCode("**************");

            if (CurrentUserId > 0)
            {
                return Redirect("/");
            }

            //登录后的跳转链接
            string jumpUrl = Request.GetQueryString("JumpUrl", false);

            return View(new { jumpUrl = jumpUrl });
        }

        #region 找回密码

        /// <summary>
        /// 重置密码请求页面
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult FindPwd()
        {
            return View();
        }


        /// <summary>
        /// 找回密码
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> OnFindPassword()
        {
            var jm = new WebApiCallBack();

            //获取对象
            string email = Request.GetFormString("email");

            if (email.IsNullOrEmpty())
            {
                jm.status = false;
                jm.msg = T["web.footer.enter_email"]; // "邮箱不能为空";

                return Json(jm);
            }

            if (!email.IsValidEmail())
            {
                jm.status = false;
                jm.msg = T["web.register.incorrect"];//"错误的邮箱格式";

                return Json(jm);
            }

            var userInfo = await _userService.QueryByClauseAsync(it => it.Email == email.ToLower());
            if (userInfo == null)
            {
                jm.status = false;
                jm.msg = T["user.login.error_note"];// "邮箱不存在";

                return Json(jm);
            }

            string uuid = Guid.NewGuid().ToUUID();
            string userLang = AppSettingsConstVars.LanguageDefault; //默认语言
            //发邮件
            var s = await _emailTplService.SendResetpwdEmail(email, uuid, userLang);
            if (s.code > 0)
            {
                //设置缓存 保存到redis  3个小时过期
                DateTime expTime = DateTime.Now.AddHours(3);
                TimeSpan ts = new TimeSpan(3, 0, 0); // 小时, 分钟, 秒

                //_caching.HashSet(GlobalConstVars.user_reset_email, uuid, userInfo.Email);
                _caching.Set<string>(GlobalConstVars.user_reset_email.FormatWith(uuid), userInfo.Email, ts);

                jm.status = true;
                jm.msg = T["user.forgot.sentEmail"];
            }
            else
            {
                jm.status = false;
                jm.msg = s.msg;
            }

            return Json(jm);
        }


        public IActionResult FinishResetPwd()
        {
            return View();
        }
        //public IActionResult ResetPwd()
        //{
        //    return View();
        //}

        /// <summary>
        /// 改密码页面
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> ResetPwd()
        {
            //get请求
            string uuid = Request.GetQueryString("uuid");
            if (string.IsNullOrEmpty(uuid))
            {
                //跳转到首页
                return Redirect("/");
            }


            string uid = uuid.FromBase64();

            var email = await _caching.GetAsync<string>(GlobalConstVars.user_reset_email.FormatWith(uid));
            //var email = _caching.HashGet(GlobalConstVars.user_reset_email, uid);
            if (email.IsNullOrEmpty())
            {
                //跳转到首页
                return Redirect("/");
            }

            var model = new
            {
                UUID = uuid
            };
            //await _caching.DelByPatternAsync(uid);

            return View(model);
        }

        /// <summary>
        /// 提交找回密码请求
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> OnResetPwd()
        {
            var jm = new WebApiCallBack();

            string uuid = Request.GetFormString("uuid");
            string password = Request.GetFormString("password");
            string confirmPwd = Request.GetFormString("confirmPwd");


            if (uuid.IsNullOrEmpty())
            {
                jm.status = false;
                jm.msg = "参数错误";
                return Json(jm);
            }

            if (password != confirmPwd)
            {
                jm.status = false;
                jm.msg = T["user.forgot.matchPWD"].Value;

                return Json(jm);
            }

            if (password.IsNullOrEmpty())
            {
                jm.status = false;
                jm.msg = T["user.account.rePWD"].Value;

                return Json(jm);
            }

            if (password.Length < 6 || password.Length > 32)
            {
                jm.status = false;
                jm.msg = "密码长度必须介于6到32之间";

                return Json(jm);
            }

            string uuidbase64 = uuid.FromBase64();

            var partUserEmail = _caching.Get<string>(GlobalConstVars.user_reset_email.FormatWith(uuidbase64)); //获取缓存信息
            //var partUserEmail = _caching.HashGet(GlobalConstVars.user_reset_email, uuidbase64);//获取缓存信息
            if (partUserEmail.IsNullOrEmpty())
            {
                jm.status = false;
                jm.msg = "参数错误";

                //404
                return Json(jm);
            }

            //获取用户信息
            var userInfo = await _userService.QueryByClauseAsync(it => it.Email == partUserEmail);
            if (userInfo == null)
            {
                jm.status = false;
                jm.msg = "参数错误";

                return Json(jm);
            }

            //生成密码
            userInfo.Password = _userService.Password(password);

            //更新密码
            var updUser = await _userService.Update(new { Password = userInfo.Password, UserId = userInfo.UserId });
            if (updUser == false)
            {
                jm.status = false;
                jm.msg = T["web.global.set_error"].Value;

                return Json(jm);
            }

            await _caching.DelByPatternAsync(GlobalConstVars.user_reset_email.FormatWith(uuidbase64)); //移除缓存

            //录入修改密码日志
            user_operation_log log = new user_operation_log
            {
                UserId = userInfo.UserId,
                OperationType = false,
                Log = "用户重置密码",
                Data = Request.Form.ToJson(),
                AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                Ip = Request.GetIP(),
            };

            await _userOperationLogServices.AddWithIntId(log);

            jm.status = true;
            jm.msg = T["user.forgot.successfully"].Value;

            return Json(jm);
        }

        #endregion


        #region 登录 注册 登出

        [HttpGet]
        public async Task<IActionResult> SignOut()
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            Response.Headers["HX-Redirect"] = "/";
            return Redirect("/");
        }

        [HttpPost]
        public async Task<IActionResult> UserLogin(UserLoginModel user)
        {
            try
            {
                var validationErrors = ValidateLoginUser(user);
                if (validationErrors.Count > 0)
                {
                    var errors = validationErrors.Select(e => e.Item2).ToList();
                    ViewData["Errors"] = errors;
                    ViewData["ErrCount"] = validationErrors.Count;
                    ViewData["ErrKeySum"] = validationErrors.Select(u => u.Item1).Sum();
                    return PartialView("SignInError", user);
                }
                else
                {
                    var ret = _userService.UserLogin(user.Email, user.Password);
                    if (ret.Item1)
                    {
                        //领优惠券
                        await _salesCouponService.GiveCouponAsync(ret.Item2!.UserId, "", 0, null);

                        string ip = Request.GetIP();

                        //更新最近登录时间
                        ret.Item2.LastLoginTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                        ret.Item2.LastLoginIp = ip;
                        ret.Item2.LoginTimes++; //登录次数+1

                        //获取ip的国家
                        var ipcountry = _commonService.GetAliyunCountryCode(ip);
                        ret.Item2.LastLoginIpCountry = ipcountry;
                        if (ret.Item2.Language.IsNullOrEmpty())
                        {
                            ret.Item2.Language = AppSettingsConstVars.LanguageDefault; //默认语言
                        }
                        if (ret.Item2.Currency.IsNullOrEmpty())
                        {
                            ret.Item2.Currency = AppSettingsConstVars.CurrentDefault; //默认币种
                        }

                        //更新用户信息
                        var updt = await _userService.Update(ret.Item2);

                        //获取购物车数量
                        var cartCount = await _shoppingCartServices.GetCountAsync(ret.Item2!.UserId);

                        //获取收藏数量
                        int wishCunt = 0;

                        var claims = new List<Claim>
                        {
                            new Claim(ClaimTypes.NameIdentifier, ret.Item2!.UserId.ToString()),
                            new Claim(ClaimTypes.Email, ret.Item2!.Email),
                            new Claim(ClaimTypes.Name, ret.Item2!.NickName),
                            new Claim(ClaimTypes.Sid, HttpContext.Session.Id),
                            new Claim("FirstName", ret.Item2!.FirstName),
                            new Claim("LastName", ret.Item2!.LastName),
                            new Claim(ClaimTypes.Role, "User"),
                            new Claim("cartCount", cartCount.ToString()),
                            new Claim("wishCount", wishCunt.ToString()),
                            new Claim("currency",  ret.Item2!.Currency??AppSettingsConstVars.CurrentDefault),//币种
                            new Claim("lang",  ret.Item2!.Language??AppSettingsConstVars.LanguageDefault),//语言
                            new Claim("salesAreaCode", ret.Item2!.SalesArea??AppSettingsConstVars.SalesAreaCodeDefault ),//销售区域

                        };
                        var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                        var principal = new ClaimsPrincipal(identity);
                        var authOption = new AuthenticationProperties();
                        if (user.RememberMe)
                        {
                            authOption.ExpiresUtc = DateTimeOffset.UtcNow.AddDays(15);
                        }

                        authOption.IsPersistent = true;
                        await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal,
                            authOption);



                        //设置币种语言
                        CurrentCurrency = ret.Item2!.Currency;
                        CurrentLang = ret.Item2!.Language;
                        //销售分区
                        CurrentSalesAreaCode = ret.Item2!.SalesArea;
                        //币种符号
                        var currencyItem = await _currencyService.GetCurrency(ret.Item2.Currency);
                        if (currencyItem != null)
                        {
                            CurrentCurrencySymbol = currencyItem.Symbol;
                        }

                        string jumpUrl = user.JumpUrl;

                        if (user.JumpUrl.IsNullOrEmpty())
                        {
                            jumpUrl = "/";

                            Response.Headers["HX-Redirect"] = jumpUrl;
                            return Ok();
                        }

                        Response.Headers["HX-Redirect"] = jumpUrl;
                        return Ok();
                    }
                    else
                    {
                        ModelState.AddModelError("1", "Invalid username or password.");
                        ViewData["Errors"] = new List<string>() { "Invalid username or password." };
                        ViewData["ErrCount"] = 1;
                        ViewData["ErrKeySum"] = 13;

                        ModelState.AddModelError("", "登录失败，请检查用户名和密码。");
                        return PartialView("SignInError", user);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return StatusCode(500, "Internal server error");
            }
        }

        private List<ValueTuple<int, string>> ValidateLoginUser(UserLoginModel model)
        {
            var errList = new List<ValueTuple<int, string>>();

            if (string.IsNullOrWhiteSpace(model.Email))
            {
                errList.Add(new(2, T["EmailRequired"]));
            }
            else if (!model.Email.IsValidEmail())
            {
                errList.Add(new(4, T["EmailInvalid"]));
            }


            //if (!Regex.IsMatch(model.Email, @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", RegexOptions.IgnoreCase))
            //{

            //    errList.Add(new(8, T["EmailInvalid"]));

            //}

            if (string.IsNullOrWhiteSpace(model.Password))
            {
                errList.Add(new(8, T["PasswordRequired"]));
            }
            else if (model.Password.Length < 6 || model.Password.Length > 50)
            {
                errList.Add(new(16, T["PasswordLength"]));
            }

            return errList;
        }

        private List<ValueTuple<int, string>> ValidateRegUser(UserRegModel model)
        {
            var errList = new List<ValueTuple<int, string>>();
            //if (string.IsNullOrWhiteSpace(model.FirstName))
            //{
            //    errList.Add(new(1, T["FirstNameRequired"]));
            //}
            //else if (!IsValidName(model.FirstName))
            //{
            //    errList.Add(new(4, T["FirstNameInvalid"]));
            //}
            //if (string.IsNullOrWhiteSpace(model.LastName))
            //{
            //    errList.Add(new(2, T["LastNameRequired"]));
            //}
            //else if (!IsValidName(model.LastName))
            //{
            //    errList.Add(new(8, T["LastNameInvalid"]));
            //}
            if (string.IsNullOrWhiteSpace(model.Email))
            {
                errList.Add(new(16, T["EmailRequired"]));
            }
            else if (!model.Email.IsValidEmail())
            {
                errList.Add(new(32, T["EmailInvalid"]));
            }

            if (string.IsNullOrWhiteSpace(model.Password))
            {
                errList.Add(new(64, T["PasswordRequired"]));
            }
            else if (model.Password.Length < 6 || model.Password.Length > 50)
            {
                errList.Add(new(128, T["PasswordLength"]));
            }

            if (model.Password != model.ReEnterPassword)
            {
                errList.Add(new(256, T["register.tryAgain"]));
            }

            if (!model.Agree)
            {
                errList.Add(new(512, T["Agreement"]));
            }

            return errList;
        }

        /// <summary>
        /// 注册页面
        /// </summary>
        /// <param name="ReturnUrl"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult SignUp(string ReturnUrl)
        {
            ViewData["ReturnUrl"] = ReturnUrl;
            return View();
        }

        public IActionResult SignUpSuccess()
        {
            return View();
        }

        /// <summary>
        /// 注册提交
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> UserSignUp(UserRegModel user)
        {
            var validationErrors = ValidateRegUser(user);
            try
            {
                if (validationErrors.Count > 0)
                {
                    var errors = validationErrors.Select(e => e.Item2).ToList();
                    ViewData["Errors"] = errors;
                    ViewData["ErrCount"] = validationErrors.Count;
                    ViewData["ErrKeySum"] = validationErrors.Select(u => u.Item1).Sum();
                    return PartialView("SignUpError", user);
                }
                else
                {
                    string ip = Request.GetIP();

                    //获取注册链接
                    var registerUrl = user.ReturnUrl ?? "/";

                    //注册
                    var ret = _userService.UserReg(user.Email, user.Password, user.FirstName, user.LastName, ip, returnUrl: registerUrl);
                    if (ret.Item1)
                    {
                        var usermailserver = GetUserMailServerUrl(user.Email);
                        ViewData["MailServer"] = usermailserver;
                        ViewData["UserEmail"] = user.Email;

                        //领优惠券
                        await _salesCouponService.GiveCouponAsync(ret.Item2!.UserId, "", 1, null);


                        #region 发送验证邮箱邮件

                        string tokenKey = Guid.NewGuid().ToUUID();


                        //设置缓存 保存到redis  1个小时过期
                        DateTime expTime = DateTime.Now.AddHours(1);
                        TimeSpan ts = new TimeSpan(1, 0, 0); // 小时, 分钟, 秒

                        string email = user.Email;

                        //设置缓存 1小时有效 
                        _caching.Set(GlobalConstVars.user_validate_email.FormatWith(tokenKey), email, ts);
                        //_caching.HashSet(GlobalConstVars.user_validate_email, tokenKey, email);

                        //发邮件
                        string base64Token = tokenKey.ToBase64();

                        string userLang = AppSettingsConstVars.LanguageDefault; //默认语言
                        string url = AppSettingsConstVars.WebSiteUrl + $"/account/validat-mail?token={base64Token}";
                        var resent = await _emailTplService.SendValidateEmail(email, url, userLang);

                        #endregion

                        return PartialView("SignUpSuccess", user);
                    }
                    else
                    {
                        ViewData["Errors"] = new List<string>() { T[ret.Item3!] };
                        ViewData["ErrCount"] = 1;
                        ViewData["ErrKeySum"] = 1024;
                        return PartialView("SignUpError", user);
                    }
                }
            }
            catch (Exception ex)
            {
                Response.Headers["HX-Redirect"] = "/";
                return PartialView("SignUpError", user);
            }
        }

        private string GetUserMailServerUrl(string email)
        {
            var server = email.Split('@');
            if (server.Length >= 2)
            {
                var mailsub = $"@{server[1]}";
                if (GlobalConstVars.EmailServerUrl.ContainsKey(mailsub))
                {
                    return GlobalConstVars.EmailServerUrl[mailsub];
                }
            }

            return "";
        }

        public bool IsValidName(string name)
        {
            // 检查空值或空字符串
            if (string.IsNullOrWhiteSpace(name))
            {
                return false;
            }

            // 正则匹配检查
            return !ContainsHtmlTags(name);
        }

        private bool ContainsHtmlTags(string input)
        {
            return Regex.IsMatch(input, @"<[^>]+>|&\w+;|javascript:|on\w+\s*=",
                RegexOptions.IgnoreCase);
        }

        #endregion

        /// <summary>
        /// 从收藏列表中移除商品
        /// </summary>
        /// <param name="favoriteId">收藏记录ID</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> RemoveFromWishlist([FromBody] RemoveWishlistRequest request)
        {
            try
            {
                if (request == null || request.FavoriteId <= 0)
                {
                    return Json(new { success = false, message = "无效的请求参数" });
                }

                // 获取当前用户ID
                if (!int.TryParse(User.FindFirstValue(ClaimTypes.NameIdentifier), out int userId))
                {
                    return Json(new { success = false, message = "用户未登录" });
                }

                // 调用服务删除收藏记录
                bool result = await _customerListService.RemoveFavoriteProduct(userId, request.FavoriteId);

                return Json(new { success = result, message = result ? "成功删除收藏" : "删除失败，请稍后再试" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除收藏记录时出错: {ex.Message}");
                return Json(new { success = false, message = "操作失败，发生异常" });
            }
        }

        /// <summary>
        /// 通过产品ID从收藏列表中移除商品
        /// </summary>
        /// <param name="request">移除收藏请求参数</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> RemoveFromWishlistByProductId([FromBody] AddToWishlistRequest request)
        {
            try
            {
                if (request == null || request.ProductId <= 0)
                {
                    return Json(new { success = false, message = "Invalid request parameters" });
                }

                // 获取当前用户ID
                if (!int.TryParse(User.FindFirstValue(ClaimTypes.NameIdentifier), out int userId))
                {
                    return Json(new { success = false, message = "User not logged in" });
                }

                // 调用服务删除收藏记录
                bool result = await _customerListService.RemoveFavoriteProductByProductId(userId, request.ProductId);

                return Json(new { success = result, message = result ? "Removed from wishlist successfully" : "Failed to remove from wishlist" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除收藏记录时出错: {ex.Message}");
                return Json(new { success = false, message = "Operation failed" });
            }
        }

        /// <summary>
        /// 添加商品到收藏列表
        /// </summary>
        /// <param name="request">添加收藏请求参数</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> AddToWishlist([FromBody] AddToWishlistRequest request)
        {
            try
            {
                if (request == null || request.ProductId <= 0)
                {
                    return Json(new { success = false, message = "Invalid request parameters" });
                }

                // 获取当前用户ID
                if (!int.TryParse(User.FindFirstValue(ClaimTypes.NameIdentifier), out int userId))
                {
                    return Json(new { success = false, message = "User not logged in" });
                }

                // 调用服务添加收藏记录
                bool result = await _customerListService.AddFavoriteProduct(userId, request.ProductId);

                return Json(new { success = result, message = result ? "Added to wishlist successfully" : "Failed to add to wishlist" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error adding to wishlist: {ex.Message}");
                return Json(new { success = false, message = "Operation failed due to an error" });
            }
        }


        #region 订阅

        /// <summary>
        /// 订阅处理
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> OnSubscribute()
        {
            var jm = new WebApiCallBack();
            string email = Request.GetFormString("email");
            //var isSubscribe = Request.GetFormByte("isSubscribe", 1);
            if (email.IsNullOrEmpty())
            {
                jm.status = false;
                jm.msg = "邮箱不能为空";
                return Json(jm);
            }

            if (!email.IsValidEmail())
            {
                jm.status = false;
                jm.msg = "错误的邮箱格式";
                return Json(jm);
            }

            string ip = Request.GetIP();

            var userInfo = await _userService.QueryByClauseAsync(it => it.Email == email.ToLower());
            if (userInfo == null)
            {
                UserLabel userLabel = new UserLabel();
                //添加用户表
                var newUser = new user
                {
                    Email = email!.Trim(),
                    Password = "",
                    FirstName = "",
                    LastName = "",
                    NickName = "",
                    Language = AppSettingsConstVars.LanguageDefault,
                    Currency = AppSettingsConstVars.CurrentDefault,
                    CountryId = 0,
                    LevelUpdateType = "auto",
                    DISTUId = "0,",
                    DISTDept = 1,
                    DISTBalance = 0,
                    DISTTotalBalance = 0,
                    Locked = false,
                    IsNewsletter = (sbyte)1,
                    RefererId = 0,
                    RefererName = "",
                    IsTaxExempt = false,
                    MessageTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    ReviewStatus = "pending",
                    RejectReason = "",
                    IsFreeShipping = false,
                    Points = 0,
                    TotalPoints = 0,
                    RegIp = ip,
                    RegTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    RegIpCountry = _commonService.GetAliyunCountryCode(ip) ?? "",
                    LastLoginTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    LastLoginIp = ip,
                    Status = 0, //默认启用
                    IsLocked = false,
                    IsRegistered = false, //未注册
                    IsChecked = false,
                    SalesId = 0,
                    Level = 0,
                    Age = 0,
                    IsSubscribe = false,
                    ConsumptionTime = 0,
                    Tags = "",
                    Label = userLabel.ToJson(),
                    Other = "[]",
                    FacebookId = "",
                    TwitterId = "",
                    GoogleId = "",
                    PaypalId = "",
                    VKId = "",
                    InstagramId = "",
                    Fax = "",
                    Birthday = "",
                    Company = "",
                    Consumption = 0,
                    Source = "",
                    SourceId = "",
                    LastLoginIpCountry = _commonService.GetAliyunCountryCode(ip) ?? "",
                    SalesArea = AppSettingsConstVars.SalesAreaCodeDefault,
                    AccountType = 0,
                    SoftwareAccountID = "",
                    RegisterUrl = "",
                    Remark = "",
                    PasswordFormat = 0,
                };

                //添加用户
                var addUser = await _userService.AddWithIntId(newUser);
                if (addUser > 0)
                {
                    //设置用户标签
                    user_label_collection userLabelCollection = new user_label_collection
                    {
                        UserId = addUser,
                        Type = "system",
                        Value = "newsletter", //订阅标签

                    };
                    await _userLabelCollectionServices.AddWithIntId(userLabelCollection);

                    //发送订阅邮件
                    string userLang = AppSettingsConstVars.LanguageDefault; //默认语言
                    var sendEmail = await _emailTplService.SendSubscribedEmail(userInfo.Email, userLang);



                    jm.status = true;
                    jm.msg = T["web.footer.newsletter_tips_1"].Value;
                }
                else
                {
                    jm.status = false;
                    jm.msg = T["user.account.error_Error"].Value; // "订阅失败，请稍后再试";
                }
            }
            else
            {
                if (userInfo.IsNewsletter == 1)
                {
                    jm.status = false;
                    jm.msg = T["web.global.newsletter_exists"].Value;
                    return Json(jm);
                }

                //修改订阅状态
                userInfo.IsNewsletter = (sbyte)1;

                var result = await _userService.Update(userInfo);
                if (result)
                {

                    //设置用户标签
                    user_label_collection userLabelCollection = new user_label_collection
                    {
                        UserId = userInfo.UserId,
                        Type = "system",
                        Value = "newsletter", //订阅标签

                    };
                    await _userLabelCollectionServices.AddWithIntId(userLabelCollection);

                    //发送订阅邮件
                    string userLang = AppSettingsConstVars.LanguageDefault; //默认语言
                    var sendEmail = await _emailTplService.SendSubscribedEmail(userInfo.Email, userLang);



                    jm.status = true;
                    jm.msg = T["web.footer.newsletter_tips_1"].Value;
                }
                else
                {
                    jm.status = false;
                    jm.msg = T["user.account.error_Error"].Value; // "订阅失败，请稍后再试";
                }


            }

            return Json(jm);
        }

        #endregion

        #region 邮箱验证

        /// <summary>
        /// 发送邮箱验证邮件
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ResendEmail()
        {
            var jm = new WebApiCallBack();
            string email = Request.GetQueryString("email");
            if (email.IsNullOrEmpty())
            {
                jm.status = false;
                jm.msg = T["checkout.checkout.required_fields_tips"]; //"邮箱不能为空";
                return Json(jm);
            }

            if (!email.IsValidEmail())
            {
                jm.status = false;
                jm.msg = T["user.account.error_EmailEntered"]; // "您输入的邮箱地址不正确。"
                return Json(jm);
            }

            var userInfo = await _userService.QueryByClauseAsync(it => it.Email == email.ToLower());
            if (userInfo == null)
            {
                jm.status = false;
                jm.msg = T["user.verify.error_Forgot"].Value; //"对不起，这个邮箱地址还没有注册。"
                return Json(jm);
            }

            //发送邮件
            string tokenKey = Guid.NewGuid().ToUUID();


            //设置缓存 保存到redis  1个小时过期
            DateTime expTime = DateTime.Now.AddHours(1);
            TimeSpan ts = new TimeSpan(1, 0, 0); // 小时, 分钟, 秒

            //设置缓存 1小时有效 
            _caching.Set(GlobalConstVars.user_validate_email.FormatWith(tokenKey), email, ts);
            //_caching.HashSet(GlobalConstVars.user_validate_email, tokenKey, email);

            //发邮件
            string base64Token = tokenKey.ToBase64();

            string userLang = AppSettingsConstVars.LanguageDefault; //默认语言
            string url = AppSettingsConstVars.WebSiteUrl + $"/account/validat-mail?token={base64Token}";
            var resent = await _emailTplService.SendValidateEmail(email, url, userLang);

            jm.status = resent.status;
            jm.msg = resent.msg;
            jm.data = base64Token;

            return Json(jm);
        }


        /// <summary>
        /// 验证注册链接
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        [Route("/account/validat-mail")]
        [HttpGet]
        public async Task<IActionResult> ValidateMail(string token)
        {
            var jm = new WebApiCallBack();
            if (token.IsNullOrEmpty())
            {
                jm.status = false;
                jm.msg = GlobalConstVars.DataParameterError;

                Response.Headers["HX-Redirect"] = "/";
                //重定向到404
                return Redirect("/");
            }

            string uuid = token.FromBase64();
            var email = _caching.Get<string>(GlobalConstVars.user_validate_email.FormatWith(uuid));
            //var email = _caching.HashGet(GlobalConstVars.user_validate_email, uuid);
            if (email.IsNullOrEmpty())
            {
                jm.msg = T["user.verify.link_expired"].Value; // "链接已过期或无效，请重新发送验证邮件";
                jm.status = false;
                Response.Headers["HX-Redirect"] = "/";

                //重定向到404
                return Redirect("/");
            }


            var userInfo = await _userService.QueryByClauseAsync(it => it.Email == email.ToLower());
            if (userInfo == null)
            {
                jm.status = false;
                jm.msg = T["user.verify.error_Forgot"].Value;// "对不起，这个邮箱地址还没有注册。";

                Response.Headers["HX-Redirect"] = "/";
                return Redirect("/");
            }
            userInfo.Status = 1;
            userInfo.IsRegistered = true;
            userInfo.ReviewStatus = "passed"; //已验证
            userInfo.LastLoginTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
            userInfo.LastLoginIp = Request.GetIP();
            if (userInfo.Language.IsNullOrEmpty())
            {
                userInfo.Language = AppSettingsConstVars.LanguageDefault; //默认语言
            }
            if (userInfo.Currency.IsNullOrEmpty())
            {
                userInfo.Currency = AppSettingsConstVars.CurrentDefault; //默认币种
            }

            //更新最新登录时间,验证状态
            var updTime = await _userService.Update(userInfo);
            if (updTime)
            {
                //录入修改密码日志
                user_operation_log log = new user_operation_log
                {
                    UserId = userInfo.UserId,
                    OperationType = false,
                    Log = "注册验证邮箱",
                    Data = Request.QueryString.ToString(),
                    AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    Ip = Request.GetIP(),
                };

                await _userOperationLogServices.AddWithIntId(log);

                //发送注册成功邮件
                string userLang = AppSettingsConstVars.LanguageDefault; //默认语言
                var sendEmail = await _emailTplService.SendRegistEmail(userInfo.Email, userInfo.NickName, userLang);

                //获取购物车数量
                var cartCount = await _shoppingCartServices.GetCountAsync(userInfo.UserId);

                //获取收藏数量
                int wishCunt = 0;

                var claims = new List<Claim>
                {
                    new Claim(ClaimTypes.NameIdentifier, userInfo.UserId.ToString()),
                    new Claim(ClaimTypes.Email, userInfo.Email),
                    new Claim(ClaimTypes.Name, userInfo.NickName),
                    new Claim(ClaimTypes.Sid, HttpContext.Session.Id),
                    new Claim("FirstName", userInfo.FirstName),
                    new Claim("LastName", userInfo.LastName),
                    new Claim(ClaimTypes.Role, "User"),
                    new Claim("cartCount", cartCount.ToString()),
                    new Claim("wishCount", wishCunt.ToString()),
                    new Claim("currency", userInfo.Currency??AppSettingsConstVars.CurrentDefault),
                    new Claim("lang", userInfo.Language??AppSettingsConstVars.LanguageDefault),
                    new Claim("salesAreaCode", userInfo.SalesArea??AppSettingsConstVars.SalesAreaCodeDefault ),//销售区域
                };
                var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                var principal = new ClaimsPrincipal(identity);
                var authOption = new AuthenticationProperties();
                authOption.ExpiresUtc = DateTimeOffset.UtcNow.AddDays(15);
                authOption.IsPersistent = true;
                await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal, authOption);


                //移除缓存
                await _caching.DelByPatternAsync(GlobalConstVars.user_validate_email.FormatWith(uuid));
                //_caching.HashRemove(GlobalConstVars.user_validate_email, uuid);

                CurrentCurrency = userInfo.Currency;
                CurrentLang = userInfo.Language;
                CurrentSalesAreaCode = userInfo.SalesArea;
                //币种符号
                var currencyItem = await _currencyService.GetCurrency(userInfo.Currency);
                if (currencyItem != null)
                {
                    CurrentCurrencySymbol = currencyItem.Symbol;
                }


                string jumpUrl = userInfo.RegisterUrl;
                if (jumpUrl.IsNullOrEmpty())
                {
                    jumpUrl = "/";

                    Response.Headers["HX-Redirect"] = jumpUrl;
                    return Redirect(jumpUrl);
                }

                Response.Headers["HX-Redirect"] = jumpUrl;
                return Redirect(jumpUrl);
            }
            else
            {
                jm.status = false;
                jm.msg = GlobalConstVars.SetDataException;

                return Redirect("/");
            }
        }

        #endregion


        #region 获取用户语言，币种，购物车数量

        /// <summary>
        /// 更新销售区域
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> UpdateSalesArea()
        {
            var jm = new WebApiCallBack();
            //修改后的币种
            var salesArea = Request.GetFormString("SalesAreaCode");

            if (CurrentSalesAreaCode == salesArea)
            {
                jm.status = false;
                jm.msg = "";
                return Json(jm);
            }

            //获取币种
            var currencyItem = await _salesAreasService.GetSalesAreasInfoCache(salesArea);
            if (currencyItem == null)
            {
                jm.status = false;
                jm.msg = T["cart.global.invalid"];
                return Json(jm);
            }

            if (CurrentUserId > 0)
            {
                //更新token用户币种
                CurrentSalesAreaCode = currencyItem.Code;

                var user = await _userService.QueryById(CurrentUserId);
                if (user != null)
                {
                    user.SalesArea = currencyItem.Code;
                    //更新用户币种
                    await _userService.Update(user);
                }
                else
                {
                    jm.status = false;
                    jm.msg = "用户信息不存在";
                    return Json(jm);
                }

            }
            else
            {
                //更新token用户币种
                CurrentSalesAreaCode = currencyItem.Code;
            }

            jm.status = true;
            jm.msg = "";
            return Json(jm);

        }


        /// <summary>
        /// 更新币种
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> UpdateCurrency()
        {
            var jm = new WebApiCallBack();
            //修改后的币种
            var currency = Request.GetFormString("currency");

            if (CurrentCurrency == currency)
            {
                jm.status = false;
                jm.msg = "";
                return Json(jm);
            }

            //获取币种
            var currencyItem = await _currencyService.GetCurrency(currency);
            if (currencyItem == null)
            {
                jm.status = false;
                jm.msg = T["cart.global.invalid"];
                return Json(jm);
            }

            if (CurrentUserId > 0)
            {
                //更新token用户币种
                CurrentCurrency = currencyItem.Currency;
                CurrentCurrencySymbol = currencyItem.Symbol;

                //await SetTokenCurrentCurrency(currencyItem.Currency);
                //await SetTokenCurrentCurrencySymbol(currencyItem.Symbol);

                var user = await _userService.QueryById(CurrentUserId);
                if (user != null)
                {
                    user.Currency = currencyItem.Currency;
                    //更新用户币种
                    await _userService.Update(user);
                }
                else
                {
                    jm.status = false;
                    jm.msg = "用户信息不存在";
                    return Json(jm);
                }

            }
            else
            {
                //更新session 用户币种
                CurrentCurrency = currencyItem.Currency;
                CurrentCurrencySymbol = currencyItem.Symbol;

            }

            jm.status = true;
            jm.msg = "";
            return Json(jm);

        }

        /// <summary>
        /// 更新语言
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> UpdateLang()
        {
            var jm = new WebApiCallBack();
            //修改后的币种
            var lang = Request.GetFormString("lang");

            if (CurrentLang == lang)
            {
                jm.status = false;
                jm.msg = "";
                return Json(jm);
            }

            //获取语言
            var language = await _languageServices.GetLang(lang);
            if (language == null)
            {
                jm.status = false;
                jm.msg = T["cart.global.invalid"];
                return Json(jm);
            }

            if (CurrentUserId > 0)
            {
                //更新token用户语言
                //await SetTokenCurrentLang(lang);

                CurrentLang = lang;

                var user = await _userService.QueryById(CurrentUserId);
                if (user != null)
                {
                    user.Language = lang;
                    //更新用户币种
                    await _userService.Update(user);
                }
                else
                {
                    jm.status = false;
                    jm.msg = "用户信息不存在";
                    return Json(jm);
                }

            }
            else
            {
                //更新session 用户语言
                CurrentLang = lang;
            }

            jm.status = true;
            jm.msg = "";
            return Json(jm);
        }


        /// <summary>
        /// 获取用户配置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetUserConf()
        {
            //logo,品牌

            //获取当前分区的币种，语言




            return Json(new
            {
                lang = CurrentLang,//语言
                currency = CurrentCurrency,//币种
                currencySymbol = CurrentCurrencySymbol,//币种符号
                saleArea = CurrentSalesAreaCode,//销售分区
                cartCount = CurrentCartCount,//购物车数量
                wishlistCount = CurrentWishListCount,//收藏数量
            });

        }
        #endregion
    }
}