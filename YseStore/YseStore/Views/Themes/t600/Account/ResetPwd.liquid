<div class="container py-100">
    <div class="col-md-5 mx-auto">
        <div class="login-form">
            <div class="login-header">
                <img src="{{static_path}}/assets/img/logo/logo.png" alt="logo" />
                <p>{{ "user.forgot.enterPWD"|translate}}</p>
            </div>
            <form hx-post="/Account/OnResetPwd" id="resetPwd-form"
                  hx-target="#resetpwd-result"
                  hx-swap="afterbegin"
                  hx-trigger="submit throttle:2s"
                  hx-on::after-request="if(event.detail.successful){ resetPwdSubmitCallback(event.detail.xhr.responseText) }">

                <div class="form-group">
                    <label>{{ "user.global.password"|translate}}</label>
                    <input type="password" class="form-control" name="password" placeholder="{{ "user.forgot.newPWD"|translate}}"
                           onkeyup="this.setCustomValidity(''); $(this).removeClass('is-invalid');"
                           hx-on:htmx:validation:validate="if(this.value == '') {
                    this.setCustomValidity('Please enter your new password')
					$(this).addClass('is-invalid') }else{$(this).removeClass('is-invalid')}" />
                </div>
                <div class="form-group">
                    <label>{{ "user.global.password"|translate}}</label>
                    <input type="password" class="form-control" name="confirmPwd" placeholder="{{ "user.account.reg_err_PWDConfirm"|translate}}"
                           onkeyup="this.setCustomValidity('')"
                           hx-on:htmx:validation:validate="if(this.value == '') {
                    this.setCustomValidity('Please Confirm your new password')
					$(this).addClass('is-invalid')
                }" />
                </div>
                <div class="d-flex align-items-center">
                    <input type="hidden" name="uuid" value="{{Model.UUID}}" />
                    <button type="submit" class="theme-btn" hx-on="htmx:configRequest: this.disabled = true; this.innerText = 'Submitting...'" hx-disabled-elt="this" hx-indicator="#spinner">
                        <i class="far fa-key"></i>
                        {{ "web.global.send"|translate}}
                        <span id="spinner" class="htmx-indicator">
                            <i class="fa fa-spinner fa-spin"></i>
                        </span>
                    </button>
                </div>
            </form>
            <div id="resetpwd-result" class="mt-2"></div>
        </div>
    </div>
</div>

<script>
    function resetPwdSubmitCallback(responseText) {

        const notification = document.getElementById('resetpwd-result');
        notification.classList.remove('hidden');
        const response = JSON.parse(responseText);

        notification.innerHTML = `
        <div class="alert alert-${response.status ? 'success' : 'danger'}">
          ${response.msg}
        </div>
      `;

        if (response.status == true) {

            document.getElementById('resetPwd-form').reset();
        }
        // 3秒后自动隐藏
        setTimeout(() => {
            notification.classList.add('hidden');
        }, 10000);
    }
</script>
