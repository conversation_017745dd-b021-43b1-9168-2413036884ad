<div class="user-area bg pt-100 pb-80">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 mb-4 user-sidebar">
                {% assign sidebar= '/Themes/'| append: theme | append:'/Account/Sidebar' %}
                {% include sidebar -%}
            </div>
            <div class="col-lg-9 mb-5 user-content">
                         <div class="dashboard-content padding-30px-all md-padding-15px-all" style="">
                <!-- Dashboard -->
                <h3>{{ "user.account.indexTitle"|translate}} </h3>
                <p>
                    {{ "user.account.MyProfileInfo_1"|translate}}
                    <a class="text-decoration-underline" href="/Account/MyOrders">{{ "user.account.MyProfileInfo_2"|translate}}</a>{{ "user.account.MyProfileInfo_3"|translate}}
                    <a class="text-decoration-underline" href="/Account/MyAddress">{{ "user.account.MyProfileInfo_4"|translate}}</a> {{ "user.account.MyProfileInfo_5"|translate}}
                    <a class="text-decoration-underline" href="/Account/FindPwd">{{ "user.account.MyProfileInfo_6"|translate}}</a>{{ "user.account.MyProfileInfo_5"|translate}}
                    <a class="text-decoration-underline" href="/Account/AccountSettings">{{ "user.account.MyProfileInfo_7"|translate}}</a>
                </p>
                <hr />
                <div class="row">
                    <div class="col-12 block-box block-dashboard-info mt-3">
                        <div class="block-title mb-3"><h4 class="mb-0">{{ "user.account.MyProfile_Information"|translate}}</h4></div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-6 mb-3">
                        <div class="box box-information">
                            <strong class="box-title">{{ "user.account.MyProfile_Contact"|translate}}</strong>
                            <div class="box-content">
                                <p>{{ Model.UserData.FirstName }} {{ Model.UserData.LastName }}</p>
                                <p>{{ Model.UserData.Email }}</p>
                            </div>
                            <div class="box-actions  mt-2">
                                <a class="action edit" href="#"><i class="an an-lg an-edit"></i></a>
                                <a href="/Account/FindPwd" class="action change-password">{{ "user.account.changePWD"|translate}}</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 block-box block-dashboard-info mt-3">
                        <div class="block-title mb-3"><h4 class="mb-0">{{ "user.account.address_book"|translate}}</h4> <a class="action edit" href="/Account/MyAddress">{{ "user.account.addressTitle"|translate}}</a></div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-6 mb-3">
                        <div class="box box-information">
                            <strong class="box-title">{{ "web.global.default"|translate}} {{ "user.account.bill_addr"|translate}}</strong>
                            <div class="box-content">
                                <span>{{ "user.account.name"|translate}}:</span><span style="color:var( --theme-color);">{{ Model.BillingAddress.FirstName }} {{ Model.BillingAddress.LastName }}</span> <br />

                                <span>{{ "user.account.country"|translate}}:</span><span style="color:var( --theme-color);">{{ Model.BillingAddress.Country }}</span> <br />
                                <span>{{ "user.account.province_state"|translate}}:</span><span style="color:var( --theme-color);">{{ Model.BillingAddress.State }}</span> <br />
                                <span>{{ "user.account.city"|translate}}:</span><span style="color:var( --theme-color);">{{ Model.BillingAddress.City }}</span> <br />
                                <span>{{ "address.shipping.zip"|translate}}:</span><span style="color:var( --theme-color);">{{ Model.BillingAddress.ZipCode }}</span> <br />
                                <span>{{ "address.shipping.phone"|translate}}:</span><span style="color:var( --theme-color);">(+{{ Model.BillingAddress.CountryCode }}) {{ Model.BillingAddress.PhoneNumber }}</span> <br />
                               
                                <span>{{ "user.account.address1"|translate}} 1:</span><span style="color:var( --theme-color);">{{ Model.BillingAddress.AddressLine1 }}</span> <br />
                                <span>{{ "user.account.address1"|translate}} 2:</span><span style="color:var( --theme-color);">{{ Model.BillingAddress.AddressLine2 }}</span> <br />
                               
                            </div>
                            <div class="box-actions mt-2">
                                <a class="action edit" href="/Account/Addaddress/{{ Model.BillingAddress.AId }}?Billing=1"><b>{{ "web.global.edit"|translate}} {{ "user.account.bill_addr"|translate}}</b> <i class="an an-lg an-arrow-right"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-6 mb-3">
                        <div class="box box-information">
                            <strong class="box-title">{{ "web.global.default"|translate}} {{ "user.account.ship_addr"|translate}}</strong>
                            <div class="box-content">
                                <span>{{ "user.account.name"|translate}}:</span><span style="color:var( --theme-color);">{{ Model.address.FirstName }} {{ Model.address.LastName }}</span> <br />
                               
                                <span>{{ "user.account.country"|translate}}:</span><span style="color:var( --theme-color);">{{ Model.address.Country }}</span> <br />
                                <span>{{ "user.account.province_state"|translate}}:</span><span style="color:var( --theme-color);">{{ Model.address.State }}</span> <br />
                                <span>{{ "user.account.city"|translate}}:</span><span style="color:var( --theme-color);">{{ Model.address.City }}</span> <br />
                              
                                <span>{{ "address.shipping.phone"|translate}}:</span><span style="color:var( --theme-color);">(+{{ Model.address.CountryCode }}) {{ Model.address.PhoneNumber }}</span> <br />
                               
                                <span>{{ "user.account.address1"|translate}} 1:</span><span style="color:var( --theme-color);">{{ Model.address.AddressLine1 }}</span> <br />
                                <span>{{ "user.account.address1"|translate}} 2:</span><span style="color:var( --theme-color);">{{ Model.address.AddressLine2 }}</span> <br />
                                <span>{{ "user.account.vat_id_num"|translate}}:</span><span style="color:var( --theme-color);">{{ Model.address.TaxCode }}</span> <br />
                            </div>
                            <div class="box-actions mt-2">
                                <a class="action edit" href="/Account/Addaddress/{{ Model.address.AId }}"><b>{{ "web.global.edit"|translate}} {{ "user.account.ship_addr"|translate}}</b> <i class="an an-lg an-arrow-right"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Dashboard -->
            </div>


                        </div>
            </div>
        </div>
    </div>
</div>