<div class="user-area bg pt-100 pb-80">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 mb-4 user-sidebar">
                {% assign sidebar= '/Themes/'| append: theme | append:'/Account/Sidebar' %}
                {% include sidebar -%}
            </div>
            <div class="col-lg-9 mb-5 user-content">
                            <div class="content-wrapper">
                                <div class="content my-addressbook">
                                    <h1 class="content-title">{{ "user.account.addressTitle"|translate}}</h1>
                                    <div>
                                        <div class="row">
                                            <div class="col-12 col-sm-12">
                                                <div class="d-flex-center pb-4"><h4 class="billing-address me-auto">{{ "user.account.bill_addr"|translate}}</h4>
                                                {% if Model.BillingAddress.size == 0  %}
                                                <a href="/Account/Addaddress?Billing=1" class="collapsed btn btn--small rounded">{{ "web.global.add"|translate}} {{ "checkout.checkout.pAddress"|translate}}</a>
                                                 {% endif %}
                                                </div>
                                                <hr class="clear my-0">

                                                {% if Model.BillingAddress != null and Model.BillingAddress.size > 0 %}
                                                    {% for item in Model.BillingAddress %}
                                                    <div class="d-flex-center mt-2 mb-2 closestAddress">
                                                        <div class="me-auto"><p>{{ item.FirstName }} {{ item.LastName }} <br> (+{{ item.CountryCode }}) {{ item.PhoneNumber }} <br> {{ item.ZipCode }}</p></div>
                                                        <div class="me-auto"><p>{{ item.AddressLine2 }},{{ item.AddressLine1 }} <br> {{ item.City }}{{ item.State }} <br> {{ item.Country }}</p></div>
                                                        

                                                        <a class="link-underline view me-3" href="/Account/Addaddress/{{ item.AId }}?Billing=1">{{ "web.global.edit"|translate}}</a>
                                                       
                                                    </div>
                                                  {% endfor %}
                                                {% endif %}

                                         <div class="col-12 col-sm-12">
                                                <div class="d-flex-center pb-4"><h4 class="billing-address me-auto">{{ "user.account.ship_addr"|translate}}</h4>
                                                <a href="/Account/Addaddress" class="collapsed btn btn--small rounded">{{ "web.global.add"|translate}} {{ "checkout.checkout.pAddress"|translate}}</a>
                                                </div>
                                                <hr class="clear my-0">

                                                {% if Model.ShippingAddress != null and Model.ShippingAddress.size > 0 %}
                                                    {% for item in Model.ShippingAddress %}
                                                    <div class="d-flex-center mt-2 mb-2 closestAddress">
                                                        <div class="me-auto"><p>{{ item.FirstName }} {{ item.LastName }} <br> (+{{ item.CountryCode }}) {{ item.PhoneNumber }} <br> {{ item.TaxCode }}</p></div>
                                                        <div class="me-auto"><p>{{ item.AddressLine2 }},{{ item.AddressLine1 }} <br> {{ item.City }}{{ item.State }} <br> {{ item.Country }}</p></div>
                                                      

                                                        <a class="link-underline view me-3" href="/Account/Addaddress/{{ item.AId }}">{{ "web.global.edit"|translate}}</a>
                                                        <span class="link-underline view deleteAddress" value="{{ item.AId }}" style="color: #ef4444;">{{ "web.global.delete"|translate}}</span>
                                                        
                                                        <div class="col-12 col-lg-12" style="text-align:left;padding-top: 10px;">
                                                            <label class="form-check-label">
                                                                <input type="checkbox"  {% if item.IsDefault  %}checked{% endif %}  class="form-check-box IsDefault" value="{{ item.AId }}"> {{ "web.global.default"|translate}} {{ "checkout.checkout.pAddress"|translate}}
                                                            </label>
                                                        </div>


                                                    </div>
                                                  {% endfor %}
                                                {% endif %}



                                            </div>
                                            
                                        </div>
                                    </div>
                                </div>
                            </div>
            </div>
        </div>
    </div>
</div>

<!-- 引入自定义弹窗JS库 -->
<script src="/js/Pop-ups/frame-message.js"></script>
<script>

    // 原有代码保持不变，在文档底部添加以下内容
    document.addEventListener('DOMContentLoaded', function() {
       
    

        // 监听表单提交事件
        $('.deleteAddress').click(function(e){
            e.preventDefault();
             var form = $(this);
            var formData = {
                AId: form.attr('value')
            };

                const productItem = this.closest('.closestAddress');
                customize_pop.confirm('{{'user.account.delete_shipping'|translate}}', function() {


           
            sendData(formData);
            function sendData(data) {
                $.ajax({
                    url: '/api/account/comment/DeleteAddress',
                    type: 'POST',
                    data: data,
                    success: function(response) {
                        if(response.status) {
                           // 移除成功，显示成功消息
                            customize_pop.success('{{'web.global.submit_success'|translate}}');
                         
                            // 移除DOM元素
                            productItem.remove();
                        } else {
                            // 显示错误消息
                            customize_pop.error(response.message || 'Failed to delete item. Please try again');
                        }
                    },
                    error: function(xhr) {
                        console.error('AJAX Error:', xhr.responseText);
                        customize_pop.error('An error occurred. Please try again later');
                    }
                });
            }
             }, null, '{{'web.global.confirm'|translate}}', '{{'web.global.cancel'|translate}}', '{{'web.global.confirm'|translate}}', false);


        });

        
                 // 使用事件委托处理动态生成的复选框
    $(document).on('change', '.IsDefault', function() {
        var checkbox = $(this);
        var addressId = checkbox.val();
        var isChecked = checkbox.is(':checked');
        
        // 如果当前复选框被选中，则取消其他复选框的选中状态
        if (isChecked) {
            $('.IsDefault').not(checkbox).prop('checked', false);

             // 发送AJAX请求
        $.ajax({
            url: '/api/account/comment/SetDefaultAddress', 
            type: 'POST',
            dataType: 'json',
            data: {
                addressId: addressId,
                isDefault: isChecked
            },
            success: function(response) {
                if (response.status) {
                    console.log('successfully');
                } else {
                    // 回滚复选框状态
                    checkbox.prop('checked', !isChecked);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                // 回滚复选框状态
                checkbox.prop('checked', !isChecked);
            }
        });


        }
       
       
    });




    });




</script>

