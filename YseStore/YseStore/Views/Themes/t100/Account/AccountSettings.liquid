<div class="user-area bg pt-100 pb-80">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 mb-4 user-sidebar">
                <!--{% assign sidebar= '/Themes/'| append: theme | append:'/Account/Sidebar' %}-->
                {% include sidebar -%}
            </div>
            <div class="col-lg-9 user-content">
                            <div class="content-wrapper">
                                <div class="content my-security">
                                    <h1 class="content-title">{{ "user.account.settingTitle"|translate}}</h1>
                                  <div class="account-login-form bg-light-gray padding-20px-all">
                        <form action="#" >
                         <fieldset>
                                <div class="row">
                                    <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                        <label for="input-name">{{ "user.account.firstname"|translate}} <span class="required-f">*</span></label>
                                        <input name="FirstName" value="{{ Model.UserData.FirstName }}" type="text">
                                    </div>
                                    <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                        <label for="input-lastname">{{ "user.account.lastname"|translate}} <span class="required-f">*</span></label>
                                        <input name="LastName" value="{{ Model.UserData.LastName }}" type="text">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                        <label for="input-email">{{ "user.account.email_addr"|translate}} <span class="required-f">*</span></label>
                                        <input name="Email" value="{{ Model.UserData.Email }}" id="input-email" type="email">
                                    </div>
                                    <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                        <label for="input-lastname">{{ "user.account.nickName"|translate}} <span class="required-f">*</span></label>
                                        <input name="NickName" value="{{ Model.UserData.NickName }}" type="text">
                                    </div>
                                </div>
                            </fieldset>
                            <button type="submit" id="SaveUser" class="btn margin-15px-top btn-primary theme-btn">{{ "web.global.save"|translate}}</button>
                        </form>
                    
                    </div>
                                </div>
                            </div>
            </div>
        </div>
    </div>
</div>