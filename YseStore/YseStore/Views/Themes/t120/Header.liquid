<div>

    <!-- Start TopBar -->
    <div class="topbar">
        <div class="topbar-column">
            <a class="hidden-md-down" href="#"><i class="fa fa-phone"></i>&nbsp;****** 888 8888</a>
            <a class="hidden-md-down" href="#"><i class="icon-mail"></i>&nbsp;<EMAIL></a>
            <a class="hidden-md-down" href="#"><i class="fa fa-map-marker"></i> 221B Baker Street, London, UK</a>
        </div>
        <div class="topbar-column">
            <div class="lang-currency-switcher-wrap">
                <div class="lang-currency-switcher dropdown-toggle">
                    <span class="currency">$ USD</span>
                </div>
                <div class="dropdown-menu">
                    <div class="currency-select">
                        <select class="form-control form-control-rounded form-control-sm">
                            <option value="usd">$ USD</option>
                            <option value="usd">€ EUR</option>
                            <option value="usd">£ UKP</option>
                            <option value="usd">¥ JPY</option>
                        </select>
                    </div>
                </div>
            </div>
            <a class="social-button sb-facebook shape-none sb-dark soc-border" href="#" target="_blank"><i class="socicon-facebook"></i></a>
            <a class="social-button sb-twitter shape-none sb-dark" href="#" target="_blank"><i class="socicon-twitter"></i></a>
            <a class="social-button sb-instagram shape-none sb-dark" href="#" target="_blank"><i class="socicon-instagram"></i></a>
        </div>
    </div>
    <!-- End TopBar -->
    <!-- Start NavBar -->
    <header class="navbar navbar-sticky">
        <!-- Start Search -->
        <form class="site-search" method="get">
            <input type="text" name="site_search" placeholder="Type to search...">
            <div class="search-tools">
                <span class="clear-search">Clear</span>
                <span class="close-search"><i class="icon-cross"></i></span>
            </div>
        </form>
        <!-- End Search -->
        <!-- Start Logo -->
        <div class="site-branding">
            <div class="inner">
                <a class="offcanvas-toggle menu-toggle" href="#mobile-menu" data-toggle="offcanvas"></a>
                <a class="site-logo" href="/"><img src="{{static_path}}/assets/images/logo/logo.png" alt="Inspina"></a>
            </div>
        </div>
        <!-- End Logo -->
        <!-- Start Nav Menu - 通过HTMX加载 -->
        <div id="desktop-navigation" hx-get="/home/<USER>" hx-trigger="load" hx-select="nav.site-menu">
            <!-- 导航菜单将通过HTMX加载 -->
        </div>
        <!-- End Nav Menu -->
        <!-- Start Toolbar -->
        <div class="toolbar">
            <div class="inner">
                <div class="tools">
                    <div class="search"><i class="icon-search"></i></div>
                    <!-- Start PC  Account -->
                    <div class="account">
                        <a href="#"></a><i class="icon-head"></i>
                        <ul class="toolbar-dropdown">
                            {% if IsLogined =="true" %}
                            <li><a href="/Account/MyProfile">{{ "user.account.indexTitle"|translate}}</a></li>
                            <li><a href="/Account/MyOrders">{{ "user.account.orderTitle"|translate}}</a></li>
                            <li><a href="/Account/MyInbox">{{ "user.account.inboxTitle"|translate}}</a></li>
                            <li><a href="/Account/MyAddress">{{ "user.account.addressTitle"|translate}}</a></li>
                            <li><a href="/Account/MyCoupon">{{ "user.account.couponTitle"|translate}}</a></li>
                            <li><a href="/Account/MyWishList">{{ "user.account.favoriteTitle"|translate}}</a></li>
                            <li><a href="/Account/MyReview">{{ "user.account.reviewTitle"|translate}}</a></li>
                            <li class="sub-menu-separator"></li>
                            <li><a href="/account/SignOut"> {{ "user.account.logOut"|translate}}</a></li>
                            {% else %}
                            <!-- 未登录 -->
                            <li><a href="/account/signin">{{ "user.global.sign_in"|translate}}</a></li>
                            <li><a href="/account/signup">{{ "user.register.register_title"|translate}}</a></li>
                            {% endif %}
                        </ul>
                    </div>
                    <!-- End PC Account -->
                    <!-- Start Mobile  Account -->
                    <div style="display:inline-block">
                        <div class="d-block d-lg-none">
                            <div class=" lang-currency-switcher-wrap menu-toggle mobile-account" style=" margin-left: 0px;">
                                <div class="lang-currency-switcher dropdown-toggle">
                                    <i class="icon-head" style=" line-height: 38px;"></i>
                                </div>
                                <div class="dropdown-menu">
                                    <ul class="mobile-account-menu">
                                        {% if IsLogined =="true" %}
                                        <li><a href="/Account/MyProfile">{{ "user.account.indexTitle"|translate}}</a></li>
                                        <li><a href="/Account/MyOrders">{{ "user.account.orderTitle"|translate}}</a></li>
                                        <li><a href="/Account/MyInbox">{{ "user.account.inboxTitle"|translate}}</a></li>
                                        <li><a href="/Account/MyAddress">{{ "user.account.addressTitle"|translate}}</a></li>
                                        <li><a href="/Account/MyCoupon">{{ "user.account.couponTitle"|translate}}</a></li>
                                        <li><a href="/Account/MyWishList">{{ "user.account.favoriteTitle"|translate}}</a></li>
                                        <li><a href="/Account/MyReview">{{ "user.account.reviewTitle"|translate}}</a></li>
                                        <li class="sub-menu-separator"></li>
                                        <li><a href="/account/SignOut"> {{ "user.account.logOut"|translate}}</a></li>
                                        {% else %}
                                        <!-- 未登录 -->
                                        <li><a href="/account/signin">{{ "user.global.sign_in"|translate}}</a></li>
                                        <li><a href="/account/signup">{{ "user.register.register_title"|translate}}</a></li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- End Mobile  Account -->
                    <!-- Start Cart -->
                    <div class="cart">
                        <a href="/cart"></a>
                        <i class="icon-bag"></i>
                        <span class="count">2</span>
                    </div>
                    <!-- End Cart -->
                </div>
            </div>
        </div>
        <!-- End Toolbar -->
    </header>
    <!-- Start Mobile Menu -->
    <div class="offcanvas-container" id="mobile-menu">
        <div class="row m-0" style="padding: 15px 5px;">
            <div class="col-6 text-left" style="color: #fff;font-size: 14px;font-weight: 500;">{{ "web.global.close"|translate}}</div>
            <div class="col-6 text-right">
                <span class="closemobileMenu" style="color: #fff;font-size: 14px;font-weight: 500;"><i class="icon-cross"></i></span>
            </div>
        </div>
        <!-- 移动端导航菜单 - 通过HTMX加载 -->
        <div id="mobile-navigation" hx-get="/home/<USER>" hx-trigger="load" hx-select="nav.offcanvas-menu">
            <!-- 移动端导航菜单将通过HTMX加载 -->
        </div>
    </div>
    <!-- End Mobile Menu -->
</div>
