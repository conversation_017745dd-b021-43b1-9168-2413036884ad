<div class="page-header">
    <div class="page-title"><h1>{{ "user.account.inboxTitle"|translate}}</h1></div>
</div>
<div class="user-area bg pt-100 pb-80">
    <div class="container">
        <div class="row mb-5">
            {% assign sidebar= '/Themes/'| append: theme | append:'/Account/Sidebar' %}
            {% include sidebar -%}
            {% assign orderStatus = Model.OrderStatus | default: 0 %}
            <div class="col-xs-10 col-lg-10 col-md-12">
                <!-- Tab panes -->
                <div class="dashboard-content padding-30px-all md-padding-15px-all" style="">
                        <div class="row">
                            <div class="col-lg-12">
                                <div id="lib_user_products" style="background-color: white;">



                                    <div class="order_con">
                                        <span class="or_name">{{ "user.account.inboxTitle"|translate}}</span>
                                    </div>
                                    <div class="content_box">
                                        {% if Model.OrdersLog.Reply != null and Model.OrdersLog.Reply.size > 0 %}
                                        {% for log in Model.OrdersLog.Reply %}


                                        <div class="item_date">{{ log.Time }}</div>
                                        <div class="item  {% if log.UserType=='manager' %}message{% else %}mine{% endif %}">
                                            <div class="item_info">
                                                <div class="item_user">{% if log.UserType=='manager' %}{{ log.UserType }}{% else %}{{ Model.OrderDetailModel.ShippingFirstName }} {{ Model.OrderDetailModel.ShippingLastName }}{% endif %} </div>
                                            </div>
                                            <div class="clear"></div>
                                            <div class="item_con">
                                                <div class="item_txt">{{ log.Content }}</div>
                                                {% if log.PicPath !=null and log.PicPath !='' and log.VideoPath =='' %}
                                                <a class="light_box_pic" target="_blank" href="{{log.PicPath}}">
                                                    <img src="{{log.PicPath}}" class="">
                                                </a>
                                                {% endif %}
                                                {% if log.VideoPath !=null and log.VideoPath !=''%}
                                                <div class="item_video" data-video="{{log.VideoPath}}">
                                                    <img src="{{log.PicPath}}" class="img_loading">
                                                    <span></span>
                                                </div>
                                                {% endif %}


                                                <span></span>
                                            </div>
                                            <div class="clear"></div>
                                        </div>
                                        <div class="clear"></div>
                                        {% endfor %}
                                        {% endif %}
                                        <div id="View"></div>
                                    </div>
                                    <form id="reply_form" class="reply_form user_form contact_form" method="post" enctype="multipart/form-data">
                                        <div class="reply_tips">{{ "user.account.reply_btn" | translate }}</div>
                                        <div class="rows">
                                            <label>:</label>
                                            <span class="input"><textarea name="Content" placeholder="" class="form_text" notnull=""></textarea></span>
                                            <div class="clear"></div>
                                        </div>
                                        <div class="rows fr btn_box">
                                            <label>Image:</label>
                                            <div class="input upload_box">
                                                <i class="an an-image"></i>
                                                <input class="upload_file" id="upload_file" type="file" name="PicPath" onchange="removeFileInput('upload_video');loadImg(this);" accept="image/gif,image/jpeg,image/png">
                                            </div>
                                            <div class="input video_box">
                                                <i class="an an-video"></i>
                                                <input class="upload_video" id="upload_video" type="file" name="VideoPath" onchange="removeFileInput('upload_file');loadVideo(this);" accept="video/mp4">
                                            </div>
                                            <div class="submit">
                                                <input type="submit" class="btn_global btn_submit" name="submit_button" value="{{ "web.global.submit" | translate }}" style="background: var(--theme-color);">
                                            </div>
                                            <div class="clear"></div>
                                        </div>
                                        <div class="clear"></div>
                                        <input type="hidden" name="MId" value="{{ Model.OrdersLog.MId }}">
                                        <input type="hidden" name="userId" value="{{ Model.userId }}">
                                        <input type="hidden" name="JumpUrl" value="/account/MyContact/{{ Model.OrdersLog.OrderId }}">
                                        <input type="hidden" name="do_action" value="api/account/comment/userreply">
                                        <div id="pic_show" class="pic_box"></div>
                                    </form>
                                </div>




                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>
     <link href="/static/contact/themes-v2/global.css?v=5.**************" asp-append-version="true" rel="stylesheet">
    <link href="/static/contact/css/global.css" asp-append-version="true" rel="stylesheet">
    <link href="/static/contact/css/user.css" asp-append-version="true" rel="stylesheet">

    <link href="/static/contact/themes-v2/iconfont.css?v=5.**************" asp-append-version="true" rel="stylesheet">
    <link href="/static/contact/themes-v2/user.css?v=5.**************" asp-append-version="true" rel="stylesheet">

    <script type="text/javascript" src="/static/contact/js/jquery-1.7.2.min.js?v=5.**************" ></script>
    <script type="text/javascript" src="/static/contact/js/global.js?v=5.**************" ></script>
    <script type="text/javascript" src="/static/contact/js/themes-v2/global.js?v=5.**************" ></script>

<!-- 引入自定义弹窗JS库 -->
<script src="/js/Pop-ups/frame-message.js"></script>
<script>

     // 页面加载时初始化验证码
    document.addEventListener('DOMContentLoaded', function() {
       

          $('#reply_form').attr('action', '/api/account/comment/userreplyInbox');

    // 监听表单提交事件
    $('#reply_form').submit(function(e) {
        e.preventDefault();

        var form = $(this);
        var formData = {
            Content: $('textarea[name="Content"]').val().trim(),
            MId: $('input[name="MId"]').val(),
            userId: $('input[name="userId"]').val(),
            JumpUrl: $('input[name="JumpUrl"]').val(),
            do_action: $('input[name="do_action"]').val()
        };

        // 内容验证
        if(!formData.Content) {
            customize_pop.warning('{{'user.account.addReply'|translate}}', null, null, {showIcon: false});
            return;
        }

        // 处理图片
        var imgPreview = $('#pic_show img');
        if(imgPreview.length > 0) {
            formData.PicPath = imgPreview.attr('src');
        }
        // 处理视频
        var videoInput = $('#upload_video')[0];
        if(videoInput.files.length > 0) {
            var reader = new FileReader();
            reader.onload = function(e) {
                formData.VideoPath = e.target.result;
                sendData(form, formData);
            };
            reader.readAsDataURL(videoInput.files[0]);
            return;
        }

        sendData(form, formData);

        function sendData(form, data) {
            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: data,
                success: function(response) {
                    if(response.success) {
                         customize_pop.success('{{'web.global.submit_success'|translate}}');
                        window.location.href = window.location.href; 
                    } else {
                         customize_pop.error(response.message || 'unknown error');
                    }
                },
                error: function(xhr) {
                    console.error('AJAX Error:', xhr.responseText);
                    customize_pop.error('An error occurred. Please try again later');
                }
            });
        }
    });

        // 新增：自动滚动到最底部回复项
    function scrollToLatestReply() {
        const latestReplyDate = document.querySelector('.content_box #View:last-child');
  
        if (latestReplyDate) {
            latestReplyDate.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
 
    // 等待所有动态内容加载完成后执行（如异步加载的回复）
    setTimeout(scrollToLatestReply, 500); // 延迟 500ms 确保元素已渲染


    });
</script>
<script type="text/javascript">
if(typeof $(window).webDisplay == "function"){
    $(window).resize(function(){$(window).webDisplay(0);})
	$(document).ready(function(){$(window).webDisplay(0);});}
    var ueeshop_config = {
		}
var ueeshop_handle = {}

</script>
<style>
    body, textarea, input, button, select, keygen, legend {
        font-size: 16px;
        font-family: var(--body-font);
    }
    h1, h2, h3, h4, h5, h6 {
        color: var(--color-dark);
        font-weight: 600;
        font-family: var(--heading-font);
        line-height: 1.2;
        margin: 0px;
    }
    h5 {
        font-size: 18px;
    }
</style>
