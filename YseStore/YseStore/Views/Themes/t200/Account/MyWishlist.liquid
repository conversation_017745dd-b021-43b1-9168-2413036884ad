<div class="page-header">
    <div class="page-title"><h1>{{ "user.account.favoriteTitle" | translate }}</h1></div>
</div>
<div class="container">
    <div class="row mb-5">
        {% assign sidebar = '/Themes/' | append: theme | append: '/Account/Sidebar' %}
        {% include sidebar -%}

        <div class="col-xs-10 col-lg-10 col-md-12">
            <!-- Tab panes -->
            <div class="dashboard-content padding-30px-all md-padding-15px-all" style="">
                <div class="">
                    <div class="row">
                        <!--Main Content-->
                        <div class="col-12 col-sm-12 col-md-12 col-lg-12 main-col">
                            {% if ViewBag.IsEmpty %}
                                <!-- 空数据样式 -->
                                <div class="empty-date text-center py-5">
                                    <img src="{{ static_path }}/assets/images/empty-wishlist.png" alt="Empty Wishlist">
                                    <p class="mt-3">Your wishlist is empty.</p>
                                    {% if ViewBag.ErrorMessage %}
                                        <p class="text-danger">{{ ViewBag.ErrorMessage }}</p>
                                    {% endif %}
                                    <a href="/Shop" class="btn btn-primary mt-3">Continue Shopping</a>
                                </div>
                            {% else %}
                                <form action="#">
                                    <div class="wishlist-table table-content table-responsive">
                                        <table class="table table-bordered">
                                            <thead class="text-nowrap">
                                            <tr>
                                                <th class="product-name text-center alt-font">{{ "web.global.remove" | translate }}</th>
                                                <th class="product-price text-center alt-font">{{ "web.global.image" | translate }}</th>
                                                <th class="product-name alt-font">{{ "user.global.productName" | translate }}</th>
                                                <th class="product-price text-center alt-font">{{ "web.global.price" | translate }} </th>
                                                <th class="stock-status text-center alt-font">{{ "user.account.status" | translate }}</th>
                                                <th class="product-subtotal text-center alt-font">{{ "products.goods.addToCart" | translate }}</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            {% for item in Model %}
                                                <tr>
                                                    <td class="product-remove text-center align-middle">
                                                        <a href="#" data-favorite-id="{{ item.FavoriteId }}"
                                                           class="remove-wishlist"><i class="an an-times"></i></a>
                                                    </td>
                                                    <td class="product-thumbnail text-center">
                                                        <a href="/products/{{ item.PageUrl }}" hx-target="#page-content"
                                                           hx-push-url="true" hx-swap="innerHTML">
                                                            <img src="{{ item.PicPath }}" alt="{{ item.ProductName }}"
                                                                 title="{{ item.ProductName }}">
                                                        </a>
                                                    </td>
                                                    <td class="product-name">
                                                        <a href="/products/{{ item.PageUrl }}" hx-target="#page-content"
                                                           hx-push-url="true"
                                                           hx-swap="innerHTML">{{ item.ProductName }}</a>
                                                        {% if item.TypeLabel %}
                                                            {% comment %}<br><span class="small">{{ item.TypeLabel }}</span>{% endcomment %}
                                                        {% endif %}
                                                    </td>
                                                    <td class="product-price text-center">
                                                        {% if item.PromotionPrice > 0 %}
                                                            <!-- 有促销价时：显示促销价和原价 -->
                                                            {% if item.OriginalPriceFormat %}
                                                                <del>{{ item.OriginalPriceFormat }}</del><br>
                                                            {% endif %}
                                                            <span class="amount">{{ item.PromotionPriceFormat }}</span>
                                                        {% else %}
                                                            <!-- 没有促销价时：显示正常价格 -->
                                                            {% if item.OriginalPriceFormat %}
                                                                <del>{{ item.OriginalPriceFormat }}</del><br>
                                                            {% endif %}
                                                            <span class="amount">{{ item.PriceFormat }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td class="stock text-center">
                                                        {% if item.IsInStock %}
                                                            <span class="in-stock">{{ "products.goods.inStock" | translate }}</span>
                                                        {% else %}
                                                            <span class="out-stock">{{ "products.goods.outStock" | translate }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td class="product-subtotal text-center">
                                                        {% if item.IsInStock %}
                                                            <button type="button" class="btn btn-small product-cart-btn"
                                                                    data-page-url="{{ item.PageUrl }}">{{ "products.goods.addToCart" | translate }}</button>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </form>
                            {% endif %}
                        </div>
                        <!--End Main Content-->
                    </div>
                </div>
            </div>
            <!-- End Tab panes -->
        </div>
    </div>
</div>

<!-- 引入自定义弹窗JS库 -->
<script src="/js/Pop-ups/frame-message.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 从收藏列表移除商品
        const removeWishlistBtns = document.querySelectorAll('.remove-wishlist');
        removeWishlistBtns.forEach(btn => {
            btn.addEventListener('click', function (e) {
                e.preventDefault();
                const favoriteId = this.getAttribute('data-favorite-id');
                const productRow = this.closest('tr');
                customize_pop.confirm('Are you sure you want to remove this item from your wishlist?', function () {
                    // 确认后执行删除操作
                    // 发送AJAX请求移除收藏
                    fetch('/Account/RemoveFromWishlist', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({favoriteId: favoriteId})
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // 移除成功，显示成功消息
                                customize_pop.success('Item removed from wishlist successfully');

                                // 移除DOM元素
                                productRow.remove();

                                // 如果移除后收藏列表为空，显示空状态
                                const remainingItems = document.querySelectorAll('.wishlist-table tbody tr');
                                if (remainingItems.length === 0) {
                                    location.reload(); // 刷新页面显示空状态
                                }
                            } else {
                                // 显示错误消息
                                customize_pop.error(data.message || 'Failed to remove item. Please try again');
                            }
                        })
                        .catch(error => {
                            console.error('Error removing from wishlist:', error);
                            customize_pop.error('An error occurred. Please try again later');
                        });
                }, null, 'Confirm Removal', 'Cancel', 'Confirm', false);
            });
        });

        // 添加到购物车 - 直接跳转到产品详情页
        const addToCartBtns = document.querySelectorAll('.product-cart-btn');
        addToCartBtns.forEach(btn => {
            btn.addEventListener('click', function (e) {
                e.preventDefault();
                const pageUrl = this.getAttribute('data-page-url');

                if (!pageUrl) {
                    customize_pop.error('Product URL not found', null, null, {showIcon: false});
                    return;
                }
                
                const productUrl = `/products/${pageUrl}`;

                window.location.href = productUrl;

            });
        });
    });
</script>