<div class="page-header">
    <div class="page-title"><h1>{{ "user.account.orderTitle"|translate}}</h1></div>
</div>
<div class="container">
    <div class="row mb-5">
        {% assign sidebar= '/Themes/'| append: theme | append:'/Account/Sidebar' %}
        {% include sidebar -%}
        <div class="col-xs-10 col-lg-10 col-md-12">
            <!-- Tab panes -->
            <div class="dashboard-content padding-30px-all md-padding-15px-all" style="">
                <div class="container user-track-order">
                    <div class="row pb-4">
                        <div class="col-6 col-lg-7"><h3>#{{ "user.account.order_no"|translate}}{{ Model.OrderDetailModel.OId }}</h3></div>
                        <div class="col-6 col-lg-5" style="text-align:right;"><a href="/Account/MyOrders" class="btn cart-continue theme-btn">{{ "user.account.orderTitle"|translate}}</a></div>
                    </div>

                    <div class="track-order-step">
                     {% if Model.order_status_steps != null and Model.order_status_steps.size > 0 %}
                        {% for step in Model.order_status_steps %}
                        <div class="step-item {% if step.Status <= Model.current_order_status %}completed{% endif %}">
                            <div class="step-icon">
                             {% if forloop.index == 1 %}
                                <i class="icon an an-money-bill"></i>
                             {% elsif forloop.index == 2 %}
                                  <i class="icon an an-shipping-fast"></i>
                             {% elsif forloop.index == 3 %}
                                   <i class="icon an an-check-circle"></i>
                              {% elsif forloop.index == 4 %}
                                    <i class="icon an an-home"></i>
                              {% endif %}

                            </div>
                            
                            <h6>{{ step.StatusName | translate }} </h6>
                            {% if step.StatusTime %}
                            <p>{{ step.StatusTime  }}</p>
                            {% endif %}
                        </div>
                        {% endfor %}
                         {% endif %}
                    </div>


                    <div class="container mt-4">
                        <div class="row">
                            <div class="col-12"><h3>#{{ "user.account.order_no"|translate}}Order Information</h3></div>
                            <div class="col-12 col-sm-12 col-md-6 col-lg-6 cart__footer">
                                <div class="solid-border">
                                    <div class="row border-bottom pb-2">
                                        <span class="col-6 col-sm-6 cart__subtotal-title">{{ "checkout.checkout.subtotal"|translate}}</span>
                                        <span class="col-6 col-sm-6 text-right"><span class="money">{{ Model.OrderSymbol}}{{ Model.TotalProductPrice}}</span></span>
                                    </div>
                                    <div class="row border-bottom pb-2 pt-2">
                                        <span class="col-6 col-sm-6 cart__subtotal-title">{{ "checkout.checkout.discount"|translate}}</span>
                                        <span class="col-6 col-sm-6 text-right">-{{ Model.OrderSymbol}}{{ Model.DiscountPrice}}</span>
                                    </div>
                                    <div class="row border-bottom pb-2 pt-2">
                                        <span class="col-6 col-sm-6 cart__subtotal-title">{{ "checkout.checkout.coupon_save"|translate}}</span>
                                        <span class="col-6 col-sm-6 text-right">-{{ Model.OrderSymbol}}{{ Model.CouponPrice}}</span>
                                    </div>
                                    <div class="row border-bottom pb-2 pt-2">
                                        <span class="col-6 col-sm-6 cart__subtotal-title">{{ "checkout.points.points"|translate}}</span>
                                        <span class="col-6 col-sm-6 text-right">-{{ Model.OrderSymbol}}{{ Model.points}}</span>
                                    </div>
                                    <div class="row border-bottom pb-2 pt-2">
                                        <span class="col-6 col-sm-6 cart__subtotal-title">{{ "checkout.checkout.step_shipping"|translate}}</span>
                                        <span class="col-6 col-sm-6 text-right">{{ Model.OrderSymbol}}{{ Model.shippingFee}}</span>
                                    </div>
                                    <div class="row border-bottom pb-2 pt-2">
                                        <span class="col-6 col-sm-6 cart__subtotal-title">{{ "checkout.checkout.tax"|translate}}</span>
                                        <span class="col-6 col-sm-6 text-right">{{ Model.OrderSymbol}}{{ Model.TaxPrice}}</span>
                                    </div>
                                    <div class="row border-bottom pb-2 pt-2">
                                        <span class="col-6 col-sm-6 cart__subtotal-title">{{ "user.account.handingFee"|translate}}</span>
                                        <span class="col-6 col-sm-6 text-right">{{ Model.OrderSymbol}}{{ Model.commission}}</span>
                                    </div>


                                    <div class="row border-bottom pb-2 pt-2">
                                        <span class="col-6 col-sm-6 cart__subtotal-title"><strong>{{ "web.global.total"|translate}}</strong></span>
                                        <span class="col-6 col-sm-6 cart__subtotal-title cart__subtotal text-right"><span class="money">{{ Model.OrderSymbol}}{{ Model.OrderSum}}</span></span>
                                    </div>
                                    <div class="row border-bottom pb-2 pt-2">
                                        <span class="col-6 col-sm-6 cart__subtotal-title">#{{ "user.account.order_no"|translate}}</span>
                                        <span class="col-6 col-sm-6 text-right">{{ Model.OrderDetailModel.OId }}</span>
                                    </div>
                                    <div class="row border-bottom pb-2 pt-2">
                                        <span class="col-6 col-sm-6 cart__subtotal-title">{{ "user.account.order_status"|translate}}</span>
                                        {% if Model.OrderDetailModel.OrderStatus == 1 %}
                                        <span class="col-6 col-sm-6 text-right">{{ 'user.account.OrderStatusAry_'|append:Model.OrderDetailModel.OrderStatus | translate }}</span>
                                        {% elsif Model.OrderDetailModel.OrderStatus == 2 %}
                                        <span class="col-6 col-sm-6 text-right">{{ 'user.account.OrderStatusAry_'|append:Model.OrderDetailModel.OrderStatus | translate }}</span>
                                        {% elsif Model.OrderDetailModel.OrderStatus == 3 %}
                                        <span class="col-6 col-sm-6 text-right">{{ 'user.account.OrderStatusAry_'|append:Model.OrderDetailModel.OrderStatus | translate }}</span>
                                        {% elsif Model.OrderDetailModel.OrderStatus == 4 %}
                                        <span class="col-6 col-sm-6 text-right">{{ 'user.account.OrderStatusAry_'|append:Model.OrderDetailModel.OrderStatus | translate }}</span>
                                        {% elsif Model.OrderDetailModel.OrderStatus == 5 %}
                                        <span class="col-6 col-sm-6 text-right">{{ 'user.account.OrderStatusAry_'|append:Model.OrderDetailModel.OrderStatus | translate }}</span>
                                        {% elsif Model.OrderDetailModel.OrderStatus == 6 %}
                                        <span class="col-6 col-sm-6 text-right">{{ 'user.account.OrderStatusAry_'|append:Model.OrderDetailModel.OrderStatus | translate }}</span>
                                        {% elsif Model.OrderDetailModel.OrderStatus == 7 %}
                                        <span class="col-6 col-sm-6 text-right">{{ 'user.account.OrderStatusAry_'|append:Model.OrderDetailModel.OrderStatus | translate }}</span>
                                        {% endif %}

                                    </div>
                                </div>

                            </div>
                            <div class="col-12 col-sm-12 col-md-6 col-lg-6 mb-4 cart-col">
                                <div class="cart-col-in">
                                    <h5>{{ "user.account.ship_addr"|translate}}</h5>
                                    <p><i class="icon an an-map-marker"></i> {{ Model.ShippingAddress}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <!--Main Content-->
                        <div class="col-12 col-sm-12 col-md-12 col-lg-12 main-col">
                            <h3 style="text-transform: capitalize;">{{ "checkout.global.summary"|translate}}</h3>
                            {% if Model.shippingFee == 0 %}
                            <div class="alert alert-success text-uppercase" role="alert">
                                <i class="icon an an-truck an-lg icon-large"></i> &nbsp;<strong>Congratulations!</strong> You've got free shipping!
                            </div>
                            {% endif %}




                            <!--此处包裹循环-->
                      



                           <!-- 包裹循环 -->
           
                           {% if Model.OrderPackage != null %}
                         {% for package in Model.OrderPackage %}
                          <div>
                            <h4 style="text-transform: capitalize;">{{ "user.account.package"|translate}} {{ forloop.index }}</h4>
                            <div class="package-info">
                            {% if package.Status!=0 %}
                                <span class="package-item">(<span class="package-item-title">{{ "user.account.trackNo"|translate}}:</span> {{ package.TrackingNumber }})</span>
                                <span class="package-item"><span class="package-item-title">{{ "user.account.shippedTime"|translate}}:</span> {{ package.ShippingTime | date: "%b %d, %Y" }}</span>
                                {% endif %}
                                <span class="package-item"><span class="package-item-title">{{ "web.global.remark"|translate}}:</span> {{ package.Remarks }}</span>
                                <span class="package-item"> <span class="package-item-title">{{ "user.account.shippingMethod"|translate}}:</span> {{ package.ShippingExpress }}</span>
                                <span class="package-item"><span class="package-item-title">{{ "checkout.checkout.shipcharge"|translate}}:</span>  {{ Model.OrderSymbol }}{{ Model.shippingFee }}</span>
                            </div>
                            <div class="table-responsive mt-4">
                                <table>
                                    <thead class="cart__row cart__header">
                                        <tr>
                                            <th colspan="2" class="text-center">{{ "user.global.productName"|translate}}</th>
                                            <th class="text-center">{{ "web.global.price"|translate}}</th>
                                            <th class="text-center">{{ "web.global.qty"|translate}}</th>
                                            <th class="text-center">{{ "web.global.total"|translate}}</th>
                                            <th class="action">&nbsp;</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% assign proInfoDict = package.ProInfo | json_parse %}
                                        {% if Model.ordersProductsLists != null and Model.ordersProductsLists.size > 0 %}
                                        {% for item in Model.ordersProductsLists %}
                                         {% if proInfodict contains item.LId %}
                                        <tr class="cart__row border-bottom line1 cart-flex border-top">
                                            <td class="cart__image-wrapper cart-flex-item">
                                                <a href="/products/{{item.PageUrl}}"><img class="blur-up lazyload cart__image" src="{{item.PicPath}}" data-src="{{item.PicPath}}" alt="{{item.Name}}"></a>
                                            </td>
                                            <td class="cart__meta small--text-left cart-flex-item">
                                                <div class="list-view-item__title">
                                                    <a href="/products/{{item.PageUrl}}">{{item.Name}} </a>
                                                </div>
                                                <div class="cart__meta-text">
                                                    No.{{item.SKU}}
                                                </div>
                                            </td>
                                            <td class="cart__price-wrapper cart-flex-item text-center">
                                                <span class="money">{{item.PropertyString}}</span>
                                            </td>
                                            <td class="cart__update-wrapper cart-flex-item text-center">
                                                <span class="money">{{item.Qty}}</span>
                                            </td>

                                            <td class="small--hide cart-price text-center">
                                                <div><span class="money">{{Model.OrderSymbol}}{{item.ProductPriceStr}}</span></div>
                                            </td>
                                        </tr>
                                        {% endif %}
                                        {% endfor %}
                                        {% endif %}


                                    </tbody>
                                </table>
                            </div>
                           </div>




                         {% endfor %}
                           {% endif %}







                        </div>
                        <!--End Main Content-->
                    </div>
                    <!-- 订单未支付 -->
                    <div class="row mt-4">
                        <div class="col-6 col-lg-6">

                            {% if Model.OrderDetailModel.OrderStatus == 1  %}
                            <a class="btn theme-btn cancel_order" value="{{ Model.OrderDetailModel.OrderId }}">{{ "user.account.cancel_order"|translate}}</a>
                            {% endif %}
                        </div>
                        <div class="col-6 col-lg-6 text-right">
                            {% if Model.OrderDetailModel.OrderStatus == 1 or Model.OrderStatus == 3 %}
                            <a href="/cart/{{ Model.OrderDetailModel.OId }}/info?code={{Model.OIdBase64}}" class="btn theme-btn">{{ "checkout.global.paynow"|translate}}</a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Tab panes -->
        </div>
    </div>
</div>
<!-- 引入自定义弹窗JS库 -->
<script src="/js/Pop-ups/frame-message.js"></script>
<script>

 
    // 原有代码保持不变，在文档底部添加以下内容
    document.addEventListener('DOMContentLoaded', function() {
        // 获取URL参数中的OrderStatus
        const urlParams = new URLSearchParams(window.location.search);
        const orderStatus = urlParams.get('OrderStatus');
 
        if (orderStatus) {
            const select = document.getElementById('OrderStatus');
     
            // 设置select的value
            select.value = orderStatus;
            // 强制更新niceSelect显示
            jQuery(select).niceSelect('update');
        }



    // 监听表单提交事件
    $('.cancel_order').click(function(e){
        e.preventDefault();
        var form = $(this);
        var formData = {
            OrderId: form.attr('value')
        };

    
          customize_pop.confirm('{{'user.account.cancelOrder'|translate}}', function() {



        sendData(formData);
        function sendData(data) {
            $.ajax({
                url: '/api/account/comment/cancelorder',
                type: 'POST',
                data: data,
                success: function(response) {
                    if(response.success) {
                       customize_pop.success('{{'web.global.submit_success'|translate}}');
                       // 先获取元素
                       window.location.href=window.location.href;
    
                    } else {
                        
                          // 显示错误消息
                            customize_pop.error(response.message || 'Failed to cancel item. Please try again');
                    }
                },
                error: function(xhr) {
                    console.error('AJAX Error:', xhr.responseText);
                     customize_pop.error('An error occurred. Please try again later');
                }
            });
        }

          }, null, '{{'web.global.confirm'|translate}}', '{{'web.global.cancel'|translate}}', '{{'web.global.confirm'|translate}}', false);

    });


 });


</script>


