<div class="container pt-5 pb-5">
    <div class="row">
        <!--Sidebar-->
        <div class="col-12 col-sm-12 col-md-3 col-lg-3 sidebar filterbar">
            <div class="closeFilter d-block d-md-none d-lg-none"><i class="icon an an-times"></i></div>
            <div class="sidebar_tags">
                <div class="filter-sidebar">
                    <!--Categories-->
                    <div class="sidebar_widget filterBox categories filter-widget">
                        <div class="widget-title"><h2>{{ "web.global.category" | translate }}</h2></div>
                        <div class="widget-content" style="">
                            <ul class="sidebar_categories">
                                <!-- 使用CategoryTree["0"]访问顶级分类 -->
                                {% if Model.CategoryTree != null and Model.CategoryTree["0"] != null and Model.CategoryTree["0"].size > 0 %}
                                {% for category in Model.CategoryTree["0"] %}
                                <li class="level1 {% if category.SubCateCount > 0 %}sub-level{% else %}lvl-1{% endif %}">
                                    {% if category.SubCateCount > 0 %}
                                    <a href="javascript:void(0);"
                                       class="site-nav category-toggle">{{ category.Category_en }}</a>
                                    <a href="/collections/{{ category.PageUrl }}"
                                       class="site-nav view-all-link"
                                       style="display:none;">{{ "web.global.view_all" | translate }}</a>
                                    {% else %}
                                    <a href="/collections/{{ category.PageUrl }}"
                                       class="site-nav">{{ category.Category_en }}</a>
                                    {% endif %}

                                    <!-- 检查是否有子分类 -->
                                    {% assign catIdStr = category.CateId | append: '' %}
                                    {% if category.SubCateCount > 0 and Model.CategoryTree[catIdStr] != null and Model.CategoryTree[catIdStr].size > 0 %}
                                    <ul class="sublinks" style="display: none;">
                                        <li class="level2">
                                            <a href="/collections/{{ category.PageUrl }}"
                                               class="site-nav">
                                                {{ category.Category_en }}
                                            </a>
                                        </li>

                                        <!-- 遍历子分类 -->
                                        {% for subCategory in Model.CategoryTree[catIdStr] %}
                                        <li class="level2">
                                            <a href="/collections/{{ subCategory.PageUrl }}"
                                               class="site-nav">
                                                {{ subCategory.Category_en }}
                                            </a>
                                        </li>
                                        {% endfor %}
                                    </ul>
                                    {% endif %}
                                </li>
                                {% endfor %}
                                {% else %}
                                <li class="lvl-1">
                                    <a href="#"
                                       class="site-nav">{{ "web.global.no_data" | translate }}</a>
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                    <!--Categories-->
                </div>
            </div>
        </div>
        <!--End Sidebar-->
        <!--Main Content-->
        <div class="col-12 col-sm-12 col-md-9 col-lg-9 main-col">
            <!--Category Top Description-->
            {% if Model.CategoryTopDescription and Model.CategoryTopDescription.Description_en and Model.CategoryTopDescription.Description_en != "" %}
            <div class="category-description-top mb-4">
                {{ Model.CategoryTopDescription.Description_en | raw }}
            </div>
            {% endif %}
            <!--End Category Top Description-->
            <!--Toolbar-->
            <button type="button" class="btn btn-filter d-block d-md-none d-lg-none">
                <i class="an an-lg an-filter"></i>
                Product Filters
            </button>
            <div class="toolbar">
                <div class="filters-toolbar-wrapper">
                    <div class="row">
                        <div class="col-4 col-md-4 col-lg-4 filters-toolbar__item collection-view-as d-flex justify-content-start align-items-center">
                            <a href="/collections" title="Grid View" class="change-view">
                                <i class="an an-lg an-th-large" aria-hidden="true"></i>
                            </a>
                            <a href="/collections/ShopList" title="List View" class="change-view change-view--active">
                                <i class="an an-lg an-th-list" aria-hidden="true"></i>
                            </a>
                        </div>
                        <div class="col-4 col-md-4 col-lg-4 text-center filters-toolbar__item filters-toolbar__item--count d-flex justify-content-center align-items-center">
                            {% if Model.TotalItems > 0 %}
                            <span class="filters-toolbar__product-count">Showing: {{ Model.ShowingStart }}-{{ Model.ShowingEnd }} of {{ Model.TotalItems }} Results</span>
                            {% else %}
                            <span class="filters-toolbar__product-count">{{ "products.goods.no_products" | translate }}</span>
                            {% endif %}
                        </div>
                        <div class="col-4 col-md-4 col-lg-4 text-right">
                            <div class="filters-toolbar__item">
                                <label for="SortBy" class="hidden">Sort</label>
                                <select name="SortBy" id="SortBy"
                                        class="filters-toolbar__input filters-toolbar__input--sort"
                                        onchange="changeSorting()">
                                    <option value="title-ascending"
                                            {% if Model.SortBy=="title-ascending" %}selected{% endif %}>
                                        {{ "products.lists.sort_by" | translate }}
                                    </option>
                                    <option value="bestseller" {% if Model.SortBy=="bestseller" %}selected{% endif %}>
                                        {{ "products.lists.sort_by_ary_5" | translate }}
                                    </option>
                                    <option value="alpha-asc" {% if Model.SortBy=="alpha-asc" %}selected{% endif %}>
                                        Alphabetically, A-Z
                                    </option>
                                    <option value="alpha-desc" {% if Model.SortBy=="alpha-desc" %}selected{% endif %}>
                                        Alphabetically, Z-A
                                    </option>
                                    <option value="priceasc" {% if Model.SortBy=="priceasc" %}selected{% endif %}>
                                        {{ "products.lists.sort_by_ary_3" | translate }}
                                    </option>
                                    <option value="pricedesc" {% if Model.SortBy=="pricedesc" %}selected{% endif %}>
                                        {{ "products.lists.sort_by_ary_4" | translate }}
                                    </option>
                                    <option value="date-newest"
                                            {% if Model.SortBy=="date-newest" %}selected{% endif %}>
                                        {{ "products.lists.sort_by_ary_1" | translate }}
                                    </option>
                                    <option value="date-oldest"
                                            {% if Model.SortBy=="date-oldest" %}selected{% endif %}>
                                        {{ "products.lists.sort_by_ary_2" | translate }}
                                    </option>

                                </select>
                                <input class="collection-header__default-sort" type="hidden" value="manual">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--End Toolbar-->
            <!--Product Grid-->
            <div class="product-grid-view">
                <div class="grid-products grid--view-items">
                    <div class="row">
                        {% if Model.Products != null and Model.Products.size > 0 %}
                        {% for product in Model.Products %}
                        <div class="col-6 col-sm-6 col-md-4 col-lg-4 item">
                            <!-- start product image -->
                            <div class="product-image">
                                <!-- start product image -->
                                <a href="/products/{{ product.PageUrl }}" hx-target="#page-content"
                                   hx-push-url="true" hx-swap="innerHTML"
                                   class="product-img">
                                    <!-- image -->
                                    <img class="primary blur-up lazyload"
                                         data-src="{% if product.PicPath and product.PicPath != '' %}{{ product.PicPath }}{% else %}{{ static_path }}/assets/images/product-images/elt-p-10.jpg{% endif %}"
                                         src="{% if product.PicPath and product.PicPath != '' %}{{ product.PicPath }}{% else %}{{ static_path }}/assets/images/product-images/elt-p-10.jpg{% endif %}"
                                         alt="{{ product.Title }}"
                                         title="{{ product.Title }}">
                                    <!-- End image -->
                                    <!-- Hover image -->
                                    <img class="hover blur-up lazyload"
                                         data-src="{% if product.PicPath_1 and product.PicPath_1 != '' %}{{ product.PicPath_1 }}{% else %}{{ static_path }}/assets/images/product-images/elt-p-10-1.jpg{% endif %}"
                                         src="{% if product.PicPath_1 and product.PicPath_1 != '' %}{{ product.PicPath_1 }}{% else %}{{ static_path }}/assets/images/product-images/elt-p-10-1.jpg{% endif %}"
                                         alt="{{ product.Title }}"
                                         title="{{ product.Title }}">
                                    <!-- End hover image -->
                                    <!--<div class="product-labels"><span class="lbl on-sale">Sale</span></div>-->
                                    {% if product.Stock <= 0 %}
                                    <span class="sold-out"><span>{{ "products.goods.soldout" | translate }}</span></span>
                                    {% endif %}
                                    <!--<div class="product-labels" style="background-image:url(https://www.retekess.com/Assets/files/20250415/1616562925/easter-com-compressed.png)"></div>-->
                                </a>
                                <!-- end product image -->
                                <!--Product Button-->
                                <div class="button-set style3">
                                    <ul>
                                        <li style="display: none;">
                                            <!--Quick View Button (Hidden)-->
                                            <a href="#quickview-popup" title="Quick View"
                                               class="btn-icon quick-view-popup quick-view"
                                               data-bs-toggle="modal"
                                               data-bs-target="#quickview_popup"
                                               data-product-id="{{ product.ProductId }}">
                                                <i class="icon an an-expand-arrows-alt"></i>
                                                <span class="tooltip-label">{{ "web.global.quickView" | translate }}</span>
                                            </a>
                                            <!--End Quick View Button (Hidden)-->
                                        </li>
                                        <li>
                                            <!--Wishlist Button-->
                                            <div class="wishlist-btn">
                                                <a class="btn-icon wishlist add-to-wishlist"
                                                   href="javascript:void(0);"
                                                   data-product-id="{{ product.ProductId }}"
                                                   data-is-favorited="{% if product.IsFavorited %}true{% else %}false{% endif %}">
                                                    {% if product.IsFavorited %}
                                                    <i class="icon an an-heart"></i>
                                                    {% else %}
                                                    <i class="icon an an-heart-o"></i>
                                                    {% endif %}
                                                    <span class="tooltip-label">{{ "products.goods.addToFavorites" | translate }}</span>
                                                </a>
                                            </div>
                                            <!--End Wishlist Button-->
                                        </li>
                                    </ul>
                                </div>
                                <!--End Product Button-->
                            </div>
                            <!-- end product image -->
                            <!--start product details -->
                            <div class="product-details text-left">
                                <!--Brand Name-->
                                <div class="brand-name">{{ product.Brand | default: "Brand" }}</div>
                                <!--End Brand Name-->
                                <!-- product name -->
                                <div class="product-name">
                                    <a href="/products/{{ product.PageUrl }}" hx-target="#page-content"
                                       hx-push-url="true"
                                       hx-swap="innerHTML">{{ product.ProductName }}</a>
                                </div>
                                <!-- End product name -->
                                <!-- product price -->
                                <div class="product-price" data-price-container
                                     data-original-price="{{ product.OriginalPriceFormat }}"
                                     data-current-price="{{ product.PriceFormat }}"
                                     data-promotion-price="{{ product.PromotionPriceFormat }}">
                                    <!-- 价格内容将通过JavaScript动态生成 -->
                                </div>
                                <!-- End product price -->
                                <!--Product Review-->
                                <div class="product-review">
                                    {% assign rating = product.AvgRating | default: 0 %}
                                    {% assign whole_stars = rating | floor %}
                                    {% assign half_star = rating | minus: whole_stars %}
                                    {% assign next_star = whole_stars | plus: 1 %}

                                    {% for i in (1..5) %}
                                    {% if i <= whole_stars %}
                                    <i class="an an-star"></i>
                                    {% elsif half_star >= 0.5 and i == next_star %}
                                    <i class="an an-star-half-alt"></i>
                                    {% else %}
                                    <i class="an an-star gray-star"></i>
                                    {% endif %}
                                    {% endfor %}
                                    <span class="review-label">
                                        <a href="#;">{{ product.ReviewCount | default: 0 }} {{ "products.goods.reviews" | translate }}</a>
                                    </span>
                                </div>
                                <!--End Product Review-->
                                <!-- Dynamic Attributes Variant -->
                                {% if product.DynamicAttributes != null and product.DynamicAttributes.size > 0 %}
                                {% comment %} 遍历动态属性，查找有颜色代码的属性 {% endcomment %}
                                {% for attribute in product.DynamicAttributes %}
                                {% assign attributeName = attribute[0] %}
                                {% assign attributeOptions = attribute[1] %}

                                {% if attributeOptions != null and attributeOptions.size > 0 %}
                                {% comment %} 检查是否有颜色代码 {% endcomment %}
                                {% assign hasColorCode = false %}
                                {% for option in attributeOptions %}
                                {% if option.ColorCode != null and option.ColorCode != "" %}
                                {% assign hasColorCode = true %}
                                {% break %}
                                {% endif %}
                                {% endfor %}

                                {% if hasColorCode %}
                                <ul class="swatches">
                                    {% for option in attributeOptions %}
                                    {% if option.ColorCode != null and option.ColorCode != "" %}
                                    <li class="swatch small"
                                        style="background-color: {{ option.ColorCode }};">
                                        <span class="tooltip-label">{{ option.Name }}</span>
                                    </li>
                                    {% endif %}
                                    {% endfor %}
                                </ul>
                                {% break %} {% comment %} 只显示第一个颜色属性 {% endcomment %}
                                {% endif %}
                                {% endif %}
                                {% endfor %}
                                {% endif %}
                                <!-- End Variant -->
                                <!--Cart Button-->
                                {% if product.Stock > 0 %}
                                {% if product.HasOptions %}
                                <a href="javascript:void(0);" title="Select Options"
                                   class="btn-icon btn btn-addto-cart"
                                   data-product-id="{{ product.ProductId }}">
                                    <i class="icon an an-lg an-cog"></i> <span>Select Options</span>
                                </a>
                                {% else %}
                                <a href="javascript:void(0);" title="{{ "products.goods.addToCart" | translate }}"
                                   class="btn-icon btn btn-addto-cart theme-btn"
                                   data-product-id="{{ product.ProductId }}">
                                    <i class="icon an an-shopping-cart"></i>
                                    <span>{{ "products.goods.addToCart" | translate }}</span>
                                </a>
                                {% endif %}
                                {% endif %}
                                <!--end Cart Button-->
                            </div>
                            <!-- End product details -->
                        </div>
                        {% endfor %}
                        {% else %}
                        <div class="col-12">
                            <p class="text-center">{{ "products.goods.no_products" | translate }}.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <!--End Product Grid-->
                <!--Pagination Classic-->
                <hr class="clear">
                <div class="pagination">
                    <ul>
                        {% if Model.CurrentPage > 1 %}
                        <li class="prev">
                            <a href="/collections{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ Model.CurrentPage | minus: 1 }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">
                                <i class="an an-lg an-angle-left" aria-hidden="true"></i>
                            </a>
                        </li>
                        {% else %}
                        <li class="prev disabled">
                            <a href="#">
                                <i class="an an-lg an-angle-left"
                                   aria-hidden="true"></i>
                            </a>
                        </li>
                        {% endif %}

                        {% assign startPage = Model.CurrentPage | minus: 2 %}
                        {% if startPage < 1 %}{% assign startPage = 1 %}{% endif %}

                        {% assign endPage = startPage | plus: 4 %}
                        {% if endPage > Model.TotalPages %}{% assign endPage = Model.TotalPages %}{% endif %}

                        {% if startPage > 1 %}
                        <li>
                            <a href="/collections{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page=1{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">1</a>
                        </li>
                        {% if startPage > 2 %}
                        <li class="disabled"><a href="#">...</a></li>{% endif %}
                        {% endif %}

                        {% for i in (startPage..endPage) %}
                        <li {% if i==Model.CurrentPage %}class="active" {% endif %}>
                            <a href="/collections{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ i }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">{{ i }}</a>
                        </li>
                        {% endfor %}

                        {% if endPage < Model.TotalPages %}
                        {% assign lastPageMinusOne = Model.TotalPages | minus: 1 %}
                        {% if endPage < lastPageMinusOne %}
                        <li class="disabled"><a href="#">...</a></li>{% endif %}
                        <li>
                            <a href="/collections{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ Model.TotalPages }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">{{ Model.TotalPages }}</a>
                        </li>
                        {% endif %}

                        {% if Model.CurrentPage < Model.TotalPages %}
                        <li class="next">
                            <a href="/collections{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ Model.CurrentPage | plus: 1 }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">
                                <i class="an an-lg an-angle-right" aria-hidden="true"></i>
                            </a>
                        </li>
                        {% else %}
                        <li class="next disabled">
                            <a href="#">
                                <i class="an an-lg an-angle-right"
                                   aria-hidden="true"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
                <!--End Pagination Classic-->
                <!--Category Bottom Description-->
                {% if Model.CategoryBottomDescription and Model.CategoryBottomDescription.Description_en_bottom and Model.CategoryBottomDescription.Description_en_bottom != "" %}
                <div class="category-description-bottom mt-4">
                    {{ Model.CategoryBottomDescription.Description_en_bottom | raw }}
                </div>
                {% endif %}
                <!--End Category Bottom Description-->
            </div>
        </div>
        <!--End Main Content-->
    </div>
    <!--End Product Tabs-->
    {% assign recommendproductslider = '/Themes/' | append: theme | append: '/Shop/RecommendProductSlider' %}
    {% include recommendproductslider, RecommendProducts: Model.RecommendProducts -%}
    <!--Product Enuiry Popup-->
</div>
<!-- Quickview Modal -->
<div class="modal fade" id="quickview_popup" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12 col-sm-6 col-md-6 col-lg-6">
                        <div id="slider">
                            <!-- model thumbnail -->
                            <div id="quickView" class="carousel slide">
                                <div class="quickview-in">
                                    <!-- image slide carousel items -->
                                    <div class="carousel-inner">
                                        <!-- slide 1 -->
                                        <div class="item carousel-item active" data-bs-slide-number="0">
                                            <img class="blur-up lazyload"
                                                 data-src="{{ static_path }}/assets/images/product-images/elt-p-15.jpg"
                                                 src="{{ static_path }}/assets/images/product-images/elt-p-15.jpg"
                                                 alt="product" title="">
                                        </div>
                                        <!-- End slide 1 -->
                                        <!-- slide 2 -->
                                        <div class="item carousel-item" data-bs-slide-number="1">
                                            <img class="blur-up lazyload"
                                                 data-src="{{ static_path }}/assets/images/product-images/elt-p-15-1.jpg"
                                                 src="{{ static_path }}/assets/images/product-images/elt-p-15-1.jpg"
                                                 alt="product" title="">
                                        </div>
                                        <!-- End slide 2 -->
                                        <!-- slide 3 -->
                                        <div class="item carousel-item" data-bs-slide-number="2">
                                            <img class="blur-up lazyload"
                                                 data-src="{{ static_path }}/assets/images/product-images/elt-p-9.jpg"
                                                 src="{{ static_path }}/assets/images/product-images/elt-p-9.jpg"
                                                 alt="product" title="">
                                        </div>
                                        <!-- End slide 3 -->
                                        <!-- slide 4 -->
                                        <div class="item carousel-item" data-bs-slide-number="3">
                                            <img class="blur-up lazyload"
                                                 data-src="{{ static_path }}/assets/images/product-images/elt-p-8.jpg"
                                                 src="{{ static_path }}/assets/images/product-images/elt-p-8.jpg"
                                                 alt="product" title="">
                                        </div>
                                        <!-- End slide 4 -->
                                        <!-- slide 5 -->
                                        <div class="item carousel-item" data-bs-slide-number="4">
                                            <img class="blur-up lazyload"
                                                 data-src="{{ static_path }}/assets/images/product-images/elt-p-14.jpg"
                                                 src="{{ static_path }}/assets/images/product-images/elt-p-14.jpg"
                                                 alt="product" title="">
                                        </div>
                                        <!-- End slide 5 -->
                                        <!-- slide 6 -->
                                        <div class="item carousel-item" data-bs-slide-number="5">
                                            <img class="blur-up lazyload"
                                                 data-src="{{ static_path }}/assets/images/product-images/elt-p-14-1.jpg"
                                                 src="{{ static_path }}/assets/images/product-images/elt-p-14-1.jpg"
                                                 alt="product" title="">
                                        </div>
                                        <!-- End slide 6 -->
                                    </div>
                                    <!-- End image slide carousel items -->
                                    <!-- arrow button -->
                                    <div class="np-btns">
                                        <a class="carousel-control left" href="#quickView" data-bs-target="#quickView"
                                           data-bs-slide="prev"><i class="an an-angle-left"></i></a>
                                        <a class="carousel-control right" href="#quickView" data-bs-target="#quickView"
                                           data-bs-slide="next"><i class="an an-angle-right"></i></a>
                                    </div>
                                    <!-- End arrow button -->
                                </div>
                                <!-- model thumbnail image -->
                                <div class="model-thumbnail-img">
                                    <!-- model thumbnail slide -->
                                    <ul class="carousel-indicators list-inline">
                                        <!-- slide 1 -->
                                        <li class="list-inline-item active">
                                            <a id="carousel-selector-0" class="selected" data-bs-slide-to="0"
                                               data-bs-target="#quickView">
                                                <img class="blur-up lazyload"
                                                     data-src="{{ static_path }}/assets/images/product-images/elt-p-15.jpg"
                                                     src="{{ static_path }}/assets/images/product-images/elt-p-15.jpg"
                                                     alt="product" title="">
                                            </a>
                                        </li>
                                        <!-- End slide 1 -->
                                        <!-- slide 2 -->
                                        <li class="list-inline-item">
                                            <a id="carousel-selector-1" data-bs-slide-to="1"
                                               data-bs-target="#quickView">
                                                <img class="blur-up lazyload"
                                                     data-src="{{ static_path }}/assets/images/product-images/elt-p-15-1.jpg"
                                                     src="{{ static_path }}/assets/images/product-images/elt-p-15-1.jpg"
                                                     alt="product" title="">
                                            </a>
                                        </li>
                                        <!-- End slide 2 -->
                                        <!-- slide 3 -->
                                        <li class="list-inline-item">
                                            <a id="carousel-selector-2" data-bs-slide-to="2"
                                               data-bs-target="#quickView">
                                                <img class="blur-up lazyload"
                                                     data-src="{{ static_path }}/assets/images/product-images/elt-p-9.jpg"
                                                     src="{{ static_path }}/assets/images/product-images/elt-p-9.jpg"
                                                     alt="product" title="">
                                            </a>
                                        </li>
                                        <!-- End slide 3 -->
                                        <!-- slide 4 -->
                                        <li class="list-inline-item">
                                            <a id="carousel-selector-3" data-bs-slide-to="3"
                                               data-bs-target="#quickView">
                                                <img class="blur-up lazyload"
                                                     data-src="{{ static_path }}/assets/images/product-images/elt-p-8.jpg"
                                                     src="{{ static_path }}/assets/images/product-images/elt-p-8.jpg"
                                                     alt="product" title="">
                                            </a>
                                        </li>
                                        <!-- End slide 4 -->
                                        <!-- slide 5 -->
                                        <li class="list-inline-item">
                                            <a id="carousel-selector-4" data-bs-slide-to="4"
                                               data-bs-target="#quickView">
                                                <img class="blur-up lazyload"
                                                     data-src="{{ static_path }}/assets/images/product-images/elt-p-14.jpg"
                                                     src="{{ static_path }}/assets/images/product-images/elt-p-14.jpg"
                                                     alt="product" title="">
                                            </a>
                                        </li>
                                        <!-- End slide 5 -->
                                        <!-- slide 6 -->
                                        <li class="list-inline-item">
                                            <a id="carousel-selector-5" data-bs-slide-to="5"
                                               data-bs-target="#quickView">
                                                <img class="blur-up lazyload"
                                                     data-src="{{ static_path }}/assets/images/product-images/elt-p-14-1.jpg"
                                                     src="{{ static_path }}/assets/images/product-images/elt-p-14-1.jpg"
                                                     alt="product" title="">
                                            </a>
                                        </li>
                                        <!-- End slide 6 -->
                                    </ul>
                                    <!-- End model thumbnail slide -->
                                </div>
                                <div class="product-thumb product-horizontal-thumb">
                                    <div class="product-thumb-style1">
                                        <a data-image="{{ static_path }}/assets/images/product-images/elt-p-14-1.jpg" data-zoom-image="{{ static_path }}/assets/images/product-images/elt-p-14-1.jpg"
                                           class="slick-slide slick-cloned" data-slick-index="0"
                                           aria-hidden="true" tabindex="-1">
                                            <img class="blur-up lazyload" data-src="{{ static_path }}/assets/images/product-images/elt-p-14-1.jpg"
                                                 src="{{ static_path }}/assets/images/product-images/elt-p-14-1.jpg" alt="product" />
                                        </a>
                                        <a data-image="{{ static_path }}/assets/images/product-images/elt-p-15.jpg" data-zoom-image="{{ static_path }}/assets/images/product-images/elt-p-15.jpg"
                                           class="slick-slide slick-cloned" data-slick-index="1"
                                           aria-hidden="true" tabindex="-1">
                                            <img class="blur-up lazyload" data-src="{{ static_path }}/assets/images/product-images/elt-p-15.jpg"
                                                 src="{{ static_path }}/assets/images/product-images/elt-p-15.jpg" alt="product" />
                                        </a>
                                        <a data-image="{{ static_path }}/assets/images/product-images/elt-p-8.jpg" data-zoom-image="{{ static_path }}/assets/images/product-images/elt-p-8.jpg"
                                           class="slick-slide slick-cloned" data-slick-index="2"
                                           aria-hidden="true" tabindex="-1">
                                            <img class="blur-up lazyload" data-src="{{ static_path }}/assets/images/product-images/elt-p-8.jpg"
                                                 src="{{ static_path }}/assets/images/product-images/elt-p-8.jpg" alt="product" />
                                        </a>
                                        <a data-image="{{ static_path }}/assets/images/product-images/elt-p-9.jpg" data-zoom-image="{{ static_path }}/assets/images/product-images/elt-p-9.jpg"
                                           class="slick-slide slick-cloned" data-slick-index="3"
                                           aria-hidden="true" tabindex="-1">
                                            <img class="blur-up lazyload" data-src="{{ static_path }}/assets/images/product-images/elt-p-9.jpg"
                                                 src="{{ static_path }}/assets/images/product-images/elt-p-9.jpg" alt="product" />
                                        </a>
                                        <a data-image="{{ static_path }}/assets/images/product-images/elt-p-14-1.jpg" data-zoom-image="{{ static_path }}/assets/images/product-images/elt-p-14-1.jpg"
                                           class="slick-slide slick-cloned" data-slick-index="4"
                                           aria-hidden="true" tabindex="-1">
                                            <img class="blur-up lazyload" data-src="{{ static_path }}/assets/images/product-images/elt-p-14-1.jpg"
                                                 src="{{ static_path }}/assets/images/product-images/elt-p-14-1.jpg" alt="product" />
                                        </a>
                                        <a data-image="{{ static_path }}/assets/images/product-images/elt-p-15.jpg" data-zoom-image="{{ static_path }}/assets/images/product-images/elt-p-15.jpg"
                                           class="slick-slide slick-cloned" data-slick-index="5"
                                           aria-hidden="true" tabindex="-1">
                                            <img class="blur-up lazyload" data-src="{{ static_path }}/assets/images/product-images/elt-p-15.jpg"
                                                 src="{{ static_path }}/assets/images/product-images/elt-p-15.jpg" alt="product" />
                                        </a>
                                        <a data-image="{{ static_path }}/assets/images/product-images/elt-p-8.jpg" data-zoom-image="{{ static_path }}/assets/images/product-images/elt-p-8.jpg"
                                           class="slick-slide slick-cloned" data-slick-index="6"
                                           aria-hidden="true" tabindex="-1">
                                            <img class="blur-up lazyload" data-src="{{ static_path }}/assets/images/product-images/elt-p-8.jpg"
                                                 src="{{ static_path }}/assets/images/product-images/elt-p-8.jpg" alt="product" />
                                        </a>
                                        <a data-image="{{ static_path }}/assets/images/product-images/elt-p-9.jpg" data-zoom-image="{{ static_path }}/assets/images/product-images/elt-p-9.jpg"
                                           class="slick-slide slick-cloned" data-slick-index="7"
                                           aria-hidden="true" tabindex="-1">
                                            <img class="blur-up lazyload" data-src="{{ static_path }}/assets/images/product-images/elt-p-9.jpg"
                                                 src="{{ static_path }}/assets/images/product-images/elt-p-9.jpg" alt="product" />
                                        </a>
                                    </div>
                                </div>
                                <!-- End model thumbnail image -->
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-6 col-lg-6">
                        <div class="product-brand"><a href="#">Charcoal</a></div>
                        <h2 class="product-title">Product Quick View Popup</h2>
                        <div class="product-review">
                            <div class="rating">
                                <!-- 使用动态评级 -->
                                {% assign rating = 4.5 %}
                                {% assign whole_stars = rating | floor %}
                                {% assign half_star = rating | minus: whole_stars %}
                                {% assign next_star = whole_stars | plus: 1 %}

                                {% for i in (1..5) %}
                                {% if i <= whole_stars %}
                                <i class="an an-star"></i>
                                {% elsif half_star >= 0.5 and i == next_star %}
                                <i class="an an-star-half-alt"></i>
                                {% else %}
                                <i class="an an-star gray-star"></i>
                                {% endif %}
                                {% endfor %}
                            </div>
                            <div class="reviews"><a href="#">5 {{ "products.goods.reviews" | translate }}</a></div>
                        </div>
                        <div class="product-info">
                            <div class="product-stock">
                                <span class="instock">In Stock</span> <span class="outstock hide">Unavailable</span>
                            </div>
                            <div class="product-sku">SKU: <span class="variant-sku">19115-rdxs</span></div>
                        </div>
                        <div class="pricebox">
                            <span class="price old-price">$900.00</span>
                            <span class="price">$800.00</span>
                        </div>
                        <div class="sort-description">
                            Shoplook Multipurpose Bootstrap 5 Html Template that will give you
                            and your customers a smooth shopping experience which can be used for various kinds of
                            stores such as fashion..
                        </div>
                        <form method="post" action="#" id="product_form--option" class="product-form">
                            <div class="product-options">
                                <div class="swatch clearfix swatch-0 option1">
                                    <div class="product-form__item">
                                        <label class="label">
                                            Color:<span class="required">*</span> <span class="slVariant">Red</span>
                                        </label>
                                        <div class="swatch-element color">
                                            <input class="swatchInput" id="swatch-black" type="radio" name="option-0"
                                                   value="Black">
                                            <label class="swatchLbl small black" for="swatch-black"
                                                   title="Black"></label>
                                        </div>
                                        <div class="swatch-element color">
                                            <input class="swatchInput" id="swatch-blue" type="radio" name="option-0"
                                                   value="blue">
                                            <label class="swatchLbl small blue" for="swatch-blue" title="Blue"></label>
                                        </div>
                                        <div class="swatch-element color">
                                            <input class="swatchInput" id="swatch-red" type="radio" name="option-0"
                                                   value="Blue">
                                            <label class="swatchLbl small red" for="swatch-red" title="Red"></label>
                                        </div>
                                        <div class="swatch-element color">
                                            <input class="swatchInput" id="swatch-pink" type="radio" name="option-0"
                                                   value="Pink">
                                            <label class="swatchLbl color small pink" for="swatch-pink"
                                                   title="Pink"></label>
                                        </div>
                                        <div class="swatch-element color">
                                            <input class="swatchInput" id="swatch-orange" type="radio" name="option-0"
                                                   value="Orange">
                                            <label class="swatchLbl color small orange" for="swatch-orange"
                                                   title="Orange"></label>
                                        </div>
                                        <div class="swatch-element color">
                                            <input class="swatchInput" id="swatch-yellow" type="radio" name="option-0"
                                                   value="Yellow">
                                            <label class="swatchLbl color small yellow" for="swatch-yellow"
                                                   title="Yellow"></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="swatch clearfix swatch-1 option2">
                                    <div class="product-form__item">
                                        <label class="label">
                                            Size:<span class="required">*</span> <span class="slVariant">XS</span>
                                        </label>
                                        <div class="swatch-element xs">
                                            <input class="swatchInput" id="swatch-1-xs" type="radio" name="option-1"
                                                   value="XS">
                                            <label class="swatchLbl medium" for="swatch-1-xs" title="XS">XS</label>
                                        </div>
                                        <div class="swatch-element s">
                                            <input class="swatchInput" id="swatch-1-s1" type="radio" name="option-1"
                                                   value="S">
                                            <label class="swatchLbl medium" for="swatch-1-s1" title="S">S</label>
                                        </div>
                                        <div class="swatch-element m">
                                            <input class="swatchInput" id="swatch-1-m" type="radio" name="option-1"
                                                   value="M">
                                            <label class="swatchLbl medium" for="swatch-1-m" title="M">M</label>
                                        </div>
                                        <div class="swatch-element l">
                                            <input class="swatchInput" id="swatch-1-l" type="radio" name="option-1"
                                                   value="L">
                                            <label class="swatchLbl medium" for="swatch-1-l" title="L">L</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="product-action clearfix">
                                    <div class="quantity">
                                        <div class="wrapQtyBtn">
                                            <div class="qtyField">
                                                <a class="qtyBtn minus" href="javascript:void(0);">
                                                    <i class="an an-minus" aria-hidden="true"></i>
                                                </a>
                                                <input type="text" id="quantityp" name="quantity" value="1"
                                                       class="product-form__input qty">
                                                <a class="qtyBtn plus" href="javascript:void(0);">
                                                    <i class="an an-plus"
                                                       aria-hidden="true"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="add-to-cart">
                                        <button type="button" class="btn button-cart theme-btn">
                                            <span>{{ "products.goods.addToCart" | translate }}</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="wishlist-btn">
                            <a class="wishlist add-to-wishlist" href="#"
                               title="{{ "products.goods.addToFavorites" | translate }}">
                                <i class="icon an an-heart-o" aria-hidden="true"></i>
                                <span>{{ "products.goods.addToFavorites" | translate }}</span>
                            </a>
                        </div>
                        <div class="share-icon">
                            <span>Share:</span>
                            <ul class="list--inline social-icons">
                                <li><a href="#"><i class="icon an an-facebook-f"></i></a></li>
                                <li><a href="#"><i class="icon an an-twitter"></i></a></li>
                                <li><a href="#"><i class="icon an an-pinterest-p"></i></a></li>
                                <li><a href="#"><i class="icon an an-instagram"></i></a></li>
                                <li><a href="#"><i class="icon an an-youtube"></i></a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--End Quickview Modal-->
<!-- Start Addtocart Added Popup -->
<div class="modal fade" id="pro-addtocart-popup" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-12">
                        <div class="addtocart-inner text-center clearfix">
                            <h4 class="title">{{ "user.orders.buy_again_success" | translate }}</h4>
                            <div class="pro-img mb-2">
                                <img class="img-fluid blur-up lazyload" src="/assets/images/cart/loading.gif"
                                     data-src="/assets/images/cart/loading.gif"
                                     alt="" title="{{ "user.orders.buy_again_success" | translate }}" />
                            </div>
                            <div class="pro-details">
                                <p class="pro-name mb-1"></p>
                                <p class="sku mb-0"></p>
                                <p class="mb-0 qty-total"></p>

                                <div class="button-action">
                                    <a href="/cart"
                                       class="btn btn-primary view-cart mx-1 theme-btn">{{ "cart.global.viewCartCheckout" | translate }}</a>
                                    <a href="/collections"
                                       class="btn btn-secondary theme-btn">{{ "user.forgot.continueShopping" | translate }}</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End Addtocart Added Popup -->

<style>
    /* 当前选中分类的高亮样式 */
    .sidebar_categories a.active-category {
        color: var(--theme-color, #2d68a8) !important;
        font-weight: bold;
    }

        .sidebar_categories a.active-category:before {
            content: "▶ ";
            color: var(--theme-color, #2d68a8);
            font-size: 12px;
            margin-right: 5px;
        }
</style>

<script>
    function changeSorting() {
        var sortBy = document.getElementById('SortBy').value;
        var currentUrl = new URL(window.location.href);

        // 映射前端选项到后端支持的排序参数
        var sortMap = {
            "title-ascending": "title-ascending",
            "bestseller": "bestseller",
            "alpha-asc": "alpha-asc",
            "alpha-desc": "alpha-desc",
            "priceasc": "priceasc",
            "pricedesc": "pricedesc",
            "date-newest": "date-newest",
            "date-oldest": "date-oldest"
        };

        // 获取对应的后端排序值
        var mappedSort = sortMap[sortBy];

        // 设置排序参数
        if (mappedSort) {
            currentUrl.searchParams.set('sortBy', mappedSort);
        } else {
            currentUrl.searchParams.delete('sortBy');
        }

        // 保持当前页面
        if (!currentUrl.searchParams.has('page')) {
            currentUrl.searchParams.set('page', '1');
        }

        // 跳转到新URL
        window.location.href = currentUrl.toString();
    }

    // 添加分类展开/折叠功能
    document.addEventListener('DOMContentLoaded', function () {
        var categoryToggles = document.querySelectorAll('.category-toggle');

        // 获取当前页面的分类URL
        var currentPath = window.location.pathname;
        var currentCategoryUrl = '';

        // 从URL中提取分类URL
        var pathMatch = currentPath.match(/\/collections\/(.+)/);
        if (pathMatch) {
            currentCategoryUrl = pathMatch[1];
        }

        // 初始化菜单状态：展开包含当前分类的父菜单
        function initializeMenuState() {
            var allCategoryLinks = document.querySelectorAll('.sidebar_categories a[href*="/collections/"]');

            allCategoryLinks.forEach(function (link) {
                var linkHref = link.getAttribute('href');
                var linkMatch = linkHref.match(/\/collections\/(.+)/);

                if (linkMatch && linkMatch[1] === currentCategoryUrl) {
                    // 找到当前分类，高亮显示
                    link.classList.add('active-category');

                    // 如果是子分类，展开父分类菜单
                    var parentLi = link.closest('li.level2');
                    if (parentLi) {
                        var parentSublinks = parentLi.closest('.sublinks');
                        if (parentSublinks) {
                            parentSublinks.style.display = 'block';

                            // 同时显示"查看全部"链接
                            var parentLevel1 = parentSublinks.closest('li.level1');
                            if (parentLevel1) {
                                var viewAllLink = parentLevel1.querySelector('.view-all-link');
                                if (viewAllLink) {
                                    viewAllLink.style.display = 'inline';
                                }
                            }
                        }
                    }
                }
            });
        }

        // 处理分类展开/折叠点击事件
        categoryToggles.forEach(function (toggle) {
            toggle.addEventListener('click', function (e) {
                e.preventDefault();

                // 获取父级li元素
                var parentLi = this.closest('li');

                // 获取子分类列表
                var sublinks = parentLi.querySelector('.sublinks');
                var viewAllLink = parentLi.querySelector('.view-all-link');

                // 切换显示/隐藏状态
                if (sublinks) {
                    if (sublinks.style.display === 'none' || sublinks.style.display === '') {
                        sublinks.style.display = 'block';
                        if (viewAllLink) {
                            viewAllLink.style.display = 'inline';
                        }
                    } else {
                        sublinks.style.display = 'none';
                        if (viewAllLink) {
                            viewAllLink.style.display = 'none';
                        }
                    }
                }
            });
        });

        // 初始化菜单状态
        initializeMenuState();

        // 禁用主题默认的"加载更多"功能
        if (window.jQuery) {
            jQuery(document).ready(function ($) {
                // 覆盖主题的loadMore函数
                $(".loadMore").off("click");

                // 确保所有产品项都显示
                $(".product-grid-view .item").show();
            });
        }
    });

    // 设置静态资源路径全局变量，供quickView_t200.js使用
    window.staticPath = '{{ static_path }}';
</script>

<!-- 导入图片处理助手 -->
<script src="/businessJs/imageUrlHelper.js"></script>
<!-- 导入弹窗组件 -->
<script src="/js/Pop-ups/frame-message.js"></script>
<!-- 导入快速预览脚本 -->
<script src="/businessJs/Product/Index/quickView_t200.js"></script>
<!-- 导入价格工具类 -->
<script src="/businessJs/Common/priceUtils.js"></script>
<!-- 导入产品列表价格显示模块 -->
<script src="/businessJs/Product/Index/priceDisplay_Index_t200.js"></script>
<script src="/assets/js/SaleSticker.js"></script>
<script>
    // 页面加载完成后处理所有图片URL
    document.addEventListener('DOMContentLoaded', function () {
        // 处理产品列表中的所有图片
        const productImages = document.querySelectorAll('.product-image img');
        productImages.forEach(function (img) {
            if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {
                img.src = ImageUrlHelper.getMediumUrl(img.src);
            }
            if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {
                img.dataset.src = ImageUrlHelper.getMediumUrl(img.dataset.src);
            }
        });

        // 处理快速预览模态框中的图片
        const quickViewImages = document.querySelectorAll('#quickview_popup img');
        quickViewImages.forEach(function (img) {
            if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {
                img.src = ImageUrlHelper.getMediumUrl(img.src);
            }
            if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {
                img.dataset.src = ImageUrlHelper.getMediumUrl(img.dataset.src);
            }
        });

        // 处理添加到购物车弹窗中的图片
        const cartPopupImages = document.querySelectorAll('#pro-addtocart-popup img');
        cartPopupImages.forEach(function (img) {
            if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {
                img.src = ImageUrlHelper.getMediumUrl(img.src);
            }
            if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {
                img.dataset.src = ImageUrlHelper.getMediumUrl(img.dataset.src);
            }
        });

        // 处理购物车按钮点击事件，阻止默认弹窗，改为打开快速预览
        document.addEventListener('click', function (e) {
            const addToCartBtn = e.target.closest('.btn-addto-cart');
            if (addToCartBtn) {
                e.preventDefault();
                e.stopPropagation();

                // 获取产品ID
                const productId = addToCartBtn.getAttribute('data-product-id');
                if (!productId) {
                    console.error('Product ID not found');
                    return;
                }

                // 查找对应的隐藏的快速查看按钮并触发点击
                const quickViewBtn = addToCartBtn.closest('.item').querySelector('.quick-view');
                if (quickViewBtn) {
                    quickViewBtn.click();
                } else {
                    console.error('Quick view button not found');
                }
            }

            // 处理收藏按钮点击事件
            const wishlistBtn = e.target.closest('.wishlist-btn .add-to-wishlist');
            if (wishlistBtn) {
                e.preventDefault();
                e.stopPropagation();

                // 直接从wishlist按钮获取产品ID和收藏状态
                const productId = wishlistBtn.getAttribute('data-product-id');
                const isFavorited = wishlistBtn.getAttribute('data-is-favorited') === 'true';

                if (!productId) {
                    if (typeof customize_pop !== 'undefined' && customize_pop.warning) {
                        customize_pop.warning('Unable to get product ID', null, null, { showIcon: false });
                    } else {
                        alert('Unable to get product ID');
                    }
                    return;
                }

                // 根据当前收藏状态决定操作
                const url = isFavorited ? '/Account/RemoveFromWishlistByProductId' : '/Account/AddToWishlist';
                const requestBody = { productId: parseInt(productId) };

                // 发送收藏请求
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 根据操作类型显示不同的成功消息
                            const message = isFavorited ? 'Removed from wishlist successfully' : 'Added to wishlist successfully';
                            if (typeof customize_pop !== 'undefined' && customize_pop.success) {
                                customize_pop.success(message, null, null, { showIcon: false });
                            } else {
                                alert(message);
                            }

                            // 更新按钮状态
                            const heartIcon = wishlistBtn.querySelector('i');
                            if (heartIcon) {
                                if (isFavorited) {
                                    heartIcon.className = 'icon an an-heart-o'; // 改为空心心形
                                    wishlistBtn.setAttribute('data-is-favorited', 'false');
                                } else {
                                    heartIcon.className = 'icon an an-heart'; // 改为实心心形
                                    wishlistBtn.setAttribute('data-is-favorited', 'true');
                                }
                            }
                        } else {
                            if (typeof customize_pop !== 'undefined' && customize_pop.warning) {
                                customize_pop.warning(data.message || 'Operation failed', null, null, { showIcon: false });
                            } else {
                                alert(data.message || 'Operation failed');
                            }
                        }
                    })
                    .catch(error => {
                        console.error('收藏操作时出错:', error);
                        if (typeof customize_pop !== 'undefined' && customize_pop.error) {
                            customize_pop.error('Network error, please try again later', null, null, { showIcon: false });
                        } else {
                            alert('Network error, please try again later');
                        }
                    });
            }
        });
    });
</script>