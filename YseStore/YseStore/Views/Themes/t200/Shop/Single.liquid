{% section meta_keywords -%}
   {% if Model.Product.ProductSeo and Model.Product.ProductSeo.SeoKeyword_en and Model.Product.ProductSeo.SeoKeyword_en != "" %}
   <meta name="keywords" content="{{ Model.Product.ProductSeo.SeoKeyword_en }}">
   {% elsif Model.Product.Tags and Model.Product.Tags.size > 0 %}
   <meta name="keywords" content="{% for tag in Model.Product.Tags %}{{ tag.Name }}{% unless forloop.last %}, {% endunless %}{% endfor %}, {{ Model.Product.ProductName }}">
   {% else %}
   <meta name="keywords" content="{{ Model.Product.ProductName }}, {{ Model.Product.BrandName }}">
   {% endif %}
  {% endsection -%}
  {% section meta_description -%}
   {% if Model.Product.ProductSeo and Model.Product.ProductSeo.SeoDescription_en and Model.Product.ProductSeo.SeoDescription_en != "" %}
   <meta name="description" content="{{ Model.Product.ProductSeo.SeoDescription_en }}">
   {% elsif Model.Product.BriefDescription and Model.Product.BriefDescription != "" %}
   <meta name="description" content="{{ Model.Product.BriefDescription }}">
   {% else %}
   {% endif %}
  {% endsection -%}
  {% section title -%}
   {% if Model.Product.ProductSeo and Model.Product.ProductSeo.SeoTitle_en and Model.Product.ProductSeo.SeoTitle_en != "" %}
   <title>{{ Model.Product.ProductSeo.SeoTitle_en }}</title>
   {% else %}
   <title>{{ Model.Product.ProductName }}</title>
   {% endif %}
  {% endsection -%}

<div class="container template-product pt-5">

    <div class="product-detail-container product-single-style1">
        <div class="product-single">
            <div class="row">
                <div class="col-lg-6 col-md-6 col-sm-12 col-12">
                    <div class="product-details-img product-horizontal-style">
                        <div class="zoompro-wrap product-zoom-right pl-20" style="height: max-content;">
                            <div class="zoompro-span">
                                {% comment %} 优先使用PicPath，如果为空则使用ProductImages集合中的第一张图片，最后使用默认图片 {% endcomment %}
                                {% assign mainImageSrc = Model.Product.PicPath %}
                                {% if mainImageSrc == null or mainImageSrc == "" %}
                                    {% if Model.Product.ProductImages != null and Model.Product.ProductImages.size > 0 %}
                                        {% assign mainImageSrc = Model.Product.ProductImages[0].PicPath %}
                                    {% else %}
                                        {% assign mainImageSrc = static_path | append: "/assets/images/product-images/default-product.jpg" %}
                                    {% endif %}
                                {% endif %}

                                <img id="zoompro" class="zoompro prlightbox" src="{{ mainImageSrc }}"
                                     data-zoom-image="{{ mainImageSrc }}"
                                     alt="{{ Model.Product.ProductName }}"/>
                            </div>
                            <div class="product-labels">
                                {% if Model.Product.IsOnSale %}<span
                                        class="lbl on-sale">{{ "products.lists.sale" | translate }}</span>{% endif %}
                                {% if Model.Product.IsNew %}<span
                                        class="lbl pr-label1">{{ "products.goods.new" | translate }}</span>{% endif %}
                            </div>
                            <!--背景图-->
                            <!--<div class="product-labels product-labels-img" style="background-image:url(https://www.retekess.com/Assets/files/20250415/1616562925/easter-com-compressed.png)"></div>-->   
                            <div class="product-buttons">
                                <a href="#" class="btn prlightbox theme-btn" title="Zoom">
                                    <i class="icon an an-expand-arrows-alt" aria-hidden="true"></i>
                                    <span class="tooltip-label">Zoom Image</span>
                                </a>
                            </div>
                        </div>
                        <div class="product-thumb product-horizontal-thumb">
                            <div id="gallery" class="product-thumb-style1">
                                {% if Model.Product.ProductImages != null and Model.Product.ProductImages.size > 0 %}
                                    {% for pic in Model.Product.ProductImages %}
                                        <a data-image="{{ pic.PicPath }}" data-zoom-image="{{ pic.PicPath }}"
                                           class="slick-slide slick-cloned" data-slick-index="{{ forloop.index }}"
                                           aria-hidden="true" tabindex="-1">
                                            <img class="blur-up lazyload" data-src="{{ pic.PicPath }}"
                                                 src="{{ pic.PicPath }}" alt="{{ Model.Product.ProductName }}"/>
                                        </a>
                                    {% endfor %}
                                {% else %}
                                    {% comment %} 当没有ProductImages时，使用与主图相同的逻辑 {% endcomment %}
                                    <a data-image="{{ mainImageSrc }}"
                                       data-zoom-image="{{ mainImageSrc }}"
                                       class="slick-slide slick-cloned" data-slick-index="0" aria-hidden="true"
                                       tabindex="-1">
                                        <img class="blur-up lazyload" data-src="{{ mainImageSrc }}"
                                             src="{{ mainImageSrc }}" alt="{{ Model.Product.ProductName }}"/>
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                        <div class="lightboximages">
                            {% if Model.Product.ProductImages != null and Model.Product.ProductImages.size > 0 %}
                                {% for pic in Model.Product.ProductImages %}
                                    <a href="{{ pic.PicPath }}" data-size="1000x1280"></a>
                                {% endfor %}
                            {% else %}
                                {% comment %} 当没有ProductImages时，使用与主图相同的逻辑 {% endcomment %}
                                <a href="{{ mainImageSrc }}" data-size="1000x1280"></a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-12">
                    <div class="product-single__meta">
                        <h1 class="product-single__title">{{ Model.Product.ProductName }}</h1>
                        <div class="prInfoRow d-flex flex-column">
                            <div class="product-review mx-0 mb-2">
                                <a class="reviewLink" href="#tabs-listing">
                                    {% for i in (1..5) %}
                                        {% if i <= Model.Product.Rating %}
                                            <i class="an an-star"></i>
                                        {% else %}
                                            <i class="an an-star gray-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                    <span class="review-label"> {{ Model.Product.ReviewCount }} {{ "products.goods.reviews" | translate }}</span>
                                </a>
                            </div>
                            <div class="product-sku mx-0 mb-1">
                                <b>{{ "products.goods.sku" | translate }}:</b> <span
                                        class="variant-sku">{{ Model.Product.Sku }}</span>
                            </div>
                            <div class="product-stock mx-0 mb-1">
                                <b>{{ "user.account.order_status" | translate }}:</b>
                                {% if Model.Product.IsInStock %}
                                    <span class="instock">{{ "products.goods.inStock" | translate }}</span> <span
                                        class="outstock hide">Unavailable</span>
                                {% else %}
                                    <span class="instock hide">{{ "products.goods.outStock" | translate }}</span> <span
                                        class="outstock">Unavailable</span>
                                {% endif %}
                            </div>
                            <div class="brands mx-0"><b>Brand:</b> <span>{{ Model.Product.BrandName }}</span></div>
                        </div>
                        <p class="product-single__price product-single__price-product-template">
                            <!-- 使用JavaScript进行价格比较 -->
                            <span id="price-comparison-container"
                                  data-original-price="{{ Model.Product.OriginalPriceFormat }}"
                                  data-current-price="{{ Model.Product.PriceFormat }}"
                                  data-promotion-price="{{ Model.Product.PromotionPriceFormat }}">
                                <!-- 价格内容将通过JavaScript动态生成 -->
                            </span>
                        </p>
                        {% comment %}<div>
                            {% endcomment %}
                        {% comment %}<span>Promotional Price ：</span><span{% endcomment %}
                        {% comment %}style="color: var(--bs-red);">
{{ Model.Product.PromotionPriceFormat }}</span>{% endcomment %}
                        {% comment %}
                </div>{% endcomment %}
                    </div>
                    <hr>
                    <!--Sort Description-->
                    <div class="product-single__description rte">
                        <div class="row">
                            <div class="col-12 col-sm-12 col-md-12 col-lg-12 mb-2">
                                <p>{{ Model.Product.BriefDescription | raw }}</p>
                            </div>
                            {% if Model.Product.Features != null and Model.Product.Features.size > 0 %}
                                <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                                    <ul class="list-items">
                                        {% for feature in Model.Product.Features limit: 3 %}
                                            <li>{{ feature }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% if Model.Product.Features.size > 3 %}
                                    <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                                        <ul class="list-items">
                                            {% for feature in Model.Product.Features offset: 3 limit: 3 %}
                                                <li>{{ feature }}</li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                    <!--End Sort Description-->
                    <hr>
                    <form method="post" action="#" class="product-form product-form-product-template">
                        {% comment %} 动态渲染所有属性，保持原有的 t200 主题样式结构 {% endcomment %}
                        {% if Model.Product.DynamicAttributes != null and Model.Product.DynamicAttributes.size > 0 %}
                            {% assign optionIndex = 1 %}
                            {% for attribute in Model.Product.DynamicAttributes %}
                                {% assign attributeName = attribute[0] %}
                                {% assign attributeOptions = attribute[1] %}

                                {% if attributeOptions != null and attributeOptions.size > 0 %}
                                    {% comment %} 检查是否有颜色代码，决定渲染方式 {% endcomment %}
                                    {% assign hasColorCode = false %}
                                    {% for option in attributeOptions %}
                                        {% if option.ColorCode != null and option.ColorCode != "" %}
                                            {% assign hasColorCode = true %}
                                            {% break %}
                                        {% endif %}
                                    {% endfor %}

                                    <div class="swatch clearfix swatch-{{ optionIndex }} option{{ optionIndex }}"
                                         data-option-index="{{ optionIndex }}">
                                        <div class="product-form__item">
                                            <label class="label">
                                                {{ attributeName }}:<span class="required">*</span>
                                                <span class="slVariant">{{ attributeOptions[0].Name }}</span>
                                                {% if attributeName contains "Size" or attributeName contains "size" %}
                                                    <a href="#sizechart" title="Size Chart" class="sizelink"
                                                       data-bs-toggle="modal" data-bs-target="#sizechart">
                                                        <i class="an an-lg an-ruler"></i> Size Guide
                                                    </a>
                                                {% endif %}
                                            </label>

                                            {% if hasColorCode %}
                                                {% comment %} 渲染颜色样式的选项 {% endcomment %}
                                                {% for option in attributeOptions %}
                                                    <div data-value="{{ option.Name }}"
                                                         class="swatch-element color {{ option.Name | downcase }} available">
                                                        <input class="swatchInput"
                                                               id="swatch-{{ optionIndex }}-{{ option.Name | downcase }}{{ forloop.index }}"
                                                               type="radio" name="option-{{ optionIndex }}"
                                                               value="{{ option.Name }}"
                                                               data-attribute="{{ attributeName }}"
                                                               data-option-id="{{ option.Id }}"
                                                               {% if forloop.first %}checked{% endif %}>
                                                        <label class="swatchLbl color"
                                                               for="swatch-{{ optionIndex }}-{{ option.Name | downcase }}{{ forloop.index }}"
                                                               style="background-color: {{ option.ColorCode }};"
                                                               title="{{ option.Name }}">
                                                            <span class="swatchLbl-text">{{ option.Name }}</span>
                                                        </label>
                                                        <span class="tooltip-label">{{ option.Name }}</span>
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                {% comment %} 渲染普通文本样式的选项 {% endcomment %}
                                                {% for option in attributeOptions %}
                                                    <div data-value="{{ option.Name }}"
                                                         class="swatch-element {{ option.Name | downcase }} available">
                                                        <input class="swatchInput"
                                                               id="swatch-{{ optionIndex }}-{{ option.Name | downcase }}{{ forloop.index }}"
                                                               type="radio" name="option-{{ optionIndex }}"
                                                               value="{{ option.Name }}"
                                                               data-attribute="{{ attributeName }}"
                                                               data-option-id="{{ option.Id }}"
                                                               {% if forloop.first %}checked{% endif %}>
                                                        <label class="swatchLbl medium"
                                                               for="swatch-{{ optionIndex }}-{{ option.Name | downcase }}{{ forloop.index }}">
                                                            {{ option.Name }}
                                                        </label>
                                                        <span class="tooltip-label">{{ option.Name }}</span>
                                                    </div>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% assign optionIndex = optionIndex | plus: 1 %}
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                        <!-- Product Action -->
                        <div class="product-action clearfix">
                            <!-- 调试用的隐藏字段 -->
                            <input type="hidden" id="selectedVariantsCombination" name="selectedVariantsCombination"
                                   value="">
                            <input type="hidden" id="matchedVariantsId" name="matchedVariantsId" value="">

                            <!-- 调试显示区域 -->
                            {% comment %}<div class="debug-info"{% endcomment %}
                            {% comment %}style="background: #f8f9fa; padding: 10px; margin-bottom: 15px; border: 1px solid #dee2e6; border-radius: 4px;">{% endcomment %}
                            {% comment %}<h6 style="margin: 0 0 10px 0; color: #495057;">调试信息：</h6>{% endcomment %}
                            {% comment %}<div>{% endcomment %}
                            {% comment %}<strong>匹配模式：</strong> <span{% endcomment %}
                            {% comment %}id="debugMode">{% if Model.Product.IsCombination == 0 %}单规格{% elsif Model.Product.IsCombination == 1 %}多规格(组合匹配){% elsif Model.Product.IsCombination == 2 %}多规格加价(单独匹配){% else %}未知({{ Model.Product.IsCombination }}){% endif %}</span>{% endcomment %}
                            {% comment %}</div>{% endcomment %}
                            {% comment %}<div><strong>选中的变体组合：</strong> <span id="debugCombination">未选择</span></div>{% endcomment %}
                            {% comment %}<div><strong>选中的仓库：</strong> <span id="debugWarehouse">未选择</span></div>{% endcomment %}
                            {% comment %}<div><strong>匹配的VariantsId：</strong> <span id="debugVariantsId">未匹配</span></div>{% endcomment %}
                            {% comment %}{% endcomment %}
                            {% comment %}</div>{% endcomment %}

                            {% comment %} 仓库选择区域 {% endcomment %}
                            {% if Model.Product.OptionalWarehouses != null and Model.Product.OptionalWarehouses.size > 0 %}
                                <div class="swatch clearfix swatch-warehouse" data-option-index="warehouse">
                                    <div class="product-form__item">
                                        <label class="label">
                                            仓库选择:<span class="required">*</span>
                                            <span class="slVariant">{{ Model.Product.OptionalWarehouses[0].Name }}</span>
                                        </label>

                                        {% if Model.Product.OptionalWarehouses.size > 0 %}
                                            {% comment %} 多个仓库时显示选择器 {% endcomment %}
                                            {% for warehouse in Model.Product.OptionalWarehouses %}
                                                <div data-value="{{ warehouse.Name }}"
                                                     class="swatch-element warehouse {{ warehouse.Name | downcase }} available">
                                                    <input class="swatchInput warehouseInput"
                                                           id="warehouse-{{ warehouse.OvId }}"
                                                           type="radio"
                                                           name="warehouse-option"
                                                           value="{{ warehouse.OvId }}"
                                                           data-warehouse-name="{{ warehouse.Name }}"
                                                           {% if forloop.first %}checked{% endif %}>
                                                    <label class="swatchLbl medium"
                                                           for="warehouse-{{ warehouse.OvId }}">
                                                        {{ warehouse.Name }}
                                                    </label>
                                                    <span class="tooltip-label">{{ warehouse.Name }}</span>
                                                </div>
                                            {% endfor %}
                                        {% else %}
                                            {% comment %} 只有一个仓库时自动选中 {% endcomment %}
                                            <input type="hidden"
                                                   id="warehouse-{{ Model.Product.OptionalWarehouses[0].OvId }}"
                                                   name="warehouse-option"
                                                   value="{{ Model.Product.OptionalWarehouses[0].OvId }}"
                                                   data-warehouse-name="{{ Model.Product.OptionalWarehouses[0].Name }}"
                                                   checked>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}

                            <div class="product-form__item--quantity">
                                <div class="wrapQtyBtn">
                                    <div class="qtyField">
                                        <a class="qtyBtn minus" href="javascript:void(0);">
                                            <i class="an an-minus"
                                               aria-hidden="true"></i>
                                        </a>
                                        <input type="text" id="quantityp1" name="quantity" value="1"
                                               class="product-form__input qty">
                                        <a class="qtyBtn plus" href="javascript:void(0);">
                                            <i class="an an-plus"
                                               aria-hidden="true"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="product-form__item--submit">
                                <button type="button" name="add" class="btn product-form__cart-submit theme-btn-border"
                                        hx-trigger="submit throttle:2s"
                                        hx-on:click="addToCart()"
                              
                                        {% if Model.Product.IsInStock==false %}disabled="disabled" {% endif %}>
                                    <i class="icon an an-lg an-shopping-cart"></i>
                                    <span>{{ "products.goods.addToCart" | translate }}</span>
                        
                                </button>
                            </div>
                            <div class="buy-it-btn mt-2">
                                <button type="button" class="btn"
                                        hx-on:click="buyItNow()"
                                        hx-trigger="submit throttle:2s"
                                        {% if Model.Product.IsInStock==false %}disabled="disabled" {% endif %}>
                                    {{ "products.goods.buyNow" | translate }}
                                 
                                </button>
                            </div>
                        </div>
                        <!-- End Product Action -->
                    </form>

                    <p class="infolinks d-flex">
                        <a class="wishlist add-to-wishlist" href="javascript:void(0);"
                           data-product-id="{{ Model.Product.ProductId }}"
                           title="{{ "products.goods.addToFavorites" | translate }}">
                            <i class="icon an an-lg an-heart-o" aria-hidden="true"></i>
                            <span>{{ "products.goods.addToFavorites" | translate }}</span>
                        </a>
                        <a href="#ShippingInfo" title="Delivery &amp; Returns" class="shippingInfo btn theme-btn"
                           data-bs-toggle="modal" data-bs-target="#ShippingInfo">
                            <i class="icon an an-lg an-truck"></i>
                            Delivery &amp; Returns
                        </a>
                        <a href="#productInquiry" title="Product Inquiry" class="emaillink btn theme-btn"
                           data-bs-toggle="modal"
                           data-bs-target="#productInquiry">
                            <i class="icon an an-lg an-envelope"></i> {{ "products.goods.inquiry_title" | translate }}
                        </a>
                    </p>


                    <div class="userViewMsg" data-user="20" data-time="11000">
                        <i class="an an-lg an-user-friends" aria-hidden="true"></i> <strong class="uersView">14</strong>
                        People are Looking for this Product
                    </div>
                    <div class="trustseal-img">
                        <img src="{{ static_path }}/assets/images/checkout-cards.png" alt="">
                    </div>
                    <div class="social-sharing">
                        <span class="label"><b>{{ "user.account.DIST_how_to_share" | translate }}:</b></span>
                        <a href="#" class="btn btn--small btn--secondary btn--share share-facebook"
                           title="Share on Facebook">
                            <i class="icon an an-facebook-f"></i>
                        </a>
                        <a href="#" class="btn btn--small btn--secondary btn--share share-twitter"
                           title="Tweet on Twitter">
                            <i class="icon an an-twitter"></i>
                        </a>
                        <a href="#" title="Share on google+" class="btn btn--small btn--secondary btn--share">
                            <i class="an an-google-plus" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="btn btn--small btn--secondary btn--share share-pinterest"
                           title="Pin on Pinterest">
                            <i class="icon an an-pinterest-p"></i>
                        </a>
                        <a href="#" class="btn btn--small btn--secondary btn--share" title="WhatsApp">
                            <i class="icon an an-whatsapp"></i>
                        </a>
                        <a href="#" class="btn btn--small btn--secondary btn--share" title="RSS">
                            <i class="icon an an-rss"></i>
                        </a>
                        <a href="#" class="btn btn--small btn--secondary btn--share" title="Share by Email">
                            <i class="an an-envelope" aria-hidden="true"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--Product Tabs-->
    <div id="tabs-listing" class="tabs-listing tabs-listing-style2">
        <ul class="product-tabs text-left">
            <!-- 从 Model.ProductSwitches 中动态生成所有面板标签 -->
            {% if Model.ProductSwitches != null and Model.ProductSwitches.size > 0 %}
                {% for switch in Model.ProductSwitches %}
                    <li data-id="{{ switch.DataId }}" rel="tab-{{ switch.SId }}"
                        {% if forloop.first %}class="active" {% endif %}>
                        <a class="tablink">{{ switch.Name }}</a>
                    </li>
                {% endfor %}
            {% else %}
                <!-- 默认标签，当没有面板数据时显示 -->
                <li rel="tab1" class="active"><a class="tablink">{{ "web.global.index_view" | translate }}</a></li>
                <li rel="tab2" data-id="reviews"><a class="tablink">{{ "products.goods.reviews" | translate }} (0)</a>
                </li>
            {% endif %}
        </ul>
        <div class="tab-container">
            <!-- 动态生成面板内容，对 Description 和 Reviews 特殊处理 -->
            {% if Model.ProductSwitches != null and Model.ProductSwitches.size > 0 %}
                {% for switch in Model.ProductSwitches %}
                    <h3 class="tabs-ac-style {% if forloop.first %}active{% endif %}"
                        rel="tab-{{ switch.SId }}">
                        {{ switch.Name }}
                    </h3>
                    <div id="tab-{{ switch.SId }}" class="tab-content">
                        <!-- 如果是 Description 面板，显示原有的产品描述内容 -->
                        {% if switch.Identity == "Description" %}
                            <div class="product-description rte">
                                <div class="row">
                                    <div class="col-12 col-sm-12 col-md-12 col-lg-12">
                                        {{ Model.Product.Description | raw }}

                                        {% if Model.Product.Features != null and Model.Product.Features.size > 0 %}
                                            <h3>Features</h3>
                                            <ul class="list-items">
                                                {% for feature in Model.Product.Features %}
                                                    <li>{{ feature }}</li>
                                                {% endfor %}
                                            </ul>
                                        {% endif %}
                                        {% comment %}{% endcomment %}
                                        {% comment %}{% if Model.Product.Specifications != null and Model.Product.Specifications.size > 0 %}{% endcomment %}
                                        {% comment %}<h3>Specifications</h3>{% endcomment %}
                                        {% comment %}<ul class="list-items">{% endcomment %}
                                        {% comment %}{% for spec in Model.Product.Specifications %}{% endcomment %}
                                        {% comment %}<li><strong>{{ spec.Name }}:</strong> {{ spec.Value }}</li>{% endcomment %}
                                        {% comment %}{% endfor %}{% endcomment %}
                                        {% comment %}</ul>{% endcomment %}
                                        {% comment %}{% endif %}{% endcomment %}
                                    </div>
                                </div>
                            </div>

                            <!-- 如果是 Reviews 面板，显示原有的评论内容 -->
                        {% elsif switch.Identity == "Reviews" %}
                            <div class="row">
                                <div class="col-12 col-sm-12 col-md-6 col-lg-6 mb-4">
                                    <div class="spr-form clearfix">
                                        <form method="post" action="#" id="new-review-form" class="new-review-form"
                                              data-product-id="{{ Model.Product.Id }}">
                                            <h3 class="spr-form-title">{{ "blog.global.leaveAReply" | translate }}</h3>
                                            <fieldset class="spr-form-contact">
                                                <div class="spr-form-review-rating">
                                                    <label class="spr-form-label">
                                                        {{ "products.goods.your_rating" | translate }}<span
                                                                class="required">*</span>
                                                    </label>
                                                    <div class="spr-form-input spr-starrating">
                                                        <div class="product-review rating-stars">
                                                            <i class="an an-lg an-star rating-star" data-rating="1"></i>
                                                            <i class="an an-lg an-star rating-star" data-rating="2"></i>
                                                            <i class="an an-lg an-star rating-star" data-rating="3"></i>
                                                            <i class="an an-lg an-star rating-star" data-rating="4"></i>
                                                            <i class="an an-lg an-star rating-star" data-rating="5"></i>
                                                        </div>
                                                        <!-- 隐藏的输入字段用于存储评分值 -->
                                                        <input type="hidden" name="rating" id="rating-value" value="5">
                                                    </div>
                                                </div>
                                                <div class="spr-form-contact-name">
                                                    <label class="spr-form-label">
                                                        {{ "products.goods.write_your_name" | translate }}<span
                                                                class="required">*</span>
                                                    </label>
                                                    <input class="spr-form-input spr-form-input-text" type="text"
                                                           name="name" value="" required>
                                                </div>
                                                <div class="spr-form-contact-email">
                                                    <label class="spr-form-label" for="email">
                                                        {{ "web.global.newsletter_your_email" | translate }}<span
                                                                class="required">*</span>
                                                    </label>
                                                    <input class="spr-form-input spr-form-input-email" id="email"
                                                           type="email" name="email" value="" required>
                                                </div>
                                            </fieldset>
                                            <fieldset class="spr-form-review">
                                                <div class="spr-form-review-title">
                                                    <label class="spr-form-label"
                                                           for="review">{{ "products.goods.write_your_review" | translate }} {{ "products.goods.name" | translate }}</label>
                                                    <input class="spr-form-input spr-form-input-text" id="review"
                                                           type="text" name="subject" value="">
                                                </div>

                                                <div class="spr-form-review-body">
                                                    <label class="spr-form-label" for="message">
                                                        <span class="spr-form-review-body-charactersremaining">{{ "products.goods.review_max" | translate }}</span>
                                                    </label>
                                                    <div class="spr-form-input">
                                            <textarea class="spr-form-input spr-form-input-textarea"
                                                      id="message" name="content" rows="5"
                                                      required></textarea>
                                                    </div>
                                                </div>
                                            </fieldset>
                                            <div class="spr-form-actions">
                                                <input type="submit"
                                                       class="spr-button spr-button-primary button button-primary btn btn-primary theme-btn theme-btn"
                                                       value="{{ "products.goods.writeReview" | translate }}"
                                                       onclick="submitReview()">
                                            </div>
                                        </form>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="spr-reviews">
                                        <h3 class="spr-form-title">{{ "products.goods.customer_review" | translate }}</h3>
                                        <p>
                                <span class="product-review spr-starratings spr-review-header-starratings">
                                    <span class="reviewLink">
                                        {% for i in (1..5) %}
                                            {% if i <= Model.Product.AvgRating %}
                                                <i class="an an-lg an-star"></i>
                                            {% else %}
                                                <i class="an an-lg an-star gray-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </span>
                                </span>
                                            <b>{{ Model.Product.AvgRating }} (Overall)</b> | Based
                                            on {{ Model.Product.ReviewCount }} {{ "products.goods.reviews" | translate }}
                                        </p>
                                        <div class="review-inner">
                                            {% if Model.Product.Reviews != null and Model.Product.Reviews.size > 0 %}
                                                {% for review in Model.Product.Reviews %}
                                                    <div class="spr-review">
                                                        <div class="spr-review-header">
                                        <span class="product-review spr-starratings spr-review-header-starratings">
                                            <span class="reviewLink">
                                                {% for i in (1..5) %}
                                                    {% if i <= review.Rating %}
                                                        <i class="an an-lg an-star"></i>
                                                    {% else %}
                                                        <i class="an an-lg an-star gray-star"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            </span>
                                        </span>
                                                            <h3 class="spr-review-header-title">{{ review.Title }}</h3>
                                                            <span class="spr-review-header-byline"><strong>{{ review.UserName }}</strong> on <strong>{{ review.ReviewDate | date: "%b %d, %Y" }}</strong></span>
                                                        </div>
                                                        <div class="spr-review-content">
                                                            <p class="spr-review-content-body">{{ review.Content }}</p>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                <div class="spr-review">
                                                    <p>{{ "products.goods.no_review_data" | translate }}</p>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 其他自定义面板，显示对应面板的内容 -->
                        {% else %}
                            <div class="custom-tab-content">
                                {% if switch.Content != null and switch.Content != "" %}
                                    {{ switch.Content | raw }}
                                {% else %}
                                    <p>No content available for this section.</p>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            {% else %}
                <!-- 默认面板内容，当没有面板数据时显示 -->
                <h3 class="tabs-ac-style active" rel="tab1">{{ "web.global.index_view" | translate }}</h3>
                <div id="tab1" class="tab-content">
                    <div class="product-description rte">
                        <div class="row">
                            <div class="col-12 col-sm-12 col-md-12 col-lg-12">
                                {{ Model.Product.Description | raw }}
                            </div>
                        </div>
                    </div>
                </div>

                <h3 class="tabs-ac-style" rel="tab2">{{ "products.goods.reviews" | translate }}</h3>
                <div id="tab2" class="tab-content">
                    <!-- 默认评论面板内容 -->
                    <p>No reviews available.</p>
                </div>
            {% endif %}
        </div>
    </div>
    <!--End Product Tabs-->
    {% assign recommendproductslider = '/Themes/' | append: theme | append: '/Shop/RecommendProductSlider' %}
    {% include recommendproductslider, RecommendProducts: Model.RecommendProducts -%}
    <!--Product Enuiry Popup-->
    <div class="modal fade" id="productInquiry" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12 col-sm-12 col-md-12 col-lg-12">
                            <div class="contact-form form-vertical">
                                <div class="page-title text-center">
                                    <i class="an an-lg an-envelope-open"></i>
                                    <h3>{{ "checkout.result.sAwaitTips2" | translate }}</h3>
                                </div>
                                {% getFormTool  %} {{'33'}} {% endgetFormTool %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<!--Sticky Cart-->
{% if Model.StickyCartConfig.AddToCartFlow == "1" %}
    <div class="stickyCart" id="stickyCart" style="display: none;">
        <div class="container">
            <div class="img">
                <img src="{{ Model.Product.PicPath | default: '/static/assets/images/product-images/40x50.jpg' }}"
                     class="product-featured-img" alt="{{ Model.Product.ProductName }}">
            </div>
            <div class="sticky-title">
                <span class="sticky-product-name">{{ Model.Product.ProductName }}</span>
                <div class="sticky-price-container">
                    <span class="sticky-old-price" style="display: none;"></span>
                    <span class="sticky-current-price"></span>
                </div>
            </div>
            <div class="sticky-buttons">
                <button name="add" class="btn product-form__cart-submit sticky-add-cart theme-btn-border" style="display: none;">
                    <span>{{ "products.goods.addToCart" | translate }}</span>
                </button>
                <button name="add" class="btn product-form__cart-submit theme-btn sticky-buy-now"
                        style="display: none;">
                    <span>{{ "products.goods.buyNow" | translate }}</span>
                </button>
            </div>
        </div>
    </div>
{% endif %}
<!--End Sticky Cart-->
<!-- Start Addtocart Added Popup -->
<div class="modal fade" id="pro-addtocart-popup" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-12">
                        <div class="addtocart-inner text-center clearfix">
                            <h4 class="title">{{ "user.orders.buy_again_success" | translate }}</h4>
                            <div class="pro-img mb-2">
                                <img class="img-fluid blur-up lazyload" src="/assets/images/cart/loading.gif"
                                     data-src="/assets/images/cart/loading.gif"
                                     alt="" title="{{ "user.orders.buy_again_success" | translate }}"/>
                            </div>
                            <div class="pro-details">
                                <p class="pro-name mb-1"></p>
                                <p class="sku mb-0"></p>
                                <p class="mb-0 qty-total"></p>

                                <div class="button-action">
                                    <a href="/cart"
                                       class="btn btn-primary view-cart mx-1 theme-btn">{{ "cart.global.viewCartCheckout" | translate }}</a>
                                    <a href="/collections"
                                       class="btn btn-secondary theme-btn">{{ "user.forgot.continueShopping" | translate }}</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End Addtocart Added Popup -->

<!-- Quickview Modal -->
<div class="modal fade" id="quickview_popup" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 内容将通过JavaScript动态填充 -->
            </div>
        </div>
    </div>
</div>
<!--End Quickview Modal-->

<!-- 阿里云验证码SDK -->
<script src="/js/AliyunCaptcha.js"></script>
<!-- 阿里云验证码核心模块 -->
<script src="/businessJs/aliyun-captcha-core.js"></script>
<!-- 现代化消息提示框 -->
<script src="/js/Pop-ups/frame-message.js"></script>
<!-- 公用价格处理工具 -->
<script src="/businessJs/Common/priceUtils.js"></script>
<!-- 价格显示功能模块 -->
<script src="/businessJs/Product/Single/priceDisplay_t200.js"></script>
<!-- Sticky Cart功能模块 -->
<script src="/businessJs/Product/Single/stickyCart_t200.js"></script>
<!-- 快速预览功能模块 -->
<script src="/businessJs/Product/Index/quickView_t200.js"></script>

<!-- 产品变体数据 -->
<script>
    // 设置静态资源路径全局变量，供quickView_t200.js使用
    window.staticPath = '{{ static_path }}';

    // HTML实体解码函数
    function decodeHtmlEntities(text) {
        if (!text) return text;
        const $textarea = $('<textarea>').html(text);
        return $textarea.text();
    }

    // 将产品变体数据输出到JavaScript中
    window.productVariants = [
        {% if Model.Product.ProductVariants != null and Model.Product.ProductVariants.size > 0 %}
        {% for variant in Model.Product.ProductVariants %}
        {
            CId: "{{ variant.CId }}",
            OvId: "{{ variant.OvId }}",
            Sku: "{{ variant.SKU }}",
            VariantsId: "{{ variant.VariantsId }}",
            Title: "{{ variant.Title | escape }}",
            AttrName: "{{ variant.AttrName | escape }}",
            PriceFormat: "{{ variant.PriceFormat | default: '' }}",
            OldPriceFormat: "{{ variant.OldPriceFormat | default: '' }}",
            PromotionPriceFormat: "{{ variant.PromotionPriceFormat | default: '' }}",
            Stock: "{{ variant.Stock | default: 0 }}",
            PicPath: "{{ variant.PicPath | default: '' }}"
        }{% unless forloop.last %},{% endunless %}
        {% endfor %}
        {% endif %}
    ];
    setTimeout(() => {
        console.log('Product Variants:', window.productVariants);
    }, 2000)


    // 解码所有变体数据中的HTML实体
    window.productVariants = window.productVariants.map(variant => ({
        ...variant,
        Title: decodeHtmlEntities(variant.Title),
        AttrName: decodeHtmlEntities(variant.AttrName),
        PriceFormat: decodeHtmlEntities(variant.PriceFormat),
        OldPriceFormat: decodeHtmlEntities(variant.OldPriceFormat),
        PromotionPriceFormat: decodeHtmlEntities(variant.PromotionPriceFormat),
        PicPath: decodeHtmlEntities(variant.PicPath)
    }));

    // StickyCart配置数据
    window.stickyCartConfig = {
        AddToCartFlow: "{{ Model.StickyCartConfig.AddToCartFlow | default: '0' }}",
        FloatingButton: [
            {% if Model.StickyCartConfig.FloatingButton != null and Model.StickyCartConfig.FloatingButton.size > 0 %}
            {% for button in Model.StickyCartConfig.FloatingButton %}
            "{{ button }}"{% unless forloop.last %},{% endunless %}
            {% endfor %}
            {% endif %}
        ],
        Pc: "{{ Model.StickyCartConfig.Pc | default: '0' }}",
        PcPosition: "{{ Model.StickyCartConfig.PcPosition | default: 'top' }}",
        Mobile: "{{ Model.StickyCartConfig.Mobile | default: '0' }}",
        MobilePosition: "{{ Model.StickyCartConfig.MobilePosition | default: 'down' }}"
    };

    // 产品基础价格数据
    window.productPriceData = {
        PriceFormat: decodeHtmlEntities("{{ Model.Product.PriceFormat }}"),
        OriginalPriceFormat: decodeHtmlEntities("{{ Model.Product.OriginalPriceFormat }}"),
        PromotionPriceFormat: decodeHtmlEntities("{{ Model.Product.PromotionPriceFormat }}")
    };

</script>

<script>


    // 初始化评论表单
    function initReviewForm() {
        const $form = $('#new-review-form');
        if ($form.length) {
            // 监听表单提交
            $form.on('submit', function (e) {
                // 阻止默认提交行为
                e.preventDefault();

                // 获取表单数据
                const productId = '{{ Model.Product.ProductId }}';
                const name = $form.find('input[name="name"]').val();
                const email = $form.find('input[name="email"]').val();
                const subject = $form.find('input[name="subject"]').val();
                const content = $form.find('textarea[name="content"]').val();

                // 获取用户选择的评分
                const $ratingInput = $('#rating-value');
                const rating = $ratingInput.length ? parseInt($ratingInput.val()) : 0;

                // 先验证必填字段
                if (!name || !email || !content || rating === 0) {
                    customize_pop.warning('Please fill in all required fields', null, null, {showIcon: false});
                    return false;
                }

                // 验证ProductId
                if (!productId) {
                    customize_pop.error('Unable to identify the current product. Cannot submit review.', null, null, {showIcon: false});
                    return false;
                }

                // 必填字段验证通过后，再检查验证码
                if (window.YseCaptcha.isVerified()) {
                    handleReviewSubmit($form[0]);
                } else {
                    // 触发验证码验证
                    window.YseCaptcha.verify();
                }

                return false;
            });
        }

        // 为评分星星添加点击事件
        const $stars = $('.rating-stars .rating-star');
        const $ratingInput = $('#rating-value');

        // 设置默认评分为5星
        setStarRating(5);

        $stars.on('click', function () {
            const rating = parseInt($(this).attr('data-rating'));
            setStarRating(rating);
        });

        // 添加鼠标悬停效果
        $stars.on('mouseenter', function () {
            const rating = parseInt($(this).attr('data-rating'));
            highlightStars(rating);
        });

        // 鼠标离开星星区域时恢复当前评分显示
        const $starContainer = $('.rating-stars');
        if ($starContainer.length) {
            $starContainer.on('mouseleave', function () {
                const currentRating = parseInt($ratingInput.val());
                setStarRating(currentRating);
            });
        }

        // 设置星星评分的函数
        function setStarRating(rating) {
            $stars.each(function (index) {
                if (index < rating) {
                    $(this).removeClass('gray-star');
                } else {
                    $(this).addClass('gray-star');
                }
            });
            $ratingInput.val(rating);
        }

        // 高亮星星的函数（用于悬停效果）
        function highlightStars(rating) {
            $stars.each(function (index) {
                if (index < rating) {
                    $(this).removeClass('gray-star');
                } else {
                    $(this).addClass('gray-star');
                }
            });
        }
    }

    // 页面加载完成后初始化
    $(document).ready(function () {
        // 初始化验证码
        window.YseCaptcha.init({
            onSuccess: function () {
                // 验证成功后自动提交表单
                const $form = $('#new-review-form');
                if ($form.length) {
                    handleReviewSubmit($form[0]);
                }
            }
        });

        // 初始化评论表单
        initReviewForm();

        // 初始化产品变体选择功能
        initProductVariants();
    });

    //获取加购信息
    function getAddOnsInfo() {
        // 获取产品基本信息
        const productName = document.querySelector('.product-single__title')?.textContent?.trim() || '';

        // 获取选中的属性信息
        const selectedAttributes = [];
        const $checkedInputs = $('.swatchInput:checked').not('.warehouseInput');

        $checkedInputs.each(function () {
            const attributeName = $(this).data('attribute') || '';
            const attributeValue = $(this).val()?.trim() || '';
            if (attributeName && attributeValue) {
                selectedAttributes.push({
                    name: attributeName,
                    value: attributeValue,
                    display: `${attributeName}:${attributeValue}`
                });
            }
        });

        // 获取选中的仓库信息
        const $checkedWarehouse = $('.warehouseInput:checked');
        const warehouseName = $checkedWarehouse.data('warehouse-name') || '';
        const warehouseId = $checkedWarehouse.val() || '';

        // 获取当前价格信息
        const currentPrice = document.querySelector('.product-price__price .money')?.textContent?.trim() || '';
        const oldPrice = document.querySelector('.product-price__old span')?.textContent?.trim() || '';
        const promotionPrice = document.querySelector('span[style*="color: var(--bs-red)"]')?.textContent?.trim() || '';

        // 获取主图片信息
        const mainImage = document.querySelector('#zoompro')?.src || '';
        const zoomImage = document.querySelector('#zoompro')?.getAttribute('data-zoom-image') || '';

        // 获取所有产品图片
        const productImages = [];
        document.querySelectorAll('#gallery a').forEach(function (link) {
            const imageSrc = link.getAttribute('data-image') || '';
            const zoomSrc = link.getAttribute('data-zoom-image') || '';
            if (imageSrc) {
                productImages.push({
                    src: imageSrc,
                    zoomSrc: zoomSrc
                });
            }
        });

        return {
            // 调试信息
            debug: {
                mode: document.getElementById('debugMode')?.textContent || '',
                combination: document.getElementById('debugCombination')?.textContent || '',
                warehouse: document.getElementById('debugWarehouse')?.textContent || '',
                variantsId: document.getElementById('debugVariantsId')?.textContent || ''
            },
            // 产品基本信息
            product: {
                name: productName,
                currentPrice: currentPrice,
                oldPrice: oldPrice,
                promotionPrice: promotionPrice
            },
            // 选中的属性
            selectedAttributes: selectedAttributes,
            // 仓库信息
            warehouse: {
                id: warehouseId,
                name: warehouseName
            },
            // 图片信息
            images: {
                main: mainImage,
                mainZoom: zoomImage,
                gallery: productImages
            }
        };
    }

    // 初始化产品变体选择功能
    function initProductVariants() {
        // 保存初始主图，以便在需要时恢复
        const initialMainImage = '{{ mainImageSrc }}';
        window.initialMainImage = initialMainImage;

        console.log('Initial main image:', initialMainImage);

        // 确保主图在初始化时正确显示
        if (initialMainImage) {
            updateMainImage(initialMainImage);
        }

        // 获取所有的变体选择输入框
        const $variantInputs = $('.swatchInput');

        $variantInputs.on('change', function () {
            if ($(this).is(':checked')) {
                // 获取选中的选项值
                const selectedValue = $(this).val();

                // 获取当前选项所属的属性组
                const $swatchContainer = $(this).closest('.swatch');

                if ($swatchContainer.length) {
                    // 查找该属性组中的 slVariant 元素
                    const $slVariantElement = $swatchContainer.find('.slVariant');

                    if ($slVariantElement.length) {
                        // 更新显示的内容为选中的选项值
                        $slVariantElement.text(selectedValue);
                    }
                }

                // 更新变体组合和匹配VariantsId
                updateVariantsCombination();
            }
        });

        // 初始化时处理默认选中的属性
        initDefaultSelections();

        // 初始化仓库选择功能
        initWarehouseSelection();

        // 初始化时也执行一次更新
        updateVariantsCombination();
    }

    // 从格式化价格字符串中提取数值（使用公用工具）
    function extractPriceValue(priceString) {
        return window.PriceUtils ? window.PriceUtils.extractPriceValue(priceString) : 0;
    }

    // 初始化默认选中状态
    function initDefaultSelections() {
        // 处理所有已选中的属性选项
        $('.swatchInput:checked').not('.warehouseInput').each(function () {
            const selectedValue = $(this).val();
            const $swatchContainer = $(this).closest('.swatch');

            if ($swatchContainer.length) {
                const $slVariantElement = $swatchContainer.find('.slVariant');
                if ($slVariantElement.length) {
                    $slVariantElement.text(selectedValue);
                }
            }
        });
    }

    // 初始化仓库选择功能
    function initWarehouseSelection() {
        // 仓库选择变化事件
        $('.warehouseInput').on('change', function () {
            if ($(this).is(':checked')) {
                const selectedWarehouseName = $(this).data('warehouse-name');
                const selectedWarehouseId = $(this).val();

                // 更新仓库显示标签
                const $warehouseContainer = $(this).closest('.swatch-warehouse');
                if ($warehouseContainer.length) {
                    const $slVariantElement = $warehouseContainer.find('.slVariant');
                    if ($slVariantElement.length) {
                        $slVariantElement.text(selectedWarehouseName);
                    }
                }

                // 更新调试信息
                $('#debugWarehouse').text(selectedWarehouseName + ' (ID: ' + selectedWarehouseId + ')');

                // 仓库选择变化时也需要更新变体组合
                updateVariantsCombination();
            }
        });

        // 初始化仓库显示
        initWarehouseDisplay();
    }

    // 初始化仓库显示
    function initWarehouseDisplay() {
        // 获取选中的仓库（包括隐藏的单仓库情况）
        const $selectedWarehouse = $('input[name="warehouse-option"]:checked');

        if ($selectedWarehouse.length) {
            const warehouseName = $selectedWarehouse.data('warehouse-name');
            const warehouseId = $selectedWarehouse.val();

            // 更新调试信息
            $('#debugWarehouse').text(warehouseName + ' (ID: ' + warehouseId + ')');
        } else {
            $('#debugWarehouse').text('无可选仓库');
        }
    }

    // 获取选中的仓库ID
    function getSelectedWarehouseId() {
        const $selectedWarehouse = $('input[name="warehouse-option"]:checked');
        return $selectedWarehouse.length ? parseInt($selectedWarehouse.val(), 10) : 0;
    }

    // 获取选中的仓库名称
    function getSelectedWarehouseName() {
        const $selectedWarehouse = $('input[name="warehouse-option"]:checked');
        return $selectedWarehouse.length ? $selectedWarehouse.data('warehouse-name') : '';
    }

    // 更新变体组合和匹配VariantsId
    function updateVariantsCombination() {
        // 获取产品的组合模式
        const isCombinationRaw = '{{ Model.Product.IsCombination }}';

        // 处理可能的空值或undefined情况
        let isCombination = 1; // 默认为1（多规格模式）
        if (isCombinationRaw && isCombinationRaw !== '' && isCombinationRaw !== 'null' && isCombinationRaw !== 'undefined') {
            isCombination = parseInt(isCombinationRaw, 10);
            if (isNaN(isCombination)) {
                isCombination = 1; // 如果解析失败，默认为1
            }
        }

        // 获取所有选中的变体值（排除仓库选择）
        const selectedVariants = [];
        const $checkedInputs = $('.swatchInput:checked').not('.warehouseInput');

        $checkedInputs.each(function () {
            selectedVariants.push($(this).val().trim());
        });

        // 获取选中的仓库信息
        const selectedWarehouseName = getSelectedWarehouseName();
        const selectedWarehouseId = getSelectedWarehouseId();

        // 生成组合字符串（用 / 分割）
        const combinationString = selectedVariants.join(' / ');

        let matchedVariantsId = '';

        if (window.productVariants && selectedVariants.length > 0) {
            if (isCombination === 0) {
                // 单规格模式：根据仓库筛选变体
                let filteredVariants = window.productVariants;

                // 如果有选中的仓库，按仓库ID筛选
                if (selectedWarehouseId) {
                    filteredVariants = window.productVariants.filter(variant => {
                        // 通过OvId匹配仓库ID
                        return variant.OvId && variant.OvId.toString() === selectedWarehouseId.toString();
                    });
                }

                if (filteredVariants.length > 0) {
                    matchedVariantsId = filteredVariants[0].VariantsId || '';
                }
            } else if (isCombination === 2) {
                // 多规格加价模式：每个属性单独匹配，按位置组合VariantsId
                const matchedIds = [];

                // 按属性组的顺序获取选中的值（排除仓库选择）
                $('.swatch').not('.swatch-warehouse').each(function (index) {
                    const $checkedInput = $(this).find('.swatchInput:checked');
                    if ($checkedInput.length) {
                        const selectedValue = $checkedInput.val().trim();

                        // 在变体数据中查找匹配的VariantsId
                        let foundId = '';

                        window.productVariants.forEach((variant, variantIndex) => {
                            // 尝试多种匹配方式
                            if (variant.Title && variant.Title.trim() === selectedValue) {
                                foundId = variant.VariantsId || '';
                                return;
                            }

                            // 如果Title不匹配，尝试AttrName
                            if (variant.AttrName && variant.AttrName.trim() === selectedValue) {
                                foundId = variant.VariantsId || '';
                                return;
                            }

                            // 尝试部分匹配（包含关系）
                            if (variant.Title && variant.Title.includes(selectedValue)) {
                                foundId = variant.VariantsId || '';
                                return;
                            }
                        });

                        if (foundId) {
                            matchedIds.push(foundId);
                        }
                    }
                });

                // 如果有选中的仓库，根据仓库名称匹配额外的VariantsId
                if (selectedWarehouseName) {
                    window.productVariants.forEach((variant, variantIndex) => {
                        // 根据仓库名称与Title匹配
                        if (variant.Title && variant.Title.trim() === selectedWarehouseName.trim()) {
                            const foundId = variant.VariantsId || '';
                            if (foundId && !matchedIds.includes(foundId)) {
                                matchedIds.push(foundId);
                            }
                        }
                    });
                }

                // 组合所有匹配的ID
                matchedVariantsId = matchedIds.join(',');
            } else if (isCombination === 1) {
                // 多规格模式：使用原来的组合匹配逻辑，变体ID只有一个
                let matchedVariant = null;
                let filteredVariants = window.productVariants;

                // 如果有选中的仓库，按仓库ID筛选
                if (selectedWarehouseId) {
                    filteredVariants = window.productVariants.filter(variant => {
                        // 通过OvId匹配仓库ID
                        return variant.OvId && variant.OvId.toString() === selectedWarehouseId.toString();
                    });
                }

                filteredVariants.forEach((variant, index) => {
                    if (!variant.Title && !variant.AttrName) {
                        return;
                    }

                    // 检查Title字段
                    let titleMatch = false;
                    if (variant.Title) {
                        titleMatch = checkVariantMatch(variant.Title, selectedVariants);
                    }

                    // 检查AttrName字段
                    let attrNameMatch = false;
                    if (variant.AttrName) {
                        attrNameMatch = checkVariantMatch(variant.AttrName, selectedVariants);
                    }

                    if (titleMatch || attrNameMatch) {
                        matchedVariant = variant;
                        matchedVariantsId = variant.VariantsId || '';
                    }
                });
            }
        } else if (window.productVariants && selectedVariants.length === 0 && isCombination === 0) {
            // 单规格模式下，即使没有选择变体也需要考虑仓库筛选
            let filteredVariants = window.productVariants;

            // 如果有选中的仓库，按仓库ID筛选
            if (selectedWarehouseId) {
                filteredVariants = window.productVariants.filter(variant => {
                    // 通过OvId匹配仓库ID
                    return variant.OvId && variant.OvId.toString() === selectedWarehouseId.toString();
                });
            }

            if (filteredVariants.length > 0) {
                matchedVariantsId = filteredVariants[0].VariantsId || '';
            }
        }

        // 更新隐藏字段
        const $combinationField = $('#selectedVariantsCombination');
        const $variantsIdField = $('#matchedVariantsId');

        if ($combinationField.length) {
            $combinationField.val(combinationString);
        }

        if ($variantsIdField.length) {
            $variantsIdField.val(matchedVariantsId);
        }

        // 更新SKU显示
        updateSkuDisplay(isCombination, matchedVariantsId);

        // 更新价格显示
        updatePriceDisplay(isCombination, matchedVariantsId);

        // 更新调试显示
        const $debugCombination = $('#debugCombination');
        const $debugVariantsId = $('#debugVariantsId');

        if ($debugCombination.length) {
            $debugCombination.text(combinationString || '未选择');
        }

        if ($debugVariantsId.length) {
            $debugVariantsId.text(matchedVariantsId || '未匹配');
        }

        // 更新产品图片显示
        updateProductImages(matchedVariantsId);
    }

    // 更新SKU显示
    function updateSkuDisplay(isCombination, matchedVariantsId) {
        const $skuContainer = $('.product-sku');
        const $skuSpan = $('.variant-sku');

        if (isCombination === 2) {
            // IsCombination为2时隐藏SKU字段
            if ($skuContainer.length) {
                $skuContainer.hide();
            }
        } else {
            // IsCombination为0或1时显示SKU字段
            if ($skuContainer.length) {
                $skuContainer.show();
            }

            // 根据VariantsId更新SKU显示
            if (matchedVariantsId && window.productVariants) {
                // 查找匹配的变体
                const matchedVariant = window.productVariants.find(variant =>
                    variant.VariantsId === matchedVariantsId
                );

                if (matchedVariant && matchedVariant.Sku) {
                    // 更新SKU显示
                    if ($skuSpan.length) {
                        $skuSpan.text(matchedVariant.Sku);
                    }
                } else {
                    // 如果没有找到匹配的变体，显示默认SKU
                    if ($skuSpan.length) {
                        $skuSpan.text('{{ Model.Product.Sku }}');
                    }
                }
            } else {
                // 如果没有匹配的VariantsId，显示默认SKU
                if ($skuSpan.length) {
                    $skuSpan.text('{{ Model.Product.Sku }}');
                }
            }
        }
    }

    // 更新价格显示
    function updatePriceDisplay(isCombination, matchedVariantsId) {
        // 获取基础价格（产品默认价格）并解码HTML实体
        const basePriceFormat = decodeHtmlEntities('{{ Model.Product.PriceFormat }}');
        const baseOldPriceFormat = decodeHtmlEntities('{{ Model.Product.OriginalPriceFormat }}');
        const basePromotionPriceFormat = decodeHtmlEntities('{{ Model.Product.PromotionPriceFormat }}');

        // 获取价格显示元素
        const $currentPriceSpan = $('.product-price__price .money');
        const $oldPriceSpan = $('.product-price__old span');
        const $promotionPriceSpan = $('span[style*="color: var(--bs-red)"]');

        let finalPriceFormat = basePriceFormat;
        let finalOldPriceFormat = baseOldPriceFormat;
        let finalPromotionPriceFormat = basePromotionPriceFormat;

        if (window.productVariants && window.productVariants.length > 0) {
            if (isCombination === 0 || isCombination === 1) {
                // IsCombination为0或1时，直接根据VariantsId匹配对应的价格
                let matchedVariant = null;

                if (matchedVariantsId) {
                    // 如果有匹配的VariantsId，优先使用匹配的变体
                    matchedVariant = window.productVariants.find(variant =>
                        variant.VariantsId === matchedVariantsId
                    );
                }

                // 如果没有找到匹配的变体，或者在单规格模式下VariantsId为空，使用第一个变体
                if (!matchedVariant && (isCombination === 0 || !matchedVariantsId)) {
                    matchedVariant = window.productVariants[0];
                }

                if (matchedVariant) {
                    // 优先使用变体的价格，如果变体价格为空或"0"才使用基础价格
                    finalPriceFormat = (matchedVariant.PriceFormat && matchedVariant.PriceFormat !== "0") ? matchedVariant.PriceFormat : basePriceFormat;
                    finalOldPriceFormat = (matchedVariant.OldPriceFormat && matchedVariant.OldPriceFormat !== "0") ? matchedVariant.OldPriceFormat : baseOldPriceFormat;
                    finalPromotionPriceFormat = (matchedVariant.PromotionPriceFormat && matchedVariant.PromotionPriceFormat !== "0") ? matchedVariant.PromotionPriceFormat : basePromotionPriceFormat;

                    // 调试信息
                    // console.log('Price Update Debug:', {
                    //     matchedVariantId: matchedVariant.VariantsId,
                    //     variantPriceFormat: matchedVariant.PriceFormat,
                    //     variantOldPriceFormat: matchedVariant.OldPriceFormat,
                    //     variantPromotionPriceFormat: matchedVariant.PromotionPriceFormat,
                    //     basePriceFormat: basePriceFormat,
                    //     baseOldPriceFormat: baseOldPriceFormat,
                    //     basePromotionPriceFormat: basePromotionPriceFormat,
                    //     finalPriceFormat: finalPriceFormat,
                    //     finalOldPriceFormat: finalOldPriceFormat,
                    //     finalPromotionPriceFormat: finalPromotionPriceFormat
                    // });
                }
            // } else if (isCombination === 2 && matchedVariantsId) {
            } else if (isCombination === 2) {
                // IsCombination为2时，需要累加计算价格
                const variantIds = matchedVariantsId.split(',');
                let additionalPrice = 0;
                let additionalOldPrice = 0;
                let additionalPromotionPrice = 0;

                // 循环匹配每个VariantsId并累加价格
                variantIds.forEach(variantId => {
                    const matchedVariant = window.productVariants.find(variant =>
                        variant.VariantsId === variantId.trim()
                    );

                    if (matchedVariant) {
                        // 使用智能价格提取函数处理不同货币格式
                        const variantPrice = extractPriceValue(matchedVariant.PriceFormat);
                        const variantOldPrice = extractPriceValue(matchedVariant.OldPriceFormat);
                        const variantPromotionPrice = extractPriceValue(matchedVariant.PromotionPriceFormat);

                        additionalPrice += variantPrice;
                        additionalOldPrice += variantOldPrice;
                        additionalPromotionPrice += variantPromotionPrice;
                    }
                });

                // 计算最终价格：基础价 + 累加价
                const basePrice = extractPriceValue(basePriceFormat);
                const baseOldPrice = extractPriceValue(baseOldPriceFormat);
                const basePromotionPrice = extractPriceValue(basePromotionPriceFormat);

                const finalPrice = basePrice + additionalPrice;
                const finalOldPrice = baseOldPrice + additionalOldPrice;
                const finalPromotionPrice = basePromotionPrice + additionalPromotionPrice;

                // 格式化最终价格（保持原有的货币符号格式）
                const currencySymbolRaw = basePriceFormat.replace(/[0-9.-]+/g, '').trim();
                const currencySymbol = decodeHtmlEntities(currencySymbolRaw);
                finalPriceFormat = currencySymbol + finalPrice.toFixed(2);
                finalOldPriceFormat = currencySymbol + finalOldPrice.toFixed(2);
                finalPromotionPriceFormat = currencySymbol + finalPromotionPrice.toFixed(2);
            }
        }

        // 更新价格显示
        if ($currentPriceSpan.length) {
            $currentPriceSpan.text(finalPriceFormat);
        }

        if ($oldPriceSpan.length) {
            $oldPriceSpan.text(finalOldPriceFormat);
        }

        if ($promotionPriceSpan.length) {
            $promotionPriceSpan.text(finalPromotionPriceFormat);
        }

        // 更新sticky cart的价格显示
        if (window.StickyCartT200 && window.StickyCartT200.updatePriceDisplay) {
            window.StickyCartT200.updatePriceDisplay({
                PriceFormat: finalPriceFormat,
                OriginalPriceFormat: finalOldPriceFormat,
                PromotionPriceFormat: finalPromotionPriceFormat
            });
        }

        // 更新价格比较容器的data属性并重新计算折扣百分比
        const $priceComparisonContainer = $('#price-comparison-container');
        if ($priceComparisonContainer.length) {
            // 更新data属性
            $priceComparisonContainer.attr('data-original-price', finalOldPriceFormat);
            $priceComparisonContainer.attr('data-current-price', finalPriceFormat);
            $priceComparisonContainer.attr('data-promotion-price', finalPromotionPriceFormat);

            // 调用价格显示模块重新计算折扣百分比
            if (window.PriceDisplayT200 && window.PriceDisplayT200.initializePriceDisplay) {
                window.PriceDisplayT200.initializePriceDisplay();
            }
        }
    }

    // 检查变体是否匹配（不区分顺序）
    function checkVariantMatch(variantString, selectedVariants) {
        if (!variantString || selectedVariants.length === 0) {
            return false;
        }

        // 将变体字符串按 / 分割并清理空格，同时解码HTML实体
        const variantParts = variantString.split('/').map(part => decodeHtmlEntities(part.trim())).filter(part => part);

        // 检查长度是否一致
        if (variantParts.length !== selectedVariants.length) {
            return false;
        }

        // 检查是否所有选中的变体都在变体字符串中
        const allMatch = selectedVariants.every(selected => {
            return variantParts.some(part => part === selected);
        });

        return allMatch;
    }

    // 更新产品图片显示
    function updateProductImages(matchedVariantsId) {
        // 获取默认主图路径（优先使用保存的初始图片）
        const defaultMainImage = window.initialMainImage || '{{ mainImageSrc }}';

        if (!matchedVariantsId || !window.productVariants) {
            // 如果没有匹配的变体ID，显示默认主图
            if (defaultMainImage) {
                updateMainImage(defaultMainImage);
                updateThumbnailSelection(defaultMainImage);
            }
            return;
        }

        // 查找匹配的变体
        const matchedVariant = window.productVariants.find(variant =>
            variant.VariantsId === matchedVariantsId
        );

        // 如果找到匹配的变体且有图片路径
        if (matchedVariant && matchedVariant.PicPath && matchedVariant.PicPath.trim() !== '') {
            const picPath = matchedVariant.PicPath.trim();

            // 更新主图
            updateMainImage(picPath);

            // 更新缩略图选中状态
            updateThumbnailSelection(picPath);
        } else {
            console.log('Variant has no image, using default image');
            // 如果变体没有图片，回退到显示默认主图
            if (defaultMainImage) {
                updateMainImage(defaultMainImage);
                updateThumbnailSelection(defaultMainImage);
            }
        }
    }

    // 更新主图显示
    function updateMainImage(picPath) {
        const $mainImage = $('#zoompro');
        if ($mainImage.length) {
            $mainImage.attr('src', picPath);
            $mainImage.attr('data-zoom-image', picPath);
        }

        // 同时更新sticky cart中的图片
        const $stickyImage = $('.stickyCart .product-featured-img');
        if ($stickyImage.length) {
            $stickyImage.attr('src', picPath);
            $stickyImage.attr('alt', '{{ Model.Product.ProductName }}');
        }

        // 通过sticky cart的API更新图片
        if (window.StickyCartT200 && window.StickyCartT200.updateProductImage) {
            window.StickyCartT200.updateProductImage(picPath);
        }
    }

    // 更新缩略图选中状态
    function updateThumbnailSelection(picPath) {
        // 移除所有缩略图的选中状态
        $('.product-thumb .slick-slide').removeClass('slick-current slick-active');

        // 查找匹配的缩略图并设为选中状态
        $('.product-thumb .slick-slide').each(function () {
            const $thumb = $(this);
            const thumbImageSrc = $thumb.attr('data-image');

            if (thumbImageSrc && thumbImageSrc.trim() === picPath) {
                $thumb.addClass('slick-current slick-active');
                return false; // 找到后退出循环
            }
        });
    }

    // 评论表单提交处理
    function handleReviewSubmit(form) {
        // 获取表单数据
        const productId = '{{ Model.Product.ProductId }}';
        const $form = $(form);
        const name = $form.find('input[name="name"]').val();
        const email = $form.find('input[name="email"]').val();
        const subject = $form.find('input[name="subject"]').val();
        const content = $form.find('textarea[name="content"]').val();

        // 获取用户选择的评分
        const $ratingInput = $('#rating-value');
        const rating = $ratingInput.length ? parseInt($ratingInput.val()) : 0;

        // 准备提交的数据（不再重复验证必填字段，因为已在表单提交时验证过）
        const reviewData = {
            ProductId: productId,
            Name: name,
            Email: email,
            Title: subject || 'Product Review',
            Content: content,
            Rating: rating,
            CaptchaVerifyParam: window.YseCaptcha.getVerifyParam()
        };

        // 显示加载提示
        customize_pop.loading('Submitting review...');

        // 发送评论数据到服务器
        $.ajax({
            url: '/api/product/review/submit',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(reviewData),
            success: function (data) {
                // 关闭加载提示
                customize_pop.loadingClose();

                if (data.success) {
                    customize_pop.success('Review submitted successfully!', function () {
                        // 重置表单
                        $form[0].reset();
                        // 重置星星评分为默认5星
                        const $ratingInput = $('#rating-value');
                        if ($ratingInput.length) {
                            $ratingInput.val(5);
                        }
                        // 重置星星显示
                        const $stars = $('.rating-stars .rating-star');
                        $stars.each(function (index) {
                            if (index < 5) {
                                $(this).removeClass('gray-star');
                            } else {
                                $(this).addClass('gray-star');
                            }
                        });
                        // 刷新页面以显示新评论
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    }, null, {showIcon: false});
                } else {
                    customize_pop.error('Failed to submit review: ' + (data.message || 'Unknown error'), null, null, {showIcon: false});
                }
            },
            error: function (xhr, status, error) {
                // 关闭加载提示
                customize_pop.loadingClose();
                customize_pop.error('Error submitting review. Please try again later.', null, null, {showIcon: false});
            },
            complete: function () {
                // 重置验证状态，防止重复提交
                window.YseCaptcha.reset();
            }
        });
    }

    // 兼容原有代码的submitReview函数
    function submitReview() {
        const $form = $('#new-review-form');
        if ($form.length) {
            $form.trigger('submit');
        }
        return false;
    }
</script>
<script>
    // 处理购物车按钮点击事件，阻止默认弹窗，改为打开快速预览
    document.addEventListener('click', function (e) {
        const addToCartBtn = e.target.closest('.btn-addto-cart');
        if (addToCartBtn) {
            e.preventDefault();
            e.stopPropagation();

            // 获取产品ID
            const productId = addToCartBtn.getAttribute('data-product-id');
            if (!productId) {
                console.error('Product ID not found');
                return;
            }

            // 查找对应的隐藏的快速查看按钮并触发点击
            const quickViewBtn = addToCartBtn.closest('.item').querySelector('.quick-view');
            if (quickViewBtn) {
                quickViewBtn.click();
            } else {
                console.error('Quick view button not found');
            }
        }

        // 处理收藏按钮点击事件
        const wishlistBtn = e.target.closest('.wishlist-btn .add-to-wishlist');
        if (wishlistBtn) {
            e.preventDefault();
            e.stopPropagation();

            // 直接从wishlist按钮获取产品ID和收藏状态
            const productId = wishlistBtn.getAttribute('data-product-id');
            const isFavorited = wishlistBtn.getAttribute('data-is-favorited') === 'true';

            if (!productId) {
                if (typeof customize_pop !== 'undefined' && customize_pop.warning) {
                    customize_pop.warning('Unable to get product ID', null, null, {showIcon: false});
                } else {
                    alert('Unable to get product ID');
                }
                return;
            }

            // 根据当前收藏状态决定操作
            const url = isFavorited ? '/Account/RemoveFromWishlistByProductId' : '/Account/AddToWishlist';
            const requestBody = {productId: parseInt(productId)};

            // 发送收藏请求
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 根据操作类型显示不同的成功消息
                        const message = isFavorited ? 'Removed from wishlist successfully' : 'Added to wishlist successfully';
                        if (typeof customize_pop !== 'undefined' && customize_pop.success) {
                            customize_pop.success(message, null, null, {showIcon: false});
                        } else {
                            alert(message);
                        }

                        // 更新按钮状态
                        const heartIcon = wishlistBtn.querySelector('i');
                        if (heartIcon) {
                            if (isFavorited) {
                                heartIcon.className = 'icon an an-heart-o'; // 改为空心心形
                                wishlistBtn.setAttribute('data-is-favorited', 'false');
                            } else {
                                heartIcon.className = 'icon an an-heart'; // 改为实心心形
                                wishlistBtn.setAttribute('data-is-favorited', 'true');
                            }
                        }
                    } else {
                        if (typeof customize_pop !== 'undefined' && customize_pop.warning) {
                            customize_pop.warning(data.message || 'Operation failed', null, null, {showIcon: false});
                        } else {
                            alert(data.message || 'Operation failed');
                        }
                    }
                })
                .catch(error => {
                    console.error('收藏操作时出错:', error);
                    if (typeof customize_pop !== 'undefined' && customize_pop.error) {
                        customize_pop.error('Network error, please try again later', null, null, {showIcon: false});
                    } else {
                        alert('Network error, please try again later');
                    }
                });
        }
    });
 </script>

<script>
    //跳转评论
    document.querySelector(".review-label").addEventListener('click', function () {
        document.querySelectorAll('.product-tabs li').forEach(function (tab) {
            // 隐藏所有 tab-content
            document.querySelectorAll('.tab-content').forEach(function (content) {
                content.style.display = 'none';
            });
            //
            var dataId = tab.getAttribute('data-id');
            // 获取当前 tab 的 rel 属性
            var activeTab = tab.getAttribute('rel');
            var activeContent = document.getElementById(activeTab);
            if (activeContent) {
                // 使用淡入效果（可选，简单实现）
                activeContent.style.opacity = 0;
                activeContent.style.display = 'block';
                setTimeout(function () {
                    activeContent.style.transition = 'opacity 0.3s';
                    activeContent.style.opacity = 1;
                }, 10);
            }
            // 移除所有 tab 的 active 类
            document.querySelectorAll('.product-tabs li').forEach(function (li) {
                li.classList.remove('active');
            });
            if (dataId == "reviews") {
                // 当前 tab 添加 active 类
                tab.classList.add('active');
            }
        });
    })


    //加购
    function addToCart() {

        // 获取并输出加购信息
        const addOnsInfo = getAddOnsInfo();
        //console.log('完整信息对象:', addOnsInfo);
        //console.log('===================');

        var nums = $(".product-form__input").val();
        var cartType = 1;
        //variantsId ="10050,10052"
        var variantsId = $('#matchedVariantsId').val();
        var ovId = getSelectedWarehouseId(); // 使用选中的仓库ID
        var ProductId = parseInt('{{ Model.Product.ProductId }}', 10);

        const addCartData = {
            ProductId: ProductId,
            variantsId: variantsId,
            Nums: nums,
            cartType: cartType,
            ovId: ovId,
        };

        //var param = { entity: addCartData };

        //console.log(param);

        // 发送加购数据到服务器

        $.ajax({
            url: '/Cart/AddCart',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(addCartData),
            success: function (data) {

                // 关闭加载提示
                customize_pop.loadingClose();

                var msg = data.msg;
                if (msg != '') {
                    msg = data.msg;
                }

                if (data.status) {

                    //更新购物车数量角标
                    $(".site-cart-count").text(data.otherData);

                    console.log(addOnsInfo.images.main);
                    $("#pro-addtocart-popup .img-fluid")
                        .attr("src", addOnsInfo.images.main)
                        .attr("data-src", addOnsInfo.images.main);//image
                    $("#pro-addtocart-popup .pro-name").text('{{ Model.Product.ProductName | raw }}')//name

                    var proAttr = "";
                    if (addOnsInfo.selectedAttributes != null) {
                        $.each(addOnsInfo.selectedAttributes, function (index, item) {
                            proAttr += item.display;
                            proAttr += "<br />";
                        });
                    }

                    $("#pro-addtocart-popup .sku").html(proAttr);//property
                    $("#pro-addtocart-popup .qty-total").text(addOnsInfo.product.currentPrice);//price

                    //弹出
                    $("#pro-addtocart-popup").modal("show");
                    //customize_pop.success(msg, function () {

                    //}, null, {showIcon: false});
                } else {
                    customize_pop.error('Failed to add to cart: ' + (data.msg || 'Unknown error'), null, null, {showIcon: true});
                }
            },
            error: function (xhr, status, error) {
                // 关闭加载提示
                customize_pop.loadingClose();
                customize_pop.error('Please try again later.', null, null, {showIcon: true});
            },
            complete: function () {
                // 可以在这里添加完成后的处理逻辑
            }
        });
    };


    //立即购买
    function buyItNow() {

        var nums = $(".product-form__input").val();
        var cartType = 2;
        //variantsId ="10050,10052"
        var variantsId = $('#matchedVariantsId').val();
        var ovId = getSelectedWarehouseId(); // 使用选中的仓库ID
        var ProductId = parseInt('{{ Model.Product.ProductId }}', 10);

        const addCartData = {
            ProductId: ProductId,
            variantsId: variantsId,
            Nums: nums,
            cartType: cartType,
            ovId: ovId,
        };

        //var param = { entity: addCartData };

        //console.log(param);

        // 发送加购数据到服务器
        $.ajax({
            url: '/Cart/BuyItNow',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(addCartData),
            success: function (data) {
                //debugger;
                // 关闭加载提示
                customize_pop.loadingClose();

                var msg = data.msg;
                if (msg != '') {
                    msg = data.msg;
                }
                if (data.status) {
                    //更新购物车数量角标
                    $(".site-cart-count").text(data.otherData);

                    //结算
                    window.location.href = data.data.location;

                } else {
                    customize_pop.error('Failed to buy it now: ' + (data.msg || 'Unknown error'), null, null, {showIcon: true});
                }
            },
            error: function (xhr, status, error) {
                // 关闭加载提示
                customize_pop.loadingClose();
                customize_pop.error('Please try again later.', null, null, {showIcon: true});
            },
            complete: function () {
                // 可以在这里添加完成后的处理逻辑
            }
        });
    };

</script>


<style>
    /* 星星评分样式 */
    .rating-stars {
        display: inline-block;
        cursor: pointer;
    }

    .rating-star {
        color: #ffc107;
        font-size: 1.2em;
        margin-right: 2px;
        transition: color 0.2s ease;
        cursor: pointer;
    }

    .rating-star:hover {
        color: #ffb300;
    }

    .rating-star.gray-star {
        color: #ddd;
    }

    .rating-star.gray-star:hover {
        color: #ffb300;
    }

    /* 评分容器样式 */
    .spr-starrating {
        margin-bottom: 15px;
    }

    .spr-starrating .product-review {
        margin-bottom: 5px;
    }
</style>

<!-- 导入图片处理助手 -->
{% comment %}
<script src="/businessJs/imageUrlHelper.js"></script>{% endcomment %}
{% comment %}{% endcomment %}
{% comment %}
<script>
{% endcomment %}
{% comment %}// 页面加载完成后处理所有图片URL{% endcomment %}
{% comment %}document.addEventListener('DOMContentLoaded', function() {{% endcomment %}
    {% comment %}// 处理产品详情页面中的所有图片{% endcomment %}
    {% comment %}const productDetailImages = document.querySelectorAll('.product-details-img img, .lightboximages img, .zoompro');{% endcomment %}
    {% comment %}productDetailImages.forEach(function(img) {{% endcomment %}
        {% comment %}if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {{% endcomment %}
            {% comment %}img.src = ImageUrlHelper.getMediumUrl(img.src);{% endcomment %}
        {% comment %}}{% endcomment %}
        {% comment %}if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {{% endcomment %}
            {% comment %}img.dataset.src = ImageUrlHelper.getMediumUrl(img.dataset.src);{% endcomment %}
        {% comment %}}{% endcomment %}
        {% comment %}if (img.dataset.zoomImage && !ImageUrlHelper.hasOssParams(img.dataset.zoomImage)) {{% endcomment %}
            {% comment %}img.dataset.zoomImage = ImageUrlHelper.getLargeUrl(img.dataset.zoomImage);{% endcomment %}
        {% comment %}}{% endcomment %}
    {% comment %}});{% endcomment %}
{% comment %}{% endcomment %}
    {% comment %}// 处理添加到购物车弹窗中的图片{% endcomment %}
    {% comment %}const cartPopupImages = document.querySelectorAll('#pro-addtocart-popup img');{% endcomment %}
    {% comment %}cartPopupImages.forEach(function(img) {{% endcomment %}
        {% comment %}if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {{% endcomment %}
            {% comment %}img.src = ImageUrlHelper.getMediumUrl(img.src);{% endcomment %}
        {% comment %}}{% endcomment %}
        {% comment %}if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {{% endcomment %}
            {% comment %}img.dataset.src = ImageUrlHelper.getMediumUrl(img.dataset.src);{% endcomment %}
        {% comment %}}{% endcomment %}
    {% comment %}});{% endcomment %}
{% comment %}{% endcomment %}
    {% comment %}// 处理产品详情页面中的其他图片（如描述中的图片）{% endcomment %}
    {% comment %}const descriptionImages = document.querySelectorAll('.tab-content img');{% endcomment %}
    {% comment %}descriptionImages.forEach(function(img) {{% endcomment %}
        {% comment %}if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {{% endcomment %}
            {% comment %}img.src = ImageUrlHelper.getMediumUrl(img.src);{% endcomment %}
        {% comment %}}{% endcomment %}
        {% comment %}if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {{% endcomment %}
            {% comment %}img.dataset.src = ImageUrlHelper.getMediumUrl(img.dataset.src);{% endcomment %}
        {% comment %}}{% endcomment %}
    {% comment %}});{% endcomment %}
{% comment %}});{% endcomment %}
</script>