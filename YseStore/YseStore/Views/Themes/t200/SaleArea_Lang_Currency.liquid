{% layout '' %}
<style>
    .cnrLangList li a.active {
        color: var( --theme-color);
        border: 1px solid var( --theme-color);
        opacity: 1;
        text-decoration: none;
    }
</style>

{% if Model != null %}
{% if Model.LanguageList!=null and Model.LanguageList.size>0 and ViewData["languageShow"]=="1" %}

<div class="language-picker">
    <span class="ttl">{{"web.global.change_language"|translate}}</span>
    <ul id="language" class="cnrLangList">
        {% for lang in Model.LanguageList %}
        {% assign activeClass="" %}
        {% if lang.Language==ViewData["language"] %}
        {% assign activeClass="active" %}
        {% endif %}
        <li>
            <a href="#" class="{{activeClass}}" data-lang="{{lang.Language}}"
               hx-post="/Account/UpdateLang"
               hx-vars="{ lang :'{{lang.Language}}' }"
               hx-trigger="click"
               hx-swap="none"
               hx-on::after-request="if(event.detail.successful){ setLanguage(event.detail.xhr.responseText) }">{{lang.Name}}</a>
        </li>
        {% endfor %}
    </ul>
</div>
{% endif %}

{% if Model.CurrencyList!=null and Model.CurrencyList.size>0 and ViewData["currencyShow"]=="1"%}
<div class="currency-picker">
    <span class="ttl">{{"web.global.change_currency"|translate}}</span>
    <ul id="currencies" class="cnrLangList">
        {% for currency in Model.CurrencyList %}

        {% assign activeClass="" %}
        {% if currency.Currency==ViewData["currency"] %}
        {% assign activeClass="active" %}
        {% endif %}

        <li>
            <a href="#" class="{{activeClass}}" data-currency="{{currency.Currency}}"
               hx-post="/Account/UpdateCurrency"
               hx-vars="{ currency :'{{currency.Currency}}' }"
               hx-trigger="click"
               hx-swap="none"
               hx-on::after-request="if(event.detail.successful){ setCurrency(event.detail.xhr.responseText) }">{{currency.Currency}}</a>
        </li>
        {% endfor %}
    </ul>
</div>
{% endif %}
{% endif %}

<script>

    function setLanguage(responseText) {
        const response = JSON.parse(responseText);
        if (response.status == true) {
            window.location.reload();
        }
    }

    function setCurrency(responseText) {
        const response = JSON.parse(responseText);
        if (response.status == true) {
            window.location.reload();
        }
    }

</script>