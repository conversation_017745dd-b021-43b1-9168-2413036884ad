/* 评论区样式 */
.blog-comments-item {
    display: flex;
    margin-bottom: 30px;
    position: relative;
}

.blog-comments-img {
    margin-right: 20px;
    flex-shrink: 0;
}

.blog-comments-img img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.blog-comments-content {
    flex-grow: 1;
}

.blog-comments-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.blog-comments-name h5 {
    margin-bottom: 5px;
    font-size: 16px;
    font-weight: 600;
}

.blog-comments-name span {
    color: #777;
    font-size: 14px;
}

.blog-rating {
    color: #ffb800;
}

.blog-comments-btn {
    margin-top: 10px;
}

.blog-comments-btn a {
    color: #007bff;
    font-weight: 500;
    text-decoration: none;
}

.blog-comments-btn a:hover {
    text-decoration: underline;
}

.blog-comments-reply {
    margin-left: 40px;
    margin-top: 20px;
    position: relative;
    display: flex;
}

.blog-comments-reply::before {
    content: "";
    position: absolute;
    left: -20px;
    top: 15px;
    width: 20px;
    height: 1px;
    background-color: #ddd;
}

.admin-badge {
    background-color: #007bff;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    margin-left: 5px;
}

.no-reviews {
    padding: 20px;
    text-align: center;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.review-images {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.review-image-item img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 5px;
}

/* 回复表单样式 */
.leave-form-area {
    margin-top: 40px;
}

.comment-reply-title {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
}

.leave-form {
    margin-bottom: 20px;
}

.leave-form input,
.leave-form select,
.text-leave textarea {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    transition: border-color 0.3s;
}

.leave-form input:focus,
.leave-form select:focus,
.text-leave textarea:focus {
    border-color: #007bff;
    outline: none;
}

.text-leave textarea {
    min-height: 150px;
    resize: vertical;
}

.text-leave button.submit {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    margin-top: 15px;
    transition: background-color 0.3s;
}

.text-leave button.submit:hover {
    background-color: #0056b3;
}

#captcha-element {
    margin-top: 15px;
}

/* 回复信息提示样式 */
.reply-to-tag {
    color: #007bff;
    font-weight: 600;
    margin-right: 5px;
}

/* 确保非激活的标签内容不可见 */
.tab-pane:not(.active) {
    display: none !important;
}

/* 确保激活的标签内容可见 */
.tab-pane.active {
    display: block !important;
} 