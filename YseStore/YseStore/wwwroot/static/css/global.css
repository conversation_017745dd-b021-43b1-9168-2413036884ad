:root {
    --CheckoutMainColor: #1875b4;
    --CheckoutMainLightColor: #74acd2
}

html {
/*    font-family: Arial*/
}

/*body {
    font-size: 12px
}
*/
    body.hidden {
        overflow: hidden
    }

img {
    max-height: 100%;
    max-width: 100%
}

.wide {
    width: 1200px;
    margin: 0 auto;
    min-width: 1200px
}

.full {
    width: 100%;
    margin: 0 auto
}

.show {
    display: block
}

.hide {
    display: none
}

.white_bg {
    background: #fff
}

.blue {
    color: #005ab0
}

.form_button_bg {
    background-color: #005ab0;
    border-color: #005ab0
}

.form_select_tips {
    border-color: red
}

.error, .error_info {
    color: #900;
    margin: 0;
    word-break: normal
}

.clearfix:after {
    content: '';
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0
}

.headerFixed {
    z-index: 101 !important
}

.visual_plugins_container {
    width: inherit
}

    .visual_plugins_container a.visual_cka {
        color: inherit
    }

.w_1200 .sitemap_box dl {
    width: 210px
}

.min {
    min-width: 980px
}

.PriceColor, a.<PERSON> {
    color: #c00
}

.global_trans {
    transition: all .3s;
    -webkit-transition: all .3s;
    -moz-transition: all .3s
}

.visual_hide {
    display: none !important
}

.operation_activities.show {
    display: flex
}

img.lazyload, img.lazyloading {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center center;
    background-color: var(--PreBackgroundColor)
}

.logo_img img.lazyload {
    background-color: #e5e5e5
}

.img.img_loading, img.lazyloading {
    font-size: 0
}

.container_screen {
    max-width: 1200px;
    margin: 0 auto
}

.container_width_1200 {
    margin: 0 auto;
    width: 1200px;
    box-sizing: border-box
}

.visual_plugins_container {
    position: relative
}

    .visual_plugins_container[data-type=cases_list], .visual_plugins_container[data-type=news_list] {
        position: static
    }

    .visual_plugins_container.cur_hover_plugins, .visual_plugins_container.cur_plugins {
        position: relative
    }

        .visual_plugins_container.cur_plugins:after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 2px solid #0baf4d;
            box-sizing: border-box;
            z-index: 10000;
            pointer-events: none
        }

        .visual_plugins_container.cur_hover_plugins::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 2px solid #0baf4d;
            box-sizing: border-box;
            z-index: 10;
            pointer-events: none
        }

    .visual_plugins_container .plugins_type_box {
        position: absolute;
        top: 0;
        left: 0;
        font-size: 14px;
        color: #fff;
        background-color: #0baf4d;
        padding: 3px 15px
    }

    .visual_plugins_container .operate_item_box {
        position: absolute;
        display: flex;
        left: 50%;
        transform: translateX(-50%);
        top: 5px;
        background-color: #1a1c1d;
        border-radius: 5px;
        justify-content: center;
        align-items: center;
        z-index: 10001
    }

        .visual_plugins_container .operate_item_box .operate_item {
            position: relative;
            width: 22px;
            height: 25px;
            margin: 0 10px;
            cursor: pointer;
            margin: 12px 10px
        }

            .visual_plugins_container .operate_item_box .operate_item.operate_moveoperate_move_dis {
                cursor: no-drop
            }

            .visual_plugins_container .operate_item_box .operate_item::after {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                font-size: 18px;
                font-family: iconfont !important;
                color: #fff;
                text-align: center;
                line-height: 25px
            }

            .visual_plugins_container .operate_item_box .operate_item.operate_up::after {
                content: "\e65a"
            }

            .visual_plugins_container .operate_item_box .operate_item.operate_down::after {
                content: "\e658"
            }

            .visual_plugins_container .operate_item_box .operate_item.operate_copy::after {
                content: "\e65b"
            }

            .visual_plugins_container .operate_item_box .operate_item.operate_hide::after {
                content: "\e657"
            }

            .visual_plugins_container .operate_item_box .operate_item.operate_del::after {
                content: "\e659";
                color: #e90e0e
            }

.visual_edit_mode .headerFixed {
    z-index: 10006 !important
}

.visual_edit_mode .shopping_add_to_cart {
    z-index: 99 !important
}

@media screen and (max-width:1240px) {
    .container_width_1200 {
        width: 1000px
    }
}

@media screen and (max-width:1040px) {
    .container_width_1200 {
        padding: 0 15px;
        width: 98%
    }
}

.app_blog .compute_process_img, .app_gallery .compute_process_img {
    padding-top: 100%
}

@media (max-width:1279px) {
    .wide {
        width: 980px;
        min-width: 980px
    }
}

@media (max-width:1023px) {
    .wide {
        width: 100%;
        min-width: inherit
    }
}

@media screen and (min-width:1000px) {
    #footer_outer, #header, #nav_outer, #service_outer, #top_bar_outer {
        min-width: 980px
    }
}

@media(max-width:1000px) {
    .follow_us_list li > a:focus, .follow_us_list li > a:hover {
        transform: none;
        -webkit-transform: none
    }
}

.container_full {
    padding: 0 40px;
    width: 100%;
    box-sizing: border-box
}

@media screen and (max-width:1000px) {
    .container_full {
        padding: 0 20px
    }
}

.sys_bg_button {
    background-color: #fff;
    transition: all .3s;
    -webkit-transition: all .3s;
    -moz-transition: all .3s
}

    .sys_bg_button.cur, .sys_bg_button:hover {
        text-decoration: none;
        background-color: #f6f6f6
    }

.sys_shadow_button {
    transition: all .3s;
    -webkit-transition: all .3s;
    -moz-transition: all .3s
}

    .sys_shadow_button.cur, .sys_shadow_button:hover {
        text-decoration: none;
        color: #fff;
        box-shadow: inset 0 0 10rem 2px rgba(105,105,105,.2);
        -webkit-box-shadow: inset 0 0 10rem 2px rgba(105,105,105,.2)
    }

.btn_global {
    height: 42px;
    line-height: 42px;
    overflow: hidden;
    padding: 0 20px;
    text-decoration: none;
    font-size: 16px;
    color: #fff;
    cursor: pointer;
    border: 1px #e5e5e5 solid;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    display: inline-block;
    vertical-align: top
}

.btn_processing {
    color: #bcbcbc !important;
    background-color: #f2f2f2 !important
}

.btn_disabled {
    background-color: #f2f2f2 !important;
    color: #bcbcbc !important;
    cursor: no-drop
}

.unShowScrollBar {
    overflow-x: auto !important;
    overflow-y: hidden !important
}

    .unShowScrollBar::-webkit-scrollbar {
        padding: 0 !important;
        width: 0 !important;
        height: 0 !important;
        display: none !important;
        background-color: #fff !important
    }

    .unShowScrollBar::-webkit-scrollbar-thumb {
        background-color: #fff !important;
        color: #fff !important
    }

    .unShowScrollBar::-webkit-scrollbar-track {
        background-color: #fff !important
    }

    .unShowScrollBar::-webkit-scrollbar-thumb {
        background-color: #fff !important
    }

        .unShowScrollBar::-webkit-scrollbar-thumb:hover {
            background-color: #fff !important
        }

        .unShowScrollBar::-webkit-scrollbar-thumb:active {
            background-color: #fff !important
        }

.main_process {
    display: none
}

.video_con {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,.5);
    -webkit-transform: translateY(-100%);
    -ms-transform: translateY(-100%);
    transform: translateY(-100%);
    z-index: 10000
}

    .video_con.cur {
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0)
    }

    .video_con .video_con_in {
        position: absolute;
        left: 50%;
        top: -100%;
        width: 800px;
        height: 500px;
        background: #fff;
        -webkit-transform: translate(-50%,-50%);
        -ms-transform: translate(-50%,-50%);
        transform: translate(-50%,-50%);
        -o-transition: top .25s;
        transition: top .25s;
        -webkit-transition: top .25s;
        -moz-transition: top .25s
    }

    .video_con:not(.mobile) .video_con_in .videoContent {
        height: 100%
    }

    .video_con:not(.mobile) .video_con_in embed {
        width: 100%;
        height: 100%
    }

    .video_con:not(.mobile) .video_con_in iframe {
        width: 100%;
        height: 100%
    }

    .video_con:not(.mobile) .video_con_in video {
        width: 100%;
        height: 100%;
        object-fit: cover
    }

    .video_con.mobile .videoContent {
        display: inline-block
    }

    .video_con .video_con_in .close_btn {
        position: absolute;
        right: -12px;
        top: -12px;
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        width: 25px;
        height: 25px;
        background: #fff;
        text-align: center;
        border-radius: 50%;
        background-image: url(/static/themes-v2/t144/images/close_btn.png);
        background-repeat: no-repeat;
        background-position: center
    }

.video_public_box iframe {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    z-index: 10
}

.video_public_box video {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 100%;
    height: 100%;
    z-index: 10;
    background-color: #000
}

    .video_public_box video.poster_cover {
        object-fit: cover
    }

@media screen and (max-width:991px) {
    .video_con .video_con_in {
        width: 88%
    }
}

@media screen and (max-width:1000px) {
    .main_process {
        display: block;
        width: 50px;
        height: 6px;
        background: #d8d8d8;
        border-radius: 10px;
        margin: 10px auto 0
    }

    .scroll_progressSon {
        width: 15px;
        height: 6px;
        border-radius: 10px;
        background: #000
    }
}

.icon_success_status {
    width: 44px;
    height: 44px;
    overflow: hidden;
    background-image: url(/assets/images/cart/icon_success_status.png);
    background-repeat: no-repeat;
    position: absolute;
    top: 40px;
    left: 244px;
    display: block
}

    .icon_success_status.await {
        background-position: 0 -54px
    }

    .icon_success_status.fail {
        background-position: 0 -108px
    }

.ajax_search {
    position: relative;
    z-index: 1002
}

.visual_edit_mode .ajax_search {
    z-index: 10006
}

.ajax_search .search_content_box {
    position: absolute;
    top: 0;
    left: 0;
    background: #fff;
    padding: 5px 0;
    border: 1px solid #ccc
}

    .ajax_search .search_content_box .search_item {
        margin-top: 5px;
        padding-right: 10px;
        padding-left: 50px
    }

    .ajax_search .search_content_box .first {
        margin-top: 0
    }

    .ajax_search .search_content_box .pic {
        float: left;
        width: 30px;
        height: 30px;
        margin-left: -45px;
        padding: 1px;
        border: 1px solid #ccc;
        text-align: center;
        vertical-align: middle;
        font-size: 0;
        line-height: 0;
        opacity: 1
    }

        .ajax_search .search_content_box .pic img {
            opacity: 1
        }

    .ajax_search .search_content_box .name {
        float: left;
        width: 100%;
        height: 34px;
        line-height: 34px;
        overflow: hidden;
        -ms-text-overflow: ellipsis;
        text-overflow: ellipsis;
        -webkit-text-overflow: ellipsis;
        white-space: nowrap;
        text-align: left
    }

        .ajax_search .search_content_box .name em {
            color: red
        }

@media (max-width:768px) {
    .ajax_search {
        z-index: auto
    }

    .search_side .menu_list .search_content_box {
        overflow: hidden
    }

        .search_side .menu_list .search_content_box .search_item {
            margin-top: .875rem;
            height: 3.125rem;
            border-bottom: 1px solid #eee;
            padding-bottom: .5rem
        }

            .search_side .menu_list .search_content_box .search_item .pic {
                width: 3.125rem;
                height: 3.125rem;
                float: left;
                overflow: hidden;
                border-radius: 5px
            }

                .search_side .menu_list .search_content_box .search_item .pic img {
                    opacity: 1
                }

            .search_side .menu_list .search_content_box .search_item .name {
                float: left;
                width: 79%;
                margin-left: .8125rem;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden
            }
}

.ajax_search .search_history_container {
    position: absolute;
    top: 0;
    left: 0;
    overflow: hidden;
    min-width: 300px;
    padding: 25px 15px;
    border-radius: 4px;
    text-align: left;
    background-color: #fff;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    box-shadow: rgb(0 0 0 / 8%) 0 0 10px 5px
}

    .ajax_search .search_history_container .search_item {
        margin-top: 20px
    }

        .ajax_search .search_history_container .search_item:first-child {
            margin-top: 0
        }

    .ajax_search .search_history_container .search_title {
        padding: 0 5px;
        line-height: 22px;
        font-size: 16px;
        color: #333
    }

    .ajax_search .search_history_container .search_list {
        margin-top: 10px
    }

        .ajax_search .search_history_container .search_list > a {
            display: inline-block;
            vertical-align: top;
            overflow: hidden;
            margin: 5px;
            padding: 0 20px;
            max-width: 140px;
            height: 32px;
            border-radius: 4px;
            line-height: 32px;
            text-decoration: none;
            font-size: 12px;
            color: #555;
            white-space: nowrap;
            text-overflow: ellipsis;
            background-color: #f4f4f4
        }

#location {
    padding: 25px 0
}

    #location, #location a {
        font-size: 12px
    }

.pro_item {
    background: #fff
}

#cookies_agreement {
    background-color: #fff;
    border-radius: 4px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    padding: 20px;
    position: fixed;
    bottom: 140px;
    left: 20px;
    z-index: 100001;
    box-shadow: 0 0 5px rgba(0,0,0,.3);
    transition: opacity .3s ease-in-out;
    display: flex;
    flex-wrap: wrap;
    opacity: 1;
    max-width: 374px;
    width: 94%;
    justify-content: space-between;
    box-sizing: border-box;
    margin: auto
}

    #cookies_agreement > .content {
        line-height: 26px;
        color: #333;
        width: 100%;
        font-size: 16px
    }

    #cookies_agreement > .button {
        display: block;
        background-color: #2e2e2e;
        line-height: 20px;
        margin-top: 15px;
        padding: 10px;
        font-size: 14px;
        color: #fff;
        cursor: pointer;
        width: 48%
    }

        #cookies_agreement > .button.reject {
            background-color: #fff;
            color: #2e2e2e;
            border: 1px solid #2e2e2e
        }

@media screen and (min-width:1000px) {
    #cookies_agreement.bottom {
        padding: 30px 15%;
        width: 100%;
        bottom: 0;
        left: 0;
        border-radius: 0;
        border: 1px solid #d0d0d0;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        max-width: none
    }

        #cookies_agreement.bottom > .content {
            flex: 1;
            margin-right: 20px
        }

        #cookies_agreement.bottom > .button {
            width: auto;
            padding: 10px 60px;
            margin-left: 20px;
            margin-top: 0
        }
}

@media screen and (max-width:1000px) {
    #cookies_agreement.bottom {
        padding: 20px;
        left: 0;
        right: 0;
        bottom: 0;
        max-width: none;
        width: 100%;
        border-radius: 0
    }

        #cookies_agreement.bottom > .content {
            width: 100%;
            flex: none
        }

        #cookies_agreement.bottom > .button {
            margin-top: 15px
        }
}

#real_time_order {
    height: 100px;
    overflow: hidden;
    background-color: #fff;
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 9999;
    transition: opacity .3s ease-in-out;
    opacity: 0
}

    #real_time_order .pro_image {
        width: 100px;
        height: 100px;
        float: left;
        text-align: center
    }

        #real_time_order .pro_image > a {
            display: block;
            width: inherit;
            height: inherit
        }

            #real_time_order .pro_image > a > img {
                max-width: 100%;
                max-height: 100%;
                opacity: 1
            }

    #real_time_order .content {
        width: 270px;
        line-height: 18px;
        margin-right: 22px;
        margin-left: 11px;
        padding-top: 4px;
        float: left
    }

        #real_time_order .content .buy_info {
            width: 80%;
            max-height: 36px;
            overflow: hidden
        }

        #real_time_order .content .title {
            height: 36px;
            overflow: hidden;
            margin-top: 1px;
            font-size: 13px;
            font-weight: 800
        }

        #real_time_order .content .close {
            width: 12px;
            height: 12px;
            overflow: hidden;
            background: url(/assets/images/cart/icon_shopping_close.png) no-repeat center/100%;
            position: absolute;
            top: 10px;
            right: 10px;
            text-indent: 99px
        }

@media screen and (max-width:750px) {
    #real_time_order {
        padding: 14px 11px;
        width: calc(100% - 40px);
        height: 100px;
        overflow: hidden;
        background-color: #fff;
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 10019;
        transition: opacity .3s ease-in-out;
        opacity: 0;
        border-radius: 6px;
        box-shadow: 0 3px 7px rgb(0 0 0 / 30%);
        box-sizing: border-box
    }

        #real_time_order .pro_image {
            width: 75px;
            height: 75px;
            float: left;
            text-align: center
        }

            #real_time_order .pro_image > a {
                display: block;
                width: inherit;
                height: inherit
            }

                #real_time_order .pro_image > a > img {
                    max-width: 100%;
                    max-height: 100%
                }

        #real_time_order .content {
            width: 205px;
            line-height: 18px;
            margin-right: 22px;
            margin-left: 12px;
            float: left
        }

            #real_time_order .content .buy_info {
                width: 100%;
                max-height: 18px;
                overflow: hidden
            }

            #real_time_order .content .title {
                height: 36px;
                overflow: hidden;
                margin-top: 1px;
                font-size: 13px;
                font-weight: 800
            }

            #real_time_order .content .close {
                width: 12px;
                height: 12px;
                overflow: hidden;
                background: url(/assets/images/cart/icon_shopping_close.png) no-repeat center/100%;
                position: absolute;
                top: 10px;
                right: 10px;
                text-indent: 99px
            }
}

@media screen and (max-width:375px) {
    #real_time_order {
        left: 20px;
        transform: translateX(0)
    }

        #real_time_order .content {
            width: calc(100% - 75px - 34px)
        }
}

#turn_page {
    text-align: center;
    height: 26px;
    line-height: 26px;
    clear: both
}

    #turn_page li {
        display: inline-block;
        font-size: 14px;
        height: 24px;
        line-height: 24px;
        background: #fff;
        vertical-align: top;
        margin: 0 4px;
        border-radius: 12px;
        -ms-border-radius: 12px;
        -moz-border-radius: 12px;
        -webkit-border-radius: 12px;
        box-sizing: border-box;
        overflow: hidden
    }

        #turn_page li a:hover {
            background-color: #fff
        }

        #turn_page li em {
            width: 100%;
            height: 100%;
            display: block;
            position: relative
        }

        #turn_page li span {
            color: #959595
        }

        #turn_page li span {
            color: #959595
        }

    #turn_page a, #turn_page font {
        min-width: 24px;
        height: 24px;
        display: block;
        text-align: center;
        text-decoration: none
    }

    #turn_page .page_item_current {
        background: #333;
        color: #fff;
        cursor: default
    }

    #turn_page .bigger_item {
        padding: 0 5px
    }

    #turn_page .page_button, #turn_page .page_noclick {
        width: auto
    }

    #turn_page .page_noclick {
        cursor: no-drop
    }

.icon_page_prev::before {
    content: '\e63c';
    font-family: iconfont;
    font-size: 12px;
    color: #000
}

.icon_page_next::before {
    content: '\e641';
    font-family: iconfont;
    font-size: 12px;
    color: #000
}

.page_noclick .icon_page_prev::before {
    color: #cdcdcd
}

.page_noclick .icon_page_next::before {
    color: #cdcdcd
}

@media (max-width:768px) {
    #turn_page .page_button, #turn_page .page_noclick {
        width: 8px;
        position: relative
    }

    .icon_page_prev {
        margin-right: 0
    }

    .icon_page_next {
        margin-left: 0
    }
}

.loading {
    z-index: 100;
    position: absolute;
    background: #ccc;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.loading_msg {
    z-index: 9999;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
    cursor: wait
}

.loading_big {
    background: url(../images/global/loading.gif) no-repeat;
    width: 32px;
    height: 32px
}

.loading_small {
    background: url(../images/global/loading_small.gif) no-repeat;
    width: 14px;
    height: 14px
}

.global-button-loading {
    position: relative;
    text-indent: -9999px
}

    .global-button-loading:after {
        content: '';
        display: block;
        width: 18px;
        height: 18px;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        border-radius: 50%;
        border: 3px solid #fff;
        border-top-color: transparent;
        animation: spin 1s infinite linear
    }

#loading {
    display: none;
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: 0 0;
    transition: all .3s ease-in-out;
    z-index: 99
}

    #loading .loading_box {
        position: absolute;
        display: block;
        width: 12rem;
        height: 12rem;
        line-height: 12rem;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
        text-align: center;
        background: rgba(0,0,0,.8);
        border-radius: 10px
    }

        #loading .loading_box .loading {
            background-color: transparent
        }

.review_star {
    font-size: 0;
    text-align: center;
    display: inline-block;
    vertical-align: middle
}

    .review_star span {
        display: inline-block;
        vertical-align: middle;
        position: relative
    }

    .review_star .half_star:after {
        content: "\e632";
        width: 50%;
        overflow: hidden;
        position: absolute;
        top: 0;
        left: 0
    }

    .review_star .star_0 {
        display: inline-block;
        color: #ccc
    }

.review_star_s span {
    font-size: 12px;
    margin-right: 3px
}

.review_star_m span {
    font-size: 14px;
    margin-right: 5px
}

.review_star_l span {
    font-size: 16px;
    margin-right: 5px
}

.review_star_xl span {
    font-size: 21px;
    margin-right: 8px
}

.input_box {
    position: relative;
    display: flex;
    flex: 1
}

    .input_box .input_box_label {
        max-width: 80%;
        height: 18px;
        line-height: 18px;
        padding: 0 5px;
        font-size: 13px;
        font-weight: 400;
        color: #666;
        background-color: #fff;
        position: absolute;
        top: 4px;
        left: 11px;
        z-index: 10;
        pointer-events: none;
        opacity: 0;
        transform: translateY(3px);
        -webkit-transform: translateY(3px);
        transition-property: opacity,-webkit-transform;
        -webkit-transition-property: opacity,-webkit-transform;
        transition-property: opacity,transform;
        transition-property: opacity,transform,-webkit-transform;
        transition-duration: 150ms;
        -webkit-transition-duration: 150ms;
        overflow: hidden
    }

        .input_box .input_box_label.zi20 {
            z-index: 20
        }

    .input_box .input_box_txt {
        width: 100%;
        height: 22px;
        padding: 11px 16px;
        font-size: 13px;
        color: #333;
        background-color: #fff;
        border: 1px #d9d9d9 solid;
        border-radius: 5px;
        position: relative;
        display: inline-block;
        transition: all 150ms;
        -webkit-transition: all 150ms
    }

        .input_box .input_box_txt:focus {
            border-color: #666;
            box-shadow: 0 0 2px #666
        }

    .input_box .input_box_textarea {
        height: 60px
    }

    .input_box.filled .input_box_txt {
        padding-top: 22px;
        height: 18px;
        line-height: 18px;
        padding-bottom: 4px
    }

    .input_box.filled .input_box_label {
        opacity: 1;
        transform: translateY(0);
        -webkit-transform: translateY(0)
    }

    .input_box.filled .input_box_textarea {
        height: 56px
    }

.box_select {
    width: calc(100% - 2px);
    height: 44px;
    line-height: 44px;
    background-color: #fff;
    border: 1px #e4e4e4 solid;
    border-radius: 5px;
    position: relative
}

    .box_select .select_box_label {
        max-width: 80%;
        height: 18px;
        line-height: 18px;
        padding: 0 5px;
        font-size: 13px;
        font-weight: 400;
        color: #666;
        background-color: #fff;
        position: absolute;
        top: 4px;
        left: 11px;
        z-index: 10;
        pointer-events: none;
        opacity: 0;
        transform: translateY(3px);
        -webkit-transform: translateY(3px);
        transition-property: opacity,-webkit-transform;
        -webkit-transition-property: opacity,-webkit-transform;
        transition-property: opacity,transform;
        transition-property: opacity,transform,-webkit-transform;
        transition-duration: 150ms;
        -webkit-transition-duration: 150ms;
        overflow: hidden
    }

    .box_select::before {
        content: '\e6bd';
        width: 30px;
        line-height: 44px;
        text-align: center;
        font-size: 12px;
        font-family: iconfont;
        color: #919191;
        transform: scale(.75);
        position: absolute;
        right: 0;
        z-index: 1
    }

    .box_select > select {
        width: 100%;
        height: 44px;
        background: 0 0;
        border: 0;
        outline: 0;
        padding: 0 16px;
        appearance: none;
        -moz-appearance: none;
        -webkit-appearance: none;
        -ms-appearance: none;
        font-size: 13px;
        position: relative;
        z-index: 2
    }

        .box_select > select::-ms-expand {
            display: none
        }

        .box_select > select > option {
            line-height: 1.6;
            font-size: 16px
        }

    .box_select.filled > select {
        padding-top: 22px;
        padding-bottom: 4px
    }

    .box_select.filled .select_box_label {
        opacity: 1;
        transform: translateY(0);
        -webkit-transform: translateY(0)
    }

.follow_us_list {
    min-height: 40px;
    padding: 0
}

    .follow_us_list li {
        width: 40px;
        margin-right: 10px;
        display: inline-block
    }

        .follow_us_list li > a {
            width: inherit;
            height: 40px;
            display: block;
            line-height: 40px;
            white-space: nowrap;
            text-indent: -999px;
            overflow: hidden;
            transition: all .2s ease-in-out;
            -webkit-transition: all .2s ease-in-out;
            position: relative
        }

            .follow_us_list li > a > i {
                position: absolute;
                top: 0;
                left: 0;
                text-indent: 0;
                font-size: 32px;
                line-height: normal
            }

            .follow_us_list li > a:focus, .follow_us_list li > a:hover {
                transform: translateY(-4px);
                -webkit-transform: translateY(-4px)
            }

#shopbox {
    width: auto;
    margin-left: -29pc;
    overflow: hidden;
    background: 0 0;
    position: fixed;
    top: 0;
    bottom: 0;
    margin-top: auto;
    margin-bottom: auto;
    left: 50%;
    z-index: 99999
}

    #shopbox .shopbox_close {
        position: absolute;
        right: 17px;
        top: 16px;
        width: 14px;
        height: 14px;
        overflow: hidden;
        cursor: pointer;
        background: url(//ueeshop.ly200-cdn.com/static/v0/themes/operation_activities/newsletter/popup/mode_2/icon.png) no-repeat center/100%;
        border: 0
    }

        #shopbox .shopbox_close > span {
            display: none
        }

    #shopbox .shopbox_wrap {
        width: 980px;
        overflow: hidden
    }

    #shopbox .shopbox_skin {
        width: auto;
        height: auto;
        padding: 0;
        background: url(../images/global/loading_oth.gif) no-repeat center center #fff;
        border-radius: 2px;
        -webkit-border-radius: 2px;
        box-shadow: 0 10px 25px rgba(0,0,0,.5);
        -ms-box-shadow: 0 10px 25px rgba(0,0,0,.5);
        -o-box-shadow: 0 10px 25px rgba(0,0,0,.5);
        -webkit-box-shadow: 0 10px 25px rgba(0,0,0,.5)
    }

    #shopbox .shopbox_inner {
        height: 628px
    }

    #shopbox .shopbox_frame {
        display: block;
        width: 100%;
        height: 100%
    }

    #shopbox[data-type=wide] {
        margin-left: -37.5pc
    }

        #shopbox[data-type=wide] .shopbox_wrap {
            width: 1200px
        }

        #shopbox[data-type=wide] .shopbox_inner {
            height: 720px
        }

.window_cover {
    display: none;
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 100;
    background: rgba(13,22,35,.7);
    transition: all .3s ease-in-out
}

.window_popup {
    max-width: 100%;
    padding: 2.5rem 1rem 2rem;
    box-sizing: border-box;
    z-index: 11000;
    background-color: #fff
}

.window_popup_bottom {
    position: fixed;
    bottom: 0;
    width: 100%;
    max-height: 50rem;
    overflow-y: scroll;
    padding-top: 5rem;
    padding-bottom: 2rem;
    transform: translateY(100%);
    transition: all .3s linear
}

.window_popup_bottom_show {
    bottom: 0;
    transform: translateY(0);
    transition: all .3s linear
}

.window_popup_close {
    position: absolute;
    top: .8rem;
    right: 1rem;
    width: 1rem;
    height: 1rem;
    line-height: 1rem;
    text-align: center;
    font-size: .9rem;
    color: #0b0b0b;
    font-family: iconfont !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #fff
}

    .window_popup_close::before {
        content: "\e631"
    }

#product_detail_quick_view {
    height: calc(100% - 10rem);
    padding: 2.5rem 1rem 0;
    max-height: unset;
    overflow: hidden
}

    #product_detail_quick_view .window_popup_wrap {
        height: 100%
    }

    #product_detail_quick_view .prod_info_form {
        height: 100%;
        overflow-y: hidden;
        flex-wrap: nowrap;
        flex-direction: column
    }

    #product_detail_quick_view .product_detail_header {
        display: flex;
        margin-bottom: .5rem;
        justify-content: flex-start;
        align-items: flex-start
    }

    #product_detail_quick_view .product_detail_image {
        width: 6.5rem;
        height: 6.5rem;
        background-color: #f5f5f5
    }

        #product_detail_quick_view .product_detail_image .product_detail_image_item {
            display: none;
            position: relative;
            width: 6.5rem;
            height: 6.5rem;
            text-align: center
        }

            #product_detail_quick_view .product_detail_image .product_detail_image_item > a {
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                margin: auto
            }

            #product_detail_quick_view .product_detail_image .product_detail_image_item:first-child {
                display: inline-block
            }

    #product_detail_quick_view .product_detail_info {
        width: calc(100% - 6.5rem);
        min-height: 6.5rem;
        padding: 0 .9rem;
        background-color: #f5f5f5
    }

        #product_detail_quick_view .product_detail_info .product_detail_name {
            line-height: 1.1rem;
            padding: .6rem 0
        }

        #product_detail_quick_view .product_detail_info .themes_products_title {
            display: -webkit-box;
            overflow: hidden;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical
        }

        #product_detail_quick_view .product_detail_info .product_detail_price {
            line-height: 1.5rem;
            padding: .6rem 0
        }

            #product_detail_quick_view .product_detail_info .product_detail_price .prod_info_currency {
                margin-right: .3125rem;
                padding-top: 0;
                padding-bottom: 0;
                vertical-align: top
            }

            #product_detail_quick_view .product_detail_info .product_detail_price .themes_products_price {
                margin-right: .3125rem;
                font-size: 1.125rem
            }

            #product_detail_quick_view .product_detail_info .product_detail_price .themes_products_sales {
                font-size: .75rem
            }

    #product_detail_quick_view .product_detail_content {
        position: relative;
        flex: 1;
        height: calc(100% - 7rem);
        padding-bottom: 2rem;
        overflow-y: scroll;
        overflow-x: hidden;
        box-sizing: border-box
    }

#float_chat {
    position: fixed;
    top: 150px;
    z-index: 1000
}

    #float_chat .inner_chat {
        min-height: 90px
    }

#go_top {
    display: block;
    height: 24px;
    line-height: 24px;
    color: #fff;
    text-align: center;
    font-size: 14px
}

    #go_top:hover {
        text-decoration: none
    }

.chat_box {
    margin: 0 auto
}

    .chat_box > a {
        display: block;
        text-decoration: none
    }

        .chat_box > a > span {
            display: none
        }

.message_us {
    position: fixed;
    right: 5vw;
    bottom: 3vw;
    width: 128px;
    z-index: 88
}

.editor_txt {
    overflow: hidden;
    line-height: 1.6;
    background: #fff;
    font-size: 13px
}

    .editor_txt * {
        font-family: inherit
    }

    .editor_txt h1, .editor_txt h2, .editor_txt h3, .editor_txt h4, .editor_txt h5, .editor_txt h6 {
        font-weight: 400;
        line-height: 1.2;
        margin: 1em 0
    }

    .editor_txt b, .editor_txt strong {
        font-weight: 700;
        line-height: inherit
    }

    .editor_txt h1 {
        font-size: 2em
    }

    .editor_txt h2 {
        font-size: 1.5em
    }

    .editor_txt h3 {
        font-size: 1.17em
    }

    .editor_txt h4 {
        font-size: 1em;
        margin: 1.33em 0
    }

    .editor_txt h5 {
        font-size: .83em
    }

    .editor_txt h6 {
        font-size: .67em
    }

    .editor_txt li {
        list-style-type: inherit
    }

    .editor_txt cite, .editor_txt em, .editor_txt i {
        font-style: italic
    }

    .editor_txt span, .editor_txt strong {
        color: inherit;
        font-size: inherit;
        font-family: inherit
    }

    .editor_txt p {
        margin: 1em 0
    }

    .editor_txt ul {
        margin-block-start: 1em;
        padding-inline-start: 20px
    }

    .editor_txt img {
        opacity: inherit
    }

    .editor_txt .selectTdClass {
        background-color: #edf5fa !important
    }

    .editor_txt table.noBorderTable caption, .editor_txt table.noBorderTable td, .editor_txt table.noBorderTable th {
        border: 1px #ddd dashed !important
    }

    .editor_txt table {
        border-collapse: collapse;
        display: table;
        border-spacing: 0
    }

        .editor_txt table:not([cellpadding]) td, .editor_txt table:not([cellpadding]) th {
            padding: .4rem
        }

        .editor_txt table:not([border="0"]):not([style*=border-width]) td, .editor_txt table:not([border="0"]):not([style*=border-width]) th {
            border-width: 1px
        }

        .editor_txt table:not([border="0"]):not([style*=border-style]) td, .editor_txt table:not([border="0"]):not([style*=border-style]) th {
            border-style: solid
        }

        .editor_txt table:not([border="0"]):not([style*=border-color]) td, .editor_txt table:not([border="0"]):not([style*=border-color]) th {
            border-color: #ccc
        }

    .editor_txt caption {
        border: 1px #ddd dashed;
        border-bottom: 0;
        padding: 3px;
        text-align: center
    }

    .editor_txt table tr.firstRow th {
        border-top-width: 2px
    }

    .editor_txt table td, .editor_txt table th {
        min-width: 2em;
        padding: .4em
    }

    .editor_txt .ue-table-interlace-color-single {
        background-color: #fcfcfc
    }

    .editor_txt .ue-table-interlace-color-double {
        background-color: #f7faff
    }

    .editor_txt .pagebreak {
        display: block;
        clear: both !important;
        cursor: default !important;
        width: 100% !important;
        margin: 0
    }

    .editor_txt pre {
        margin: .5em 0;
        padding: .4em .6em;
        border-radius: 8px;
        background: #f8f8f8
    }

    .editor_txt u {
        text-decoration: underline
    }

    .editor_txt blockquote {
        font-style: italic;
        font-family: Georgia,Times,"Times New Roman",serif;
        padding: 2px 8px 2px 20px;
        border-style: solid;
        border-color: #ccc;
        border-width: 0;
        border-left-width: 5px
    }

    .editor_txt ol {
        margin-left: 35px
    }

        .editor_txt ol li {
            list-style: decimal
        }

    .editor_txt .marker-yellow {
        background-color: #fdfd77
    }

    .editor_txt .marker-pink {
        background-color: #fc7899
    }

    .editor_txt .marker-green {
        background-color: #62f962
    }

    .editor_txt .marker-blue {
        background-color: #72ccfd
    }

    .editor_txt .pen-red {
        background-color: transparent;
        color: #e71313
    }

    .editor_txt .pen-green {
        background-color: transparent;
        color: #128a00
    }

    .editor_txt iframe {
        max-width: 100%
    }

@media screen and (max-width:768px) {
    .editor_txt table {
        table-layout: fixed;
        word-wrap: break-word;
        overflow: hidden
    }

        .editor_txt table td {
            word-break: break-all;
            word-wrap: break-word
        }
}

.editor_txt .editor_table_wrap {
    width: 100%;
    overflow: hidden;
    overflow-x: auto;
    padding-bottom: 10px
}

    .editor_txt .editor_table_wrap::-webkit-scrollbar-track-piece {
        background-color: #aaa;
        border-left: 1px solid transparent;
        border-radius: 5px
    }

    .editor_txt .editor_table_wrap::-webkit-scrollbar {
        width: 5px;
        height: 5px;
        border-radius: 5px
    }

    .editor_txt .editor_table_wrap::-webkit-scrollbar-thumb {
        background-color: rgba(0,0,0,.7);
        background-clip: padding-box;
        border-radius: 5px;
        min-height: 28px
    }

        .editor_txt .editor_table_wrap::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0,0,0,.5);
            border-radius: 5px
        }

#dituContent img {
    max-width: inherit;
    max-height: inherit
}

.partner {
    text-align: center
}

    .partner .partners_box {
        margin-bottom: 22px
    }

.partners_box a {
    display: inline-block;
    margin: 5px
}

.partners_box img {
    max-width: 200px;
    max-height: 32px;
    vertical-align: middle
}

.agreement_outer {
    display: block;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,.5);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    transition: opacity .3s ease-in-out;
    opacity: 1
}

    .agreement_outer .agreement_in {
        width: 80%;
        height: 90%;
        background: #fff;
        padding: 40px 20px 20px 20px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
        -webkit-transform: translate(-50%,-50%);
        box-sizing: border-box;
        -webkit-box-sizing: border-box
    }

        .agreement_outer .agreement_in .choose_close {
            position: absolute;
            z-index: 100000;
            text-indent: -999px;
            width: 20px;
            height: 20px;
            background: url(/assets/images/cart/icon_shopping_close.png) no-repeat center center;
            top: 16px;
            right: 20px;
            border: none
        }

    .agreement_outer .agreement_content {
        width: 100%;
        height: 100%;
        overflow-y: auto
    }

    .agreement_outer .agreement_in .agreement_content::-webkit-scrollbar {
        width: 5px;
        background: #fff;
        border-radius: 5px
    }

    .agreement_outer .agreement_in .agreement_content::-webkit-scrollbar-thumb {
        background: rgba(0,0,0,.1);
        border-radius: 5px
    }

        .agreement_outer .agreement_in .agreement_content::-webkit-scrollbar-thumb:hover {
            background: rgba(0,0,0,.3)
        }

    .agreement_outer .agreement_content .title {
        width: 100%;
        height: 50px;
        line-height: 50px;
        margin: 0 auto;
        padding-bottom: 30px;
        text-align: center;
        font-size: 30px;
        font-weight: 600
    }

    .agreement_outer .agreement_content .wrap {
        width: 100%;
        margin: 0 auto
    }

@media (max-width:999px) {
    .agreement_outer .agreement_in {
        width: 100%;
        height: 100%
    }
}

i.icon_heart {
    width: 18px;
    height: 18px;
    background: url(../images/global/normal.png) no-repeat -394px -116px;
    display: inline-block;
    vertical-align: top
}

.is_in i.icon_heart {
    background-position: -678px -70px
}

.right_main {
    border: 1px #ebebeb solid
}

.main_title {
    height: 28px;
    line-height: 28px;
    border-bottom: 1px #ececec solid;
    background: #f4f4f4;
    color: #333;
    padding-left: 20px;
    font-size: 14px
}

.main_content {
    padding: 10px;
    min-height: 300px
}

.info_list li {
    height: 30px;
    line-height: 30px;
    overflow: hidden;
    padding: 0 6px;
    border-bottom: 1px #ececec dashed
}

    .info_list li .time {
        float: right
    }

.prod_price .pp_inputbox {
    display: inline-block;
    height: 21px;
    padding: 2px 0 0 5px;
    border: 1px #ddd solid;
    background: #fff;
    border-radius: 3px;
    -ms-border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px
}

.prod_price em {
    display: inline-block;
    line-height: 19px;
    margin-right: 3px
}

.prod_price .pp_heng {
    line-height: 24px;
    height: auto
}

.prod_price input {
    display: inline-block;
    width: 32px;
    height: 19px;
    line-height: 19px;
    border: 0;
    color: #555;
    font-size: 12px;
    background: #fff;
    outline: 0
}

.prod_price .pp_btn {
    display: inline-block;
    margin-left: 3px;
    border: 1px #ddd solid;
    background: #eee;
    cursor: default;
    width: 40px;
    height: 24px;
    font-weight: 700;
    border-radius: 3px;
    -ms-border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px
}

    .prod_price .pp_btn:hover {
        background: #f3f3f3
    }

.share_toolbox {
    padding: 0
}

    .share_toolbox li {
        width: 30px;
        margin-right: 20px;
        display: inline-block;
        vertical-align: top
    }

        .share_toolbox li a {
            width: inherit;
            height: 30px;
            display: block;
            line-height: 30px;
            white-space: nowrap;
            text-indent: -999px;
            overflow: hidden;
            background-image: url(../images/global/icon_follow_detail_cur.png);
            background-repeat: no-repeat;
            transition: all .2s ease-in-out;
            -webkit-transition: all .2s ease-in-out;
            background-size: 30px auto
        }

            .share_toolbox li a:hover {
                background-image: url(../images/global/icon_follow_detail.png)
            }

    .share_toolbox .share_s_more_box {
        position: relative;
        z-index: 1
    }

        .share_toolbox .share_s_more_box:hover .share_hover {
            display: block
        }

    .share_toolbox .share_hover {
        display: none;
        position: absolute;
        top: 40px;
        left: -14px;
        background: #fff;
        border: 1px solid #f5f5f5;
        box-shadow: 0 0 5px rgba(0,0,0,.1);
        padding: 14px;
        border-radius: 5px
    }

        .share_toolbox .share_hover a {
            height: 32px;
            line-height: 32px;
            font-size: 14px;
            width: auto;
            display: block;
            text-indent: 37px;
            text-align: left;
            text-transform: capitalize;
            background-size: 32px auto;
            color: #555;
            padding-right: 10px
        }

.share_s_facebook {
    background-position: 0 0
}

.share_s_twitter {
    background-position: 0 -30px
}

.share_s_youtube {
    background-position: 0 -120px
}

.share_s_googleplus {
    background-position: 0 -150px
}

.share_s_vk {
    background-position: 0 -180px
}

.share_s_pinterest {
    background-position: 0 -60px
}

.share_s_more {
    background-position: 0 -520px
}

.share_s_instagram {
    background-position: 0 -224px
}

.share_s_linkedin {
    background-position: 0 -90px
}

.share_s_google {
    background-position: 0 -384px
}

.share_s_digg {
    background-position: 0 -256px
}

.share_s_reddit {
    background-position: 0 -288px
}

.share_s_stumbleupon {
    background-position: 0 -320px
}

.share_s_delicious {
    background-position: 0 -352px
}

.share_s_line {
    background-position: 0 -420px
}

.share_s_whatsapp {
    background-position: 0 -450px
}

.share_s_tumblr {
    background-position: 0 -480px
}

@media (max-width:768px) {
    .share_toolbox li {
        width: 20px;
        margin-right: 10px
    }

        .share_toolbox li a {
            height: 20px;
            line-height: 20px;
            background-size: 20px auto
        }

            .share_toolbox li a:hover {
                background-size: 20px auto
            }

    .share_s_twitter {
        background-position: 0 -20px
    }

    .share_s_pinterest {
        background-position: 0 -40px
    }

    .share_s_linkedin {
        background-position: 0 -60px
    }

    .share_s_line {
        background-position: 0 -280px
    }

    .share_s_whatsapp {
        background-position: 0 -300px
    }

    .share_s_tumblr {
        background-position: 0 -320px
    }

    .share_s_line {
        background-position: 0 -280px
    }

    .share_s_whatsapp {
        background-position: 0 -300px
    }

    .share_s_tumblr {
        background-position: 0 -320px
    }
}

body.error_page {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

#error_page {
    padding: 150px 0;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    text-align: center
}

    #error_page .error_page_logo img {
        max-height: 130px
    }

    #error_page .error_warning {
        display: inline-block;
        min-width: 0;
        margin: 30px;
        padding: 5px 10px;
        line-height: 30px;
        font-size: 20px
    }

    #error_page .error_nav {
        margin-bottom: 80px;
        text-align: center
    }

        #error_page .error_nav > a {
            display: inline-block;
            margin: 0 30px;
            padding: 0 50px;
            font-size: 16px;
            height: 38px;
            line-height: 38px;
            border: 1px solid #2c61dd;
            border-radius: 100px;
            cursor: pointer
        }

        #error_page .error_nav > .error_nav_home {
            color: #fff;
            background-color: #2c61dd
        }

    #error_page .error_logo {
        background: url(../images/global/error_logo.png) no-repeat center center;
        height: 385px;
        opacity: .1
    }

.blank_temp_hostname .inner {
    width: 100vw;
    text-align: center;
    box-sizing: border-box;
    background: #fff
}

.blank_temp_hostname .icon {
    display: block;
    min-width: 300px;
    width: 100%;
    height: 150px;
    margin: 0 auto;
    background: rgba(0,0,0,0) url(../images/global/icon_hostname.png) no-repeat center
}

.blank_temp_hostname .msg {
    margin: 30px auto;
    font-size: 24px;
    line-height: 1.7
}

.blank_temp_hostname a {
    color: #0bba91;
    position: relative;
    display: inline-block;
    margin: 0 2px
}

    .blank_temp_hostname a::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 15%;
        display: block;
        width: 100%;
        height: 1px;
        background-color: #0bba91
    }

    .blank_temp_hostname a:hover {
        text-decoration: none
    }

@media (max-width:999px) {
    #error_page {
        width: 100%;
        padding: 0
    }

        #error_page .error_logo {
            height: 160px;
            background-size: contain
        }

        #error_page .error_warning {
            margin: 33px 0 18px;
            padding: 5px 10px;
            line-height: 30px;
            font-size: 16px
        }

        #error_page .error_nav {
            margin-bottom: 50px
        }

            #error_page .error_nav > a {
                height: 30px;
                line-height: 30px;
                margin: 0 15px;
                padding: 0 30px;
                font-size: 12px
            }

    .blank_temp_hostname .inner {
        padding: 0 20px
    }

    .blank_temp_hostname .icon {
        background-size: 65%
    }

    .blank_temp_hostname .msg {
        margin: 20px auto;
        font-size: 20px;
        line-height: 1.4
    }
}

.default_nav_style li {
    float: left
}

    .default_nav_style li > a {
        display: block;
        padding: 0 20px
    }

        .default_nav_style li > a .iconfont {
            display: inline-block
        }

.header .default_nav .nav_sec {
    display: none;
    width: 100%;
    position: absolute;
    left: 0;
    top: 100%;
    opacity: 0;
    padding-top: 1px;
    z-index: 10006;
    overflow-x: hidden;
    overflow-y: auto;
    max-height: 460px;
    margin-top: 30px;
    box-shadow: 5px 5px 10px -5px rgba(0,0,0,.12);
    background: #fff
}

.header .default_nav .small_nav_sec {
    border-bottom: none;
    width: 260px;
    padding-top: 1px;
    max-height: none;
    overflow: visible
}

.header .default_nav .nav_sec .top {
    height: 0
}

.header .default_nav .nav_sec .nav_list {
    position: relative;
    float: left
}

.header .default_nav .small_nav_sec .nav_list {
    float: none
}

.header .default_nav .nav_sec .nav_list:after {
    content: '';
    display: block;
    width: 100%;
    clear: both
}

.header .default_nav .nav_sec .nav_sec_box {
    min-height: 200px;
    padding: 7px 20px 12px;
    background: #fff
}

.header .default_nav .small_nav_sec .nav_sec_box {
    background: #fff;
    min-height: 0;
    padding: 13px 0 17px;
    box-shadow: 0 0 10px rgba(0,0,0,.12)
}

.header .default_nav .nav_sec .nav_sec_item {
    float: left;
    margin: 5px 10px 20px;
    width: 160px;
    position: relative
}

    .header .default_nav .nav_sec .nav_sec_item dt a {
        color: var(--ThemesNavLevel2TextColor);
        font-size: 14px;
        display: block;
        height: 22px;
        line-height: 22px;
        padding: 7px 0;
        overflow: hidden;
        -ms-text-overflow: ellipsis;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: left;
        padding: 0
    }

.header .default_nav .small_nav_sec .nav_sec_item {
    position: relative;
    width: 100%;
    margin: 0
}

    .header .default_nav .small_nav_sec .nav_sec_item .nav_icon {
        margin-right: 10px;
        display: inline-block;
        vertical-align: top;
        max-width: 26px;
        height: 100%;
        max-height: 20px;
        line-height: normal
    }

        .header .default_nav .small_nav_sec .nav_sec_item .nav_icon span {
            height: 100%;
            display: inline-block;
            vertical-align: middle
        }

    .header .default_nav .small_nav_sec .nav_sec_item a, .header .default_nav .small_nav_sec .nav_sec_item dt a {
        position: relative;
        display: block;
        height: 20px;
        line-height: 20px;
        padding: 5px 20px;
        overflow: hidden;
        -ms-text-overflow: ellipsis;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 14px
    }

.header .default_nav .full_nav_style .small_nav_sec .nav_sec_item a, .header .default_nav .full_nav_style .small_nav_sec .nav_sec_item dt a {
    padding-left: 0;
    padding-right: 0
}

.header .default_nav .small_nav_sec .nav_third_item {
    width: 100%;
    position: relative
}

.header .default_nav .small_nav_sec .nav_sec_item.has_third a.nav_sec_a em {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 10px;
    width: 7px;
    height: 11px;
    margin: auto;
    background: url(../images/global/icon_down_has.png) no-repeat center top
}

.header .default_nav .small_nav_sec .nav_third_item.has_four a.nav_third_a em {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 10px;
    width: 7px;
    height: 11px;
    margin: auto;
    background: url(../images/global/icon_down_has.png) no-repeat center top
}

.header .default_nav .small_nav_sec .nav_third_item.has_fourth a.nav_third_a em {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 10px;
    width: 7px;
    height: 11px;
    margin: auto;
    background: url(../images/global/icon_down_has.png) no-repeat center top
}

.header .default_nav .full_nav_style .small_nav_sec .nav_sec_item.has_third a em {
    display: none
}

.header .default_nav .small_nav_sec .nav_sec_item a:hover {
    color: var(--ThemesNavLevel2TextHoverColor)
}

    .header .default_nav .small_nav_sec .nav_sec_item a:hover em {
        background-position: center bottom
    }

.header .default_nav .nav_sec .nav_sec_item .nav_third_a {
    color: var(--ThemesNavLevel3TextColor)
}

    .header .default_nav .nav_sec .nav_sec_item .nav_third_a:hover {
        color: var(--ThemesNavLevel3TextHoverColor)
    }

.header .default_nav .nav_sec .nav_sec_item .nav_four_a {
    color: var(--ThemesNavLevel4TextColor)
}

    .header .default_nav .nav_sec .nav_sec_item .nav_four_a:hover {
        color: var(--ThemesNavLevel4TextHoverColor)
    }

.default_nav .small_nav_sec .nav_thd_list {
    padding: 0;
    position: absolute;
    left: 100%;
    top: 0;
    display: none;
    width: 260px;
    padding: 13px 0 17px;
    border: 1px solid #f5f5f5;
    box-shadow: 0 0 10px rgba(0,0,0,.12);
    background: #fff
}

.default_nav .small_nav_sec .nav_thd_item {
    position: relative
}

.default_nav .small_nav_sec .nav_sec_item.has_third:hover .nav_thd_list {
    display: block
}

.default_nav .small_nav_sec .nav_thd_item:hover .nav_four_item {
    display: block
}

.default_nav .nav_sec .more_box {
    display: inline-block;
    position: relative
}

    .default_nav .nav_sec .more_box .nav_four_item {
        position: absolute;
        bottom: -15px;
        left: 100%;
        margin: auto;
        background: #fff;
        padding: 15px;
        border-radius: 4px;
        box-shadow: 0 0 10px rgba(0,0,0,.12)
    }

        .default_nav .nav_sec .more_box .nav_four_item a {
            display: block;
            height: 18px;
            line-height: 18px;
            padding: 3px 0;
            font-size: 12px;
            color: #888;
            white-space: nowrap
        }

    .default_nav .nav_sec .more_box:hover .nav_four_item {
        display: block
    }

.default_nav .nav_sec .more {
    color: #999;
    font-size: 12px;
    line-height: 30px
}

.default_nav .nav_sec .nav_img {
    font-size: 0;
    text-align: left;
    white-space: normal
}

    .default_nav .nav_sec .nav_img .imgl {
        display: inline-block;
        vertical-align: top;
        max-width: 150px;
        max-height: 150px;
        margin: 10px 10px 0 0;
        text-align: center
    }

        .default_nav .nav_sec .nav_img .imgl img {
            max-width: 100%;
            max-height: 100%
        }

.default_nav .nav_sec_item .nav_third_box {
    display: none;
    position: absolute;
    width: 260px;
    top: 0;
    left: 100%;
    background-color: #fff;
    max-height: 50vw;
    box-shadow: 0 0 10px rgba(0,0,0,.12)
}

    .default_nav .nav_sec_item .nav_third_box::-webkit-scrollbar {
        width: 5px;
        background: #fff;
        border-radius: 5px
    }

    .default_nav .nav_sec_item .nav_third_box::-webkit-scrollbar-thumb {
        border-radius: 5px;
        background-color: rgba(0,0,0,.1)
    }

        .default_nav .nav_sec_item .nav_third_box::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0,0,0,.3)
        }

    .default_nav .nav_sec_item .nav_third_box .nav_four_box {
        display: none;
        position: absolute;
        top: 0;
        left: 100%;
        z-index: 1;
        width: 260px;
        background-color: #fff;
        box-shadow: 0 0 10px rgba(0,0,0,.12)
    }

.default_nav .full_nav_style .nav_sec {
    top: auto;
    width: 100%;
    max-width: 1920px;
    box-sizing: border-box;
    border-top: 1px solid #f2f2f2;
    max-height: 630px;
    overflow-y: auto
}

    .default_nav .full_nav_style .nav_sec::-webkit-scrollbar {
        width: 5px;
        background: #fff;
        border-radius: 5px
    }

    .default_nav .full_nav_style .nav_sec::-webkit-scrollbar-thumb {
        background: rgba(0,0,0,.3);
        border-radius: 5px
    }

        .default_nav .full_nav_style .nav_sec::-webkit-scrollbar-thumb:hover {
            background: rgba(0,0,0,.5)
        }

.default_nav .full_nav_style .small_nav_sec .nav_four_box, .default_nav .full_nav_style .small_nav_sec .nav_sec_box, .default_nav .full_nav_style .small_nav_sec .nav_third_box {
    box-shadow: none
}

.default_nav .full_nav_style .small_nav_sec .nav_sec_item {
    display: inline-block;
    vertical-align: top;
    float: none;
    width: calc(100% / 6);
    margin-top: 15px;
    margin-bottom: 10px
}

.default_nav .full_nav_style .small_nav_sec .small .nav_sec_item {
    width: 200px
}

.default_nav .full_nav_style .small_nav_sec .nav_sec_item > a, .default_nav .full_nav_style .small_nav_sec .nav_sec_item > dt > a {
    font-weight: 700
}

.default_nav .full_nav_style .small_nav_sec .nav_sec_a {
    padding-left: 0;
    padding-right: 0
}

.default_nav .full_nav_style .small_nav_sec .nav_sec_item .nav_third_item .nav_third_a {
    margin-bottom: 3px;
    font-size: 14px
}

.default_nav .full_nav_style .small_nav_sec .nav_sec_item .nav_third_item .nav_four_box .nav_four_item a {
    font-size: 12px
}

.default_nav .full_nav_style .nav_sec_item .nav_third_box {
    position: static;
    display: block;
    width: inherit
}

    .default_nav .full_nav_style .nav_sec_item .nav_third_box .nav_four_box {
        position: static;
        display: block;
        width: inherit
    }

.default_nav .full_nav_style .nav_sec .nav_list {
    float: none
}

.default_nav .full_nav_style .nav_sec .nav_img {
    display: inline-block;
    vertical-align: top;
    width: 285px;
    margin-top: 25px;
    margin-bottom: 30px
}

    .default_nav .full_nav_style .nav_sec .nav_img .imgl {
        vertical-align: middle;
        overflow: hidden;
        width: 130px;
        max-width: 200px;
        max-height: 240px;
        margin: 0 25px 25px 0
    }

        .default_nav .full_nav_style .nav_sec .nav_img .imgl a {
            display: block;
            max-width: 150px;
            height: inherit;
            padding: 0
        }

        .default_nav .full_nav_style .nav_sec .nav_img .imgl:nth-child(2n) {
            margin-right: 0
        }

.default_nav .full_nav_style .nav_sec .small .nav_list {
    display: inline-block;
    vertical-align: top;
    max-width: calc(90% - 288px);
    text-align: left
}

.default_nav .full_nav_style .nav_type_3 .nav_list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center
}

.default_nav .full_nav_style .nav_type_3 .nav_sec_item {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 200px;
    margin-right: 25px
}

    .default_nav .full_nav_style .nav_type_3 .nav_sec_item a, .default_nav .full_nav_style .nav_type_3 .nav_sec_item dt a {
        padding-right: 0;
        padding-left: 0
    }

.default_nav .full_nav_style .nav_type_3 .nav_img {
    width: inherit;
    max-width: inherit;
    margin: 0;
    margin-top: 12px;
    text-align: center
}

    .default_nav .full_nav_style .nav_type_3 .nav_img .imgl {
        width: inherit
    }

        .default_nav .full_nav_style .nav_type_3 .nav_img .imgl a {
            max-width: inherit;
            padding: 0
        }

.default_nav .full_nav_style .nav_type_2 .nav_sec_box, .default_nav .full_nav_style .nav_type_3 .nav_sec_box {
    text-align: center
}

.default_nav .full_nav_style .nav_type_3 .nav_sec_item .nav_third_box {
    margin-top: 6px
}

    .default_nav .full_nav_style .nav_type_3 .nav_sec_item .nav_third_box .nav_third_item {
        margin-bottom: 10px
    }

.default_currency_style {
    line-height: 30px
}

    .default_currency_style > strong {
        float: left
    }

    .default_currency_style dl {
        float: left;
        position: relative
    }

        .default_currency_style dl dt {
            padding: 0 26px 0 10px;
            position: relative;
            cursor: pointer
        }

            .default_currency_style dl dt strong {
                vertical-align: middle
            }

                .default_currency_style dl dt strong:after {
                    display: inline-block;
                    vertical-align: middle;
                    height: 100%
                }

                .default_currency_style dl dt strong img {
                    vertical-align: middle;
                    width: 16px;
                    max-height: 12px;
                    overflow: hidden;
                    margin-right: 5px
                }

            .default_currency_style dl dt i {
                position: absolute;
                top: 2px;
                right: 5px;
                bottom: 0;
                margin: auto;
                font-size: 12px;
                transform: scale(.73)
            }

.default_currency dl dd {
    display: none;
    width: max-content;
    -webkit-width: max-content;
    min-width: 100%;
    padding: 15px;
    border-top: none;
    background: #fff;
    position: absolute;
    top: 100%;
    box-sizing: border-box;
    -webkit-box-sizing: border-box
}

.default_currency:hover dl dd {
    display: block
}

.default_currency dl dd a {
    display: block;
    height: 22px;
    line-height: 22px;
    overflow: hidden
}

    .default_currency dl dd a:after {
        content: '';
        display: inline-block;
        height: 100%;
        vertical-align: middle
    }

.default_currency dl dd img {
    display: inline-block;
    vertical-align: middle;
    width: 16px;
    max-height: 12px;
    overflow: hidden;
    margin-right: 5px
}

.default_currency dl.currency_none dt {
    padding: 0 10px
}

    .default_currency dl.currency_none dt:after {
        display: none
    }

    .default_currency dl.currency_none dt i {
        display: none
    }

.default_currency:hover dl.currency_none dd {
    display: none
}

.default_language_style {
    line-height: 30px;
    float: left;
    position: relative
}

    .default_language_style dt {
        padding: 0 26px 0 10px;
        position: relative;
        cursor: pointer
    }

        .default_language_style dt i {
            position: absolute;
            top: 2px;
            right: 5px;
            margin: auto;
            font-size: 12px;
            transform: scale(.73)
        }

.default_language dd {
    display: none;
    width: max-content;
    -webkit-width: max-content;
    min-width: 100%;
    padding: 15px;
    border-top: none;
    background: #fff;
    position: absolute;
    top: 100%;
    box-sizing: border-box;
    -webkit-box-sizing: border-box
}

.default_language:hover dd {
    display: block
}

.default_language dd a {
    display: block;
    height: 22px;
    line-height: 22px;
    overflow: hidden
}

    .default_language dd a:after {
        content: '';
        display: inline-block;
        height: 100%;
        vertical-align: middle
    }

.default_language dd img {
    vertical-align: middle;
    width: 16px;
    max-height: 12px;
    overflow: hidden;
    margin-right: 5px
}

.default_language.language_none dt {
    padding: 0 10px;
    cursor: pointer
}

    .default_language.language_none dt:after {
        display: none
    }

    .default_language.language_none dt i {
        display: none
    }

.default_language.language_none:hover dd {
    display: none
}

.default_account_style dl dt i, .default_currency_style dl dt i, .default_language_style dt i {
    transition: all .3s ease-out
}

.default_account_style dl:hover dt i, .default_currency_style dl:hover dt i, .default_language_style:hover dt i {
    transform: rotate(180deg) scale(.73)
}

.default_language_currency_style {
    line-height: 30px;
    float: left;
    position: relative;
    cursor: pointer
}

    .default_language_currency_style dt {
        padding: 0 26px 0 10px
    }

        .default_language_currency_style dt span.iconfont {
            float: left;
            margin-right: 5px;
            font-size: 20px;
            vertical-align: middle
        }

        .default_language_currency_style dt i {
            width: 11px;
            height: 5px;
            position: absolute;
            top: 0;
            right: 8px;
            bottom: 0;
            margin: auto
        }

    .default_language_currency_style i:not(.iconfont):before {
        content: '';
        width: 0;
        height: 0;
        border-width: 5px 5.5px 0;
        border-style: solid;
        border-color: transparent;
        border-top-color: #646464;
        position: absolute
    }

#default_language_currency_box {
    width: 480px;
    min-height: 215px;
    background: #fff;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    z-index: 999999
}

    #default_language_currency_box h4 {
        height: 55px;
        line-height: 55px;
        text-align: center;
        font-size: 20px
    }

        #default_language_currency_box h4 i {
            display: block;
            width: 56px;
            height: 56px;
            position: absolute;
            right: 0;
            top: 0;
            background: url(../images/global/icon_close1.jpg) center no-repeat;
            cursor: pointer
        }

    #default_language_currency_box .default_language_currency_container {
        height: 100%;
        padding-bottom: 50px;
        box-sizing: border-box
    }

        #default_language_currency_box .default_language_currency_container .default_language_currency_scroll {
            height: 100%;
            box-sizing: border-box;
            padding: 0 30px 0 27px
        }

        #default_language_currency_box .default_language_currency_container h5 {
            line-height: 24px;
            margin-bottom: 8px;
            font-size: 16px;
            color: #333;
            text-align: left
        }

        #default_language_currency_box .default_language_currency_container .default_language_currency_list {
            margin-bottom: 10px
        }

            #default_language_currency_box .default_language_currency_container .default_language_currency_list a {
                display: block;
                width: 100%;
                height: 48px;
                line-height: 48px;
                margin-left: 15px;
                margin-bottom: 15px;
                padding: 0 15px;
                border-color: #bbb;
                background: 0 0;
                font-size: 14px;
                text-align: left;
                box-sizing: border-box;
                overflow: hidden
            }

                #default_language_currency_box .default_language_currency_container .default_language_currency_list a:hover {
                    text-decoration: none
                }

            #default_language_currency_box .default_language_currency_container .default_language_currency_list.default_currency_item a {
                margin-bottom: 0;
                font-size: 16px
            }

            #default_language_currency_box .default_language_currency_container .default_language_currency_list a:nth-child(4n+1) {
                margin-left: 0
            }

            #default_language_currency_box .default_language_currency_container .default_language_currency_list img {
                vertical-align: middle;
                margin-right: 7px
            }

    #default_language_currency_box .default_language_currency_submit {
        width: 202px;
        height: 52px;
        line-height: 52px;
        margin: 38px auto 0;
        background: #222;
        color: #fff;
        text-align: center;
        font-size: 16px;
        border-radius: 5px;
        cursor: pointer
    }

    #default_language_currency_box .chzn-container-active .chzn-single-with-drop {
        box-shadow: unset;
        border: 1px solid #bbb
    }

    #default_language_currency_box .chzn-container-single .chzn-drop {
        box-shadow: 0 2px 6px 1px rgb(0 0 0 / 30%)
    }

    #default_language_currency_box .chzn-container .chzn-drop .chzn-results {
        max-height: 300px
    }

@media(max-width:500px) {
    #default_language_currency_box {
        width: 92%
    }
}

#default_search_box {
    display: none;
    width: 100%;
    height: 100%;
    position: fixed;
    background: rgba(0,0,0,.8);
    left: 0;
    top: 0;
    z-index: 9999
}

    #default_search_box .form {
        width: 50%;
        max-width: 500px;
        height: 50px;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        margin: auto
    }

        #default_search_box .form > i {
            width: 30px;
            height: 30px;
            position: absolute;
            right: -60px;
            top: -60px;
            background: url(../images/search_close.png) center no-repeat;
            cursor: pointer
        }

    #default_search_box .text {
        width: 100%;
        height: 50px;
        line-height: 50px;
        border: 0;
        border-bottom: 1px solid #fff;
        padding-right: 35px;
        color: #fff;
        background: 0 0;
        outline: 0;
        text-indent: 10px;
        box-sizing: border-box
    }

    #default_search_box .button {
        width: 35px;
        height: 48px;
        position: absolute;
        right: 0;
        top: 0;
        background: url(../images/search_btn.svg) center no-repeat;
        background-size: 25px;
        cursor: pointer;
        border: 0;
        outline: 0
    }

    #default_search_box .search_history_container {
        position: absolute;
        top: 0;
        left: 0;
        overflow: hidden;
        min-width: 300px;
        padding: 25px 15px;
        border-radius: 4px;
        text-align: left;
        background-color: #111;
        -webkit-box-sizing: border-box;
        box-sizing: border-box
    }

        #default_search_box .search_history_container .search_item {
            margin-top: 20px
        }

            #default_search_box .search_history_container .search_item:first-child {
                margin-top: 0
            }

        #default_search_box .search_history_container .search_title {
            padding: 0 5px;
            line-height: 22px;
            font-size: 16px;
            color: #fff
        }

        #default_search_box .search_history_container .search_list {
            margin-top: 10px
        }

            #default_search_box .search_history_container .search_list > a {
                display: inline-block;
                vertical-align: top;
                overflow: hidden;
                margin: 5px;
                padding: 0 20px;
                max-width: 140px;
                height: 32px;
                border-radius: 4px;
                line-height: 32px;
                text-decoration: none;
                color: #fff;
                white-space: nowrap;
                text-overflow: ellipsis;
                background-color: #5b5b5b
            }

.default_shopping_cart {
    position: relative
}

.default_shopping_cart_down .cart_note {
    position: absolute;
    left: -1px;
    top: 100%;
    width: 368px;
    background: #fff;
    display: none;
    z-index: 1001;
    cursor: default
}

.default_shopping_cart_down .cart_empty {
    padding: 35px 0;
    text-align: center;
    line-height: 24px
}

.default_shopping_cart_down .cart_list .more_pro {
    height: 363px;
    overflow-x: hidden;
    overflow-y: scroll
}

.default_shopping_cart_down .cart_list .sales_title {
    height: 23px;
    line-height: 23px;
    overflow: hidden;
    padding: 6px 20px 0;
    font-size: 14px;
    text-align: left
}

.default_shopping_cart_down .cart_list .sales_price {
    font-weight: 700
}

.default_shopping_cart_down .cart_list li {
    position: relative;
    padding: 10px 0;
    height: 60px;
    margin: 0 10px;
    border: none;
    border-bottom: 1px #e5e5e5 solid;
    text-align: left
}

    .default_shopping_cart_down .cart_list li:hover {
        background: #f7f7f7
    }

    .default_shopping_cart_down .cart_list li.sales_box {
        border-color: #eee
    }

    .default_shopping_cart_down .cart_list li.sales_last {
        border-color: #ccc
    }

.default_shopping_cart_down .cart_list .cart_pro_img {
    position: absolute;
    left: 0;
    top: 10px;
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: normal
}

    .default_shopping_cart_down .cart_list .cart_pro_img img {
        max-width: 100%;
        max-height: 100%
    }

    .default_shopping_cart_down .cart_list .cart_pro_img > span {
        position: absolute;
        right: -8px;
        top: -8px;
        min-width: 10px;
        width: auto;
        height: 16px;
        line-height: 16px;
        text-align: center;
        padding: 0 3px;
        border-radius: 8px;
        background: #fb4729;
        color: #fff;
        font-size: 12px
    }

.default_shopping_cart_down .cart_list .cart_pro_name, .default_shopping_cart_down .cart_list .cart_pro_property {
    margin-left: 73px;
    height: 14px;
    line-height: 14px;
    overflow: hidden;
    white-space: nowrap;
    color: #999;
    display: block;
    -webkit-text-overflow: ellipsis;
    -moz-text-overflow: ellipsis;
    text-overflow: ellipsis
}

.default_shopping_cart_down .cart_list .cart_pro_property {
    position: relative;
    height: 28px;
    margin-top: 5px;
    padding-right: 100px
}

    .default_shopping_cart_down .cart_list .cart_pro_property span {
        margin-right: 5px
    }

.default_shopping_cart_down .cart_list .cart_pro_qty {
    float: left;
    height: 12px;
    line-height: 12px;
    font-size: 12px;
    margin-left: 73px;
    margin-top: 3px;
    color: #333
}

.default_shopping_cart_down .cart_list .cart_pro_price {
    position: absolute;
    right: 10px;
    top: 24px;
    height: 12px;
    line-height: 12px;
    font-size: 12px;
    margin-top: 5px;
    font-weight: 700
}

.default_shopping_cart_down .cart_pro_view {
    padding: 15px 10px 10px;
    text-align: center;
    line-height: 18px
}

    .default_shopping_cart_down .cart_pro_view .cart_num {
        font-size: 14px;
        font-weight: 700
    }

    .default_shopping_cart_down .cart_pro_view .cart_total {
        margin-right: 10px;
        font-size: 14px;
        font-weight: 700;
        color: #333
    }

.default_shopping_cart_down .cart_pro_btn {
    padding: 5px 10px 10px
}

    .default_shopping_cart_down .cart_pro_btn a {
        color: #333;
        text-decoration: none
    }

    .default_shopping_cart_down .cart_pro_btn .cart_view {
        display: block;
        height: 34px;
        line-height: 34px;
        text-align: center;
        font-size: 14px;
        color: #fff;
        cursor: pointer;
        border-radius: 2px;
        -moz-border-radius: 2px;
        -webkit-border-radius: 2px;
        background: #e62e04
    }

.global_gifts_tips {
    line-height: 22px;
    margin-top: 5px;
    font-size: 12px;
    color: #eb3e3e
}

    .global_gifts_tips i {
        margin-right: 6px;
        font-size: 18px
    }

.header .default_account_style {
    line-height: 30px;
    position: relative;
    display: inline-block
}

    .header .default_account_style dl {
        float: left
    }

        .header .default_account_style dl dt {
            line-height: 30px;
            padding: 0 26px 0 10px;
            position: relative
        }

            .header .default_account_style dl dt i {
                width: 11px;
                height: 5px;
                position: absolute;
                top: 0;
                right: 8px;
                bottom: 0;
                margin: auto
            }

                .header .default_account_style dl dt i:before {
                    content: '';
                    width: 0;
                    height: 0;
                    border-width: 5px 5.5px 0;
                    border-style: solid;
                    border-color: transparent;
                    border-top-color: #646464;
                    position: absolute
                }

.header .default_account dl dd {
    display: none;
    width: max-content;
    -webkit-width: max-content;
    padding: 20px;
    background: #fff;
    border-radius: 6px;
    position: absolute;
    top: 100%;
    right: 0;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    min-width: 200px
}

.header .default_account:hover dl dd {
    min-width: 200px
}

.header .default_account dl dd.user a {
    display: block;
    height: 25px;
    line-height: 25px;
    overflow: hidden;
    margin-bottom: 10px;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 16px
}

.header .default_account dl dd.user .sign_out {
    display: block;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-width: 1px;
    border-style: solid;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
    margin-top: 20px;
    text-decoration: none
}

.header .default_account dl dd.user a b {
    min-width: 14px;
    height: 20px;
    line-height: 20px;
    border-radius: 10px;
    padding: 0 3px;
    text-align: center;
    font-size: 12px
}

.default_foot_menu_style .menu_list dl dt > i {
    display: none
}

.default_foot_menu_style .menu_list.menu_list_contact dl dd a {
    cursor: default
}

@media screen and (max-width:1000px) {
    .default_foot_menu_style .menu_list dl dt > i {
        display: block
    }

    .default_foot_menu_style .menu_list.menu_list_newsletter .default_newsletter_style .clear {
        margin: 0
    }
}

.themes_bor {
    border-color: #e7e7e7;
    border-style: solid
}

.themes_dropdown {
    box-shadow: 0 0 7px rgba(0,0,0,.3);
    border: 1px solid #ddd;
    border-radius: 6px
}

.themes_popups {
    border-radius: 6px;
    box-shadow: 0 3px 7px rgba(0,0,0,.3)
}

.themes_box_title {
    color: #333
}

.themes_box_subtitle {
    color: #333
}

.themes_products_title {
    color: #333
}

.themes_products_subtitle {
    color: #666
}

.themes_products_price {
    color: #c00
}

.themes_products_origin_price {
    color: #999
}

.article_content_box {
    padding-top: 30px;
    padding-bottom: 50px
}

    .article_content_box .main_title {
        height: auto;
        line-height: 46px;
        background: 0 0;
        border: none;
        padding: 0 2% 35px;
        font-size: 30px;
        text-align: center
    }

    .article_content_box .main_content {
        min-height: 0
    }

@media screen and (max-width:999px) {
    .article_location {
        display: none
    }

    .article_content_box {
        padding: 18px 0
    }

        .article_content_box .main_title {
            line-height: 34px;
            padding: 2% 0;
            font-size: 22px
        }

        .article_content_box .editor_txt {
            font-size: 14px;
            padding: 10px 20px
        }
}

.pop_up {
    width: 88%;
    max-width: 400px;
    background-color: #fff;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    margin: auto;
    z-index: 10020;
    -webkit-transform: -webkit-translateX(-100%);
    transform: translateX(-100%);
    -webkit-transition: all .5s;
    transition: all .5s;
    visibility: hidden
}

    .pop_up .close {
        width: 6vw;
        height: 6vw;
        max-width: 50px;
        max-height: 50px;
        position: absolute;
        right: -9vw;
        top: 2vh
    }

        .pop_up .close::after {
            content: "\e631";
            font-size: 20px;
            font-family: iconfont !important;
            color: #fff
        }

        .pop_up .close.hide {
            opacity: 0;
            z-index: -1
        }

    .pop_up.show {
        -webkit-transform: -webkit-translateX(0);
        transform: translateX(0)
    }

.pop_up_right {
    left: auto;
    right: 0;
    -webkit-transform: -webkit-translateX(100%);
    transform: translateX(100%);
    position: fixed
}

.pop_up_container {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch
}

    .pop_up_container .category_close {
        width: 85%;
        height: 3.4375rem;
        line-height: 3.4375rem;
        border-bottom: 1px solid #dfdfdf;
        font-size: 1.1rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        position: relative;
        max-width: none;
        padding: 0;
        margin: 0 auto;
        box-sizing: border-box;
        top: 0;
        left: 0
    }

        .pop_up_container .category_close::after {
            display: none
        }

        .pop_up_container .category_close > em {
            display: inline-block;
            transform: rotate(180deg);
            width: 18px;
            height: 18px;
            line-height: 18px;
            position: relative;
            vertical-align: sub;
            margin-right: 10px
        }

            .pop_up_container .category_close > em::after {
                position: absolute;
                content: "\e619";
                width: 100%;
                height: 100%;
                text-align: center;
                font-size: 18px;
                font-family: iconfont !important
            }

    .pop_up_container .side_head {
        height: 3.4375rem;
        line-height: 3.4375rem;
        text-align: center
    }

        .pop_up_container .side_head .side_close {
            width: 1rem;
            height: 3.3rem;
            line-height: 3.3rem;
            padding-left: .625rem;
            padding-right: 1rem;
            background-color: #fff;
            position: absolute;
            right: auto;
            top: auto;
            left: 0;
            display: inline-block
        }

            .pop_up_container .side_head .side_close > em {
                width: auto;
                height: auto;
                margin: 1.3rem .2rem 0 0;
                background: 0 0;
                border-width: .5rem .5rem .5rem 0;
                border-color: transparent #848484 transparent transparent;
                border-style: solid;
                float: right;
                display: block;
                position: relative;
                z-index: 10
            }

                .pop_up_container .side_head .side_close > em > i {
                    border-width: .5rem .5rem .5rem 0;
                    border-color: transparent #fff transparent transparent;
                    border-style: solid;
                    display: block;
                    position: absolute;
                    top: -.5rem;
                    left: .1rem;
                    z-index: 11
                }

        .pop_up_container .side_head .side_title {
            font-size: 1rem
        }

.left_fixed_side {
    overflow: hidden;
    display: none
}

    .left_fixed_side .menu_list {
        max-width: 100%;
        -webkit-overflow-scrolling: touch
    }

        .left_fixed_side .menu_list .menu_container {
            overflow-y: auto;
            padding: 0 26px
        }

        .left_fixed_side .menu_list .item {
            border-color: #ddd;
            height: 50px;
            line-height: 50px;
            overflow: hidden;
            position: relative
        }

            .left_fixed_side .menu_list .item a {
                display: inline-block;
                width: 83%;
                font-size: 18px;
                color: #222;
                text-overflow: ellipsis;
                white-space: nowrap;
                vertical-align: middle;
                overflow: hidden
            }

                .left_fixed_side .menu_list .item a > span {
                    display: inline-block;
                    vertical-align: middle
                }

                .left_fixed_side .menu_list .item a > img {
                    width: 16px;
                    height: 11px;
                    overflow: hidden;
                    margin-right: 10px
                }

                .left_fixed_side .menu_list .item a > b {
                    font-weight: 700;
                    color: #333
                }

                .left_fixed_side .menu_list .item a .nav_icon {
                    margin-right: 10px;
                    display: inline-block;
                    vertical-align: middle;
                    width: 26px;
                    height: 26px;
                    position: relative
                }

                    .left_fixed_side .menu_list .item a .nav_icon img {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%,-50%)
                    }

                    .left_fixed_side .menu_list .item a .nav_icon span {
                        display: inline-block;
                        vertical-align: middle;
                        height: 100%
                    }

            .left_fixed_side .menu_list .item:last-child {
                border: 0;
                background: 0 0
            }

            .left_fixed_side .menu_list .item > .icon {
                width: 40px;
                height: 100%;
                position: absolute;
                top: 0;
                right: 0
            }

                .left_fixed_side .menu_list .item > .icon.item_font {
                    padding-right: 5px;
                    font-size: 14px;
                    color: #ababab;
                    text-align: right
                }

                    .left_fixed_side .menu_list .item > .icon.item_font > span {
                        margin-right: 12px;
                        width: 48px;
                        display: -webkit-box;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 1;
                        float: left
                    }

                .left_fixed_side .menu_list .item > .icon > em {
                    position: absolute;
                    top: 50%;
                    right: 0;
                    transform: translateY(-50%);
                    width: 20px;
                    height: 20px;
                    line-height: 20px
                }

                    .left_fixed_side .menu_list .item > .icon > em::after {
                        position: absolute;
                        content: "\e619";
                        width: 100%;
                        height: 100%;
                        text-align: center;
                        font-size: 20px;
                        font-family: iconfont !important
                    }

            .left_fixed_side .menu_list .item .current_list {
                height: 20px;
                line-height: 20px
            }

                .left_fixed_side .menu_list .item .current_list > span {
                    padding: 0 5px;
                    font-size: 12px;
                    color: #ccc
                }

            .left_fixed_side .menu_list .item.open {
                height: auto
            }

        .left_fixed_side .menu_list .currency_item img {
            margin-left: 5px
        }

        .left_fixed_side .menu_list .menu_son {
            display: none;
            border-color: #f8f8f8
        }

            .left_fixed_side .menu_list .menu_son .item {
                margin-left: 10px;
                border: 0
            }

                .left_fixed_side .menu_list .menu_son .item a {
                    border-bottom: 1px #f2f2f2 solid
                }

                .left_fixed_side .menu_list .menu_son .item:last-child a {
                    border: 0
                }

        .left_fixed_side .menu_list .menu_grandson {
            border: 0
        }

            .left_fixed_side .menu_list .menu_grandson a {
                color: #878787
            }

            .left_fixed_side .menu_list .menu_grandson .item:last-child a {
                border-bottom: 1px #f2f2f2 solid
            }

        .left_fixed_side .menu_list .attr_son {
            padding: 10px;
            padding-bottom: 0
        }

            .left_fixed_side .menu_list .attr_son span {
                line-height: 32px;
                margin: 0 5px 10px 0;
                padding: 0 10px;
                color: #333;
                white-space: nowrap;
                cursor: pointer;
                border-radius: 5px;
                position: relative;
                vertical-align: middle;
                display: inline-block
            }

                .left_fixed_side .menu_list .attr_son span > em {
                    width: 18px;
                    height: 18px;
                    margin-left: 5px;
                    margin-top: 12px;
                    background: url(../images/mweb/icon_remove_white.png) no-repeat center;
                    background-size: 100%;
                    float: right;
                    display: block
                }

                .left_fixed_side .menu_list .attr_son span.current {
                    color: #fff
                }

        .left_fixed_side .menu_list .menu_divide {
            height: 8px;
            background-color: #eee
        }

        .left_fixed_side .menu_list .cate_nav_box > .item.son > .icon {
            left: 0;
            width: 100%
        }

    .left_fixed_side .category_side .nav_container {
        position: relative;
        height: 100%
    }

    .left_fixed_side .category_side .menu_list {
        position: absolute;
        top: 3.4375rem;
        bottom: 5px;
        left: 0;
        right: 0
    }

        .left_fixed_side .category_side .menu_list .menu_container {
            max-height: 100%
        }

    .left_fixed_side .nav_container .user {
        display: flex;
        align-items: center;
        justify-content: left;
        height: 61px;
        line-height: 61px;
        overflow: hidden;
        font-size: 16px;
        color: #222;
        border-bottom: 1px solid #dfdfdf
    }

        .left_fixed_side .nav_container .user > a {
            font-size: 16px;
            margin: 0 4px;
            color: #222
        }

        .left_fixed_side .nav_container .user .user_logo {
            position: relative;
            width: 24px;
            height: 24px;
            margin-right: 6px;
            line-height: 24px
        }

            .left_fixed_side .nav_container .user .user_logo::after {
                position: absolute;
                content: "\e60f";
                width: 100%;
                height: 100%;
                font-family: iconfont !important;
                font-size: 24px;
                color: #222
            }

        .left_fixed_side .nav_container .user.center {
            text-align: center
        }

    .left_fixed_side .nav_container .currency_language_box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 61px
    }

        .left_fixed_side .nav_container .currency_language_box .flex_item {
            width: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: left;
            font-size: 16px
        }

            .left_fixed_side .nav_container .currency_language_box .flex_item i {
                display: inline-block;
                width: 24px;
                height: 24px;
                font-size: 26px;
                margin-right: 5px
            }

            .left_fixed_side .nav_container .currency_language_box .flex_item .item_font em {
                display: inline-block;
                width: 14px;
                height: 14px;
                font-size: 14px;
                margin-left: 4px
            }

@media screen and (max-width:1000px) {
    .left_fixed_side {
        display: block
    }
}

.search_side {
    width: 100%
}

    .search_side .close {
        width: 2.5rem;
        height: 3.4375rem;
        line-height: 3.4375rem;
        background-color: #fff;
        position: inherit;
        top: auto;
        left: 0;
        z-index: 1;
        display: inline-block
    }

        .search_side .close i {
            position: absolute;
            left: .5rem;
            top: 1.3rem
        }

            .search_side .close i:after, .search_side .close i:before {
                border: 10px solid transparent;
                border-right: 10px solid #fff;
                width: 0;
                height: 0;
                position: absolute;
                top: 0;
                left: 0;
                content: ''
            }

            .search_side .close i:before {
                border-right-color: #000;
                left: -3px
            }

    .search_side .search {
        height: 4rem;
        padding-left: 2.625rem;
        padding-right: .7rem;
        position: relative
    }

        .search_side .search .search_blank {
            height: .7rem
        }

        .search_side .search form {
            background: #f5f5f4
        }

        .search_side .search .text {
            width: 85%;
            height: 2.6rem;
            line-height: 2.6rem;
            padding-left: .7rem;
            font-size: 1.2rem;
            background: 0 0;
            border: 0;
            border-radius: 0
        }

            .search_side .search .text::-webkit-input-placeholder {
                color: #ddd
            }

        .search_side .search .sub {
            width: 15%;
            height: 2.6rem;
            background: url(../images/mweb/icon_search_submit.png) no-repeat center/1.5rem;
            right: .5rem
        }

        .search_side .search .reset {
            background: url(../images/mweb/icon_search_reset.png) no-repeat center/1.5rem;
            right: .625rem
        }

        .search_side .search .reset {
            width: 2.5rem;
            height: 3.5rem;
            border: 0;
            position: absolute
        }

    .search_side .menu_list {
        padding: 0 .625rem
    }

        .search_side .menu_list .search_title {
            height: 1.25rem;
            line-height: 1.25rem;
            margin-top: 1.5rem;
            font-size: 1rem
        }

        .search_side .menu_list .search_list > a {
            line-height: 2rem;
            margin-right: 1rem;
            margin-top: 1rem;
            padding: 0 1.25rem;
            font-size: .875rem;
            color: #555;
            background-color: #f5f5f4;
            border-radius: .3125rem;
            display: inline-block;
            vertical-align: top
        }

@media (min-width:500px) {
    .pop_up .close {
        left: 430px
    }

    .pop_up_container .category_close {
        left: 0
    }
}

.left_fixed_side .menu_list .menu_container.menu_buttom_box {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #f8f8f8
}

.left_fixed_side .pop_buttom {
    position: absolute;
    left: 0;
    right: 0;
    height: 90%;
    background-color: #fff;
    -webkit-transform: -webkit-translateY(112%);
    transform: translateY(112%);
    -webkit-transition: all .5s;
    transition: all .5s;
    opacity: 0;
    z-index: -1;
    height: 0;
    overflow: hidden
}

    .left_fixed_side .pop_buttom.show {
        -webkit-transform: -webkit-translateY(12%);
        transform: translateY(12%);
        opacity: 1;
        z-index: 100030;
        height: 90%;
        overflow: unset
    }

    .left_fixed_side .pop_buttom .pop_mask {
        content: '';
        position: absolute;
        width: 100%;
        height: 120%;
        top: 0;
        left: 0;
        background-color: rgba(0,0,0,.6);
        z-index: 100021
    }

    .left_fixed_side .pop_buttom.show .pop_mask {
        top: -20%
    }

.left_fixed_side .pop_buttom_container {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    position: absolute;
    width: 100%;
    background-color: #fff;
    z-index: 100025
}

    .left_fixed_side .pop_buttom_container .category_close {
        position: relative;
        width: 100%;
        height: 3.4rem;
        line-height: 3.4rem;
        font-size: 1.1rem;
        padding: 0 1.6rem;
        border-bottom: 1px solid #dfdfdf;
        box-sizing: border-box
    }

        .left_fixed_side .pop_buttom_container .category_close > em {
            position: absolute;
            top: 50%;
            right: 1.6rem;
            transform: translateY(-50%);
            width: 5vw;
            height: 5vw;
            line-height: 5vw
        }

            .left_fixed_side .pop_buttom_container .category_close > em::after {
                position: absolute;
                content: "\e631";
                width: 100%;
                height: 100%;
                text-align: center;
                font-size: 18px;
                font-family: iconfont !important;
                color: #bbb
            }

    .left_fixed_side .pop_buttom_container .menu_list .item a {
        position: relative;
        width: 100%
    }

        .left_fixed_side .pop_buttom_container .menu_list .item a em {
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            width: 28px;
            height: 28px;
            line-height: 28px
        }

            .left_fixed_side .pop_buttom_container .menu_list .item a em::after {
                position: absolute;
                content: "\e647";
                width: 100%;
                height: 100%;
                text-align: center;
                font-size: 28px;
                font-family: iconfont !important;
                color: #222
            }

.left_fixed_side .nav_img_box {
    display: grid;
    gap: 15px;
    grid-template-columns: repeat(2,1fr);
    padding: 0 1.6rem;
    margin-top: 15px
}

.left_fixed_side .menu_item {
    opacity: 0;
    transform: translateY(10px)
}

    .left_fixed_side .menu_item.op_show {
        opacity: 1;
        transform: translateY(0)
    }

@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0);
        -moz-transform: rotate(0);
        -ms-transform: rotate(0);
        -o-transform: rotate(0);
        transform: rotate(0)
    }

    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@-moz-keyframes spin {
    0% {
        -webkit-transform: rotate(0);
        -moz-transform: rotate(0);
        -ms-transform: rotate(0);
        -o-transform: rotate(0);
        transform: rotate(0)
    }

    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@-ms-keyframes spin {
    0% {
        -webkit-transform: rotate(0);
        -moz-transform: rotate(0);
        -ms-transform: rotate(0);
        -o-transform: rotate(0);
        transform: rotate(0)
    }

    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes spin {
    0% {
        -webkit-transform: rotate(0);
        -moz-transform: rotate(0);
        -ms-transform: rotate(0);
        -o-transform: rotate(0);
        transform: rotate(0)
    }

    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@font-face {
    font-family: icons;
    src: url(../../../static/font/icons.eot);
    src: url(../../../static/font/icons.eot?#iefix) format("embedded-opentype"),url(../../../static/font/icons.woff2) format("woff2"),url(../../../static/font/icons.woff) format("woff"),url(../../../static/font/icons.ttf) format("truetype"),url(../../../static/font/icons.svg#uxfonteditor) format("svg");
    font-display: swap
}

.global_pro_info_text {
    font-size: 12px;
    word-break: break-word
}

    .global_pro_info_text b {
        font-weight: 700
    }

span.gicon {
    font-family: icons
}

span.gicon-x:before {
    content: '\e604'
}

.global_shopping_cart_bg {
    display: none;
    position: fixed;
    background-color: rgba(0,0,0,.4);
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 100001
}

.global_shopping_cart_box {
    width: 340px;
    padding: 0 30px;
    position: fixed;
    top: 0;
    right: -400px;
    bottom: 0;
    background-color: #fff;
    color: #fff;
    z-index: 100001;
    transition: all .2s ease-out
}

    .global_shopping_cart_box .cart_empty {
        font-size: 16px
    }

    .global_shopping_cart_box a {
        color: #fff
    }

    .global_shopping_cart_box .cart_title {
        line-height: 30px;
        padding: 25px 0;
        font-size: 24px;
        color: #222
    }

    .global_shopping_cart_box .cart_close {
        float: right;
        margin-right: -20px;
        padding: 0 20px;
        font-size: 16px;
        cursor: pointer
    }

    .global_shopping_cart_box .cart_list {
        overflow-y: auto;
        margin-right: -30px
    }

        .global_shopping_cart_box .cart_list::-webkit-scrollbar {
            width: 8px;
            border-radius: 5px
        }

        .global_shopping_cart_box .cart_list::-webkit-scrollbar-thumb {
            background: rgba(0,0,0,.2);
            border-radius: 5px
        }

            .global_shopping_cart_box .cart_list::-webkit-scrollbar-thumb:hover {
                background: rgba(0,0,0,.15)
            }

    .global_shopping_cart_box .cart_item {
        margin-bottom: 30px;
        padding-bottom: 30px
    }

        .global_shopping_cart_box .cart_item:first-child {
            padding-top: 30px
        }

    .global_shopping_cart_box .c_img {
        float: left;
        width: 69.5px;
        min-height: 69.5px;
        max-height: 99px;
        text-align: center
    }

        .global_shopping_cart_box .c_img img {
            opacity: 1;
            max-height: 99px
        }

    .global_shopping_cart_box .c_info {
        float: left;
        width: 251px;
        padding-left: 15px
    }

    .global_shopping_cart_box .name_box {
        display: flex;
        justify-content: space-between
    }

    .global_shopping_cart_box .pname {
        line-height: 18px;
        font-size: 14px;
        color: #222;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical
    }

    .global_shopping_cart_box .pdel {
        margin-left: 20px
    }

        .global_shopping_cart_box .pdel .icon:before {
            font-size: 16px;
            color: #888
        }

    .global_shopping_cart_box .c_flex {
        display: flex;
        justify-content: space-between;
        margin-top: 10px
    }

        .global_shopping_cart_box .c_flex .w_flex {
            flex: 1
        }

    .global_shopping_cart_box .global_pro_info_text {
        line-height: 24px;
        padding-top: 3px;
        color: #222
    }

    .global_shopping_cart_box .pattr {
        line-height: 24px;
        font-size: 12px;
        color: #222;
        word-break: break-all
    }

    .global_shopping_cart_box .c_qty {
        max-width: 95px;
        height: 22px;
        line-height: 22px;
        position: relative;
        text-align: center
    }

    .global_shopping_cart_box .price {
        color: #222;
        line-height: 24px
    }

    .global_shopping_cart_box .pprice {
        float: right;
        font-size: 14px;
        color: #000
    }

    .global_shopping_cart_box .qless {
        width: 23px;
        height: 22px;
        background-color: unset;
        border: 1px solid transparent;
        position: absolute;
        top: 0;
        left: 0;
        color: #000;
        font-size: 12px;
        border-radius: 2px
    }

    .global_shopping_cart_box .qadd {
        width: 23px;
        height: 22px;
        background-color: unset;
        border: 1px solid transparent;
        position: absolute;
        top: 0;
        right: 0;
        color: #000;
        font-size: 12px;
        border-radius: 2px
    }

    .global_shopping_cart_box .qinput {
        width: calc(100% - 46px);
        height: 20px;
        background-color: unset;
        border: 1px solid transparent;
        color: #000;
        text-align: center;
        font-size: 14px;
        border-radius: 2px
    }

    .global_shopping_cart_box .qless .gicon:before {
        content: '\e602'
    }

    .global_shopping_cart_box .qadd .gicon:before {
        content: '\e603'
    }

    .global_shopping_cart_box .cart_foot {
        padding-top: 15px;
        padding-bottom: 30px
    }

    .global_shopping_cart_box .ftprice {
        float: right;
        color: #000
    }

    .global_shopping_cart_box .ftotal {
        color: #000;
        line-height: 25px;
        padding-bottom: 5px;
        font-size: 16px
    }

    .global_shopping_cart_box .fdesc {
        color: #555;
        line-height: 25px;
        font-size: 14px
    }

    .global_shopping_cart_box .fdesc {
        padding-bottom: 15px
    }

    .global_shopping_cart_box .fdesc {
        opacity: .7
    }

    .global_shopping_cart_box .disable_tips {
        display: none;
        line-height: 20px;
        margin-bottom: 10px;
        padding: 17px 22px;
        font-size: 14px;
        color: #333;
        background-color: #ffeed4
    }

        .global_shopping_cart_box .disable_tips.show {
            display: block
        }

    .global_shopping_cart_box .checkout_btn .gicon {
        padding-left: 3px;
        display: none
    }

        .global_shopping_cart_box .checkout_btn .gicon:before {
            content: '\e901'
        }

    .global_shopping_cart_box .checkout_btn {
        display: block;
        text-decoration: none
    }

        .global_shopping_cart_box .checkout_btn button {
            display: block;
            width: 100%;
            height: 48px;
            line-height: 48px;
            background-color: #000;
            border: none;
            text-transform: uppercase;
            transition: all .2s;
            color: #fff;
            font-weight: 700;
            font-size: 16px
        }

html[data-theme=t112] .global_shopping_cart_box .checkout_btn button {
    border-radius: 4px
}

.global_shopping_cart_box .checkout_btn.disabled button {
    background-color: #ddd;
    color: #333
}

.global_shopping_cart_box .checkout_btn .btn-loading {
    position: relative;
    background-color: #131314;
    color: #131314
}

    .global_shopping_cart_box .checkout_btn .btn-loading:active, .global_shopping_cart_box .checkout_btn .btn-loading:hover {
        background-color: #131314;
        color: #131314
    }

    .global_shopping_cart_box .checkout_btn .btn-loading:after {
        content: '';
        display: block;
        width: 18px;
        height: 18px;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        border-radius: 50%;
        border: 3px solid #fff;
        border-top-color: transparent;
        -webkit-animation: spin 1s infinite linear;
        -moz-animation: spin 1s infinite linear;
        -o-animation: spin 1s infinite linear;
        animation: spin 1s infinite linear
    }

.global_shopping_cart_box .shopapps_reminder {
    font-size: 12px
}

    .global_shopping_cart_box .shopapps_reminder > i {
        background-image: url(../../../ico/icon_discount_reminder.png)
    }

.global_shopping_cart_box .gifts_tips {
    display: none;
    line-height: 22px;
    font-size: 16px;
    color: #eb3e3e
}

    .global_shopping_cart_box .gifts_tips i {
        margin-right: 9px
    }

.global_shopping_cart_box .gifts_item .gifts_tips {
    display: inline-block
}

.global_shopping_cart_box .gifts_item .c_qty {
    display: none
}

.global_shopping_cart_box .gifts_item .gifts_qty {
    line-height: 26px;
    font-size: 14px;
    color: #000
}

.global_shopping_cart_box .cart_error {
    line-height: 20px;
    margin-top: 5px
}

.global_shopping_cart_box .pre_sales_info {
    margin-top: 6px
}

    .global_shopping_cart_box .pre_sales_info .tag {
        display: inline-block;
        padding: 0 8px;
        line-height: 24px;
        border-radius: 5px;
        background-color: #ffe7e7;
        color: #f16056;
        font-size: 12px
    }

    .global_shopping_cart_box .pre_sales_info .brief {
        margin-top: 4px;
        display: block;
        line-height: 16px;
        font-size: 12px;
        color: #888
    }

.global_shopping_cart_box .mixed_wholesale_tips {
    margin-top: 6px
}

@media screen and (max-width:768px) {
    .global_shopping_cart_box {
        width: 270px;
        padding: 0 15px
    }
}

@media screen and (max-width:500px) {
    .global_shopping_cart_bg {
        right: 300px
    }

    .global_shopping_cart_box .cart_close {
        font-size: 22px
    }

    .global_shopping_cart_box .cart_list {
        margin-right: -15px
    }

        .global_shopping_cart_box .cart_list::-webkit-scrollbar {
            width: 2px
        }

    .global_shopping_cart_box .c_img {
        width: 56.25px;
        min-height: 56.25px;
        max-height: 70px
    }

    .global_shopping_cart_box .c_info {
        width: 198.75px
    }

    .global_shopping_cart_box .cart_item {
        margin-bottom: 15px;
        padding-bottom: 15px
    }

        .global_shopping_cart_box .cart_item:first-child {
            padding-top: 15px
        }

    .global_shopping_cart_box .cart_foot {
        padding-bottom: 15px
    }

    .global_shopping_cart_box .gifts_tips {
        font-size: 14px
    }
}

.box_popup_center_shopping_cart {
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0,0,0,.5);
    z-index: 100002
}

    .box_popup_center_shopping_cart .box_flex {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center
    }

    .box_popup_center_shopping_cart .box_center {
        width: 94%;
        max-width: 700px;
        padding: 60px 30px;
        box-sizing: border-box;
        background-color: #fff;
        border-radius: 10px;
        position: relative
    }

    .box_popup_center_shopping_cart .close {
        position: absolute;
        top: 0;
        right: 0;
        width: 50px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        cursor: pointer
    }

        .box_popup_center_shopping_cart .close .icon {
            color: #4d4d4d;
            font-size: 14px
        }

    .box_popup_center_shopping_cart .tit {
        display: flex;
        align-items: center;
        justify-content: center
    }

        .box_popup_center_shopping_cart .tit .icon {
            width: 40px;
            height: 40px;
            border-radius: 40px;
            background-color: #19be6b;
            color: #fff;
            line-height: 40px;
            text-align: center;
            font-size: 26px
        }

        .box_popup_center_shopping_cart .tit .text {
            padding: 5px 10px;
            line-height: 30px;
            font-size: 20px;
            color: #000;
            max-width: calc(100% - 60px)
        }

    .box_popup_center_shopping_cart .btn_flex {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        padding-top: 20px
    }

        .box_popup_center_shopping_cart .btn_flex a {
            padding: 5px 33px;
            line-height: 28px;
            border: 1px solid var(--ThemesCheckoutButtonBgColor);
            text-decoration: none;
            font-size: 14px;
            border-radius: 4px;
            box-sizing: border-box;
            margin-top: 15px
        }

        .box_popup_center_shopping_cart .btn_flex .btn_contiue {
            color: var(--ThemesCheckoutButtonBgColor);
            margin-right: 12px
        }

        .box_popup_center_shopping_cart .btn_flex .btn_go_cart {
            background-color: var(--ThemesCheckoutButtonBgColor);
            color: #fff
        }

@media screen and (max-width:750px) {
    .box_popup_center_shopping_cart .box_center {
        padding: 40px 25px
    }

    .box_popup_center_shopping_cart .tit .icon {
        width: 30px;
        height: 30px;
        line-height: 30px;
        font-size: 20px;
        max-width: calc(100% - 50px)
    }

    .box_popup_center_shopping_cart .tit .text {
        font-size: 16px
    }

    .box_popup_center_shopping_cart .btn_flex a {
        width: 100%;
        text-align: center
    }

    .box_popup_center_shopping_cart .btn_flex .btn_contiue {
        margin-right: 0
    }
}

.box_drop_down_shopping_cart {
    display: none;
    width: 100%;
    max-width: 740px;
    position: fixed;
    top: 3%;
    right: 3%;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 20px 5px rgba(0,0,0,.08);
    z-index: 100001;
    box-sizing: border-box;
    overflow: hidden
}

    .box_drop_down_shopping_cart .flex_box {
        display: flex;
        align-items: stretch;
        justify-content: center;
        flex-wrap: wrap
    }

    .box_drop_down_shopping_cart .shoping_cart_box {
        width: 400px;
        padding: 40px 30px;
        box-sizing: border-box;
        box-sizing: border-box
    }

    .box_drop_down_shopping_cart .close {
        position: absolute;
        top: 0;
        right: 0;
        width: 40px;
        height: 56px;
        line-height: 56px;
        text-align: center;
        cursor: pointer
    }

        .box_drop_down_shopping_cart .close .icon {
            color: #4d4d4d;
            font-size: 13px
        }

    .box_drop_down_shopping_cart .tit {
        display: flex;
        align-items: center
    }

        .box_drop_down_shopping_cart .tit .icon {
            width: 26px;
            height: 26px;
            border-radius: 26px;
            background-color: #19be6b;
            color: #fff;
            line-height: 26px;
            text-align: center;
            font-size: 18px
        }

        .box_drop_down_shopping_cart .tit .text {
            padding: 0 8px;
            line-height: 26px;
            font-size: 16px;
            color: #000;
            max-width: calc(100% - 42px)
        }

    .box_drop_down_shopping_cart .c_list {
        margin-top: 20px;
        margin-bottom: 30px;
        overflow-y: auto;
        max-height: 400px;
        min-height: 78px
    }

        .box_drop_down_shopping_cart .c_list::-webkit-scrollbar {
            width: 5px;
            background: #fff;
            border-radius: 5px
        }

        .box_drop_down_shopping_cart .c_list::-webkit-scrollbar-thumb {
            background: rgba(0,0,0,.1);
            border-radius: 5px
        }

            .box_drop_down_shopping_cart .c_list::-webkit-scrollbar-thumb:hover {
                background: rgba(0,0,0,.3)
            }

    .box_drop_down_shopping_cart .c_item {
        margin-top: 20px;
        display: flex
    }

        .box_drop_down_shopping_cart .c_item .img {
            width: 70px;
            height: 70px;
            margin-top: 4px;
            margin-right: 18px
        }

        .box_drop_down_shopping_cart .c_item .info {
            line-height: 20px;
            font-size: 14px;
            flex: 1
        }

        .box_drop_down_shopping_cart .c_item .name {
            display: block;
            margin-bottom: 7px;
            color: #222
        }

    .box_drop_down_shopping_cart .global_pro_info_text {
        color: #888
    }

    .box_drop_down_shopping_cart .c_item p {
        color: #888
    }

    .box_drop_down_shopping_cart .c_item .pattr {
        display: flex;
        flex-wrap: wrap
    }

        .box_drop_down_shopping_cart .c_item .pattr p {
            margin-right: 10px
        }

    .box_drop_down_shopping_cart .c_item .p_q {
        margin-top: 7px;
        color: #222
    }

    .box_drop_down_shopping_cart .cart_total {
        padding: 10px 0;
        line-height: 20px
    }

        .box_drop_down_shopping_cart .cart_total .c_t {
            font-size: 16px;
            color: #888;
            padding-right: 16px
        }

        .box_drop_down_shopping_cart .cart_total .c_t_p {
            font-size: 18px;
            color: #222;
            font-weight: 700
        }

    .box_drop_down_shopping_cart .btn_flex {
        display: flex;
        justify-content: center;
        flex-wrap: wrap
    }

        .box_drop_down_shopping_cart .btn_flex a {
            width: 100%;
            padding: 6px 20px;
            line-height: 28px;
            border: 1px solid var(--ThemesCheckoutButtonBgColor);
            text-decoration: none;
            font-size: 16px;
            border-radius: 4px;
            box-sizing: border-box;
            margin-top: 12px;
            text-align: center
        }

        .box_drop_down_shopping_cart .btn_flex .disable_tips {
            line-height: 20px;
            margin-top: 12px;
            padding: 17px 22px;
            font-size: 14px;
            color: #333;
            background-color: #ffeed4
        }

        .box_drop_down_shopping_cart .btn_flex .btn_go_cart {
            color: var(--ThemesCheckoutButtonBgColor)
        }

        .box_drop_down_shopping_cart .btn_flex .btn_go_checkout {
            background-color: var(--ThemesCheckoutButtonBgColor);
            color: #fff
        }

            .box_drop_down_shopping_cart .btn_flex .btn_go_checkout.disabled {
                background-color: #ddd;
                color: #333;
                border-color: #ddd
            }

@media screen and (max-width:768px) {
    .box_drop_down_shopping_cart {
        width: 92%;
        top: 50%;
        left: 50%;
        right: unset;
        transform: translate(-50%,-50%)
    }

        .box_drop_down_shopping_cart .shoping_cart_box {
            width: 100%;
            padding: 20px
        }

        .box_drop_down_shopping_cart .close {
            height: 40px;
            line-height: 40px
        }

        .box_drop_down_shopping_cart .c_list {
            max-height: 300px;
            margin-top: 15px;
            margin-bottom: 20px
        }

        .box_drop_down_shopping_cart .c_item {
            margin-top: 15px
        }

            .box_drop_down_shopping_cart .c_item .info {
                font-size: 12px;
                line-height: 18px
            }
}

.global_shopping_products_recommend {
    width: 190px;
    padding: 30px 25px;
    position: fixed;
    top: 0;
    right: -660px;
    bottom: 0;
    background-color: #f4f4f4;
    color: #fff;
    z-index: 100001;
    transition: all .2s ease-out;
    box-sizing: border-box;
    overflow-y: auto
}

    .global_shopping_products_recommend::-webkit-scrollbar {
        width: 5px;
        background: #f4f4f4;
        border-radius: 5px
    }

    .global_shopping_products_recommend::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 5px
    }

        .global_shopping_products_recommend::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,.4)
        }

    .global_shopping_products_recommend .pop_products_may_like {
        text-align: center
    }

        .global_shopping_products_recommend .pop_products_may_like .wide {
            width: auto;
            min-width: 100%;
            max-width: 100%
        }

        .global_shopping_products_recommend .pop_products_may_like a {
            text-decoration: none
        }

        .global_shopping_products_recommend .pop_products_may_like .like_title {
            margin: 12px 0 30px;
            font-size: 14px
        }

        .global_shopping_products_recommend .pop_products_may_like .like_products_item {
            margin-top: 50px;
            padding: 0 5px
        }

            .global_shopping_products_recommend .pop_products_may_like .like_products_item:first-child {
                margin-top: 0
            }

            .global_shopping_products_recommend .pop_products_may_like .like_products_item .item_img {
                display: block;
                margin: 0 auto 10px;
                width: 94px;
                height: 94px;
                max-width: 100%;
                border-radius: 5px;
                position: relative;
                overflow: hidden
            }

                .global_shopping_products_recommend .pop_products_may_like .like_products_item .item_img img {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%,-50%)
                }

            .global_shopping_products_recommend .pop_products_may_like .like_products_item .sticker_box {
                display: none
            }

            .global_shopping_products_recommend .pop_products_may_like .like_products_item .item_name {
                line-height: 20px;
                font-size: 12px;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden
            }

            .global_shopping_products_recommend .pop_products_may_like .like_products_item .item_price {
                margin-top: 10px
            }

            .global_shopping_products_recommend .pop_products_may_like .like_products_item .themes_products_origin_price {
                margin: 0 5px
            }

@media screen and (max-width:1000px) {
    .global_shopping_products_recommend {
        padding: 10px
    }

        .global_shopping_products_recommend .pop_products_may_like .like_products_item {
            margin-top: 12px
        }

        .global_shopping_products_recommend .pop_products_may_like .like_title {
            margin: 12px 0 15px
        }

        .global_shopping_products_recommend .pop_products_may_like .like_products_item .item_img {
            margin-bottom: 5px
        }
}

@media screen and (max-width:500px) {
    .global_shopping_products_recommend {
        width: calc(100% - 300px)
    }
}

.popup_center_products_recommend {
    margin-top: 45px
}

    .popup_center_products_recommend .box {
        display: none
    }

    .popup_center_products_recommend a {
        text-decoration: none
    }

    .popup_center_products_recommend .wide {
        min-width: 100%;
        max-width: 100%;
        width: auto
    }

    .popup_center_products_recommend .pop_products_may_like .themes_position {
        display: none
    }

    .popup_center_products_recommend .pop_products_may_like .like_title {
        margin-bottom: 20px;
        font-size: 14px
    }

    .popup_center_products_recommend .pop_products_may_like .common_products_box .srcoll_btn {
        display: block;
        width: 11px;
        height: 19px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        font-family: iconfont !important;
        font-size: 16px;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        color: #aaa
    }

        .popup_center_products_recommend .pop_products_may_like .common_products_box .srcoll_btn:before {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%)
        }

        .popup_center_products_recommend .pop_products_may_like .common_products_box .srcoll_btn.srcoll_btn_next {
            right: -7.5%
        }

            .popup_center_products_recommend .pop_products_may_like .common_products_box .srcoll_btn.srcoll_btn_next:before {
                content: "\e641";
                font-size: 22px;
                color: #aaa
            }

        .popup_center_products_recommend .pop_products_may_like .common_products_box .srcoll_btn.srcoll_btn_prev {
            left: -7.5%
        }

            .popup_center_products_recommend .pop_products_may_like .common_products_box .srcoll_btn.srcoll_btn_prev:before {
                content: "\e63c";
                font-size: 22px;
                color: #aaa
            }

    .popup_center_products_recommend .pop_products_may_like .common_products_box .like_products_item .item_img {
        width: 100%
    }

        .popup_center_products_recommend .pop_products_may_like .common_products_box .like_products_item .item_img .item_img_tab {
            padding-bottom: 100%;
            position: relative
        }

        .popup_center_products_recommend .pop_products_may_like .common_products_box .like_products_item .item_img img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%)
        }

    .popup_center_products_recommend .pop_products_may_like .common_products_box .like_products_item .item_name {
        margin: 8px 0;
        font-size: 12px;
        line-height: 20px;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden
    }

    .popup_center_products_recommend .pop_products_may_like .common_products_box .clear {
        display: none
    }

@media screen and (min-width:1001px) {
    .popup_center_products_recommend .pop_products_may_like .common_products_box {
        display: grid;
        gap: 28px;
        grid-template-columns: repeat(4,1fr)
    }
}

@media screen and (max-width:1000px) {
    .popup_center_products_recommend .pop_products_may_like .common_products_box {
        margin: 0 auto;
        width: 51.2vw;
        position: relative
    }

        .popup_center_products_recommend .pop_products_may_like .common_products_box .like_products_item {
            display: inline-block;
            vertical-align: top;
            width: 22vw;
            margin-right: 7vw
        }
}

@media screen and (max-width:600px) {
    .popup_center_products_recommend .pop_products_may_like .common_products_box {
        width: 70vw
    }

        .popup_center_products_recommend .pop_products_may_like .common_products_box .like_products_item {
            width: 32vw;
            margin-right: 6vw
        }
}

.drop_row_products_recommend {
    padding: 40px 30px;
    width: 340px;
    background-color: #f4f4f4;
    box-sizing: border-box
}

    .drop_row_products_recommend a {
        text-decoration: none
    }

    .drop_row_products_recommend .wide {
        width: auto;
        max-width: 100%;
        min-width: 100%
    }

    .drop_row_products_recommend .like_title {
        margin-top: 8px;
        text-align: center;
        font-size: 14px
    }

    .drop_row_products_recommend .common_products_box {
        margin-top: 25px;
        display: grid;
        gap: 25px;
        grid-template-columns: repeat(2,1fr)
    }

        .drop_row_products_recommend .common_products_box .clear {
            display: none
        }

        .drop_row_products_recommend .common_products_box .like_products_item {
            text-align: center
        }

            .drop_row_products_recommend .common_products_box .like_products_item .item_img {
                display: block;
                margin: 0 auto;
                width: 90px
            }

                .drop_row_products_recommend .common_products_box .like_products_item .item_img .item_img_tab {
                    display: block;
                    width: 100%;
                    height: 0;
                    padding-bottom: 100%;
                    position: relative
                }

                .drop_row_products_recommend .common_products_box .like_products_item .item_img .item_quick_btn {
                    display: none
                }

                .drop_row_products_recommend .common_products_box .like_products_item .item_img .item_img_tab img {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%,-50%)
                }

            .drop_row_products_recommend .common_products_box .like_products_item .item_name {
                display: block;
                margin-top: 10px;
                font-size: 12px;
                line-height: 18px;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden
            }

            .drop_row_products_recommend .common_products_box .like_products_item .item_price {
                margin-top: 10px
            }

                .drop_row_products_recommend .common_products_box .like_products_item .item_price .themes_products_origin_price {
                    margin-left: 5px
                }

@media screen and (max-width:768px) {
    .drop_row_products_recommend {
        width: 100%;
        padding: 20px
    }

        .drop_row_products_recommend .like_title {
            text-align: left
        }

        .drop_row_products_recommend .common_products_box {
            display: block;
            width: 72vw;
            margin: 20px auto 0;
            position: relative
        }

            .drop_row_products_recommend .common_products_box .like_products_item {
                width: 34vw;
                margin-right: 2vw;
                display: inline-block;
                vertical-align: top
            }

        .drop_row_products_recommend .pop_products_may_like .common_products_box .srcoll_btn {
            display: block;
            width: 24px;
            height: 24px;
            font-size: 2.75vw;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            font-family: iconfont !important;
            font-size: 16px;
            font-style: normal;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            z-index: 10
        }

            .drop_row_products_recommend .pop_products_may_like .common_products_box .srcoll_btn:before {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%,-50%);
                z-index: 1
            }

            .drop_row_products_recommend .pop_products_may_like .common_products_box .srcoll_btn.srcoll_btn_next {
                right: -5.5vw
            }

                .drop_row_products_recommend .pop_products_may_like .common_products_box .srcoll_btn.srcoll_btn_next:before {
                    content: "\e641"
                }

            .drop_row_products_recommend .pop_products_may_like .common_products_box .srcoll_btn.srcoll_btn_prev {
                left: -5.5vw
            }

                .drop_row_products_recommend .pop_products_may_like .common_products_box .srcoll_btn.srcoll_btn_prev:before {
                    content: "\e63c"
                }
}

.global_payment_icon_html {
    text-align: center
}

    .global_payment_icon_html li {
        display: inline-block;
        width: 38px;
        height: 24px;
        margin: 4px;
        font-size: 0
    }

        .global_payment_icon_html li img {
            max-height: 24px
        }

@media (max-width:750px) {
    .partners_box {
        text-align: center;
        font-size: 0;
        margin-bottom: 16px
    }

        .partners_box a {
            display: inline-block;
            width: 20%;
            margin: 0 2.5% 5px;
            font-size: 12px;
            color: #fff;
            box-sizing: border-box
        }

            .partners_box a img {
                vertical-align: middle;
                max-width: 100%;
                max-height: 100%
            }
}

.global_drafts_tips {
    width: 100%;
    height: 80px;
    padding-top: 26px;
    position: fixed;
    left: 0;
    bottom: -100px;
    box-sizing: border-box;
    z-index: 100;
    transition: all .8s ease-in-out;
    text-align: center;
    font-family: "微软雅黑"
}

    .global_drafts_tips::after {
        width: 100%;
        height: 100%;
        content: '';
        background-color: #333;
        position: absolute;
        top: 0;
        left: 0;
        opacity: .8;
        z-index: -1
    }

    .global_drafts_tips.current {
        bottom: 0
    }

    .global_drafts_tips .drafts_txt {
        display: inline-block;
        margin-right: 15px;
        line-height: 30px;
        font-size: 18px;
        color: #fff;
        cursor: default;
        vertical-align: bottom
    }

    .global_drafts_tips a {
        display: inline-block;
        min-width: 111px;
        height: 30px;
        line-height: 30px;
        margin: 0 5px;
        padding: 0 20px;
        background-color: #0baf4d;
        box-sizing: border-box;
        color: #fff;
        font-size: 14px;
        text-align: center;
        border-radius: 16px;
        text-decoration: none
    }

    .global_drafts_tips .drafts_hide_btn {
        background-color: #fff;
        color: #404852
    }

@media (max-width:768px) {
    .global_drafts_tips {
        display: none
    }
}

.default_cate_nav_style {
    position: relative;
    display: block;
    width: 100%;
    height: auto;
    font-size: 0
}

    .default_cate_nav_style .cate_nav_title {
        display: block;
        width: 100%;
        font-size: 16px;
        line-height: 26px;
        color: #333;
        padding: 9px 17px;
        box-sizing: border-box
    }

    .default_cate_nav_style .cate_nav_list {
        position: absolute;
        display: block;
        width: 100%;
        left: 0;
        top: auto;
        z-index: 22;
        height: 0
    }

        .default_cate_nav_style .cate_nav_list .cate_nav_box > li {
            position: relative;
            font-size: 16px;
            line-height: 26px;
            text-align: left
        }

            .default_cate_nav_style .cate_nav_list .cate_nav_box > li > a {
                display: block;
                padding: 7px 17px
            }

        .default_cate_nav_style .cate_nav_list .cate_nav_sec .cate_nav_sec_box {
            display: flex;
            flex-wrap: wrap;
            min-width: 47.91666vw;
            position: absolute;
            z-index: 22;
            background-color: #fff;
            top: 0;
            left: 100%;
            padding: 16px 0;
            box-sizing: border-box;
            opacity: 0;
            visibility: hidden;
            top: 0;
            box-shadow: 0 0 10px rgb(0 0 0 / 12%);
            height: auto
        }

            .default_cate_nav_style .cate_nav_list .cate_nav_sec .cate_nav_sec_box .cate_nav_sec_item {
                width: 33.33%;
                box-sizing: border-box;
                padding: 0 1.5625vw;
                margin-bottom: 17px
            }

                .default_cate_nav_style .cate_nav_list .cate_nav_sec .cate_nav_sec_box .cate_nav_sec_item .nav_icon {
                    margin-right: 5px;
                    display: inline-block;
                    vertical-align: top;
                    max-width: 26px;
                    height: 22px;
                    margin-top: 1px;
                    line-height: normal
                }

                    .default_cate_nav_style .cate_nav_list .cate_nav_sec .cate_nav_sec_box .cate_nav_sec_item .nav_icon span {
                        height: 100%;
                        display: inline-block;
                        vertical-align: middle
                    }

            .default_cate_nav_style .cate_nav_list .cate_nav_sec .cate_nav_sec_box li {
                font-size: 14px;
                line-height: 24px
            }

                .default_cate_nav_style .cate_nav_list .cate_nav_sec .cate_nav_sec_box li a {
                    display: block;
                    padding: 7px 0;
                    color: #333;
                    font-weight: 600
                }

        .default_cate_nav_style .cate_nav_list .cate_nav_sec .cate_nav_third .cate_nav_third_box li {
            width: 100%
        }

            .default_cate_nav_style .cate_nav_list .cate_nav_sec .cate_nav_third .cate_nav_third_box li a {
                padding: 0 0;
                color: #666;
                font-size: 14px;
                line-height: 24px;
                font-weight: 400
            }

.operation_activities:not(.show) {
    opacity: 0
}

.operation_activities .operation_error {
    margin-top: 10px;
    color: #e22120;
    font-size: 14px;
    text-align: left
}

#operation_activities_bg {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0,0,0,.5);
    z-index: 9998
}

#operation_activities_tips {
    display: flex;
    width: 500px;
    height: max-content;
    background-color: #fff;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
    padding: 50px;
    justify-content: center;
    flex-wrap: wrap;
    border-radius: 5px;
    z-index: 10000
}

    #operation_activities_tips .tips_tit {
        width: 100%;
        line-height: 40px;
        font-size: 32px;
        text-align: center;
        padding-bottom: 22px
    }

    #operation_activities_tips .code {
        width: 100%;
        text-align: center
    }

        #operation_activities_tips .code .code_tit {
            line-height: 18px;
            font-size: 14px;
            color: #555
        }

        #operation_activities_tips .code .code_text {
            line-height: 56px;
            font-size: 40px;
            font-weight: 700;
            color: #f06055
        }

    #operation_activities_tips .tips_close {
        height: 46px;
        line-height: 46px;
        padding: 0 25px;
        font-size: 16px;
        color: #fff;
        background-color: #000;
        margin-top: 20px;
        border-radius: 5px
    }

@media (max-width:1000px) {
    #operation_activities_tips {
        width: 85%;
        padding: 25px;
        box-sizing: border-box
    }

        #operation_activities_tips .tips_tit {
            font-size: 18px;
            line-height: 24px;
            padding-bottom: 15px
        }

        #operation_activities_tips .code .code_tit {
            font-size: 12px;
            line-height: 20px
        }

        #operation_activities_tips .code .code_text {
            font-size: 28px;
            line-height: 36px
        }

        #operation_activities_tips .tips_close {
            margin-top: 18px;
            height: 32px;
            line-height: 32px;
            padding: 0 17px;
            font-size: 12px
        }
}

.global_products_common {
    margin-top: 60px
}

.w_1200 .global_products_common .wide {
    width: 1200px;
    max-width: 100%
}

.global_products_common .wide {
    min-width: 0;
    max-width: 100%
}

.global_products_common .like_title {
    font-size: 30px
}

.global_products_common .icon_discount, .products_may_like .icon_seckill {
    padding: 7px 10px;
    position: absolute;
    right: 0;
    top: 0;
    color: #fff;
    z-index: 1
}

.global_products_common .icon_seckill {
    display: none
}

.global_products_common .common_products_box {
    margin-top: 50px
}

    .global_products_common .common_products_box .like_products_item {
        width: 23.5%;
        margin-right: 0;
        margin-left: 2%;
        margin-bottom: 40px;
        float: left
    }

        .global_products_common .common_products_box .like_products_item:nth-child(4n+1) {
            margin-left: 0;
            clear: both
        }

        .global_products_common .common_products_box .like_products_item .item_img {
            display: block;
            padding-top: 100%;
            position: relative
        }

            .global_products_common .common_products_box .like_products_item .item_img .item_img_tab {
                display: block;
                position: absolute;
                top: 0;
                left: 0;
                overflow: hidden;
                width: 100%;
                height: 100%;
                text-align: center
            }

            .global_products_common .common_products_box .like_products_item .item_img .item_quick_btn {
                position: absolute;
                bottom: 13px;
                left: 0;
                right: 0;
                margin: auto;
                text-align: center;
                box-sizing: border-box;
                width: 100%;
                padding: 10px
            }

                .global_products_common .common_products_box .like_products_item .item_img .item_quick_btn > span {
                    display: inline-block;
                    border: 1px solid #fff;
                    width: auto;
                    height: auto;
                    font-size: 14px;
                    padding: 10px 20px;
                    color: #fff;
                    cursor: pointer;
                    opacity: 0;
                    visibility: hidden;
                    background-color: #000;
                    line-height: 24px
                }

                    .global_products_common .common_products_box .like_products_item .item_img .item_quick_btn > span:hover {
                        text-decoration: none
                    }

        .global_products_common .common_products_box .like_products_item .item_name {
            display: block;
            height: 54px;
            line-height: 18px;
            margin-top: 10px;
            font-size: 14px;
            overflow: hidden
        }

        .global_products_common .common_products_box .like_products_item .item_price {
            margin-top: 5px;
            font-size: 16px;
            height: 20px;
            overflow: hidden
        }

            .global_products_common .common_products_box .like_products_item .item_price del {
                margin-left: 5px
            }

.global_products_common .icon_discount, .global_products_common .icon_seckill {
    padding: 7px 10px;
    position: absolute;
    right: 0;
    top: 0;
    color: #fff;
    z-index: 1
}

.global_products_common .like_title {
    text-align: center
}

    .global_products_common .like_title span {
        display: inline-block;
        vertical-align: middle;
        font-size: 34px;
        color: #333
    }

        .global_products_common .like_title span.s2 {
            margin-left: 5px;
            color: #fed925
        }

.global_products_common .common_products_box .like_products_item .item_img .item_quick_btn {
    display: none
}

.global_products_common .common_products_box .like_products_item .themes_position {
    display: none
}

.global_products_common .common_products_box .list_products_item .item_img .item_img_tab img:nth-child(2) {
    opacity: 0
}

.global_products_common .common_products_box .list_products_item:hover .item_img .item_img_tab img:nth-child(2) {
    opacity: 1
}

#themes_pdetial_attr_button .global_products_common {
    margin-top: 0;
    margin-bottom: 35px
}

    #themes_pdetial_attr_button .global_products_common .like_title {
        display: flex;
        align-items: flex-start;
        align-items: center;
        font-size: 18px;
        color: #000
    }

        #themes_pdetial_attr_button .global_products_common .like_title span {
            flex: 1;
            margin-left: 6px;
            font-size: 14px;
            display: inline-block;
            vertical-align: middle;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden
        }

    #themes_pdetial_attr_button .global_products_common .common_products_box {
        margin-top: 15px;
        display: grid;
        grid-template-columns: repeat(5,5fr);
        gap: 10px
    }

        #themes_pdetial_attr_button .global_products_common .common_products_box > a {
            display: block;
            box-sizing: border-box;
            border: 2px solid transparent;
            border-radius: 5px;
            transition: .4s;
            text-decoration: none
        }

            #themes_pdetial_attr_button .global_products_common .common_products_box > a .item_name {
                font-size: 12px
            }

            #themes_pdetial_attr_button .global_products_common .common_products_box > a.selected {
                border-color: #000
            }

            #themes_pdetial_attr_button .global_products_common .common_products_box > a .img_box {
                position: relative;
                display: block;
                width: 100%;
                height: 0;
                padding-top: 100%
            }

                #themes_pdetial_attr_button .global_products_common .common_products_box > a .img_box img {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%,-50%);
                    z-index: 10;
                    overflow: hidden
                }

    #themes_pdetial_attr_button .global_products_common[data-mode=mode_2] .common_products_box {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 0
    }

        #themes_pdetial_attr_button .global_products_common[data-mode=mode_2] .common_products_box > a {
            margin-bottom: 12px;
            width: 49%;
            display: flex;
            align-items: center;
            justify-content: left;
            flex-wrap: wrap
        }

            #themes_pdetial_attr_button .global_products_common[data-mode=mode_2] .common_products_box > a .img_box {
                width: 30%;
                padding-top: 30%
            }

            #themes_pdetial_attr_button .global_products_common[data-mode=mode_2] .common_products_box > a .item_name {
                width: 64%;
                padding: 0 3%;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                overflow: hidden
            }

            #themes_pdetial_attr_button .global_products_common[data-mode=mode_2] .common_products_box > a.full {
                width: 100%
            }

                #themes_pdetial_attr_button .global_products_common[data-mode=mode_2] .common_products_box > a.full .img_box {
                    width: 24%;
                    padding-top: 24%
                }

@media screen and (min-width:1000px) {
    #themes_pdetial_attr_button .global_products_common .common_products_box > a:hover {
        border-color: #000
    }
}

@media screen and (max-width:768px) {
    #themes_pdetial_attr_button .global_products_common .common_products_box {
        grid-template-columns: repeat(4,4fr);
        gap: 15px
    }

        #themes_pdetial_attr_button .global_products_common .common_products_box > a {
            border-width: 1px
        }
}

.t092_slide .srcoll_btn_prev {
    background-image: url(../../t092/images/icon_ixbtn.svg)
}

.t092_slide .srcoll_btn_next {
    background-image: url(../../t092/images/icon_ixbtn.svg)
}

.t123_m_banner .box .r_more:after {
    background-image: url(../../t123/images/icon_white.png)
}

.t134_slide .srcoll_btn_prev {
    background-image: url(../../t134/images/arr_left.png)
}

.t134_slide .srcoll_btn_next {
    background-image: url(../../t134/images/arr_right.png)
}

.t135_slide .srcoll_btn {
    background-image: url(../../t135/images/pro_btn.png)
}

.t135_blog_btn {
    background-image: url(../../t135/images/check_out.png)
}

.t136_blog_btn span {
    background-image: url(../../t136/images/arrow.png)
}

.t136_item_btn a::after {
    background-image: url(../../t136/images/arrow.png)
}

.t136_video_con .close_btn {
    background-image: url(../../t136/images/close_btn.png)
}

.t136_play_btn {
    background-image: url(../../t136/images/video_play.png)
}

.t136_product_prev {
    background-image: url(../../t136/images/arrow.png)
}

.t136_product_next {
    background-image: url(../../t136/images/arrow.png)
}

.t136_operator_btn {
    background-image: url(../../t136/images/icon_collect.png)
}

    .t136_operator_btn.is_in, .t136_operator_btn:hover {
        background-image: url(../../t136/images/icon_collect_s.png)
    }

.t138_sl_icon {
    background-image: url(../../t138/images/ly_sl_icon.png)
}

    .t138_sl_icon.is_in {
        background-image: url(../../t138/images/ly_sl_active_icon.png)
    }

.t138_sr_icon {
    background-image: url(../../t138/images/ly_sr_icon.png)
}

    .t138_sr_icon.is_in {
        background-image: url(../../t138/images/ly_sr_active_icon.png)
    }

.t140_ly_shopnow::after {
    background-image: url(../../t140/images/shop_btn.png)
}

@media screen and (max-width:1000px) {
    .t140_ly_shopnow i {
        background-image: url(../../t140/images/p_shop_btn.png)
    }
}

.t140_ly_conbox_a::after {
    background-image: url(../../t140/images/park_four_btn.png)
}

.t140_ly_prev {
    background-image: url(../../t140/images/product_btn1.png)
}

.t140_ly_next {
    background-image: url(../../t140/images/product_btn2.png)
}

.t142_slide .srcoll_btn_prev {
    background-image: url(../../t142/images/prev_btn.png)
}

    .t142_slide .srcoll_btn_prev:hover {
        background-image: url(../../t142/images/prev_active_btn.png)
    }

.t142_slide .srcoll_btn_next {
    background-image: url(../../t142/images/next_btn.png)
}

    .t142_slide .srcoll_btn_next:hover {
        background-image: url(../../t142/images/next_active_btn.png)
    }

.t143_slide .srcoll_btn_prev {
    background-image: url(../../t143/images/poster_left.png)
}

.t143_slide .srcoll_btn_next {
    background-image: url(../../t143/images/poster_right.png)
}

.t143_ly_leftbtn {
    background-image: url(../../t143/images/product_left.png)
}

.t143_ly_rightbtn {
    background-image: url(../../t143/images/product_right.png)
}

.t144_ly_video_btn {
    background-image: url(../../t144/images/icons.png)
}

.t144_ly_close_btn {
    background-image: url(../../t144/images/close_btn.png)
}

.t144_ly_btn a {
    background-image: url(../../t144/images/icons.png)
}

.t145_ly_fullDetail::after {
    background-image: url(../../t145/images/icons.png)
}

.t145_ly_share a::before {
    background-image: url(../../t145/images/icons.png)
}

.t147_product_discount_tags {
    background-image: url(../../t147/images/m_icon_seckill.png)
}

.t149_ly_btn a i {
    background-image: url(../../t149/images/icons_pc.png)
}

.ly_newsletter_34 .newsletter_bgimg {
    background-image: url(../../t150/images/icons_pc.png)
}

.t154_ly_arrow {
    background-image: url(../../t154/images/icon.png)
}

.t157_slide .srcoll_btn_prev {
    background-image: url(../../t157/images/icon.png)
}

.t157_slide .srcoll_btn_next {
    background-image: url(../../t157/images/icon.png)
}

.t158_video_btn {
    background-image: url(../../t158/images/icons_video.svg)
}

.t158_close_btn, .video_con .video_con_in .close_btn {
    background-image: url(../../t158/images/close_btn.png)
}

.ly_header_21.header .head_wrap_bg {
    background-image: url(../../t113/images/header_top_bg.jpg)
}

.ly_blog_6 .blog_list .blog_item a .blog_item_date {
    background: url(../../t113/images/icon_date.png) no-repeat left center
}

.ly_footer_6 #footer_container .info_list dd {
    background: url(../../t098/images/bottom_phone_icon.png) left center no-repeat
}

    .ly_footer_6 #footer_container .info_list dd:first-of-type {
        background: url(../../t098/images/bottom_address_icon.png) left center no-repeat
    }

    .ly_footer_6 #footer_container .info_list dd:last-of-type {
        background: url(../../t098/images/bottom_mail_icon.png) left center no-repeat
    }

.ly_newsletter_4 .default_newsletter_style .default_newsletter_form input.text {
    background-image: url(../../t099/images/icon_massege.png);
    background-repeat: no-repeat;
    background-position: left 26px center
}

.ly_newsletter_4 .default_newsletter_style .default_newsletter_form input.subscribe {
    background-image: url(../../t099/images/icon_arrow.png);
    background-repeat: no-repeat;
    background-position: center center
}

.ly_newsletter_5 .default_newsletter_style .default_newsletter_form .FontBgColor {
    background: url(../../t100/images/icon_arrowright.png) no-repeat center center
}

.ly_footer_9 .default_newsletter_style .newsletter_title {
    background: url(../../t101/images/newsletter_logo.png) no-repeat left 30px center
}

.ly_footer_10#footer .footer_social a {
    background-image: url(../../t102/images/social_sprit.png)
}

.ly_footer_11#footer .default_contact_style dl dd.contact_add {
    background-image: url(../../t103/mages/icon_address.png);
    background-repeat: no-repeat;
    background-position: left center
}

.ly_footer_11#footer .default_contact_style dl dd.contact_tel {
    background-image: url(../../t103/mages/icon_tel.png);
    background-repeat: no-repeat;
    background-position: left center
}

.ly_footer_11#footer .default_contact_style dl dd.contact_email {
    background-image: url(../../t103/mages/icon_email.png);
    background-repeat: no-repeat;
    background-position: left center
}

.ly_products_28 .wrap_section_title {
    background: url(../../t107/images/title_bg.png) no-repeat center top
}

.ly_products_28 .product_list .product_item .product_info_box .product_rates .star_1 {
    background: url(../../t107/images/star_1.png) no-repeat
}

.ly_products_28 .product_list .product_item .product_info_box .product_rates .star_0 {
    background: url(../../t107/images/star_0.png) no-repeat
}

.ly_footer_18#footer .newsletter_area_bg {
    background: url(../../t110/images/newletter_bg.jpg) no-repeat center center
}

.ly_footer_19#footer .newsletter_area_bg {
    background: url(../../t111/images/newletter_bg.jpg) no-repeat center center
}

.ly_newsletter_14 .default_newsletter_style .default_newsletter_form .FontBgColor {
    background-image: url(../../t112/images/icon_email.png);
    background-repeat: no-repeat;
    background-size: 50%;
    background-position: center
}

.ly_footer_21.footer {
    background: url(../../t113/images/bottom_bg.jpg) no-repeat center center;
    background-size: cover
}

.ly_newsletter_15 .news_bg {
    background: url(../../t115/images/new_bg01.png) no-repeat 26.354vw center,url(../../t115/images/new_bg02.png) no-repeat 65.625vw center #f4f4f4
}

.ly_footer_24 .default_newsletter_form input:nth-of-type(2) {
    background: url(../../t116/images/search_btn.png) no-repeat center center
}

.footer .default_currency_style, .ly_footer_25 .footer .default_language_style {
    background: url(../../t117/images/select_bg.png) no-repeat right center
}

.ly_footer_25 .footer .default_currency_style dt strong {
    background: url(../../t117/images/currency_01.png) no-repeat left center
}

.ly_products_50 .list_button > a:after {
    background-image: url(../../t122/images/icon_more_products.png)
}

.ly_footer_35 .footer_content_email .default_newsletter_style .newsletter_title {
    background-image: url(../../t127/images/email.png)
}

.ly_newsletter_23 .default_newsletter_style .default_newsletter_form input.subscribe {
    background-image: url(../../t133/images/email_btn.png)
}

.ly_newsletter_27 .default_newsletter_style .default_newsletter_form .subscribe {
    background-image: url(../../t140/images/submit_btn.png)
}

.ly_newsletter_10 .default_newsletter_style .newsletter_title .top_text {
    background: url(../../t107/images/title_bg.png) no-repeat center center
}

.ly_newsletter_19 .default_newsletter_style form .FontBgColor {
    background-image: url(../../t122/images/email.png);
    background-repeat: no-repeat;
    background-position: center center
}

.ly_newsletter_22 .news_bg {
    background: url(../../t130/images/new_bg01.png) no-repeat 26.354vw center,url(../../t130/images/new_bg02.png) no-repeat 65.625vw center
}

.ly_newsletter_41.ly_email_one .default_newsletter_form .subscribe {
    background: transparent url(../../t180/images/icon_newsletter.png) no-repeat center center/100%
}

.ly_footer_9#footer .default_foot_nav_style a:after {
    background: url(../.../t101/images/icon_down.png) no-repeat center center
}

.ly_products_10 .swiper_list_container .srcoll_btn_prev {
    background: url(../../t097/images/prev_btn.png) no-repeat
}

.ly_products_10 .swiper_list_container .srcoll_btn_next {
    background: url(../../t097/images/next_btn.png) no-repeat
}

.ly_products_65 .goods_item .operator_btns .collect_btn a {
    background-size: 17px;
    background-image: url(../../t133/images/icon_collect_h.png)
}

.ly_products_65 .goods_item .operator_btns .collect_btn .is_in {
    background-image: url(../../t133/images/icon_collect.png)
}

.ly_products_65 .goods_item .operator_btns .addcart_btn a {
    background-size: 21px;
    background-image: url(../../t133/images/icon_add_cart_h.png)
}

.ly_products_65 .themes_prod .operator_btns .collect_btn:hover a {
    background-size: 17px;
    background-image: url(../../t133/images/icon_collect.png)
}

.ly_products_65 .themes_prod .operator_btns .addcart_btn:hover a {
    background-size: 21px;
    background-image: url(../../t133/images/icon_add_cart.png)
}

.ly_products_66 .goods_item .operator_btns .collect_btn a {
    background-size: 17px;
    background-image: url(../../t133/images/icon_collect_h.png)
}

.ly_products_66 .goods_item .operator_btns .collect_btn .is_in {
    background-image: url(../../t133/images/icon_collect.png)
}

.ly_products_66 .goods_item .operator_btns .addcart_btn a {
    background-size: 21px;
    background-image: url(../../t133/images/icon_add_cart_h.png)
}

.ly_products_66 .themes_prod .operator_btns .collect_btn:hover a {
    background-size: 17px;
    background-image: url(../../t133/images/icon_collect.png)
}

.ly_products_66 .themes_prod .operator_btns .addcart_btn:hover a {
    background-size: 21px;
    background-image: url(../../t133/images/icon_add_cart.png)
}

.ly_products_67 .themes_prod .operator_btns .collect_btn:hover a {
    background-size: 17px;
    background-image: url(../../t133/images/icon_collect.png)
}

.ly_products_67 .themes_prod .operator_btns .addcart_btn:hover a {
    background-size: 21px;
    background-image: url(../../t133/images/icon_add_cart.png)
}

.ly_footer_34 .default_newsletter_form input:nth-of-type(2) {
    background: url(../../t126/images/submit_bg.png) no-repeat center center
}

.ly_newsletter_6 .default_newsletter_style .newsletter_title {
    background: url(../../t101/images/newsletter_logo.png) no-repeat left 30px center
}

@media screen and (max-width:1000px) {
    .t158_slide .srcoll_btn {
        background-image: url(../../t158/images/icon_arrow.svg)
    }

    .t158_slide_seven .srcoll_btn {
        background-image: url(../../t158/images/icons_arrow2.svg)
    }

    .ly_footer_3 .follow_us_type_0 li > a:hover {
        background-image: url(../../t093/images/icon_follow_mb.png)
    }

    .ly_footer_4 .footer .icon a.icon6 {
        background-image: url(../../t094/images/footIcon6.png)
    }

    .ly_footer_4 .footer .icon a.icon7 {
        background-image: url(../../t094/images/footIcon7.png)
    }

    .ly_footer_4 .footer .icon a.icon8 {
        background-image: url(../../t094/images/footIcon8.png)
    }

    .ly_products_10 .swiper_list_container .srcoll_btn_prev {
        background: url(../../t097/images/prev_btn.png) no-repeat
    }

    .ly_products_10 .swiper_list_container .srcoll_btn_next {
        background: url(../../t097/images/next_btn.png) no-repeat
    }

    .ly_products_18 .product_list .product_item .product_info_rates .review_star .star_1 {
        width: 10px;
        height: 10px;
        background-size: cover;
        background-image: url(../../t101/images/star_1.png);
        background-position: center
    }

    .ly_products_18 .product_list .product_item .product_info_rates .review_star .star_0 {
        width: 10px;
        height: 10px;
        background-size: cover;
        background-image: url(../../t101/images/star_0.png);
        background-position: center
    }

    .ly_products_19 .product_list .product_item .product_info_rates .review_star .star_1 {
        width: 10px;
        height: 10px;
        background-size: cover;
        background-image: url(../../t101/images/star_1.png);
        background-position: center
    }

    .ly_products_19 .product_list .product_item .product_info_rates .review_star .star_0 {
        width: 10px;
        height: 10px;
        background-size: cover;
        background-image: url(../../t101/images/star_0.png);
        background-position: center
    }

    .ly_products_20 .product_list .product_item .product_info_rates .review_star .star_1 {
        width: 10px;
        height: 10px;
        background-size: cover;
        background-image: url(../../t101/images/star_1.png);
        background-position: center
    }

    .ly_products_20 .product_list .product_item .product_info_rates .review_star .star_0 {
        width: 10px;
        height: 10px;
        background-size: cover;
        background-image: url(../../t101/images/star_0.png);
        background-position: center
    }

    .ly_products_21 .product_list .product_item .product_info_rates .review_star .star_1 {
        width: 10px;
        height: 10px;
        background-size: cover;
        background-image: url(../../t101/images/star_1.png);
        background-position: center
    }

    .ly_products21 .product_list .product_item .product_info_rates .review_star .star_0 {
        width: 10px;
        height: 10px;
        background-size: cover;
        background-image: url(../../t101/images/star_0.png);
        background-position: center
    }

    .ly_footer_15#footer .default_foot_menu_style .menu_list > dl > dt::after {
        background-image: url(../../t107/images/icon_down.png)
    }

    .ly_footer_15 .follow_us_type_2 li > a {
        background-image: url(../../t107/images/icon_follow_mb.png);
        background-size: 40px
    }

    .ly_footer_16#footer .social_icons a {
        background-image: url(../../t108/images/icon_follow_mb.png);
        background-size: 40px
    }

    .ly_footer_19#footer .default_foot_nav_style a:after {
        background: url(../../t111/images/icon_down.png) no-repeat center center
    }

    .ly_footer_20#footer .default_foot_nav_style a:after {
        background: url(../../t112/images/icon_down.png) no-repeat center center
    }

    .ly_footer_20 .follow_us_type_2 li > a {
        background-image: url(../../t112/images/icon_follow_mb.png);
        background-size: 40px
    }

    .ly_footer_21 .follow_us_type_6 li > a {
        background-image: url(../../t113/images/icon_follow_mb.png)
    }

    .ly_footer_22 .follow_us_type_2 li > a {
        background-image: url(../../t114/images/icon_follow_mb.png)
    }

    .ly_footer_25 .follow_us_type_2 li > a {
        background-image: url(../../t117/images/icon_follow_mb.png)
    }

    .ly_footer_28 .follow_us_type_2 li > a {
        background-image: url(../../t120/images/icon_follow_mb.png)
    }

    .ly_products_50 .list_button > a:hover:after {
        background-image: url(../../t121/images/icon_more_products_current.png)
    }

    body .ly_footer_36 .follow_us_type_0 li > a:hover {
        background-image: url(../../t127/images/icon_follow_detail.png)
    }

    .ly_footer_50 .default_foot_menu_style .ly_open dl dt::before {
        background-image: url(../../t143/images/icon_btn2.png)
    }

    .ly_footer_31 .follow_us_type_5 li > a {
        background-image: url(../../t123/images/icon_follow_mb.png)
    }

    .ly_products_133 .ly_poster_one .poster_list .item_list .info .show_btn:hover {
        background-image: url(/static/themes-v2/t180/images/icon_arrow_1.png)
    }

    .ly_products_134 .ly_poster_two .poster_list .item_list .info .show_btn:hover {
        background-image: url(/static/themes-v2/t180/images/icon_arrow_1.png)
    }

    .ly_newsletter_29 .default_newsletter_style .default_newsletter_form input[type=submit] {
        background-image: url(../../t145/images/icons.png)
    }
}

.t162_item_cart span {
    background-image: url(../../t162/images/icons.png)
}

.t163_ly_btn i {
    background-image: url(../../t163/images/icons.png)
}

.t164_slide .srcoll_btn {
    background-image: url(../../t164/images/icons.png)
}

.ly_newsletter_1 .default_newsletter_style .newsletter_title {
    background: url(../../t091/images/ixIcon1.png) center left no-repeat
}

.form_wrapper {
    overflow: hidden
}

    .form_wrapper .form_box {
        width: 896px;
        min-width: 896px;
        margin: 35px auto 100px
    }

        .form_wrapper .form_box .form_name {
            margin-bottom: 40px;
            font-size: 30px;
            color: #000;
            font-weight: 700
        }

        .form_wrapper .form_box .form_pro_box {
            margin: 38px 0
        }

            .form_wrapper .form_box .form_pro_box .f_pro_img {
                width: 100px
            }

            .form_wrapper .form_box .form_pro_box .f_pro_info {
                width: calc(100% - 120px)
            }

                .form_wrapper .form_box .form_pro_box .f_pro_info .f_pro_info_name {
                    padding-right: 20px;
                    margin-bottom: 12px;
                    line-height: 24px;
                    font-size: 14px;
                    color: #363636
                }

                .form_wrapper .form_box .form_pro_box .f_pro_info .f_pro_info_price {
                    font-size: 18px
                }

                    .form_wrapper .form_box .form_pro_box .f_pro_info .f_pro_info_price .themes_products_origin_price {
                        font-size: 14px
                    }

        .form_wrapper .form_box .field_box .rows {
            margin-bottom: 30px
        }

            .form_wrapper .form_box .field_box .rows .input_box {
                display: block
            }

            .form_wrapper .form_box .field_box .rows label {
                display: block;
                margin-bottom: 12px;
                font-weight: 500;
                font-size: 16px;
                color: #111
            }

                .form_wrapper .form_box .field_box .rows label i {
                    color: red
                }

            .form_wrapper .form_box .field_box .rows .field_tips {
                margin-bottom: 12px;
                font-size: 14px;
                color: #888
            }

            .form_wrapper .form_box .field_box .rows .input_box .input_box_txt {
                width: calc(100% - 114px);
                line-height: 22px;
                padding: 11px 8px;
                color: #333;
                border-color: #d2d2d2
            }

            .form_wrapper .form_box .field_box .rows .input_box.filled .input_box_txt {
                height: 22px;
                line-height: 22px
            }

            .form_wrapper .form_box .field_box .rows .input_box .input_box_textarea {
                width: calc(100% - 114px);
                height: 160px;
                padding: 8px;
                font-size: 12px;
                color: #333;
                background-color: #fff;
                border: 1px #d2d2d2 solid;
                border-radius: 5px;
                position: relative;
                display: inline-block;
                transition: all 150ms;
                -webkit-transition: all 150ms
            }

            .form_wrapper .form_box .field_box .rows .input_box.filled .input_box_textarea {
                height: 160px
            }

            .form_wrapper .form_box .field_box .rows .i_submit {
                display: inline-block;
                width: 200px;
                height: 50px;
                line-height: 50px;
                background-color: #000;
                border-radius: 5px;
                text-align: center;
                font-size: 16px;
                color: #fff;
                font-weight: 500;
                cursor: pointer;
                font-family: inherit
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_box {
                margin: 0 20px 0 0
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box label {
                display: block;
                line-height: 30px;
                padding-top: 29px;
                font-size: 16px
            }

                .form_wrapper .form_box .field_box .rows .form_tool_img_box label:first-child {
                    padding-top: 0
                }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .rows {
                display: flex;
                flex-wrap: wrap
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .error_info {
                width: 100%;
                line-height: 26px
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box input[name=Name] {
                flex: 1;
                height: 46px;
                line-height: 46px;
                padding: 0 10px;
                border-radius: 5px;
                border-color: #d9d9d9;
                border-style: solid;
                border-width: 1px
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .ranting_chose {
                margin-top: 5px;
                line-height: 34px
            }

                .form_wrapper .form_box .field_box .rows .form_tool_img_box .ranting_chose .review_star {
                    vertical-align: middle;
                    cursor: pointer
                }

                    .form_wrapper .form_box .field_box .rows .form_tool_img_box .ranting_chose .review_star .icon-star1 {
                        margin-right: 0;
                        padding-right: 5px
                    }

                .form_wrapper .form_box .field_box .rows .form_tool_img_box .ranting_chose .error_info {
                    font-weight: 400
                }

                .form_wrapper .form_box .field_box .rows .form_tool_img_box .ranting_chose .star_0 {
                    display: inline-block;
                    color: #ddd
                }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .review_content {
                flex: 1;
                height: 129px;
                padding: 10px;
                border-radius: 5px;
                border-color: #d9d9d9;
                border-width: 1px;
                border-style: solid;
                width: 100%;
                box-sizing: border-box
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .upfile_condition {
                margin: 0 0 10px 0
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .button {
                height: 46px;
                line-height: 46px;
                color: #fff;
                border-radius: 4px;
                -moz-border-radius: 4px;
                -webkit-border-radius: 4px;
                background: #f16056;
                border: 0;
                font-size: 16px;
                cursor: pointer;
                margin-top: 30px;
                padding: 0 40px
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_box {
                display: none;
                float: left;
                width: 86px;
                height: 86px;
                position: relative;
                box-sizing: border-box;
                border: 2px dashed #d9d9d9;
                border-radius: 5px;
                margin: 18px 20px 0 0;
                border: solid 1px #f2f2f2;
                background: #fafafa
            }

                .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_box .iconfont {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%,-50%);
                    color: #757575;
                    font-size: 30px
                }

                .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_box label {
                    margin: 0;
                    padding-top: 0
                }

                .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_box:first-child {
                    display: block
                }

                .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_box.on:after, .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_box.on:before {
                    background: 0 0
                }

                .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_box.on .num_tips {
                    font-size: 0
                }

                .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_box .num_tips {
                    position: absolute;
                    left: 0;
                    top: 65%;
                    width: 100%;
                    text-align: center;
                    color: #ddd;
                    display: none
                }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_box {
                width: 120px;
                height: 120px
            }

                .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_box .pic_box {
                    position: relative;
                    width: 120px;
                    height: 120px;
                    vertical-align: middle;
                    font-size: 0;
                    text-align: center;
                    cursor: pointer;
                    box-sizing: border-box;
                    z-index: 1
                }

                    .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_box .pic_box img {
                        opacity: 1
                    }

                .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_box .close {
                    position: absolute;
                    top: -7px;
                    right: -7px;
                    display: none;
                    width: 24px;
                    height: 24px;
                    line-height: 24px;
                    text-align: center;
                    color: #fff;
                    background: rgba(0,0,0,.5);
                    border-radius: 50%;
                    cursor: pointer;
                    z-index: 2
                }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_file {
                width: 82px;
                height: 82px;
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                right: 0;
                padding: 0;
                -moz-opacity: 0;
                -webkit-opacity: 0;
                opacity: 0;
                cursor: pointer;
                font-size: 70px;
                z-index: 1
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_file {
                width: 120px;
                height: 120px
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .upload_tips {
                color: #999;
                font-size: 14px
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box ::-webkit-input-placeholder {
                color: #999
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box ::-moz-placeholder {
                color: #999
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box :-ms-input-placeholder {
                color: #999
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .textarea {
                position: relative
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .textarea_box {
                position: relative;
                width: 100%
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .textarea .font_tips {
                position: absolute;
                right: .4375rem;
                bottom: .4375rem;
                color: #999
            }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .textarea .font_tips_text {
                color: #999;
                font-size: 14px;
                line-height: 20px;
                margin-top: 8px;
                padding-left: 24px;
                box-sizing: border-box;
                position: relative
            }

                .form_wrapper .form_box .field_box .rows .form_tool_img_box .textarea .font_tips_text .iconfont {
                    position: absolute;
                    left: 2px;
                    top: 2px;
                    color: #f3971b
                }

            .form_wrapper .form_box .field_box .rows .form_tool_img_box .ver_code {
                border-color: #d9d9d9;
                border-radius: 5px
            }

    .form_wrapper .form_success {
        text-align: center
    }

        .form_wrapper .form_success .icon_success_status {
            display: inline-block;
            position: static;
            margin-top: 30px
        }

        .form_wrapper .form_success .success_title {
            margin-top: 15px;
            font-size: 30px;
            color: #000;
            line-height: 2
        }

        .form_wrapper .form_success .success_content {
            margin-top: 5px;
            font-size: 18px;
            color: #000;
            line-height: 1.5
        }

        .form_wrapper .form_success .btn_form_success {
            border-color: #797979;
            -webkit-border-radius: 5px;
            border-radius: 5px;
            height: 50px;
            margin-top: 30px;
            text-decoration: none;
            color: #333;
            line-height: 50px;
            background-color: #fff;
            -webkit-transition: all .5s;
            transition: all .5s
        }

            .form_wrapper .form_success .btn_form_success:hover {
                color: #fff;
                background-color: #000
            }

    .form_wrapper .global_select_box {
        height: auto;
        line-height: 46px
    }

        .form_wrapper .global_select_box .input_case {
            position: relative;
            width: calc(100% - 95px);
            border-radius: 5px
        }

            .form_wrapper .global_select_box .input_case input.imitation_select {
                cursor: pointer;
                color: #333
            }

    .form_wrapper .form_box .field_box .rows .input_box .global_select_box .imitation_select {
        width: calc(100% - 18px)
    }

    .form_wrapper .global_select_box .input_case i {
        width: 12px;
        height: 12px;
        line-height: 12px;
        position: absolute;
        right: 18px;
        top: 0;
        bottom: 0;
        margin: auto;
        transform: scale(.8);
        font-size: 16px;
        color: #7d8d9e;
        transition: all .5s ease 0s
    }

        .form_wrapper .global_select_box .input_case i::before {
            content: "\e62b";
            font-family: iconfont
        }

    .form_wrapper .global_select_box .select_ul {
        display: none;
        padding: 0;
        margin: 0;
        width: calc(100% - 99px);
        max-height: 320px;
        overflow-y: auto;
        border: 1px solid #d2d2d2;
        border-radius: 5px;
        border-top: 0
    }

        .form_wrapper .global_select_box .select_ul::-webkit-scrollbar {
            width: 10px;
            background: #edf0f5
        }

        .form_wrapper .global_select_box .select_ul::-webkit-scrollbar-thumb {
            background: rgba(193,193,193,.8);
            border-radius: 5px
        }

            .form_wrapper .global_select_box .select_ul::-webkit-scrollbar-thumb:hover {
                background: rgba(168,168,168,.8)
            }

        .form_wrapper .global_select_box .select_ul li {
            line-height: 22px;
            transition: all .5s ease 0s;
            padding: 5px 10px;
            color: #333;
            cursor: pointer
        }

            .form_wrapper .global_select_box .select_ul li.selected, .form_wrapper .global_select_box .select_ul li:hover {
                background-color: #efefef
            }

                .form_wrapper .global_select_box .select_ul li.selected.leave {
                    background-color: unset;
                    color: #333
                }

    .form_wrapper .global_select_box.focus .select_ul {
        display: block
    }

    .form_wrapper .global_select_box.focus .input_case i {
        color: #888;
        transform: scale(.8) rotate(-180deg)
    }

@media screen and (max-width:768px) {
    .form_wrapper .global_select_box .select_ul {
        width: 100%
    }
}

.field_checked_box {
    width: calc(100% - 114px);
    padding: 30px 20px 14px;
    background-color: #fafafa;
    box-sizing: border-box;
    font-size: 0
}

    .field_checked_box .item {
        display: inline-block;
        margin-right: 12px;
        margin-bottom: 16px;
        padding-left: 11px;
        padding-right: 18px;
        font-size: 14px;
        cursor: pointer;
        color: #333;
        line-height: 16px
    }

        .field_checked_box .item::before {
            display: inline-block;
            width: 14px;
            height: 14px;
            content: '';
            margin-right: 4px;
            background-color: #fff;
            border: 1px solid #ccdced;
            border-radius: 16px;
            vertical-align: bottom
        }

        .field_checked_box .item.checked::before {
            width: 6px;
            height: 6px;
            border: 5px solid #222;
            border-radius: 16px
        }

        .field_checked_box .item.disabled {
            cursor: no-drop
        }

            .field_checked_box .item.disabled::before {
                width: 6px;
                height: 6px;
                border: 5px #d2e0ee solid;
                border-radius: 16px
            }

    .field_checked_box input {
        display: none
    }

    .field_checked_box .input_checkbox_box {
        display: inline-block;
        vertical-align: top;
        margin-right: 45px;
        line-height: 20px;
        cursor: pointer;
        font-size: 14px;
        color: #333
    }

        .field_checked_box .input_checkbox_box .input_checkbox {
            width: 13px;
            height: 13px;
            margin-top: 3px;
            margin-right: 5px;
            margin-bottom: 16px;
            background-color: #fff;
            border: 1px #bbb solid;
            border-radius: 2px;
            position: relative;
            display: inline-block;
            vertical-align: top
        }

            .field_checked_box .input_checkbox_box .input_checkbox input {
                display: none
            }

            .field_checked_box .input_checkbox_box .input_checkbox:before {
                margin: auto;
                position: absolute;
                display: none;
                content: "\e647";
                font-family: iconfont
            }

        .field_checked_box .input_checkbox_box.checked .input_checkbox {
            border-color: #fff
        }

            .field_checked_box .input_checkbox_box.checked .input_checkbox:before {
                display: block;
                width: 100%;
                height: 100%;
                background-color: #222;
                border: 1px #222 solid;
                color: #fff;
                top: 0;
                bottom: 0;
                right: 0;
                left: 0;
                box-sizing: border-box;
                font-size: 12px;
                line-height: 1
            }

        .field_checked_box .input_checkbox_box.indeterminate .input_checkbox:before {
            display: block;
            width: 100%;
            height: 100%;
            background-color: #222;
            border: 1px #222 solid;
            color: #fff;
            top: 0;
            bottom: 0;
            right: 0;
            left: 0;
            box-sizing: border-box;
            font-size: 12px;
            line-height: 1
        }

@media screen and (max-width:1024px) {
    .form_wrapper #location {
        padding: 0 10px
    }

    .form_wrapper .form_box {
        width: 100%;
        min-width: unset;
        box-sizing: border-box;
        padding: 0 15px
    }
}

@media screen and (max-width:768px) {
    .form_wrapper .form_box .field_box .rows .input_box .input_box_txt {
        width: calc(100% - 18px)
    }

    .form_wrapper .form_box .field_box .rows .input_box .input_box_textarea {
        width: calc(100% - 18px)
    }

    .field_checked_box {
        width: 100%
    }
}

.field_box_form .form_tool_file_box {
    background-color: #fff;
    border-radius: 8px
}

    .field_box_form .form_tool_file_box .upload_box {
        display: none;
        float: left;
        width: 86px;
        height: 86px;
        position: relative;
        box-sizing: border-box;
        border: 2px dashed #d9d9d9;
        border-radius: 5px;
        margin: 20px 20px 0 0;
        border: solid 1px #dfdfdf;
        background: #fafafa
    }

        .field_box_form .form_tool_file_box .upload_box .iconfont {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
            color: #757575;
            font-size: 30px
        }

        .field_box_form .form_tool_file_box .upload_box label {
            margin: 0;
            padding-top: 0
        }

        .field_box_form .form_tool_file_box .upload_box:first-child {
            display: block
        }

        .field_box_form .form_tool_file_box .upload_box.on:after, .field_box_form .form_tool_file_box .upload_box.on:before {
            background: 0 0
        }

        .field_box_form .form_tool_file_box .upload_box.on .num_tips {
            font-size: 0
        }

        .field_box_form .form_tool_file_box .upload_box .num_tips {
            position: absolute;
            left: 0;
            top: 65%;
            width: 100%;
            text-align: center;
            color: #ddd;
            display: none
        }

    .field_box_form .form_tool_file_box .upload_box {
        width: 120px;
        height: 120px
    }

        .field_box_form .form_tool_file_box .upload_box .pic_box {
            position: relative;
            width: 120px;
            height: 120px;
            vertical-align: middle;
            font-size: 0;
            text-align: center;
            cursor: pointer;
            box-sizing: border-box;
            z-index: 1
        }

            .field_box_form .form_tool_file_box .upload_box .pic_box img {
                opacity: 1
            }

        .field_box_form .form_tool_file_box .upload_box .close {
            position: absolute;
            top: -7px;
            right: -7px;
            display: none;
            width: 24px;
            height: 24px;
            background: #000;
            border-radius: 50%;
            cursor: pointer;
            z-index: 2;
            transform: rotate(45deg)
        }

            .field_box_form .form_tool_file_box .upload_box .close::before {
                content: '';
                width: 12px;
                height: 1px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%,-50%);
                background-color: #fff
            }

            .field_box_form .form_tool_file_box .upload_box .close::after {
                content: '';
                width: 1px;
                height: 12px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%,-50%);
                background-color: #fff
            }

        .field_box_form .form_tool_file_box .upload_box svg {
            width: 60px;
            height: 60px;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            margin: auto
        }

    .field_box_form .form_tool_file_box .upload_file {
        width: 82px;
        height: 82px;
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        padding: 0;
        -moz-opacity: 0;
        -webkit-opacity: 0;
        opacity: 0;
        cursor: pointer;
        font-size: 70px;
        z-index: 1
    }

    .field_box_form .form_tool_file_box .upload_file {
        width: 120px;
        height: 120px
    }

@media screen and (max-width:1000px) {
    .field_box_form .form_tool_file_box {
        padding: 0 10px 10px 10px
    }

        .field_box_form .form_tool_file_box .upload_file {
            width: 80px;
            height: 80px
        }

        .field_box_form .form_tool_file_box .upload_box {
            margin: 10px 10px 0 0;
            width: 80px;
            height: 80px
        }

            .field_box_form .form_tool_file_box .upload_box .pic_box {
                width: 80px;
                height: 80px
            }
}

.global_faq_box {
    padding: 50px 0;
    width: 100%
}

    .global_faq_box .container {
        margin: 0 auto;
        width: 100%;
        max-width: 1200px
    }

        .global_faq_box .container .faq_big_title {
            margin-bottom: 24px;
            height: 24px;
            line-height: 24px;
            font-size: 30px;
            color: #000;
            font-weight: 700
        }

        .global_faq_box .container .item {
            padding: 24px 0 24px 10px;
            width: 100%;
            border-bottom: 1px solid #eaeaea;
            box-sizing: border-box
        }

            .global_faq_box .container .item .title {
                padding-right: 102px;
                width: 100%;
                line-height: 24px;
                font-size: 16px;
                color: #111;
                word-break: break-all;
                display: -webkit-box;
                overflow: hidden;
                cursor: pointer;
                box-sizing: border-box;
                position: relative
            }

            .global_faq_box .container .item i {
                position: absolute;
                top: 0;
                right: 10px;
                width: 20px;
                height: 20px;
                line-height: 22px;
                text-align: center;
                font-size: 12px;
                color: #fff;
                background-color: #111;
                border: 1px solid #111;
                border-radius: 50%;
                transition: all .4s ease-out;
                -moz-transition: all .4s ease-out;
                -ms-transition: all .4s ease-out;
                -o-transition: all .4s ease-out;
                -webkit-transition: all .4s ease-out
            }

                .global_faq_box .container .item i:before {
                    margin-left: 1.5px;
                    font-style: normal
                }

            .global_faq_box .container .item.active i {
                color: #111;
                background-color: #fff;
                transform: rotate(180deg)
            }

            .global_faq_box .container .item.active .title {
                font-weight: 700
            }

            .global_faq_box .container .item .content {
                height: 0;
                line-height: 22px;
                font-size: 14px;
                color: #555;
                overflow: hidden;
                transition: all .4s ease-out;
                -moz-transition: all .4s ease-out;
                -ms-transition: all .4s ease-out;
                -o-transition: all .4s ease-out;
                -webkit-transition: all .4s ease-out
            }

                .global_faq_box .container .item .content .inner_height {
                    padding-top: 24px;
                    padding-right: 102px
                }

@media screen and (max-width:1240px) {
    .global_faq_box .container {
        max-width: 1000px
    }
}

@media screen and (max-width:1040px) {
    .global_faq_box .container {
        max-width: 750px
    }
}

@media screen and (max-width:768px) {
    .global_faq_box {
        padding: 25px 0
    }

        .global_faq_box .container {
            max-width: 92%
        }

            .global_faq_box .container .faq_big_title {
                margin-bottom: 12px;
                font-size: 16px
            }

            .global_faq_box .container .item {
                padding: 12px 0
            }

                .global_faq_box .container .item .content .inner_height {
                    padding-top: 12px
                }

                .global_faq_box .container .item .title {
                    font-size: 12px
                }

                .global_faq_box .container .item .content {
                    line-height: 14px;
                    font-size: 12px
                }
}

.themes_prod {
    position: relative
}

    .themes_prod .sticker_box {
        position: absolute;
        top: 10px;
        z-index: 10;
        text-align: left;
        width: 86%
    }

        .themes_prod .sticker_box.left-top {
            left: 10px
        }

            .themes_prod .sticker_box.left-top.discount_top {
                top: 46px
            }

        .themes_prod .sticker_box.right-top {
            right: 10px;
            text-align: right
        }

            .themes_prod .sticker_box.right-top.discount_top {
                top: 46px
            }

        .themes_prod .sticker_box .sticker_item {
            display: inline-block;
            margin-right: 5px;
            margin-bottom: 5px
        }

        .themes_prod .sticker_box.right-top .sticker_item {
            margin-left: 10px;
            margin-right: 0
        }

        .themes_prod .sticker_box .sticker_item .sticker_info {
            background-color: #000;
            color: #fff;
            font-size: 13px;
            text-align: center;
            height: 24px;
            line-height: 24px;
            overflow: hidden
        }

            .themes_prod .sticker_box .sticker_item .sticker_info[data=oval] {
                padding: 0 9px;
                border-radius: 6px
            }

            .themes_prod .sticker_box .sticker_item .sticker_info[data=round] {
                width: 46px;
                height: 46px;
                line-height: 46px;
                border-radius: 50%
            }

            .themes_prod .sticker_box .sticker_item .sticker_info[data=square] {
                padding: 0 12px
            }

@media(max-width:768px) {
    .themes_prod .sticker_box .sticker_item .sticker_info {
        height: 20px;
        line-height: 20px;
        font-size: 11px
    }
}

.compute_item_img {
    display: block;
    position: relative;
    transition: all .3s ease-in-out
}

    .compute_item_img .compute_process_img {
        display: flex;
        position: relative;
        width: 100%;
        height: 0;
        overflow: hidden
    }

        .compute_item_img .compute_process_img img {
            position: absolute;
            top: 0
        }

        .compute_item_img .compute_process_img a.logo_title {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            margin: auto
        }

        .compute_item_img .compute_process_img .img_bg {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            margin: auto
        }

    .compute_item_img .view_scroll_box {
        position: absolute;
        display: block;
        top: 50%;
        width: 100%;
        height: 100%
    }

    .compute_item_img .compute_process_img .tiktok-embed {
        position: static
    }

@media screen and (max-width:768px) {
    .compute_item_img .view_scroll_box {
        height: 150%
    }
}

.null {
    -webkit-animation: null .3s 2 0s linear forwards;
    animation: null .3s 2 0s linear forwards;
    color: #333 !important
}

@keyframes

) {
    0%

{
    background: #fff3f3
}

50% {
    background: #ffcbcc
}

100% {
    background: #fff3f3
}

}
@-webkit-keyframes

) {
    0%

{
    background: #fff3f3
}

50% {
    background: #ffcbcc
}

}

.chzn-container {
    width: 100%;
    font-size: 11px;
    position: relative;
    display: inline-block;
    zoom: 1
}

    .chzn-container .chzn-drop {
        background: #fff;
        border-top: 0;
        position: absolute;
        top: 29px;
        left: 0;
        -webkit-box-shadow: 0 4px 5px rgba(0,0,0,.15);
        -moz-box-shadow: 0 4px 5px rgba(0,0,0,.15);
        -o-box-shadow: 0 4px 5px rgba(0,0,0,.15);
        box-shadow: 0 4px 5px rgba(0,0,0,.15);
        z-index: 999;
        overflow: hidden
    }

.chzn-container-single .chzn-single {
    height: 44px;
    line-height: 44px;
    overflow: hidden;
    padding: 0 0 0 8px;
    padding-left: 16px;
    font-size: 12px;
    color: #444;
    text-decoration: none;
    background-color: #fff;
    border: 1px #d9d9d9 solid;
    border-radius: 5px;
    position: relative;
    display: block;
    white-space: nowrap
}

    .chzn-container-single .chzn-single.null {
        border-color: red
    }

.filled .chzn-container-single .chzn-single {
    height: 18px;
    line-height: 18px;
    padding-top: 22px;
    padding-bottom: 4px
}

.chzn-container-single .chzn-single span {
    margin-right: 26px;
    display: block;
    overflow: hidden;
    color: #333;
    white-space: nowrap;
    -o-text-overflow: ellipsis;
    -ms-text-overflow: ellipsis;
    text-overflow: ellipsis;
    font-size: 14px
}

.chzn-container-single .chzn-single abbr {
    display: block;
    position: absolute;
    right: 26px;
    top: 8px;
    width: 12px;
    height: 13px;
    font-size: 1px;
    background: url(/assets/images/cart/chosen-sprite.png) right top no-repeat
}

    .chzn-container-single .chzn-single abbr:hover {
        background-position: right -11px
    }

.chzn-container-single .chzn-single div {
    -webkit-border-radius: 0 4px 4px 0;
    -moz-border-radius: 0 4px 4px 0;
    border-radius: 0 4px 4px 0;
    border: 0;
    position: absolute;
    right: 0;
    top: 0;
    display: block;
    height: 100%;
    width: 30px
}

    .chzn-container-single .chzn-single div::before {
        content: '\e6bd';
        display: block;
        line-height: 44px;
        text-align: center;
        font-size: 12px;
        font-family: iconfont;
        color: #919191;
        transform: scale(.75)
    }

.chzn-container-single .chzn-search {
    height: 40px;
    line-height: 40px;
    background: #f7f7f7 url(../images/user/address_search_icon.png) no-repeat center right 15px;
    padding: 0 50px 0 20px;
    position: relative;
    margin: 15px 16px 12px;
    white-space: nowrap
}

    .chzn-container-single .chzn-search input {
        width: 100%;
        height: 40px;
        background-color: #f7f7f7;
        margin: 0;
        padding: 0;
        outline: 0;
        border: none;
        float: right
    }

.chzn-container-single .chzn-drop {
    width: 100%;
    box-sizing: border-box;
    -webkit-border-radius: 0 0 4px 4px;
    -moz-border-radius: 0 0 4px 4px;
    border-radius: 0 0 4px 4px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    left: -9000px;
    -webkit-box-shadow: 0 4px 5px rgb(0 0 0 / 30%);
    -moz-box-shadow: 0 4px 5px rgba(0,0%,0%,30%);
    -o-box-shadow: 0 4px 5px rgba(0,0%,0%,30%);
    box-shadow: 0 4px 5px rgb(0 0 0 / 30%)
}

.chzn-container-single-nosearch .chzn-search input {
    position: absolute;
    top: -9000px
}

.chzn-container .chzn-drop .chzn-results {
    margin: 0 4px 4px 0;
    max-height: 190px;
    position: relative;
    overflow-x: hidden;
    overflow-y: auto
}

    .chzn-container .chzn-drop .chzn-results::-webkit-scrollbar {
        width: 10px;
        background: #f1f1f1;
        border-radius: 5px
    }

    .chzn-container .chzn-drop .chzn-results::-webkit-scrollbar-thumb {
        background: rgba(193,193,193,.8);
        border-radius: 5px
    }

        .chzn-container .chzn-drop .chzn-results::-webkit-scrollbar-thumb:hover {
            background: rgba(168,168,168,.8)
        }

.chzn-container .chzn-results li {
    display: none;
    line-height: 100%;
    margin: 0;
    list-style: none
}

.chzn-container .chzn-results .active-result {
    cursor: pointer;
    display: list-item
}

.chzn-container .chzn-results .highlighted {
    background: #f7f7f7;
    color: #fff
}

.chzn-container .chzn-results li em {
    background: #feffde;
    font-style: normal
}

.chzn-container .chzn-results .highlighted em {
    background: 0 0
}

.chzn-container .chzn-results .no-results {
    background: #f4f4f4;
    display: list-item
}

.chzn-container .chzn-results .group-result {
    cursor: default;
    color: #999;
    font-weight: 700
}

.chzn-container .chzn-results .group-option {
    margin-bottom: 2px;
    padding-left: 20px;
    line-height: 35px;
    font-size: 14px;
    color: #666
}

.chzn-container-active .chzn-single {
    -webkit-box-shadow: 0 0 5px rgba(0,0,0,.3);
    -moz-box-shadow: 0 0 5px rgba(0,0,0,.3);
    -o-box-shadow: 0 0 5px rgba(0,0,0,.3);
    box-shadow: 0 0 5px rgba(0,0,0,.3);
    border: 1px solid transparent
}

.chzn-container-active .chzn-single-with-drop {
    -webkit-border-bottom-left-radius: 0;
    -webkit-border-bottom-right-radius: 0;
    -moz-border-radius-bottomleft: 0;
    -moz-border-radius-bottomright: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    -webkit-box-shadow: 0 0 5px rgb(0 0 0 / 30%);
    -moz-box-shadow: 0 0 5px rgb(0 0 0 / 30%);
    -o-box-shadow: 0 4px 5px rgb(0 0 0 / 15%);
    box-shadow: 0 0 5px rgb(0 0 0 / 30%)
}

    .chzn-container-active .chzn-single-with-drop div {
        background: 0 0;
        border-left: none
    }

.chzn-container-active .chzn-choices {
    -webkit-box-shadow: 0 0 5px rgba(0,0,0,.3);
    -moz-box-shadow: 0 0 5px rgba(0,0,0,.3);
    -o-box-shadow: 0 0 5px rgba(0,0,0,.3);
    box-shadow: 0 0 5px rgba(0,0,0,.3);
    border: 1px solid #5897fb
}

    .chzn-container-active .chzn-choices .search-field input {
        color: #111 !important
    }

.chzn-disabled {
    cursor: default;
    opacity: .5 !important
}

    .chzn-disabled .chzn-single {
        cursor: default
    }

    .chzn-disabled .chzn-choices .search-choice .search-choice-close {
        cursor: default
    }

#binding_module {
    position: fixed;
    z-index: 100000;
    top: 20%
}

    #binding_module .box_bg {
        width: 100%;
        height: 100%;
        background-color: #fff;
        position: absolute;
        top: 0;
        left: 0;
        -moz-box-shadow: 0 0 20px #000;
        -webkit-box-shadow: 0 0 20px #000;
        box-shadow: 0 0 20px #000;
        -webkit-border-radius: 8px;
        -moz-border-radius: 8px;
        border-radius: 8px
    }

    #binding_module .noCtrTrack {
        top: 12px;
        right: 0;
        width: 40px;
        height: 26px;
        font-size: 30px;
        color: #000;
        opacity: .2;
        text-decoration: none;
        font-weight: 700;
        position: absolute;
        z-index: 100000
    }

        #binding_module .noCtrTrack:hover {
            color: #000;
            text-decoration: none;
            cursor: pointer;
            opacity: .4
        }

    #binding_module #lb-wrapper {
        width: 400px;
        padding: 20px;
        position: relative;
        z-index: 10;
        zoom: 1;
        background-color: #fff;
        -webkit-background-clip: padding-box;
        -moz-background-clip: padding-box;
        background-clip: padding-box
    }

        #binding_module #lb-wrapper .lib_txt {
            border: 1px solid #ccc;
            border-radius: 3px !important;
            height: 36px;
            line-height: 36px;
            width: 370px;
            padding: 0 7px;
            color: #333;
            font-size: 16px;
            box-shadow: 0 1px 0 rgba(255,255,255,.8),inset 0 1px 2px rgba(0,0,0,.06)
        }

        #binding_module #lb-wrapper .title {
            margin: 0 20px 8px 8px;
            line-height: 24px;
            font-size: 18px;
            color: #000
        }

        #binding_module #lb-wrapper .provide {
            margin: 0 8px;
            font-size: 12px
        }

        #binding_module #lb-wrapper .error_note_box {
            border: 1px #ffdb83 solid;
            color: red;
            padding: 10px;
            margin: 15px 5px;
            background: #fff9e1;
            display: none
        }

        #binding_module #lb-wrapper .on_error {
            margin: 0;
            color: red;
            display: none
        }

        #binding_module #lb-wrapper .row {
            margin-top: 10px;
            padding: 0 8px
        }

            #binding_module #lb-wrapper .row label {
                display: block;
                font-size: 14px;
                color: #555;
                margin-bottom: 4px
            }

            #binding_module #lb-wrapper .row .note {
                margin-top: 8px;
                color: #999
            }

        #binding_module #lb-wrapper .protect {
            padding-left: 30px;
            position: relative;
            overflow: hidden;
            margin-bottom: 15px
        }

            #binding_module #lb-wrapper .protect span {
                color: #767676;
                display: block;
                margin-top: 4px
            }

            #binding_module #lb-wrapper .protect .ckb {
                position: absolute;
                top: 2px;
                left: 4px
            }

        #binding_module #lb-wrapper .signbtn {
            border-radius: 4px;
            -ms-border-radius: 4px;
            -moz-border-radius: 4px;
            -webkit-border-radius: 4px;
            padding: 2px 12px;
            line-height: 25px;
            display: inline-block;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px
        }

        #binding_module #lb-wrapper .signin {
            color: #fff
        }

        #binding_module #lb-wrapper .signup {
            background: #fff;
            border: 1px solid #d0d0d0
        }

.SignInButton {
    cursor: pointer
}

.global_login_sec {
    position: relative;
    z-index: 1001;
/*    font-family: Arial*/
}

.signin_box_sec {
    position: absolute;
    display: none;
    line-height: 30px
}

    .signin_box_sec .signin_container {
        width: 280px;
        padding: 25px 20px;
        background: #fff;
        height: auto
    }

    .signin_box_sec .error_login_box {
        display: none;
        border: 1px solid #dd3c10;
        padding: 15px;
        background: #ffebe9;
        color: #777;
        font-size: 12px;
        line-height: 18px;
        margin-top: 10px;
        text-align: left
    }

    .signin_box_sec .g_s_txt {
        width: 100%;
        height: 35px;
        line-height: 35px;
        text-indent: 10px;
        border: 1px solid #eee;
        background: #eee;
        font-size: 14px;
        color: #333;
        font-style: italic;
        -moz-border-radius: 2px;
        -webkit-border-radius: 2px;
        border-radius: 2px
    }

    .signin_box_sec .forgot {
        float: right;
        text-align: right;
        font-size: 14px;
        color: #aaa;
        margin-top: 10px
    }

    .signin_box_sec .signin {
        clear: both;
        width: 100%;
        height: 35px;
        line-height: 35px;
        margin-top: 15px;
        text-align: center;
        color: #fff;
        font-size: 14px;
        border: 0;
        -moz-border-radius: 2px;
        -webkit-border-radius: 2px;
        border-radius: 2px
    }

    .signin_box_sec h4 {
        font-size: 14px;
        margin: 10px 0;
        text-indent: 3px;
        color: #333;
        text-align: left
    }

    .signin_box_sec ul li {
        float: left;
        width: 60px;
        height: 28px;
        margin: 3px 5px;
        overflow: hidden;
        border-radius: 3px;
        cursor: pointer
    }

        .signin_box_sec ul li.fb-login-button {
            background: #1877f2 url(../images/user/facebook_icon.png) no-repeat center center
        }

    .signin_box_sec #fb_button {
        background: url(../images/ico/icon_facebook.png) #3c5998 center no-repeat
    }

        .signin_box_sec #fb_button:hover {
            background: url(../images/ico/icon_facebook.png) #4363a8 center no-repeat
        }

    .signin_box_sec #twitter_btn {
        background: url(../images/ico/icon_witter.png) #3498e4 center no-repeat
    }

        .signin_box_sec #twitter_btn:hover {
            background: url(../images/ico/icon_witter.png) #38a5f8 center no-repeat
        }

    .signin_box_sec #paypalLogin {
        width: 60px !important;
        background: url(../images/ico/icon_paypal.png) #0093e9 center no-repeat
    }

        .signin_box_sec #paypalLogin:hover {
            background: url(../images/ico/icon_paypal.png) #03a1fd center no-repeat
        }

        .signin_box_sec #paypalLogin .PPBlue_V2 {
            border: 0 !important;
            box-shadow: 0 0 !important;
            background: 0 0 !important
        }

            .signin_box_sec #paypalLogin .PPBlue_V2 * {
                visibility: hidden !important
            }

    .signin_box_sec #paypalLogin2 {
        width: 60px !important;
        background: url(../images/ico/icon_paypal.png) #0093e9 center no-repeat
    }

        .signin_box_sec #paypalLogin2:hover {
            background: url(../images/ico/icon_paypal.png) #03a1fd center no-repeat
        }

        .signin_box_sec #paypalLogin2 .PPBlue_V2 {
            border: 0 !important;
            box-shadow: 0 0 !important;
            background: 0 0 !important
        }

            .signin_box_sec #paypalLogin2 .PPBlue_V2 * {
                visibility: hidden !important
            }

    .signin_box_sec #vk_button {
        background: url(../images/ico/icon_vk.png) #507299 center no-repeat
    }

        .signin_box_sec #vk_button:hover {
            background: url(../images/ico/icon_vk.png) #5f86b4 center no-repeat
        }

    .signin_box_sec #instagram_button {
        background: url(../images/ico/icon_instagram.png) #8e6151 center no-repeat
    }

        .signin_box_sec #instagram_button:hover {
            background: url(../images/ico/icon_instagram.png) #987061 center no-repeat
        }

    .signin_box_sec .signup {
        display: block;
        height: 33px;
        line-height: 33px;
        border-style: solid;
        border-width: 1px;
        -moz-border-radius: 2px;
        -webkit-border-radius: 2px;
        border-radius: 2px;
        font-size: 14px;
        text-align: center;
        text-decoration: none
    }

.user .inbox_tips {
    display: inline-block;
    height: 15px;
    line-height: 15px;
    margin-left: 5px;
    margin-bottom: -3px;
    padding: 0 4px;
    overflow: hidden;
    color: #fff;
    text-align: center;
    border-radius: 5px
}

.global_account_sec {
    position: relative;
    z-index: 10006
}

.account_container_sec {
    position: absolute;
    display: none
}

.account_box_sec {
    width: 160px;
    padding: 20px;
    background: #fff;
    border: 1px solid #ddd;
    -moz-box-shadow: 0 0 15px 0 rgba(153,153,153,.5);
    -webkit-box-shadow: 0 0 15px 0 rgba(153,153,153,.5);
    box-shadow: 0 0 15px 0 rgba(153,153,153,.5)
}

    .account_box_sec .rows {
        height: 25px;
        line-height: 25px;
        overflow: hidden;
        margin-bottom: 10px;
        text-align: left
    }

        .account_box_sec .rows a {
            font-size: 16px;
            color: #333
        }

        .account_box_sec .rows b {
            width: 20px;
            height: 20px;
            line-height: 20px;
            margin-left: 5px;
            font-size: 12px;
            display: inline-block;
            color: #fff;
            border-radius: 10px;
            text-align: center
        }

    .account_box_sec .btn {
        margin-top: 25px
    }

        .account_box_sec .btn a {
            width: 158px;
            height: 33px;
            line-height: 33px;
            text-align: center;
            display: inline-block;
            border-width: 1px;
            border-style: solid;
            -moz-border-radius: 2px;
            -webkit-border-radius: 2px;
            border-radius: 2px;
            text-decoration: none
        }

#fixed_login_popup {
    display: none;
    position: fixed;
    z-index: 10006
}

#fixed_login_box, #fixed_login_popup, #fixed_login_popup_bg {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

#fixed_login_box {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute
}

#fixed_login_popup_bg {
    background-color: rgba(0,0,0,.5);
    position: absolute
}

#fixed_login_popup .center_content {
    position: relative;
    z-index: 1;
    padding: 25px 30px;
    border-radius: 5px;
    background-color: #fff;
    width: 600px;
    position: relative
}

#fixed_login_popup .signin_close {
    position: absolute;
    top: 7px;
    right: 9px;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    color: #666
}

#fixed_login_popup h3 {
    line-height: 32px;
    font-size: 24px;
    color: #000;
    text-align: center
}

#fixed_login_popup .login_brief {
    line-height: 22px;
    font-size: 14px;
    color: #666;
    padding-bottom: 6px;
    display: none
}

#fixed_login_popup .row {
    padding-top: 12px
}

    #fixed_login_popup .row label {
        display: block;
        line-height: 24px;
        font-size: 14px;
        color: #333;
        text-align: center
    }

    #fixed_login_popup .row input {
        display: block;
        width: 100%;
        height: 44px;
        line-height: 44px;
        box-sizing: border-box;
        border: 1px solid #bbb;
        border-radius: 5px;
        padding: 0 10px
    }

#fixed_login_popup .flex_box {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 10px 0;
    line-height: 28px;
    font-size: 14px
}

#fixed_login_popup .protect {
    color: #222;
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    margin-right: 20px
}

    #fixed_login_popup .protect i {
        width: 16px;
        height: 16px;
        line-height: 16px;
        margin-right: 10px;
        border-radius: 2px;
        border-width: 1px;
        border-style: solid;
        text-align: center;
        font-size: 16px
    }

        #fixed_login_popup .protect i::before {
            content: "\e647";
            font-family: iconfont;
            color: #fff
        }

#fixed_login_popup .flex_box input {
    display: none
}

    #fixed_login_popup .flex_box input:not(:checked) ~ label i {
        background-color: unset
    }

        #fixed_login_popup .flex_box input:not(:checked) ~ label i::before {
            display: none
        }

#fixed_login_popup .forgot {
    color: #666
}

    #fixed_login_popup .forgot a {
        text-decoration: underline;
        color: #222
    }

#fixed_login_popup .signbtn {
    display: block;
    width: 100%;
    height: 50px;
    line-height: 50px;
    margin-top: 14px;
    font-size: 18px;
    color: #fff;
    border-radius: 5px
}

#fixed_login_popup .or {
    height: 42px;
    line-height: 42px;
    text-align: center;
    font-size: 14px;
    color: #666;
    text-transform: uppercase
}

#fixed_login_popup .oauth_list {
    display: flex;
    justify-content: center
}

    #fixed_login_popup .oauth_list li {
        width: 40px;
        height: 40px;
        margin: 0 16px;
        overflow: hidden;
        border-radius: 40px;
        cursor: pointer;
        position: relative
    }

        #fixed_login_popup .oauth_list li * {
            opacity: 0;
            position: relative;
            z-index: 1
        }

        #fixed_login_popup .oauth_list li#google_login * {
            opacity: 1
        }

        #fixed_login_popup .oauth_list li:after {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            font-family: iconfont;
            font-size: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 0
        }

#fixed_login_popup #facebook_btn::after {
    content: "\e711";
    color: #1878f3
}

#fixed_login_popup #twitter_btn::after {
    content: "\e9b2";
    color: #2d8dc5
}

#fixed_login_popup #google_login::after {
    background: #fff;
    z-index: 1;
    pointer-events: none;
    content: "";
    background: url(../images/global/google.svg) no-repeat 100%/contain
}

#fixed_login_popup #paypalLogin::after {
    content: "\e6db";
    color: #0093e9
}

#fixed_login_popup #paypalLogin2::after {
    content: "\e6db";
    color: #0093e9
}

#fixed_login_popup #vk_button::after {
    content: "\e6d9";
    color: #507299
}

#fixed_login_popup .oauth_list li > a {
    display: block;
    height: 40px
}

#fixed_login_popup .new_customer {
    line-height: 18px;
    font-size: 14px;
    color: #666;
    text-align: center;
    padding-top: 14px
}

    #fixed_login_popup .new_customer a {
        text-decoration: underline;
        color: #000
    }

#fixed_login_popup .error_note_box {
    display: none;
    border: 1px solid #ffdb83;
    padding: 10px;
    background: #fff9e1;
    color: red;
    font-size: 12px;
    line-height: 18px;
    margin-top: 6px
}

#fixed_login_popup .verification_button_box {
    display: none;
    margin: 10px 0;
    font-size: 0
}

    #fixed_login_popup .verification_button_box a {
        display: inline-block;
        vertical-align: middle;
        box-sizing: border-box;
        padding: 0 13px;
        width: auto;
        height: 27px;
        line-height: 25px;
        border: 1px solid #ff2521;
        border-radius: 5px;
        background-color: #ff2521;
        color: #fff;
        text-align: center;
        font-size: 12px;
        text-decoration: none
    }

    #fixed_login_popup .verification_button_box #verified_send_email_btn {
        margin-left: 10px;
        background-color: transparent;
        color: #ff2521
    }

#credential_picker_container {
    z-index: 10010 !important
}

@media screen and (max-width:500px) {
    #fixed_login_popup .center_content {
        width: 90%;
        box-sizing: border-box;
        padding: 20px 25px
    }
}

.themes_position {
    position: absolute;
    z-index: 10
}

.index .themes_position {
    display: none
}

.themes_position.lefttop {
    top: 10px;
    left: 10px
}

.themes_position.righttop {
    top: 10px;
    right: 10px
}

.themes_sales_style {
    font-size: 16px;
    width: 52px;
    line-height: 26px;
    text-align: center;
    font-size: 12px
}

    .themes_sales_style.type_oval {
        padding: 0 13px;
        width: auto;
        border-radius: 25px
    }

    .themes_sales_style.type_round {
        border-radius: 50% !important;
        padding: 0 !important;
        width: 46px;
        line-height: 46px;
        height: 46px;
        overflow: hidden
    }

        .themes_sales_style.type_round.text_word {
            line-height: 16px;
            box-sizing: border-box;
            padding: 8px !important
        }

    .themes_sales_style.type_rectangle {
        border-radius: 0 !important;
        padding: 0 12px;
        width: auto
    }

    .themes_sales_style.type_text {
        padding: 0 5px;
        width: auto;
        background-color: transparent
    }

.mixed_wholesale_tips {
    display: inline-block;
    padding: 0 8px;
    line-height: 24px;
    border-radius: 5px;
    background-color: #ffe7e7;
    color: #f16056;
    font-size: 12px
}

.password_box_mask {
    width: 100%;
    height: 100%;
    position: fixed;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    background: rgb(0,0,0,.6);
    z-index: 10000
}

#password_box {
    padding: 20px 20px 107px;
    width: 700px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    background-color: #fff;
    border-radius: 10px;
    box-sizing: border-box;
    z-index: 10001
}

    #password_box .close {
        float: right;
        cursor: pointer
    }

    #password_box .container {
        width: 523px;
        margin: 0 auto
    }

    #password_box .tips_box {
        text-align: center
    }

        #password_box .tips_box .t_title {
            font-size: 30px;
            color: #000
        }

        #password_box .tips_box .t_subtitle {
            margin-top: 15px;
            font-size: 14px;
            color: #666
        }

    #password_box .input {
        margin-top: 30px
    }

        #password_box .input .box_input {
            display: block;
            margin: 0 auto;
            padding: 0 15px;
            width: 100%;
            height: 56px;
            line-height: 54px;
            border: 1px solid #d2d2d2;
            border-radius: 10px;
            background-color: #fff;
            color: #000;
            font-size: 24px;
            box-sizing: border-box
        }

            #password_box .input .box_input.null {
                border-color: red
            }

    #password_box .tips_error {
        font-size: 14px;
        color: red;
        text-align: left;
        margin-top: 10px
    }

    #password_box .submit_btn {
        display: block;
        margin: 30px auto 0;
        width: 100%;
        height: 60px;
        line-height: 60px;
        border: 0;
        background-color: #000;
        border-radius: 25px;
        color: #fff;
        text-align: center;
        font-size: 18px;
        cursor: pointer
    }

.global_download_btn {
    display: inline-block;
    vertical-align: middle;
    border-radius: 5px;
    margin-top: 16px
}

    .global_download_btn.text_btn {
        width: 100%;
        line-height: 1.3;
        padding: 15px 20px;
        font-size: 18px;
        text-decoration: none;
        box-sizing: border-box;
        text-align: center;
        text-transform: uppercase
    }

    .global_download_btn.icon_btn {
        width: 46px;
        height: 46px;
        line-height: 46px;
        text-align: center;
        text-decoration: none;
        font-size: 24px;
        box-sizing: border-box
    }

.download_mask {
    width: 100%;
    height: 100%;
    position: fixed;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    background: rgb(0,0,0,.6);
    z-index: 10000
}

#download_list_pop {
    padding: 20px 30px;
    position: fixed;
    width: 900px;
    height: 80vh;
    background-color: #fff;
    border-radius: 10px;
    margin: auto;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 10001;
    box-sizing: border-box
}

    #download_list_pop .pop_title {
        line-height: 70px;
        font-size: 32px;
        position: relative
    }

        #download_list_pop .pop_title .close_btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 0;
            font-size: 20px
        }

    #download_list_pop .list_box {
        height: calc(80vh - 120px);
        overflow-y: auto
    }

        #download_list_pop .list_box .download_item {
            margin-top: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 30px;
            background-color: #f3f3f3;
            box-sizing: border-box
        }

            #download_list_pop .list_box .download_item:first-child {
                margin-top: 0
            }

            #download_list_pop .list_box .download_item .info_box {
                display: flex;
                align-items: center;
                justify-content: left;
                flex-wrap: wrap;
                flex: 1
            }

                #download_list_pop .list_box .download_item .info_box .info {
                    width: calc(100% - 110px)
                }

                #download_list_pop .list_box .download_item .info_box .pic_box {
                    margin-right: 20px;
                    height: 90px;
                    width: 90px;
                    position: relative
                }

                    #download_list_pop .list_box .download_item .info_box .pic_box img {
                        position: absolute
                    }

                #download_list_pop .list_box .download_item .info_box .tips {
                    display: block;
                    font-size: 16px;
                    color: #666
                }

                    #download_list_pop .list_box .download_item .info_box .tips .size {
                        margin-left: 6px
                    }

                #download_list_pop .list_box .download_item .info_box .name {
                    display: inline-block;
                    margin-bottom: 20px;
                    font-size: 16px;
                    color: #222;
                    word-break: break-word
                }

            #download_list_pop .list_box .download_item .btn a {
                display: flex;
                vertical-align: middle;
                text-decoration: none;
                padding: 0 25px;
                height: 40px;
                box-sizing: border-box;
                line-height: 40px;
                box-sizing: border-box;
                font-size: 14px;
                color: #333;
                border: 1px solid #333
            }

                #download_list_pop .list_box .download_item .btn a i {
                    display: inline-block;
                    vertical-align: middle
                }

@media screen and (max-width:1200px) {
    .global_download_btn.text_btn {
        width: 100%;
        margin-left: 0;
        margin-top: 20px;
        box-sizing: border-box;
        text-align: center
    }

    .global_download_btn.icon_btn {
        display: block;
        margin-left: 0;
        margin-top: 20px;
        width: 100%;
        box-sizing: border-box;
        text-align: center
    }
}

@media screen and (max-width:1000px) {
    #download_list_pop {
        width: 92%;
        padding: 20px 15px
    }

        #download_list_pop .pop_title {
            line-height: 45px;
            font-size: 20px
        }

        #download_list_pop .list_box .download_item {
            padding: 15px
        }

            #download_list_pop .list_box .download_item .info_box .pic_box {
                width: 60px;
                height: 60px
            }

            #download_list_pop .list_box .download_item .info_box .name {
                font-size: 14px;
                margin-bottom: 10px
            }

            #download_list_pop .list_box .download_item .info_box .tips {
                font-size: 14px
            }

            #download_list_pop .list_box .download_item .btn a {
                padding: 0 12px;
                font-size: 12px
            }

        #download_list_pop .pop_title .close_btn {
            font-size: 16px
        }

        #download_list_pop .list_box {
            height: calc(80vh - 85px);
            overflow-y: auto
        }
}

@media screen and (max-width:768px) {
    #password_box {
        width: 92%;
        padding: 20px 15px 50px
    }

        #password_box .container {
            width: 100%
        }

        #password_box .tips_box .t_title {
            font-size: 24px
        }

        #password_box .input {
            margin-top: 15px
        }

            #password_box .input .box_input {
                height: 40px;
                line-height: 38px
            }

        #password_box .submit_btn {
            margin-top: 15px;
            height: 40px;
            line-height: 40px;
            font-size: 14px
        }
}

.new_win_alert.paypal_error_alert_tips {
    width: 80%;
    max-width: 400px
}

    .new_win_alert.paypal_error_alert_tips .win_tips {
        min-height: 200px;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center
    }

#review_video_box {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100001
}

    #review_video_box .review_video_close {
        position: fixed;
        top: 10px;
        right: 10px;
        width: 37px;
        height: 37px;
        background: url(../images/global/magnify_close.png) no-repeat center #fff;
        border-radius: 50%;
        background-size: 50%
    }

    #review_video_box .content {
        position: relative;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        max-width: 700px;
        max-height: 700px;
        width: 100%;
        height: 100%;
        overflow: hidden
    }

        #review_video_box .content video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000
        }

@media screen and (max-width:768px) {
    #review_video_box .content {
        max-width: 90%;
        max-height: 90vw
    }
}

.global_products_common .like_title {
    margin-bottom: 35px
}

.global_products_common .common_products_box .list_products_item {
    margin-right: 48px;
    margin-bottom: 40px;
    float: left
}

    .global_products_common .common_products_box .list_products_item .item_img {
        display: block;
        position: relative
    }

        .global_products_common .common_products_box .list_products_item .item_img .item_img_tab {
            width: 100%;
            height: 100%;
            text-align: center;
            overflow: hidden
        }

    .global_products_common .common_products_box .list_products_item .item_name {
        display: -webkit-box;
        text-overflow: ellipsis;
        overflow: hidden;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        max-height: 48px;
        line-height: 24px;
        margin-top: 14px;
        font-size: 16px;
        overflow: hidden;
        color: #333
    }

        .global_products_common .common_products_box .list_products_item .item_name em {
            color: red
        }

    .global_products_common .common_products_box .list_products_item .star_box {
        font-size: 0;
        display: flex;
        align-items: center;
        margin-top: 0
    }

    .global_products_common .common_products_box .list_products_item .star_item {
        display: inline-block;
        vertical-align: middle;
        font-size: 14px
    }

    .global_products_common .common_products_box .list_products_item .item_price {
        margin-top: 6px;
        font-size: 20px;
        color: #e00000;
        font-family: Montserrat-Light
    }

        .global_products_common .common_products_box .list_products_item .item_price del {
            margin-left: 5px
        }

        .global_products_common .common_products_box .list_products_item .item_price .themes_products_origin_price {
            font-family: Montserrat-Light
        }

.global_products_common .common_products_box {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    margin-top: 0
}

    .global_products_common .common_products_box .list_products_item .item_attr {
        margin-top: 5px;
        font-size: 0;
        position: relative;
        z-index: 1
    }

        .global_products_common .common_products_box .list_products_item .item_attr .attr_item_select {
            margin-bottom: 5px;
            display: inline-block;
            vertical-align: middle;
            margin-__right__: 5px;
            width: 19px;
            height: 19px;
            border: 1px solid #eaecee;
            border-radius: 100%;
            position: relative;
            cursor: pointer;
            opacity: 1
        }

            .global_products_common .common_products_box .list_products_item .item_attr .attr_item_select.current, .global_products_common .common_products_box .list_products_item .item_attr .attr_item_select:hover {
                border-color: #121212
            }

            .global_products_common .common_products_box .list_products_item .item_attr .attr_item_select.color_item span {
                display: block;
                width: 15px;
                height: 15px;
                border-radius: 100%;
                position: absolute;
                top: 2px
            }

            .global_products_common .common_products_box .list_products_item .item_attr .attr_item_select.picture_item {
                border-radius: 0
            }

                .global_products_common .common_products_box .list_products_item .item_attr .attr_item_select.picture_item .check_box {
                    display: block;
                    width: 100%;
                    height: 100%
                }

                .global_products_common .common_products_box .list_products_item .item_attr .attr_item_select.picture_item span {
                    width: 17px;
                    height: 17px;
                    border: 1px solid #e0e0e1;
                    background-color: #f4f4f6;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%,-50%);
                    box-sizing: border-box
                }

                .global_products_common .common_products_box .list_products_item .item_attr .attr_item_select.picture_item img {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%,-50%);
                    opacity: 1
                }

            .global_products_common .common_products_box .list_products_item .item_attr .attr_item_select.text_item {
                width: auto;
                max-width: 50px;
                line-height: 19px;
                padding: 0 6px;
                border-radius: 0
            }

                .global_products_common .common_products_box .list_products_item .item_attr .attr_item_select.text_item .check_box {
                    display: block;
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    font-size: 12px;
                    text-overflow: ellipsis;
                    white-space: nowrap
                }

                .global_products_common .common_products_box .list_products_item .item_attr .attr_item_select.text_item span {
                    width: 17px;
                    height: 17px;
                    border: 1px solid #e0e0e1;
                    background-color: #f4f4f6;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%,-50%);
                    box-sizing: border-box
                }

        .global_products_common .common_products_box .list_products_item .item_attr .item_attr_list {
            display: inline-block;
            vertical-align: middle;
            width: calc(100% - 27px)
        }

        .global_products_common .common_products_box .list_products_item .item_attr .ext_list {
            padding-bottom: 10px;
            display: inline-block;
            vertical-align: top;
            width: 27px;
            position: relative
        }

            .global_products_common .common_products_box .list_products_item .item_attr .ext_list .ext_title {
                height: 21px;
                line-height: 21px;
                font-size: 12px;
                color: #555;
                cursor: pointer;
                text-align: center
            }

            .global_products_common .common_products_box .list_products_item .item_attr .ext_list .list {
                padding: 10px 5px;
                background-color: #fff;
                opacity: 0;
                border-radius: 5px;
                transition: .6s;
                position: absolute;
                top: calc(100% - 6px);
                right: 0;
                pointer-events: none;
                box-shadow: 0 0 20px #efefef;
                box-sizing: border-box;
                font-size: 0
            }

                .global_products_common .common_products_box .list_products_item .item_attr .ext_list .list:before {
                    content: '';
                    display: inline-block;
                    width: 0;
                    height: 0;
                    border-width: 0 7px 7px 7px;
                    border-style: solid;
                    border-color: transparent transparent #fff transparent;
                    position: absolute;
                    top: -7px
                }

            .global_products_common .common_products_box .list_products_item .item_attr .ext_list:hover .list {
                opacity: 1;
                pointer-events: unset
            }

            .global_products_common .common_products_box .list_products_item .item_attr .ext_list .list .attr_item_select {
                margin-__right__: 4px
            }

                .global_products_common .common_products_box .list_products_item .item_attr .ext_list .list .attr_item_select:last-child {
                    margin-__right__: 0
                }

    .global_products_common .common_products_box .list_products_item .attribute_show_pic {
        opacity: 0;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        background-color: #fff;
        transition: .4s
    }

        .global_products_common .common_products_box .list_products_item .attribute_show_pic.current {
            opacity: 1
        }

        .global_products_common .common_products_box .list_products_item .attribute_show_pic img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
            opacity: 1
        }

    .global_products_common .common_products_box .list_products_item .item_img {
        overflow: hidden
    }

        .global_products_common .common_products_box .list_products_item .item_img .item_img_tab img {
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            margin: auto
        }

.default_products_list4 #turn_page li span font {
    width: auto;
    display: inline-block
}

.default_products_list4 .add_cart {
    width: 100%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 14px;
    text-decoration: none
}

.global_products_common .common_products_box .list_products_item:hover .add_cart {
    bottom: 0
}

.global_products_common .common_products_box .list_products_item {
    width: calc(((100% + 19px)/ 5) - 19px);
    margin-right: 19px;
    margin-bottom: 36px
}

    .global_products_common .common_products_box .list_products_item:nth-child(5n) {
        margin-right: 0
    }

.default_products_list4 .add_cart {
    display: none
}

.global_products_common .common_products_box .list_products_item .item_img {
    padding: 1px;
    background: #e5e5e5;
    border-radius: 8px;
    overflow: hidden;
    transition: all .3s ease-in-out
}

    .global_products_common .common_products_box .list_products_item .item_img .item_img_tab {
        width: calc(100% - 2px);
        background: #fff;
        padding: 1px;
        transition: all .3s ease-in-out;
        border-radius: 8px
    }

.global_products_common .common_products_box .list_products_item .compute_item_img .compute_process_img {
    background: #fff;
    border-radius: 5px
}

.global_products_common .common_products_box .list_products_item:hover .item_img {
    background: #333
}

    .global_products_common .common_products_box .list_products_item:hover .item_img .item_img_tab {
        background: #333
    }

.global_products_common .common_products_box .list_products_item .item_fav_btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 4px
}

    .global_products_common .common_products_box .list_products_item .item_fav_btn:hover {
        text-decoration: none;
        background: #333
    }

    .global_products_common .common_products_box .list_products_item .item_fav_btn.is_in:hover {
        background: #333
    }

        .global_products_common .common_products_box .list_products_item .item_fav_btn.is_in .iconfont, .global_products_common .common_products_box .list_products_item .item_fav_btn.is_in:hover .iconfont {
            color: red
        }

    .global_products_common .common_products_box .list_products_item .item_fav_btn .iconfont {
        font-size: 15px
    }

.global_products_common .common_products_box .list_products_item.list_products_item_2 .item_fav_btn .iconfont {
    font-size: 18px
}

.global_products_common .common_products_box .list_products_item.list_products_item_2 .item_fav_btn.is_in .iconfont {
    font-size: 15px
}

.global_products_common .common_products_box .list_products_item .item_fav_btn:hover .iconfont {
    color: #fff
}

.global_products_common .common_products_box .list_products_item .item_fav_btn.is_in .iconfont:before {
    content: "\e6b0"
}

.global_products_common .common_products_box .list_products_item .item_search_btn {
    position: absolute;
    top: 45px;
    right: 10px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 4px
}

    .global_products_common .common_products_box .list_products_item .item_search_btn:hover {
        background: #333;
        text-decoration: none
    }

    .global_products_common .common_products_box .list_products_item .item_search_btn .iconfont {
        font-size: 15px
    }

    .global_products_common .common_products_box .list_products_item .item_search_btn:hover .iconfont {
        color: #fff
    }

.global_products_common .common_products_box .list_products_item .star_item:last-child {
    display: none
}

.global_products_common .common_products_box .list_products_item .pro_msg {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-top: 8px
}

    .global_products_common .common_products_box .list_products_item .pro_msg .left {
        width: calc(100% - 60px)
    }

    .global_products_common .common_products_box .list_products_item .pro_msg .right {
        width: 55px;
        height: 42px
    }

.global_products_common .common_products_box .list_products_item .ly_pro_addcart {
    position: static;
    top: unset;
    right: unset;
    transform: unset;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 55px;
    height: 42px;
    border-radius: 20px;
    transition: all .3s ease-in-out;
    background-color: #fed925;
    background-image: url(../../../ico/web/cart_icon.png);
    background-repeat: no-repeat;
    background-position: center center
}

    .global_products_common .common_products_box .list_products_item .ly_pro_addcart i {
        font-size: 0
    }

.global_products_common .common_products_box .list_products_item .products_countdown_box .ly_countdown_box {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    margin-top: 12px
}

    .global_products_common .common_products_box .list_products_item .products_countdown_box .ly_countdown_box .time_span {
        width: 38px;
        margin-left: 10px
    }

        .global_products_common .common_products_box .list_products_item .products_countdown_box .ly_countdown_box .time_span:first-child {
            margin-left: 0
        }

        .global_products_common .common_products_box .list_products_item .products_countdown_box .ly_countdown_box .time_span .num {
            display: inline-block;
            width: 38px;
            height: 38px;
            line-height: 38px;
            font-size: 16px;
            color: #333;
            text-align: center;
            background: #fffadd;
            border-radius: 4px;
            font-family: Montserrat-Bold
        }

        .global_products_common .common_products_box .list_products_item .products_countdown_box .ly_countdown_box .time_span .word {
            display: inline-block;
            width: 38px;
            margin-top: 3px;
            text-align: center;
            font-size: 12px;
            color: #666;
            font-style: italic
        }

    .global_products_common .common_products_box .list_products_item .products_countdown_box .ly_countdown_box i {
        font-size: 16px;
        color: #666;
        margin-top: 10px;
        margin-left: 9px
    }

.global_products_common .themes_prod .sticker_box {
    width: 77%
}

.global_products_common .themes_position {
    display: none
}

#lib_user_favorite .global_products_common {
    margin-top: 0
}

    #lib_user_favorite .global_products_common .common_products_box .list_products_item {
        width: calc(((100% + 19px)/ 4) - 19px)
    }

        #lib_user_favorite .global_products_common .common_products_box .list_products_item:nth-child(5n) {
            margin-right: 19px
        }

        #lib_user_favorite .global_products_common .common_products_box .list_products_item:nth-child(4n) {
            margin-right: 0
        }

    #lib_user_favorite .global_products_common .remove {
        position: absolute;
        top: -7px;
        right: -7px;
        width: 20px;
        height: 20px;
        background: url(../../../ico/web/fav_close_btn.png);
        z-index: 1
    }

#turn_page {
    height: 34px;
    line-height: 34px
}

    #turn_page li span.total_pages {
        display: none
    }

    #turn_page li {
        height: 34px;
        line-height: 34px;
        border-radius: unset
    }

    #turn_page a, #turn_page font {
        min-width: 32px;
        height: 32px;
        border: 1px solid #e5e5e5;
        border-radius: 5px
    }

    #turn_page .page_item_current {
        background: #fff;
        border-color: #333;
        color: #333
    }

.icon_page_prev::before {
    display: inline-block;
    width: 5px;
    height: 9px;
    font-size: 0;
    background: url(../../../ico/web/page_icon_left.png) no-repeat center center/5px 9px;
    margin-top: 11px
}

.icon_page_next::before {
    display: inline-block;
    width: 5px;
    height: 9px;
    font-size: 0;
    background: url(../../../ico/web/page_icon_right.png) no-repeat center center/5px 9px;
    margin-top: 11px
}

#fixed_login_popup .oauth_list, #fixed_login_popup .or {
    display: none !important
}

#fixed_login_popup .signbtn {
    background: #fed925;
    border-color: #fed925
}

#fixed_login_popup .common_box {
    padding: 20px;
    margin: 25px 0 20px;
    background: #fed925;
    border-radius: 6px
}

#fixed_login_popup .retevis_box .list_box {
    display: flex;
    align-items: flex-start;
    justify-content: space-around;
    flex-wrap: wrap
}

    #fixed_login_popup .retevis_box .list_box .list {
        text-align: center;
        margin: 0 10px
    }

    #fixed_login_popup .retevis_box .list_box .icon {
        height: 30px
    }

        #fixed_login_popup .retevis_box .list_box .icon img {
            display: inline-block;
            vertical-align: middle;
            max-width: 100%;
            max-height: 100%
        }

        #fixed_login_popup .retevis_box .list_box .icon span {
            display: inline-block;
            vertical-align: middle;
            height: 100%
        }

    #fixed_login_popup .retevis_box .list_box .name {
        margin: 12px 0;
        font-size: 20px;
        color: #333;
        font-family: Montserrat-Bold
    }

    #fixed_login_popup .retevis_box .list_box .brief {
        font-size: 14px;
        color: #333
    }

.new_star_box {
    display: inline-block
}

    .new_star_box .stars_outer {
        position: relative;
        display: inline-block;
        font-size: 22px;
        color: #fff;
        font-family: iconfont !important;
        -webkit-text-stroke: 1px #000
    }

    .new_star_box .stars_inner {
        position: absolute;
        top: 0;
        left: 0;
        white-space: nowrap;
        overflow: hidden;
        color: #000;
        width: 0%
    }

    .new_star_box .stars_outer::before {
        content: "\e632 \e632 \e632 \e632 \e632"
    }

    .new_star_box .stars_inner::before {
        content: "\e632 \e632 \e632 \e632 \e632"
    }

@media screen and (min-width:100px) {
    .global_products_common .common_products_box .like_products_item .item_img:hover .item_quick_btn > span {
        opacity: 1;
        visibility: unset
    }
}

@media screen and (max-width:1000px) {
    .global_products_common {
        margin-top: 30px
    }

        .global_products_common .wide {
            margin: 0 auto;
            width: 92%
        }

        .global_products_common .like_title {
            font-size: 24px
        }

        .global_products_common .common_products_box {
            margin-top: 30px
        }

            .global_products_common .common_products_box .like_products_item {
                margin-left: 0;
                margin-right: 2%;
                width: 48%
            }

                .global_products_common .common_products_box .like_products_item:nth-child(2n) {
                    margin-right: 0
                }
}

@media (max-width:768px) {
    .review_star_l span {
        font-size: 16px;
        margin-right: 2px
    }

    .review_star .star_0:after {
        font-size: 10px
    }

    .global_products_common {
        margin-top: 10px
    }

        .global_products_common .common_products_box {
            margin-top: 15px
        }

        .global_products_common .like_title {
            margin-bottom: 15px
        }

            .global_products_common .like_title span, .global_products_common .like_title span.s2 {
                font-size: 20px
            }

        .global_products_common .common_products_box .list_products_item {
            width: calc(((100% + 15px)/ 2) - 15px);
            margin-bottom: 15px;
            margin-right: 15px
        }

            .global_products_common .common_products_box .list_products_item:nth-child(2n) {
                margin-right: 0
            }

            .global_products_common .common_products_box .list_products_item:nth-child(5n) {
                margin-right: 15px
            }

            .global_products_common .common_products_box .list_products_item.Columns_1 {
                display: flex;
                justify-content: space-between;
                align-items: center
            }

                .global_products_common .common_products_box .list_products_item.Columns_1 .item_img {
                    width: 40%
                }

                .global_products_common .common_products_box .list_products_item.Columns_1 .prod_info {
                    flex: 1;
                    margin-__left__: 20px
                }

        .global_products_common .common_products_box .like_products_item .item_img .item_quick_btn {
            display: none
        }

        .global_products_common .common_products_box .list_products_item .item_name {
            margin-top: 7px;
            font-size: 14px;
            line-height: 20px
        }

        .global_products_common .common_products_box .list_products_item .products_countdown_box .ly_countdown_box .time_span {
            width: 28px;
            margin-left: 5px
        }

            .global_products_common .common_products_box .list_products_item .products_countdown_box .ly_countdown_box .time_span .num {
                width: 28px;
                height: 28px;
                line-height: 28px;
                font-size: 14px
            }

            .global_products_common .common_products_box .list_products_item .products_countdown_box .ly_countdown_box .time_span .word {
                font-size: 12px;
                text-align: left
            }

        .global_products_common .common_products_box .list_products_item .products_countdown_box .ly_countdown_box i {
            margin-left: 5px;
            margin-top: 4px
        }

        .global_products_common .common_products_box .list_products_item .item_price {
            font-size: 16px
        }

            .global_products_common .common_products_box .list_products_item .item_price .themes_products_origin_price {
                font-size: 14px
            }

        .global_products_common .common_products_box .list_products_item .pro_msg {
            margin-top: 4px
        }

            .global_products_common .common_products_box .list_products_item .pro_msg .right {
                width: 40px;
                height: 30px
            }

        .global_products_common .common_products_box .list_products_item .ly_pro_addcart {
            width: 40px;
            height: 30px;
            background-size: 15px 14px
        }

        .global_products_common .common_products_box .list_products_item .item_fav_btn {
            width: 20px;
            height: 20px;
            line-height: 20px
        }

        .global_products_common .common_products_box .list_products_item .item_search_btn {
            top: 35px;
            width: 20px;
            height: 20px;
            line-height: 20px
        }

            .global_products_common .common_products_box .list_products_item .item_fav_btn .iconfont, .global_products_common .common_products_box .list_products_item .item_search_btn .iconfont {
                font-size: 13px
            }

    #fixed_login_popup .center_content {
        padding: 15px 10px
    }

    #fixed_login_popup .common_box {
        padding: 12px 8px;
        margin: 20px 0
    }

    #fixed_login_popup .retevis_box .list_box .list {
        margin: 0 4px
    }

    #fixed_login_popup .retevis_box .list_box .icon {
        height: 25px
    }

    #fixed_login_popup .retevis_box .list_box .name {
        font-size: 13px;
        margin: 6px 0
    }

    #fixed_login_popup .retevis_box .list_box .brief {
        font-size: 12px
    }

    #fixed_login_popup .signbtn {
        height: 35px;
        line-height: 35px;
        margin-top: 7px
    }

    #fixed_login_popup .row input {
        height: 35px;
        line-height: 35px
    }

    .global_products_common .common_products_box {
        margin-top: 0
    }

    #lib_user_favorite .global_products_common .common_products_box .list_products_item {
        width: calc(((100% + 19px)/ 2) - 19px)
    }

    #lib_user_favorite .user_page_pro {
        margin-bottom: 20px
    }

    .global_shopping_products_recommend .themes_position.righttop {
        display: none
    }

    .global_shopping_products_recommend .pop_products_may_like .like_products_item .themes_products_origin_price {
        display: none
    }
}

#preview {
    position: absolute;
    top: 0;
    left: 0;
    width: auto;
    height: auto;
    z-index: 1;
    opacity: .3
}

.item_quick_btn {
    display: none !important
}
