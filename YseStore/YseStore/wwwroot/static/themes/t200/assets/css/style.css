/*********************************************************************************
    Template Name: Shoplook Multipurpose eCommerce Bootstrap5 Html Template
    Description: A perfect template to build beautiful and unique Electronics websites. It comes with nice and clean design.
    Version: 1.0
    bootstrapģ��⣺HttP://www.bootstrapmb.com
**********************************************************************************/

/* --------------------------------------------------
Table of Content:

1. Google Fonts
2. Basic Styles
3. Typography
4. Container
5. Promotional Top Popup
6. Pre Loader
7. Headers Style
8. Homepage Demos
9. Homepage Sections
   9.0 Image Banners
   9.1 Categories Section
   9.2 Products With Tab Slider
   9.3 Hero/Parallax Banners
   9.4 Featured Content Section
   9.5 Newletter Section
   9.6 Testimonial Slider
   9.7 Info/Simple Text Section
   9.8 Instagram Section
   9.9 Miniproduct List Section
   9.10 Homepage Slideshow
   9.11 Collection Slider
   9.12 Brands Logo Slider
   9.13 Home Blog Post
   9.14 Store Features
   9.15 Promotion Product Popup
   9.16 Custom Content
   9.17 Instagram Shop
10. Collection Banner Grid
11. Breadcrumbs
12. Section
13. Product Grid
14. Product Listview
15. Products Detail Page
16. Sidebar
17. Shop Pages
18. CMS Page
19. Blog Pages
20. Cart Pages
21. Checkout Page Styles
22. Nesletter Popup Styles
23. Footer
24. Checkout Progress
=======================================================================*/

/*======================================================================
  1. Google Fonts
========================================================================*/
@import url('./googleapis-Poppins.css');
@import url('./googleapis-Kreon.css');
@font-face {
    font-family: "Hm-Light";
    src: url("../fonts/HarmonyOS_Sans_Light.eot"); /* IE9 Compat Modes */
    src: url("../fonts/HarmonyOS_Sans_Light.eot?#iefix") format("embedded-opentype"), /* IE6-IE8 */
    url("../fonts/HarmonyOS_Sans_Light.woff2") format("woff2"), /* Super Modern Browsers */
    url("../fonts/HarmonyOS_Sans_Light.woff") format("woff"), /* Pretty Modern Browsers */
    url("../fonts/HarmonyOS_Sans_Light.ttf") format("truetype"), /* Safari, Android, iOS */
    url("../fonts/HarmonyOS_Sans_Light.svg#NotoSans") format("svg"); /* Legacy iOS */
    font-stretch: normal;
    font-style: normal;
    font-weight: normal;
    font-display: swap;
    unicode-range: U+000-5FF;
}

@font-face {
    font-family: "Hm-Regular";
    src: url("../fonts/HarmonyOS_Sans_Regular.eot"); /* IE9 Compat Modes */
    src: url("../fonts/HarmonyOS_Sans_Regular.eot?#iefix") format("embedded-opentype"), /* IE6-IE8 */
    url("../fonts/HarmonyOS_Sans_Regular.woff2") format("woff2"), /* Super Modern Browsers */
    url("../fonts/HarmonyOS_Sans_Regular.woff") format("woff"), /* Pretty Modern Browsers */
    url("../fonts/HarmonyOS_Sans_Regular.ttf") format("truetype"), /* Safari, Android, iOS */
    url("../fonts/HarmonyOS_Sans_Regular.svg#NotoSans") format("svg"); /* Legacy iOS */
    font-stretch: normal;
    font-style: normal;
    font-weight: normal;
    font-display: swap;
    unicode-range: U+000-5FF;
}

@font-face {
    font-family: "Hm-Medium";
    src: url("../fonts/HarmonyOS_Sans_Medium.eot"); /* IE9 Compat Modes */
    src: url("../fonts/HarmonyOS_Sans_Medium.eot?#iefix") format("embedded-opentype"), /* IE6-IE8 */
    url("../fonts/HarmonyOS_Sans_Medium.woff2") format("woff2"), /* Super Modern Browsers */
    url("../fonts/HarmonyOS_Sans_Medium.woff") format("woff"), /* Pretty Modern Browsers */
    url("../fonts/HarmonyOS_Sans_Medium.ttf") format("truetype"), /* Safari, Android, iOS */
    url("../fonts/HarmonyOS_Sans_Medium.svg#NotoSans") format("svg"); /* Legacy iOS */
    font-stretch: normal;
    font-style: normal;
    font-weight: normal;
    font-display: swap;
    unicode-range: U+000-5FF;
}

@font-face {
    font-family: "Hm-Bold";
    src: url("../fonts/HarmonyOS_Sans_Bold.eot"); /* IE9 Compat Modes */
    src: url("../fonts/HarmonyOS_Sans_Bold.eot?#iefix") format("embedded-opentype"), /* IE6-IE8 */
    url("../fonts/HarmonyOS_Sans_Bold.woff2") format("woff2"), /* Super Modern Browsers */
    url("../fonts/HarmonyOS_Sans_Bold.woff") format("woff"), /* Pretty Modern Browsers */
    url("../fonts/HarmonyOS_Sans_Bold.ttf") format("truetype"), /* Safari, Android, iOS */
    url("../fonts/HarmonyOS_Sans_Bold.svg#NotoSans") format("svg"); /* Legacy iOS */
    font-stretch: normal;
    font-style: normal;
    font-weight: normal;
    font-display: swap;
    unicode-range: U+000-5FF;
}

/*======================================================================
  2. Basic Styles
========================================================================*/
* { -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; }
*, ::before, ::after { box-sizing:border-box; -webkit-box-sizing:border-box; }
html { overflow:hidden; overflow-y:auto; }
body {
    font-size: 14px;
    line-height: 1.7;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-family: 'Hm-Regular','Poppins', sans-serif; color:#000; font-weight:400; margin:0; padding:0; } article, aside, details, figcaption, figure, footer, header, nav, section, summary { display: block; }
audio, canvas, video { display: inline-block; }
audio:not([controls]) { display:none; height:0; }
[hidden] { display:none; }
html, button, input, select, textarea {
    font-family: 'Hm-Regular','Poppins', sans-serif;
}
input, textarea { padding:10px 18px; }
input, select { height:40px; padding:0 10px; }
input, button, select, textarea { font-size:14px; background: transparent; border:1px solid #d7d7d7; transition:all 0.4s ease-out 0s; -webkit-transition:all 0.4s ease-out 0s; color:#424242; }
input:focus, input:active, button:focus, button:active, select:focus, select:active, textarea:focus, textarea:active, .form-control:focus { outline:none; box-shadow: none; border-color:#5b77d8; }
input, select, textarea { width: 100%; font-size:13px; box-shadow:none; -webkit-box-shadow:none; border-radius:0; }
input[type="checkbox"]:focus, input[type="radio"]:focus { outline:0; box-shadow:none; }
input[type="checkbox"], input[type="radio"] { height:auto; width:auto; }
.form-check-input[type="checkbox"] { border-radius:0; margin-right:5px; }
select { -webkit-appearance:none; -moz-appearance:none; appearance:none; background-position:right center; background-image:url(../images/arrow-select.png) !important; background-repeat:no-repeat !important; background-position:right 10px center !important; line-height:1.2; text-indent:0.01px; text-overflow: ''; cursor:pointer; padding:8px 28px 8px 15px; }
select option { background-color: #fff; }
iframe { border:0; width:100%; }
a { color:#555555; text-decoration:none; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
a, a:after, a:before { transition:all .3s ease-in-out; -webkit-transition:all .3s ease-in-out; -ms-transition:all .3s ease-in-out; }
a:focus { outline:thin dotted; }
a:active, a:hover { color:#000; outline:0; }
a:hover, a:focus { opacity:0.8; }
a:hover { color:#2358ad; text-decoration:none; }
a:focus { outline:0; }
p { margin:0 0 24px; }
pre { background: #f5f5f5; color: #666; font-family: monospace; font-size: 14px; margin: 20px 0; overflow: auto; padding: 20px; white-space: pre; white-space: pre-wrap; word-wrap: break-word; }
blockquote, q { -webkit-hyphens: none; -moz-hyphens: none; -ms-hyphens: none; hyphens: none; quotes: none; }
blockquote:before, blockquote:after, q:before, q:after { content: ""; content: none; }
blockquote { font-size: 18px; font-style: italic; font-weight: 300; margin: 24px 40px; }
blockquote blockquote { margin-right: 0; }
blockquote cite, blockquote small { font-size: 14px; font-weight: normal; text-transform: uppercase; }
blockquote em, blockquote i { font-style: normal; font-weight: 300; }
blockquote strong, blockquote b { font-weight: 400; }
img { -ms-interpolation-mode:bicubic; border:0; vertical-align:middle; }
svg:not(:root) { overflow:hidden; }
ol, ul { padding:0; margin:0; }
.list-items { margin-left:15px; list-style:none; }
.list-items li { position:relative; list-style:none !important; padding-left:20px; margin-left:0 !important; }
    .list-items li:before {
        content: "\f061";
        font-family: "'Hm-Bold'," !important;
        position: absolute;
        left: 0;
    }
.hide { display:none !important; }

/*======================================================================
  3. Typography
========================================================================*/
h1 a, .h1 a, h2 a, .h2 a, h3 a, .h3 a, h4 a, .h4 a, h5 a, .h5 a, h6 a, .h6 a { color:inherit; text-decoration:none; font-weight:inherit; }
h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6, address, p, pre, blockquote, dl, dd, menu, ol, ul, table, caption, hr { margin:0; margin-bottom:20px; }
h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
    color: #000;
    margin: 0 0 10px;
    font-family: 'Hm-Regular','Rajdhani', sans-serif; font-weight:700; line-height:1.2; overflow-wrap:break-word; word-wrap:break-word; } h1, .h1 { font-size:24px; }
h2, .h2 { font-size:19px; }
h3, .h3 { font-size:18px; }
h4, .h4 { font-size:17px; }
h5, .h5 { font-size:15px; }
h6, .h6 { font-size:14px; }

.head-font {
    font-family: 'Hm-Regular','Rajdhani', sans-serif;
    font-weight: 600;
}
img { max-width:100%; -webkit-transform:translateZ(0); transform:translateZ(0); -moz-transform:translateZ(0); }
p:last-child { margin-bottom:0; }

.pt-0 { padding-top:0 !important; }
.pb-0 { padding-bottom:0 !important; }
.pl-0 { padding-left:0 !important; }
.pr-0 { padding-right:0 !important; }
.ml-0 { margin-left:0 !important; }
.mr-0 { margin-right:0 !important; }
.mt-0 { margin-top:0 !important; }
.mb-0 { margin-bottom:0 !important; }

.no-border { border:0 !important; }
.no-border-bottom { border-bottom:0 !important; }
.no-border-top { border-top:0 !important; }
.no-border-left { border-left:0 !important; }
.no-border-right { border-right:0 !important; }
.border-bottom { border-bottom:1px solid #f2f2f2 !important; }

.text-left { text-align:left !important; }
.text-right { text-align:right !important; }

hr { margin:20px 0; border:0; border-bottom:1px solid #b5b5b5; }
.hidden { display: none; }
.visuallyhidden { border: 0; clip: rect(0 0 0 0); height: 1px; margin: -1px; overflow: hidden; padding: 0; position: absolute; width: 1px; }
.poss_relative { position: relative; }
.poss_absolute { position: absolute; }
.visuallyhidden.focusable:active, .visuallyhidden.focusable:focus { clip: auto; height: auto; margin: 0; overflow: visible; position: static; width: auto; }
.invisible { visibility: hidden; }
.clear { clear:both; }
.clearfix:before, .clearfix:after { content: ""; /* 1 */ display: table;/* 2 */ }
.clearfix:after { clear: both; }

table { margin-bottom:15px; width:100%; border-collapse:collapse; border-spacing:0; }
th { font-family:'Hm-Regular', 'Rajdhani', sans-serif; font-weight:700; text-transform:uppercase; }
.table > :not(:first-child) { border-top: none; }
.table > :not(:last-child) > :last-child > * { border-bottom-color:#ddd; }

/* Text specialized */
.text-italic { font-style: italic; }
.text-normal { font-style: normal; }
.text-underline { font-style: underline; }

/* Font specialized */
.body-font { font-family:'Hm-Regular', 'Poppins', sans-serif; }
.heading-font { font-family:'Hm-Regular', 'Rajdhani', sans-serif; }

.list--inline { padding:0; margin:0; }
.list--inline li { display:inline-block; margin-bottom:0; vertical-align:middle; }
.display-table { display:table; table-layout:fixed; width:100%; margin:0 !important; }
.display-table-cell { float:none; display:table-cell; vertical-align:middle; }

/* Buttons */
.btn { -moz-user-select:none; -ms-user-select:none; -webkit-user-select:none; user-select:none; -webkit-appearance:none; -moz-appearance:none; appearance:none; display:inline-block; width:auto; height:auto; 
       text-decoration:none; text-align:center; vertical-align:middle; cursor:pointer; border:1px solid transparent; border-radius:0; padding:8px 15px; background-color:#2358ad; color:#fff;
       font-weight:600; text-transform:uppercase; letter-spacing:0; line-height:normal; white-space:normal; font-size:13px; -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; 
}
.btn:hover, .btn:focus { background-color:#2e6ed5; opacity:0.9; color:#fff; text-decoration:none; }
.btn--small { padding:7px 12px; font-size:0.92308em; line-height:1.2; }
.btn-secondary { background-color:#2e6ed5; color:#ffffff; border:1px solid #2e6ed5; }
.btn-secondary:hover, .btn-secondary:focus { border-color:#2e6ed5; }
.btn--link { background-color:transparent; border:0; margin:0; color:#139ddd; text-align:left; text-decoration:none; outline:none !important;box-shadow:none !important; }
.btn--link:hover, .btn--link:focus { color:#000; text-decoration:none; background-color:transparent; }
.btn-large { padding:12px 20px; }
.btn-link { color:#555; }
.btn-link:hover { color:#000; }

label { margin-bottom:5px; }
input:-moz-placeholder, textarea:-moz-placeholder { opacity:0.7; -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=70)"; }
input::-webkit-input-placeholder, textarea::-webkit-input-placeholder { opacity:0.7; -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=70)"; }
input::-moz-placeholder, textarea::-moz-placeholder { opacity:0.7; -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=70)"; }
input:-ms-input-placeholder, textarea:-ms-input-placeholder { opacity:0.7; -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=70)"; }

.slick-dots { margin:20px 0 0; width:auto; padding:0; list-style:none; text-align:center; }
.slick-dots li { width:12px; height:12px; vertical-align:middle; position:relative; display:inline-block; padding:0; cursor:pointer; margin-right:8px; }
.slick-dots li button { color:transparent; line-height:0; font-size:0; border:0; background:transparent; display:block; cursor:pointer; color:#fff; width:12px; height:12px; text-indent:-9999px; padding:0; border-radius: 100%; background-color:#000; transition:all 0.2s; -webkit-transition:all 0.2s; -ms-transition:all 0.2s; opacity:0.2; }
.slick-dots li.slick-active button { opacity:1; }

.mask-overlay { content: ""; position:fixed; background-color:#000; opacity:0.5; left:0; top:0; width:100%; height:100%; z-index:10; }
.pswp__bg { background: rgba(0,0,0,.5); }

/*======================================================================
  4. Container
========================================================================*/
.container { max-width:1300px; padding-left:15px; padding-right:15px; }
.container-fluid { padding:0 50px; }
.container-fluid:before,
.container-fluid:after { content:''; clear:both; display:block; }

.grid { *zoom:1; list-style:none; margin:0; padding:0; margin-left:-30px; }
.grid__item { float:left; padding-left:30px; }
.grid--no-gutters > .grid__item { padding-left:0; }

/*======================================================================
  5. Promotional Top Popup
========================================================================*/
.notification-bar { text-align:center; position:relative; z-index:5; background-color:#2e6ed5; background-image:url(../images/prm-bg.png); background-repeat:repeat; background-position:center; }
.notification-bar__message { color:#fff; letter-spacing:1px; text-transform:uppercase; font-size:11px; padding:10px 30px; display:block; }
.notification-bar__message:hover, .notification-bar__message:active, .notification-bar__message:focus, .notification-bar__message:focus-within { color:#fff; text-decoration:none; }
.close-announcement { cursor:pointer; font-size:14px; font-weight:400; position:absolute; right:10px; top:50%; transform:translateY(-50%); -webkit-transform:translateY(-50%); height:25px; width:25px; line-height:22px; color:#fff; }

/*======================================================================
  6. Pre Loader
========================================================================*/
.preloader { color: #fff; height: 100vh; width: 100%; background: #191919; display: flex; align-items: center; justify-content: center; position: fixed; pointer-events:none; z-index:1900; }
.preloader-in { color: #fff; font-size: 3rem; text-transform: uppercase; font-weight: 800; opacity: 0.8; letter-spacing: -12px; }
.preloader-in span { display: block; float: left; margin: .5rem; opacity:1; transform-origin: center; animation: move .6s linear infinite; animation-direction: alternate; }
@keyframes move{
    0% { transform: scale(0); opacity:0; }
    50% { transform: scale(1.3); opacity:1; }
    100%{ transform: scale(1); opacity:1; }
}

/*======================================================================
  7. Headers Style
========================================================================*/
.classicHeader:not(.stickyNav) { position:absolute;width:100%; z-index:8; }
.promotion-header { color:#df1a0b; letter-spacing:1px; text-transform:uppercase; padding:10px 35px; background-color:#fff0ef; text-align:center; position:relative; z-index:5; }
.closeHeader { cursor:pointer; font-size:18px; font-weight:400; position:absolute; right:40px; top:8px; height:25px; width:25px; line-height:22px; color:#df1a0b; }

.top-header { color:#fff; padding-top:8px; padding-bottom:10px; background:#000000; height:38px; }
.top-header a { color:#fff; }

.topheader {
    border-bottom: 1px solid #ddd;
    padding: 10px 0;
    background-color: #f5f5f5;
}
.topheader a { color:#000; }
.topheader p, .topheader a, .topheader select, .topheader .fa, .topheader span.selected-currency,
.topheader .language-dd { color:#000; font-size:12px; display:inline; margin-bottom:0; text-decoration:none; text-transform:uppercase; line-height:normal; }
.topheader a:hover { color:#2358ad; text-decoration:none; }
.topheader .phone-no, .topheader .welcome-msg { display:inline; }
.topheader .phone-no .anm { vertical-align:middle; }
.topheader .phone-no a:hover { text-decoration:none; }
.topheader .social-icons li { padding:0 5px; }
.topheader .social-icons li:last-of-type{ padding-right:0; }
.topheader .social-icons li .icon { font-size:14px; color:#000; height:auto; vertical-align:middle; }

.default-msg { padding-right:10px; }
.currency-picker, .language-dropdown { display:inline-block; position:relative; }
.selected-currency, .language-dd { width:40px; display:inline-block; cursor:pointer; margin-right:10px }
.language-dd { width:65px; text-transform:uppercase; display:inline; }
    .selected-currency:after, .language-dd:after {
        content: "\f107";
        font-family: Hm-Bold;
        font-weight: 900;
        display: inline;
        vertical-align: middle;
        padding-left: 5px;
        margin-top: -4px;
    }
.top-header .currency-picker, .top-header .language-dropdown { display:inline; position:relative; vertical-align:middle; }
#currencies, #language { top:29px; left:-5px; display:none; position:absolute; background:#fff; border:1px solid #e8e8e8; padding:0; z-index:333; }
#currencies li, #language li { color:#000; font-size:12px; cursor:pointer; padding:5px 15px; list-style:none; border-bottom:solid 1px #eaeaea; }
#currencies li:last-of-type, #language li:last-of-type{ border-bottom:none; }
#currencies li:hover, #language li:hover { background:#f5f5f5; }

.topheader .user-menu .anm { font-size:19px; cursor:pointer; }
.topheader .list-inline { margin:0; list-style:none; }
.topheader .list-inline > li { display:inline-block; padding-right:5px; padding-left:5px; text-transform:uppercase; }

.header-search label.label { clip:rect(0, 0, 0, 0); height:1px; margin:-1px; overflow:hidden; padding:0; position:absolute; width:1px; }
.header-search .input-box { width:100%; position:relative; }
.header-search .input-box .input-text { padding:0 40px 0 10px; }
.header-search .search { font-size:17px; cursor:pointer; border:0; position:absolute; right:0; top:1px; width:40px; height:40px; text-align:center; }
.header-search .search:hover { opacity:0.8; }
.header-search .search-category { width:180px; }
.header-search .search-category select { border-right:0; }

.header { width:100%; min-height:70px; }
.header-1 { background-color:#ffffff; }
.main-menu { background-color:#2e6ed5; padding:0; }

.logo { margin:0; }
.logo a { float:left; display:block; }
.logo img { display:block; max-width:140px; }

.iconset .icon { font-size:22px; text-decoration:none; }
.iconset { display:inline-block; position:relative; color:#000; text-align:center; padding:0 9px; cursor:pointer; }
.iconset .label { text-transform:uppercase; }
.iconset > a { display:block; color:#000; }
.icon-in { position:relative; display:block !important; }
.site-cart-count, .wishlist-count, .compare-count { font-size:10px; display:inline-block; color:#fff; background-color:#2e6ed5; text-align:center; width:17px; height:17px; line-height:17px; position:absolute; right:5px; top:-5px; border-radius:100px; }
.site-cart-count { right:15px; }

#settingsBox, #cart-drawer, #searchPopup { color:#050000; text-align:left; background-color:#ffffff; box-shadow:0 0 15px rgba(5,0,0,0.1); -webkit-box-shadow:0 0 15px rgba(5,0,0,0.1); -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; }
#settingsBox { visibility:hidden; opacity:0; padding:20px; width:300px; position:absolute; top:130%; right:15px; z-index:99; }
#settingsBox.active { visibility:visible; opacity:1; top:100%; }
#settingsBox .btn { width:100%; }
#settingsBox #currencies li, #settingsBox #language li { padding: 0; border-bottom: 0; }
#settingsBox #currencies, #settingsBox #language { position: static; display: block; border: 0; margin-bottom: 0; }
#settingsBox #currencies .selected a, #settingsBox #language .selected a { color: #f04343 !important; border: 1px solid #f04343 !important; }
.icons-col .customer-links { font-size:12px; text-transform:uppercase; }
.icons-col .customer-links .register { text-decoration:underline; }
#settingsBox .ttl { font-size:12px; margin:20px 0 10px; display:block; letter-spacing:0.2px; text-transform:uppercase; }
.cnrLangList li { display:inline-block; margin:0 5px 5px 0; cursor:pointer; }
.cnrLangList li a { color:#000000; display:block; font-size:12px; text-transform:uppercase; padding:1px 10px; opacity:0.6; border:1px solid rgba(0,0,0,0.3);}
.cnrLangList li a:hover { color:#f04343; border:1px solid #f04343; opacity:1; text-decoration:none; }

.stickyNav, .header-transparent.stickyNav, .header-sticky.stickyNav { position:fixed; top:0; z-index:1000; width:100%; left:0; background-color:#2e6ed5; border-bottom:0; box-shadow:0 0 4px rgba(0, 0, 0, 0.14); -webkit-box-shadow:0 0 4px rgba(0, 0, 0, 0.14); }
.header-transparent.stickyNav, .header-sticky.stickyNav { background-color: #fff; }
/*.header-7.header-sticky.stickyNav { background-color: #000; }*/
.minicart-right-drawer.right .modal-dialog { position:fixed; left: auto; top: auto; -webkit-transform: none !important; transform: none !important; -ms-transform: none !important; margin:auto; width:350px; max-width:100%; height:100%; z-index:1024; -webkit-transform:translate3d(0%,0,0); -ms-transform:translate3d(0%,0,0); -o-transform:translate3d(0%,0,0); transform:translate3d(0%,0,0); }
.minicart-right-drawer.right .modal-content { border:none; border-radius:0; height:100%; overflow-y:auto; }
.minicart-right-drawer.right.fade .modal-dialog { right:-350px; -webkit-transition:opacity 0.3s linear, right 0.3s ease-out; -moz-transition:opacity 0.3s linear, right 0.3s ease-out; -o-transition:opacity 0.3s linear, right 0.3s ease-out; transition:opacity 0.3s linear, right 0.3s ease-out; }
.minicart-right-drawer.right.fade.show .modal-dialog { right:0; }
.modal-open { padding-right:0 !important; }
#cart-drawer { color:#050000; padding:15px; width:100%; height:100%; overflow:auto; z-index:10000; }
#cart-drawer.active { right:0; }
#cart-drawer > h4 { color:#050000; font-size:18px; text-transform:uppercase; font-weight:bold; text-align:left; margin:0 0 20px; border-bottom:1px solid #e8e8e8; padding-bottom:10px; }
#cart-drawer .close-cart { color:#050000; font-size:15px; float:right; margin:-3px 0 0; opacity:0.6; text-decoration:none; }
.minicart-content { padding:0; margin:0; z-index:1001; position:absolute; overflow-y:auto; width:90%; height:calc(100% - 290px); }
.minicart-content .item { padding:0 0 10px; margin:0 0 10px; line-height:normal; display:block; border-bottom:solid 1px #eee; }
.minicart-content .item .product-image { width:25%; float:left; }
.minicart-content .item .product-details { float:left; width:75%; padding-left:15px; text-align:left; }
.minicart-content .item .product-title { color:#000; font-size:13px; white-space:normal; text-decoration:none; display:block; line-height:20px; margin-right:45px; margin-bottom:0; }
.minicart-content .item .remove { color:#5c5c5c; float:right; font-size:14px; padding:0 2px 0 7px; margin-top:0; text-decoration:none; }
.minicart-content .item .remove:hover { color:#000; }
.minicart-content .item .remove i { vertical-align:middle; }
.minicart-content .item .edit-i.remove .icon { font-size:14px; padding-top:0; }
.minicart-content .item .qtyField { display:inline-block; border:1px solid #d7d7d7; }
.minicart-content .item .qtyField a { display:none; }
.minicart-content .item .qtyField span { display:inline-block; padding:0; border:0; }
.minicart-content .item .variant-cart { color:#777; font-size:11px; }
.minicart-content .item .wrapQtyBtn { display:block; float:none; margin:5px 0; }
.minicart-content .item .qtyField .qtyBtn, .minicart-content .item .qtyField .qty { font-size:11px; width:25px; height:28px; line-height:26px; display:inline-block; padding:0; text-align:center; text-decoration:none; }
.minicart-content .item .qtyField .qty { border:0; }
.minicart-content .item .qtyField .qtyBtn.plus { border-left:1px solid #ddd; }
.minicart-content .item .qtyField .qtyBtn.minus { border-right:1px solid #ddd; }
.minicart-content .item .qtyField a .fa { font-size:11px; }
.minicart-bottom { bottom:0; position:absolute; width:90%; border-top:1px solid #e8e8e8; padding-top:10px; }
.minicart-bottom .subtotal { padding:5px 0 10px; margin-bottom:10px; border-bottom:1px solid #e8e8e8; }
.minicart-bottom .subtotal:before, .minicart-bottom .subtotal:after { content:''; clear:both; display:block; }
.minicart-bottom .subtotal.list { border:0; margin:0; padding:0;}
.minicart-bottom .subtotal.list > span { font-size:14px; font-weight:400; }
.minicart-bottom .subtotal > span { float:left; text-transform:uppercase; font-size:16px; text-align:left; font-weight:700; }
.minicart-bottom .subtotal .product-price { float:right; }
.minicart-bottom .proceed-to-checkout { width:100%; margin:0; padding:10px; }
.minicart-bottom .btn-secondary { width:100%; margin-bottom:10px; padding:10px; border:0; }
.minicart-bottom .btn { font-size:13px; font-weight:500; border:0; }
.minicart-bottom .freeShipMsg { margin-bottom:0; }
.minicart-bottom .freeShipMsg .anm { font-size:15px; width:23px; display:inline-block; vertical-align:middle; }

.site-search { display:inline-block; cursor:pointer; }
.search-drawer { padding:40px 50px; background-color:#fff; opacity:0; visibility:hidden; position:fixed; top:0; left:0; z-index:9999; text-align:left; transform:translate(0,-100%); -webkit-transform:translate(0,-100%); -ms-transform:translate(0,-100%); -o-transform:translate(0,-100%); transition:all .3s ease 0s; -webkit-transition:all .3s ease 0s; -ms-transition:all .3s ease 0s; -o-transition:all .3s ease 0s; width:100%; box-shadow:0 0 6px rgba(0,0,0,0.2); -webkit-box-shadow:0 0 6px rgba(0,0,0,0.2); }
.search-drawer .container { position:relative; }
.search-drawer.search-drawer-open { opacity:1; transform:translate(0,0); -webkit-transform:translate(0,0); -ms-transform:translate(0,0); -o-transform:translate(0,0); visibility:visible; }
.search-drawer.search-drawer-open .block { background-color:transparent; padding:0; }
.search-drawer .title { color:#000000; font-size:18px; margin-bottom:20px; font-weight:bold; }
.search-drawer .label { border:0; clip:rect(0,0,0,0); height:1px; margin:-1px; overflow:hidden; padding:0; position:absolute; width:1px; }
.search-drawer .input-text { color:#050000; font-size:13px; padding:5px 50px 5px 0; background:none; border:0 none; }
.search-drawer .action.search { font-size:24px; position:absolute; right:34px; border:0; background:none; cursor:pointer; height:38px; }
.search-drawer .action.search:hover { color:#000000; }
.search-drawer .closeSearch { color:#050000; font-size:18px; position:absolute; top:0; right:25px; cursor:pointer; }
.search-drawer .searchField { display:table; width:100%; padding: 10px; background-color: #f7f7f7; }
.search-drawer .search-category { display:table-cell; padding-right:10px; }
.search-drawer .search-category select { font-size:13px; border:0; }
.search-drawer .input-box { display:table-cell; width:87%; }

#siteNav { margin:0 auto; padding:0; list-style:none; }
#siteNav.right { text-align:right; }
#siteNav.left { text-align:left; }
#siteNav.center { text-align:center; }
#AccessibleNav { padding-left:0; }
.mobile-nav-wrapper, .site-header__logo.mobileview { display:none; }

.header-vertical-menu { position:relative; font-family:'Hm-Regular', 'Poppins', sans-serif; }
.header-vertical-menu .menu-title { color:#fff; font-weight:600; text-transform:uppercase; margin:0; background-color:#2358ad; display: block; padding:15px; letter-spacing:0; font-size:16px; cursor:pointer; }
.header-vertical-menu .menu-title:after { content: "\f107"; font-family:"annimex-bold"; font-style:normal; font-size:13px; font-weight:400; position:absolute; right:15px; margin-top:0; }
.header-vertical-menu .menu-title.active:after { content: "\f106"; }
.header-vertical-menu .menu-title i { font-size:20px; display:inline-block; vertical-align:middle; margin-right:5px; }
.header-vertical-menu .vertical-menu-content { display:none; width:100%; position:absolute; top:100%; z-index:100; background-color:#2358ad; padding:0; }
.vertical-menu-content ul { list-style:none; padding:0; margin:10px 0; }
.vertical-menu-content ul > li { padding:0 10px; }
.vertical-menu-content ul > li .moreSlideOpen { margin:0; }
.vertical-menu-content ul > li .moreSlideOpen li { padding:0; }
.vertical-menu-content .nav-link, .vertical-menu-content .moreCategories { color:#fff; font-size:13px; display:block; text-transform:uppercase; padding:10px 5px; border-bottom:1px solid #164ba0; }
.vertical-menu-content .nav-link i { font-size:20px; vertical-align:middle; margin-right:5px; }
.vertical-menu-content .moreCategories { padding:5px 15px 15px; display:block; cursor:pointer; position:relative; }
.vertical-menu-content .moreCategories:after { content: "\f067"; font-family:"annimex-bold"; font-weight: 400; font-size:13px; display:block; position:absolute; right:0; top:0; width:40px; height:40px; text-align:center; line-height:30px; }
.vertical-menu-content .moreCategories.show:after { content: "\f068"; }
.vertical-menu-content li .megamenu, .vertical-menu-content li .dropdown { min-width:800px; background-color:#fff; position:absolute; top:-9999px; left:100%; margin:0; box-shadow:0 2px 35px rgba(0, 0, 0, 0.1); -webkit-box-shadow:0 2px 35px rgba(0, 0, 0, 0.1); z-index:1001; visibility:hidden; opacity:1; transition:transform 0.3s ease-out; -webkit-transition:transform 0.3s ease-out; -ms-transition:transform 0.3s ease-out; transform:translate3d(10px, 0, 0); -webkit-transform:translate3d(10px, 0, 0); -ms-transform:translate3d(10px, 0, 0); }
.vertical-menu-content li:hover .megamenu, .vertical-menu-content li:hover .dropdown { left:100%; visibility:visible; top:0; transform:translate3d(0, 0, 0); -webkit-transform:translate3d(0, 0, 0); -ms-transform:translate3d(0, 0, 0); }
.vertical-menu-content .megamenu > li { padding:20px; flex:1; -ms-flex:1; -webkit-flex:1; margin:0; }
.vertical-menu-content .megamenu > li:last-child { flex:none; -webkit-flex:none; -ms-flex:none; }
.vertical-menu-content .has-submenu > a:after,
.vertical-menu-content .sub-menu > a:after { content:"\f105"; font-family:"annimex-bold"; font-weight: 400; font-size:11px; color:#fff; position:absolute; right:15px; margin-top:2px; }
.vertical-menu-content .megamenu .sub-menu-title { color:#000; font-size:15px; background-color:transparent; padding:0; text-transform:uppercase; font-weight:600; }
.vertical-menu-content .megamenu li ul li { padding:0; }
.vertical-menu-content .megamenu li ul li a { color:#000; display:block; font-size:14px; padding:4px 0; }
.vertical-menu-content .megamenu li ul li a:hover { color:#2e6ed5; }
.vertical-menu-content li.dropdown-in { position:relative; }
.vertical-menu-content .sub-menu > a:after { color:#000; right:0; }
.vertical-menu-content li .dropdown { min-width: 230px; padding:20px; }
.vertical-menu-content li .dropdown li { padding:0; }
.vertical-menu-content li .dropdown li a { color:#000; display:block; font-size:14px; padding:4px 0; }
.vertical-menu-content .sub-menu:hover > a:after, .vertical-menu-content li .dropdown li:hover > a, .vertical-menu-content li .dropdown li a:hover { color:#2e6ed5; }
.vertical-menu-content li .dropdown .sub-menu { position:relative; }
.vertical-menu-content li .dropdown .sub-menu > .sub-level2 { display:none; top:-22px; }
.vertical-menu-content li .dropdown .sub-menu:hover > .sub-level2 { display:block; }
.vertical-menu-content .megamenu hr { margin:10px 0; }

@media (min-width:990px) {
    .header .container { position:relative; }
    .header .container .d-menu-col { position:static; }
    #siteNav a { font-family:'Hm-Regular', 'Poppins', sans-serif; text-decoration:none; font-size:13px; display:block; opacity:1; -webkit-font-smoothing: antialiased; letter-spacing:0.05em; position:relative; }
    #siteNav > li { display:inline-block; text-align:left; position:relative; }
    #siteNav > li > a { color:#000; padding:0 15px; text-transform:uppercase; line-height:40px; }
    #siteNav.medium > li a { font-weight:400; }
    #siteNav.hidearrow > li > a .an { display:none; }
    #siteNav > li > a .an { vertical-align:middle; margin-top: -3px; font-size: 11px; }

    #siteNav.left { text-align: left; }
    #siteNav.center { text-align: center; }

    #siteNav > li:hover > a, #siteNav > li > a:hover:hover, #siteNav > li > a:hover { color:#fff; }
    .header-4 #siteNav > li:hover > a, .header-4 #siteNav > li > a:hover:hover, .header-4 #siteNav > li > a:hover { color:#000; }
    .header-6 #siteNav > li:hover > a, .header-6 #siteNav > li > a:hover:hover, .header-6 #siteNav > li > a:hover { color:#000; background-color:#fed700; }
    .header-7 #siteNav > li:hover > a, .header-7 #siteNav > li > a:hover:hover, .header-7 #siteNav > li > a:hover, .header-7 #siteNav > li a.active {
        color: var( --theme-color);
        background-color: var( --theme-color-light);
    }
    .index-demo8 #siteNav > li .megamenu li.lvl-1 li .site-nav:hover, .index-demo8 #siteNav > li .megamenu .subLinks .site-nav:hover { color: #199b8c; }
    .index-demo9 #siteNav > li > a, .index-demo9 .store-l-link { color: #191919; }

    #siteNav > li .megamenu { min-width:750px; opacity:0; visibility:hidden; padding:30px; position:absolute; top:59px; left:0; z-index:999; background-color:#fff; box-shadow: 2px 2px 1px 0px rgba(0,0,0,0.3); -webkit-box-shadow: 2px 2px 1px 0px rgba(0,0,0,0.3); pointer-events:none; -ms-transition:all 0.3s ease; -webkit-transition:all 0.3s ease; transition:all 0.3s ease; max-height:600px; overflow:auto; box-shadow:0px 0px 15px rgba(0,0,0,0.1); -webkit-box-shadow:0px 0px 15px rgba(0,0,0,0.1); }
    #siteNav > li .megamenu ul { padding:0; list-style:none; }
    #siteNav > li:hover > .megamenu { top:40px; opacity:1; visibility:visible; pointer-events:visible; }
    #siteNav > li .megamenu li.lvl-1 { margin-bottom:30px; }
    #siteNav > li .megamenu.style1 li.lvl-1 { margin-bottom:0; }

    #siteNav > li .megamenu li.lvl-1 a.lvl-1, #siteNav > li .megamenu .menu-title { color:#000; font-size:14px; text-transform:uppercase; padding:0 0 8px; font-weight:400; }
    #siteNav > li .megamenu li.lvl-1 li .site-nav, #siteNav > li .megamenu .subLinks .site-nav { color:#000; padding:3px 0; font-weight:400; }

    #siteNav > li .megamenu li.lvl-1 li .site-nav:hover, #siteNav > li .megamenu .subLinks .site-nav:hover { color:#2358ad; }
    #siteNav > li .megamenu li.lvl-1 li .site-nav:before, #siteNav > li .megamenu .subLinks .site-nav:before { content: ""; display:inline-block; width:0px; height:2px; vertical-align:middle; background-color:#2358ad; -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; }
    #siteNav > li .megamenu li.lvl-1 li .site-nav:hover:before, #siteNav > li .megamenu .subLinks .site-nav:hover:before { width:5px; margin-right:3px; }

    #siteNav > li .megamenu.style1 { width:330px; overflow:hidden; min-width:inherit; }
    #siteNav > li .megamenu.style2 li.lvl-1, 
    #siteNav > li .megamenu.style4 li.lvl-1 { margin-bottom:10px; }
    #siteNav > li .megamenu .menu-title { font-weight:600 !important; }
    #siteNav > li.mdropdown { position:relative; }
    #siteNav > li .megamenu .imageCol { padding-bottom:25px; }

    #siteNav > li.Cfull { position: static; }
    #siteNav > li.Cfull .megamenu { left: 15px; right: 15px; }
    #siteNav > li.Cfull .megamenu ul { width: 100%; }
    #siteNav > li.Cfull:hover > .megamenu { top: 45px; }

    #siteNav > li ul.dropdown li a .an { position:absolute; right:3px; top:13px; }

    #siteNav a .lbl { color:#ffffff; font-size:9px; font-weight:400; letter-spacing:0; line-height:1; text-transform:uppercase; display:inline-block; padding:3px 4px; background-color:#0a9339; position:relative; vertical-align:middle; margin-top: -2px; }	
    #siteNav a .lbl:after { content:" "; display:block; width:0; height:0; position:absolute; bottom:3px; left:-7px; border:4px solid transparent; border-right-color:transparent; border-right-color:#0a9339; }
    #siteNav a .lbl.nm_label3 { background-color:#fb6c3e; }
    #siteNav a .lbl.nm_label1 { background-color:#ff0000; }
    #siteNav a .lbl.nm_label3:after { border-right-color:#fb6c3e; }
    #siteNav a .lbl.nm_label1:after { border-right-color:#ff0000; }

    #siteNav > li.dropdown { position:relative; }
    #siteNav > li .dropdown,
    #siteNav > li .dropdown ul { background-color:#fff; list-style:none; opacity:0; visibility:hidden; width:220px; position:absolute; top:59px; left:0; z-index:999; box-shadow:0px 0px 15px rgba(0,0,0,0.1); -webkit-box-shadow:0px 0px 15px rgba(0,0,0,0.1); -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; padding:10px; }
    #siteNav > li:hover > .dropdown,
    #siteNav > li .dropdown li:hover > ul { top:40px; opacity:1; visibility:visible; }
    #siteNav > li ul.dropdown li { border-top:1px solid #eeeeee; position:relative; }
    #siteNav > li ul.dropdown li:first-child { border:0; }
    #siteNav > li ul.dropdown li a { color:#000; font-weight:400; padding:8px 12px; background-color:#fff; }
    #siteNav > li ul.dropdown li:hover > a, #siteNav > li ul.dropdown li a:hover { color:#000; background-color:#eee; padding-left:17px; }
    #siteNav > li ul.dropdown li ul { top:20px; left:100%; }
    #siteNav > li ul.dropdown li:hover > ul { top:0; }

    #siteNav .menu-brand-logo { width:50%; float:left; padding-right:10px; text-align: center; }
    #siteNav .menu-brand-logo a { display:block; margin-bottom:10px; border:1px solid #ddd; }
    #siteNav .menu-brand-logo a:hover { border-color:#000; }
    #siteNav .menu-brand-logo a img { display:inline-block; vertical-align:middle; }

    #siteNav .mmbanner-full { clear: both; }

    .header-6 #siteNav > li, .header-7 #siteNav > li { position: static; }
    .header-6 #siteNav > li.dropdown, .header-6 #siteNav > li.megamenu.mdropdown,
    .header-7 #siteNav > li.dropdown, .header-7 #siteNav > li.megamenu.mdropdown { position: relative; }
    .header-6 #siteNav.medium > li > a, .header-7 #siteNav.medium > li > a { font-weight: 600; }

    .header-7 .container-fluid { position: relative; }
    .header-7 #siteNav > li .megamenu { max-width: 1280px; margin: 0 auto; right: 0; }

}

.header-content-wrapper { width:100%; padding:15px 0; }

/* Header - 2 */
.header-2 .logo { text-align:center; }
.header-2 .logo a { float:none; display:inline-block; }
.header-2 .logo a:hover { opacity:1; }
.header-2 .logo img { margin:0 auto; }
.header-2 .main-menu { background-color:#333333; }
.header-2 .header-vertical-menu .menu-title,
.header-2 .header-vertical-menu .vertical-menu-content { background-color:#ff802b; }
.header-2 .vertical-menu-content .nav-link, .header-2 .vertical-menu-content .moreCategories { border-color:#f67219; color: #fff; }
.header-2 .site-cart-count, .header-2 .wishlist-count, .header-2 .compare-count { background-color:#ff802b; }
.header-2 .iconset .icon { vertical-align:inherit; font-size:30px; }
.header-2 .header-cart { padding-right:0; padding-left:10px; border-left:1px solid #ddd; }
.header-2 .header-cart .btn-minicart { padding-right:40px; }
.header-2 .header-cart .icon-in { position:absolute; right:0; top:0; }
.header-2 .header-cart.iconset .icon { font-size:38px; }
.header-2 .header-cart .minicart-label .cart-name { text-transform:none; }
.header-2 .header-cart .minicart-label span { font-size:12px; display:block; text-align:left; }
.header-2 .header-cart .site-cart-count { right:0; top:-5px; }
.header-2 .wishlist-count { right:-5px; }
.header-2 .compare-count { right:0; }
.store-l-link:hover, .header-2 #siteNav > li:hover > a, .header-2 #siteNav > li > a:hover:hover, .header-2 #siteNav > li > a:hover,
.header-2 #siteNav > li .megamenu li.lvl-1 li .site-nav:hover, .topheader a:hover,
.header-2 .vertical-menu-content .megamenu li ul li a:hover, .header-2 .vertical-menu-content .sub-menu:hover > a::after,
.header-2 .vertical-menu-content li .dropdown li:hover > a, .header-2 .vertical-menu-content li .dropdown li a:hover { color:#ff802b; }
.header-2 #siteNav > li .megamenu li.lvl-1 li .site-nav:before { background-color:#ff802b; }
.store-l-link { color:#fff; line-height:40px; text-transform:uppercase; }

/* Header - 3 */
.header-3 .main-menu { position: relative; background-color:#28475c; padding:5px 10px; }
.header-3 .site-cart-count, .header-3 .wishlist-count, .header-3 .compare-count { background-color:#ffc64e; color:#000; }

/* Header - 5 */
.header-5 .logo a { float: none; }
.header-5 .logo img { margin: 0 auto; }
.header-5 .iconset .icon { font-size: 22px; vertical-align: top; }
.header-5 .site-cart-count { right: -5px; background-color: #ee307c; }
.header-5 .site-cart-count, .header-5 .wishlist-count, .header-5 .compare-count { right: -8px; background-color: #ee307c; }
.header-5 .main-menu { background-color: #ee307c; }
.header-5 .header-vertical-menu .vertical-menu-content, .header-5 .header-vertical-menu .menu-title { background-color: #d50256; }
.header-5 .vertical-menu-content .nav-link, .header-5 .vertical-menu-content .moreCategories { background-color: #d50256; border-bottom: 1px solid #c6004f; }
.header-5 .vertical-menu-content .nav-link:hover { color: #fff; opacity: 0.8; }
.header-5 .vertical-menu-content li .megamenu, .header-5 .vertical-menu-content li .dropdown { background-color: #d50256; }
.header-5 .vertical-menu-content .megamenu .sub-menu-title, .header-5 .vertical-menu-content .megamenu li ul li a, .header-5 .vertical-menu-content li .dropdown li a,
.header-5 .vertical-menu-content .sub-menu > a:after { color: #fff; }
.header-5 .vertical-menu-content li .dropdown li a:hover, .header-5 .vertical-menu-content .megamenu li ul li a:hover { opacity: 0.85; color: #fff; }
.header-5 .vertical-menu-content .megamenu hr { border-bottom-color: #9f0040; }
.header-5 #siteNav > li .megamenu li.lvl-1 li .site-nav:hover, .header-5 #siteNav > li .megamenu .subLinks .site-nav:hover, 
.header-5 #siteNav > li > a:hover { color: #000; opacity: 0.85; }
.header-5 #siteNav > li:hover > a, .header-5 #siteNav > li > a:hover { color: #fff; }
.header-5 #siteNav > li .megamenu li.lvl-1 li .site-nav:before, .header-5 #siteNav > li .megamenu .subLinks .site-nav:before { background-color: #000; }
.header-5 .search-drawer .title { font-size: 18px; text-transform: uppercase; }
.index-demo5 #currencies li, .index-demo5 #language li { text-align: left; }
.index-demo5 .topheader a:hover { color: #ee307c; }
.index-demo5 .home-blog-post { background-color: #f4f4f4; }
.index-demo5 .testimonial-slider { background-color: #f9f9f9; }

/* Header - 6 */
.header-6 #siteNav > li > a { color: #222; }
.header-6 .logo img { max-width: 150px; }
.header-6 .site-cart-count { right: -8px; }
.header-6 .wishlist-count { right: -2px; }
.header-6 .site-cart-count, .header-6 .wishlist-count, .header-6 .compare-count { background-color: #fed700; color: #000; }
.header-6 #siteNav > li .megamenu { width: 100%; }

/* Header - 7 */
.header-7 {
    background-color: #fff;
    border-bottom: 1px solid #e1e7ec;
    position: relative;
}
.header-7 .site-cart-count { right: -8px; }
.header-7 .wishlist-count { right: -2px; }
    .header-7 .site-cart-count, .header-7 .wishlist-count, .header-7 .compare-count {
        background-color: var(--theme-color);
        color: #fff;
    }
.header-7 .iconset, .header-7 .iconset > a { color: #000; }

.index-demo4 .topheader { color: #fff; background-color: #3754b2; border-bottom: 0; }
.index-demo4 .topheader a { color: #fff; }
.index-demo4 .topheader a:hover { color: #fff; opacity: 0.85; }
.index-demo4 .site-cart-count, .index-demo4 .wishlist-count, .index-demo4 .compare-count { background-color: #17c4b2; }
.header-transparent { position: absolute; z-index: 100; }
.header-transparent .main-menu { background-color: transparent; padding: 0; }
.header-transparent #siteNav > li > a { color: #222; }
.header-transparent #siteNav > li.megamenu:not(.mdropdown) { position: static; }
.header-transparent #siteNav > li .megamenu:not(.style1), .header-transparent #siteNav > li .megamenu ul,
.header-transparent #siteNav > li .megamenu ul > .grid__item.one-whole { width: 100%; }
.header-transparent .site-cart-count { right: -8px; }
.header-transparent .wishlist-count { right: -1px; }

.marquee-text .top-bar .flex-item.center { text-align: center; }
.marquee-text .top-info-bar .flex-item { color:#333e48; font-size:14px; flex: 1; -webkit-flex: 1; -ms-flex: 1; padding-left: 30px; padding-right: 30px; display: -webkit-inline-box; display: -moz-inline-box; display: -webkit-inline-flex; display: -ms-inline-flexbox;  display: inline-box; display: inline-flex; -webkit-box-align: center; -moz-box-align: center; -o-box-align: center; -ms-flex-align: center; -webkit-align-items: center; align-items: center; -webkit-box-pack: center; -moz-box-pack: center; -o-box-pack: center; -ms-flex-pack: center; -webkit-justify-content: center; justify-content: center; -webkit-transition: all .2s ease; -moz-transition: all .2s ease; -o-transition: all .2s ease; -ms-transition: all .2s ease; transition: all .2s ease; }
.marquee-text .top-info-bar .flex-item a { color:#333e48; white-space:nowrap; }
.marquee-text .top-info-bar .flex-item a:hover { opacity:0.8; }
.marquee-text .top-info-bar { width: 200%; display: flex !important; -webkit-animation: marquee 20s linear infinite running; -moz-animation: marquee 20s linear infinite running; -o-animation: marquee 20s linear infinite running; -ms-animation: marquee 20s linear infinite running; animation: marquee 20s linear infinite running; }
.marquee-text .top-info-bar:hover { -webkit-animation-play-state: paused; -moz-animation-play-state: paused; -o-animation-play-state: paused; -ms-animation-play-state: paused; animation-play-state: paused; }
.marquee-text { box-sizing: border-box; -webkit-box-align: center; -moz-box-align: center; -o-box-align: center; -ms-flex-align: center; -webkit-align-items: center; align-items: center; overflow: hidden; }
@-moz-keyframes marquee{0%{-webkit-transform:translateX(0);-moz-transform:translateX(0);-o-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}100%{-webkit-transform:translate(-50%);-moz-transform:translate(-50%);-o-transform:translate(-50%);-ms-transform:translate(-50%);transform:translate(-50%)}}@-webkit-keyframes marquee{0%{-webkit-transform:translateX(0);-moz-transform:translateX(0);-o-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}100%{-webkit-transform:translate(-50%);-moz-transform:translate(-50%);-o-transform:translate(-50%);-ms-transform:translate(-50%);transform:translate(-50%)}}@-o-keyframes marquee{0%{-webkit-transform:translateX(0);-moz-transform:translateX(0);-o-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}100%{-webkit-transform:translate(-50%);-moz-transform:translate(-50%);-o-transform:translate(-50%);-ms-transform:translate(-50%);transform:translate(-50%)}}@keyframes marquee{0%{-webkit-transform:translateX(0);-moz-transform:translateX(0);-o-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}100%{-webkit-transform:translate(-50%);-moz-transform:translate(-50%);-o-transform:translate(-50%);-ms-transform:translate(-50%);transform:translate(-50%)}}   

/*======================================================================
  8. Homepage Demo
========================================================================*/
/* Home - 1 */
.index-demo1 .imgBanners.style2 .details { background-color:rgba(255,255,255,0.8); } 
.index-demo1 .imgBanners.style2 .details .title { color:#000; font-size:16px; text-transform:uppercase; }
.index-demo1 .collection-banners .row .banner-item { margin-bottom: 0; }

/* Home - 2 */
.index-demo2 .topheader { border-bottom:1px solid #efefef; padding:8px 10px; }
.index-demo2 .btn, .index-demo2 .button-set.style4 .btn-icon, .index-demo2 #site-scroll { color:#fff; background-color:#ff802b; }
.index-demo2 .btn:hover, .index-demo2 .button-set.style4 .btn-icon:hover, .index-demo2 #site-scroll:hover { color:#fff; background-color:#ff6804; }
.index-demo2 .btn-secondary { border-color:#ff802b; }
.index-demo2 #site-scroll { font-size:15px; border-radius:100px; }
.index-demo2 .home-blog-post { background-color:#fff; }
.index-demo2 a:hover { color:#ff802b; }
.index-demo2 .instagram-section { background-color: #f6f6f6; }

/* Home - 3 */
.index-demo3 .header-vertical-menu .vertical-menu-content { display:block; margin-top:20px; position:static; }
.index-demo3 .header-vertical-menu .menuTitle { color:#fff; padding:20px 20px 3px; text-transform:uppercase; }
.index-demo3 .header-vertical-menu .menu-title, .index-demo3 .header-vertical-menu .vertical-menu-content { background-color:#1f3f55; }
.index-demo3 .vertical-menu-content .nav-link, .index-demo3 .vertical-menu-content .moreCategories { border-bottom:1px solid #1e3646; }
.index-demo3 .vertical-menu-content ul > li { position:relative; }
.index-demo3 .vertical-menu-content .sub-menu:hover > a::after, .index-demo3 .vertical-menu-content li .dropdown li:hover > a,
.index-demo3 .vertical-menu-content li .dropdown li a:hover, .index-demo3 .vertical-menu-content .megamenu li ul li a:hover { color:#ffc64e; }
.index-demo3 .btn, .index-demo3 .button-set.style4 .btn-icon, .index-demo3 #site-scroll { color:#000; background-color:#ffc64e; }
.index-demo3 .btn:hover, .index-demo3 .button-set.style4 .btn-icon:hover, .index-demo3 #site-scroll:hover { color:#000; background-color:#f9b72b; }
.index-demo3 .btn-secondary { border-color:#ffc64e; }
.index-demo3 a:hover, .index-demo3 #siteNav > li .megamenu li.lvl-1 li .site-nav:hover { color:#ffc64e; }
.index-demo3 #siteNav > li .megamenu li.lvl-1 li .site-nav:before { background-color: #ffc64e; }
.index-demo3 #siteNav > li:hover > a, .index-demo3 #siteNav > li > a:hover:hover, .index-demo3 #siteNav > li > a:hover { color: #ffc64e; }
.index-demo3 .slideshow-wrapper { padding-top:20px; }
.index-demo3 .slideshow .slideshow__title { font-size:33px; margin-bottom:5px; }
.index-demo3 .slideshow .slideshow__subtitle { font-size:13px; margin-bottom:15px; }
.index-demo3 .slideshow .btn { color:#fff; background-color:#1f3f55; padding:5px 10px; font-size:12px; }
.index-demo3 .slideshow .btn:hover { color:#fff; background-color:#19374b; }
.index-demo3 .button-set li .btn-icon { color:#fff; background-color:#1f3f55; }
.index-demo3 .button-set li .btn-icon .btn:hover { color:#fff; background-color:#19374b; }
.index-demo3 .slideshow-wrapper .slick-slide img { object-fit:cover; -ms-object-fit:cover; -webkit-object-fit:cover; }
.index-demo3 .slideshow-wrapper .slideshow__text-wrap .anim-tru.style1 { background:rgba(255,255,255,0.4); }
.index-demo3 .slideshow-wrapper .slideshow__text-content { width:100%; max-width:400px; }
.index-demo3 .slideshow-wrapper.slideshow .wrap-caption { width:100%; }
.index-demo3 .section-header h2 { font-size:20px; }

/* Home - 4 */
.index-demo4 .btn, .index-demo4 .slideshow .btn, .index-demo4 .featuredContentStyle3 .btn { background-color: #17c4b2; border-color: #17c4b2; font-weight: 600; }
.index-demo4 .slideshow .btn:hover, .index-demo4 .featuredContentStyle3 .btn:hover { background-color: #0ab4a2 !important; }
.index-demo4 .featuredContentStyle3 { margin-top: -5px; }
.index-demo4 #siteNav > li > a { padding: 0 10px; font-weight: 600; }
.index-demo4 .container-fluid { padding: 0; }
.index-demo4 .featured-grid .grid-products.style2 .row .col-12 { padding: 0; }
.index-demo4 .featured-grid .grid-products.style2 .item, .index-demo4 .featured-grid .grid-products .item .product-image { margin: 0; }
.index-demo4 .button-set li .btn-icon { background-color: #17c4b2; }
.index-demo4 .button-set li .btn-icon:hover { background-color: #0ab4a2 !important; }
.index-demo4 .grid-products .item .product-name a { text-transform: uppercase; font-size: 16px; font-weight: 600; }
.index-demo4 .store-features { padding: 70px 0; }

/* Home - 5 */
.index-demo5 .notification-bar { background-color: #fff0f4; color: #a22340; background-image: url(../images/prm-bg1.png); }
.index-demo5 .notification-bar__message, .index-demo5 .close-announcement { color: #a22340; }
.index-demo5 .imgBanners.style2 .row { margin: 0; }
.index-demo5 .imgBanners.style2 .col-12 { padding: 0; margin: 0 !important; }
.index-demo5 .imgBanners .img-banner-item.last .imgBanner-grid-item { margin: 0; }
.index-demo5 .btn, .index-demo5 .button-set.style5 .btn-icon, .index-demo5 #site-scroll, .index-demo5 .categories-section .btn--small { color:#fff; background-color:#000; border-color:#000; }
.index-demo5 .btn:hover, .index-demo5 .button-set.style5 .btn-icon:hover, .index-demo5 #site-scroll:hover, .index-demo5 .categories-section .btn--small:hover { color:#fff; background-color:#ee307c; border-color:#ee307c; }
.index-demo5 .btn-large { color:#fff; background-color:#ee307c; border-color:#ee307c; }
.index-demo5 .btn-large:hover, .index-demo5 .btn-large:focus { color:#fff; background-color:#000; border-color:#000; }
.index-demo5 #productTabs li a:hover, .index-demo5 #productTabs li a:focus, .index-demo5 #productTabs > li a.active { background-color: #ee307c; }
.index-demo5 .categories-section { background-color:#fff; }
.index-demo5 a:hover { color: #ee307c; }
.index-demo5 .banner-item { margin-bottom: 0; }
.index-demo5 .categories-section .category-style1 .item { background-color: #f7f7f7; }
.index-demo5 .quote-wraper .quotes-slide { background-color: #fff; border: 1px solid #f0f0f0; }
.index-demo5 .quotes-slider__text .rte-setting p { font-size: 16px; }

/* Home - 6 */
.index-demo6 { color: #333e48; }
.index-demo6 .container { max-width: 1370px; }
.index-demo6 .topbar-slider .top-info-bar { color: #000; background-color: #fed700; padding: 10px 5px; }
.index-demo6 .topbar-slider .container-fluid { padding: 0; }
.index-demo6 .btn, .index-demo6 .slideshow .btn { color: #333e48; font-weight: 600; background-color: #fed700; border-color: #fed700; }
.index-demo6 .btn:hover, .index-demo6 .btn:focus, .index-demo6 .slideshow .btn:hover, .index-demo6 .slideshow .btn:focus { color: #333e48; background-color: #ffde25; }
.index-demo6 .slideshow-wrapper { padding: 20px 0 5px; }
.index-demo6 #productTabs li a { font-size: 16px; font-weight: 700; padding: 5px 15px; }
.index-demo6 #productTabs li a:hover, .index-demo6 #productTabs li a:focus, .index-demo6 #productTabs li a.active { color: #fff; background-color: #333e48; }
.index-demo6 .store-features { background-color: #fdfcf2; }
.index-demo6 .home-blog-post { background-color: transparent; }
.index-demo6 .footer .footer-top .h4 { font-size: 16px; }
.index-demo6 #site-scroll { color: #000; background-color: #fed700; border-radius: 100px; }

/* Home - 7 */
.index-demo7 h1, .index-demo7 .h1, .index-demo7 h2, .index-demo7 .h2, .index-demo7 h3, .index-demo7 .h3, .index-demo7 h4, .index-demo7 .h4, .index-demo7 h5, .index-demo7 .h5, .index-demo7 h6, .index-demo7 .h6 { font-family:'Hm-Regular', 'Kreon', serif; }
.index-demo7 .categories-section.style2 .item img { margin: 0; }
.index-demo7 .categories-section.style2 .item-title { padding: 10px; border: 1px solid #f4f4f4; }
.index-demo7 .btn, .index-demo7 .slideshow .btn {
    color: #fff;
    font-weight: 600;
    background-color: var( --theme-color);
    border-color: var( --theme-color);
}
    .index-demo7 .btn:hover, .index-demo7 .btn:focus, .index-demo7 .slideshow .btn:hover, .index-demo7 .slideshow .btn:focus {
        color: #fff;
        background-color: var( --theme-color);
    }
.index-demo7 .footer-bottom .copytext a:hover, .index-demo7 a:hover {
    color: var( --theme-color);
}
.index-demo7 .button-set.style4 .btn-icon:hover { color: #fff; }
.index-demo7 .button-set.style4 .btn-icon { border-radius: 0; }
.index-demo7 .grid-products .item .product-name a { font-size: 14px; font-weight: 500; }
.index-demo7 .grid-products .item .brand-name { margin-bottom: 5px; }
.index-demo7 .grid-products .item .product-price .price { color: #ed0f0f; }
.index-demo7 .instagram-section { background-color: #f6f6f6; }
.index-demo7 .notification-bar { background-color: transparent; }
.index-demo7 .close-announcement, .index-demo7 .notification-bar__message { color: #000; }

.index-demo7 .theme-btn, .index-demo7 .slideshow .theme-btn { color: #fff; font-weight: 600; background-color: var( --theme-color); border-color:var( --theme-color); }
    .index-demo7 .theme-btn:hover, .index-demo7 .theme-btn:focus, .index-demo7 .slideshow .theme-btn:hover, .index-demo7 .slideshow .theme-btn:focus {
        color: #fff;
        background-color: #005273;
    }
.index-demo7 .theme-btn-border, .index-demo7 .slideshow .theme-btn-border {
    border: 1px solid var(--theme-color);
    color: var(--theme-color);
    background: #fff;
}

/* Home - 8 */
.index-demo8 h1, .index-demo8 .h1, .index-demo8 h2, .index-demo8 .h2, .index-demo8 h3, .index-demo8 .h3, .index-demo8 h4, .index-demo8 .h4, .index-demo8 h5, .index-demo8 .h5, .index-demo8 h6, .index-demo8 .h6 { font-family:'Hm-Regular', 'Kreon', serif; }
.index-demo8 .marquee-wrap { color: #fff; background-color: #199b8c; padding: 10px 0; }
.index-demo8 .marquee-wrap .container-fluid { padding: 0; }
.index-demo8 .marquee-text .top-info-bar .flex-item { line-height: normal; }
.index-demo8 .marquee-text .top-info-bar .flex-item a { color: #fff; }
.index-demo8 .store-l-link:hover, .index-demo8 .topheader a:hover { color: #199b8c; }
.index-demo8 .site-cart-count, .index-demo8 .wishlist-count, .index-demo8 .compare-count { background-color: #199b8c; }
.index-demo8 .main-menu { background-color: #333; }
.index-demo8 .header-vertical-menu .vertical-menu-content, .index-demo8 .header-vertical-menu .menu-title { background-color: #199b8c; }
.index-demo8 .vertical-menu-content .nav-link, .index-demo8 .vertical-menu-content .moreCategories { border-color:#148e80; }
.index-demo8 .vertical-menu-content .nav-link:hover { color: #fff; }
.index-demo8 .vertical-menu-content .sub-menu:hover > a:after, .index-demo8 .vertical-menu-content li .dropdown li:hover > a, 
.index-demo8 .vertical-menu-content li .dropdown li a:hover, .index-demo8 .vertical-menu-content .megamenu li ul li a:hover { color:#148e80; }
.index-demo8 .slideshow .slick-dots li button { border-radius: 0; }
.index-demo8 .left .slideshow__text-content { left: inherit; }
.index-demo8 .btn, .index-demo8 .slideshow .btn { color: #fff; font-weight: 600; background-color: #199b8c; border-color: #199b8c; }
.index-demo8 .btn:hover, .index-demo8 .btn:focus, .index-demo8 .slideshow .btn:hover, .index-demo8 .slideshow .btn:focus { color: #fff; background-color: #048778; }
.index-demo8 .footer-bottom .copytext a:hover, .index-demo8 a:hover { color: #199b8c; }
.index-demo8 .imgBanners .inner .ttl { background-color: #199b8ce0; color: #fff; }
.index-demo8 .collection-grids .category-grid-item .inner { padding: 10px 0; }
.index-demo8 .collection-grids .category-title { margin: 0; text-transform: uppercase; }
.index-demo8 .button-set.style4 { bottom: 15px; }
.index-demo8 .button-set.style4 .btn-icon { color: #fff; background-color: #199b8c; border-color: #199b8c; border-radius: 0; }
.index-demo8 .button-set.style4 .btn-icon:hover, .index-demo8 .button-set.style4 .btn-icon:focus { color: #fff; background-color: #048778; }
.index-demo8 #productTabs li a:hover, .index-demo8 #productTabs li a:focus, .index-demo8 #productTabs li a.active { background-color: #333; }
.index-demo8 .tab-slider-product { background-color: #f9f9f9; }
.index-demo8 .tab-slider-product .tab_container { background-color: transparent; padding: 0; }
.index-demo8 .hero .hero__inner .small-title { color: #bf1616; font-weight: 600; letter-spacing: 0.5px; }
.index-demo8 .hero .text-small .mega-title { font-size: 40px; letter-spacing: 0.5px; }
.index-demo8 .hero .text-small .mega-subtitle { font-size: 16px; }
.index-demo8 .parallax-banner-style-bg-fixed .wrap-text { background-color: transparent; }
.index-demo8 .hero .saleTime .count-inner { color: #000; background: #fff; border: 1px dashed #b7b6b6; }
.index-demo8 .hero .saleTime .time-count + span, .index-demo8 .hero .saleTime .time-count { color: #000; }
.index-demo8 .home-blog-post, .index-demo8 .quote-wraper .quotes-slide { background-color: #f9f9f9; }
.index-demo8 .slick-dots li button { border-radius: 0; }
.index-demo8 .section-header h2 { font-size: 20px; }

/* Home - 9 */
.index-demo9 .header-2, .index-demo9 .topheader { background-color: #f6f5f6; }
.index-demo9 .topheader { border-color: #eaeaea; }
.index-demo9 .header-2 .site-cart-count, .index-demo9 .header-2 .wishlist-count, .index-demo9 .header-2 .compare-count { background-color: #a20401; }
.index-demo9 .header-2 .main-menu { background-color: #f6f5f6; border-top: 1px solid #eaeaea; border-bottom: 1px solid #eaeaea; }
.index-demo9 .header-2 .stickyNav.main-menu { border-bottom: 0; }
.index-demo9 .store-l-link:hover, .index-demo9 .header-2 #siteNav > li:hover > a, .index-demo9 .header-2 #siteNav > li > a:hover:hover, 
.index-demo9 .header-2 #siteNav > li > a:hover, .index-demo9 .header-2 #siteNav > li .megamenu li.lvl-1 li .site-nav:hover, .index-demo9 .topheader a:hover, 
.index-demo9 .header-2 .vertical-menu-content .megamenu li ul li a:hover, .index-demo9 .header-2 .vertical-menu-content .sub-menu:hover > a:after, 
.index-demo9 .header-2 .vertical-menu-content li .dropdown li:hover > a, .index-demo9 .header-2 .vertical-menu-content li .dropdown li a:hover { color: #a20401; }
.index-demo9 .header-2 .header-vertical-menu .menu-title, .index-demo9 .header-2 .header-vertical-menu .vertical-menu-content { background-color: #a20401; }
.index-demo9 .header-2 .vertical-menu-content .nav-link, .index-demo9 .header-2 .vertical-menu-content .moreCategories { border-color: #910503; }
.index-demo9 .btn, .index-demo9 .home-slideshow-carousel .slide .details .btn { color: #fff; font-weight: 600; background-color: #a20401; border-color: #a20401; padding: 8px 20px; }
.index-demo9 .btn:hover, .index-demo9 .btn:focus, .index-demo9 .home-slideshow-carousel .slide .details .btn:hover, .index-demo9 .home-slideshow-carousel .slide .details .btn:focus,
.index-demo9 .home-slideshow-carousel .slide .details .btn:hover { color: #fff; background-color: #191919; border-color: #191919; }
.index-demo9 .slideshow-carousel { background-color: #f6f5f6; }
.index-demo9 .slideshow-carousel.style2 .slick-slide { margin: 0 10px; }
.index-demo9 .slideshow-carousel.style2 .slide .details h3 { color: #000; font-size: 18px; padding: 0 20px; display: inline-block; margin-bottom: 5px; background: transparent; }
.index-demo9 .slideshow-carousel.style2 .slide .details .inner { color: #000; background-color: #fffffff2; margin: 20px; border-radius: 5px; }
.index-demo9 .slideshow-carousel.style2 .slide .details .inner p { margin-bottom: 10px; }
.index-demo9 .slideshow-carousel.slideshow .slick-prev, .index-demo9 .slideshow-carousel.slideshow .slick-next { border-radius: 5px; }
.index-demo9 .home-slideshow-carousel.slick-slider { margin: 0 -10px; }
.index-demo9 .header-2 #siteNav > li .megamenu li.lvl-1 li .site-nav:before { background-color: #a20401; }
.index-demo9 #productTabs li a { padding: 5px 20px; }
.index-demo9 #productTabs li a:hover, .index-demo9 #productTabs li a:focus, .index-demo9 #productTabs li a.active { border-radius: 100px; background-color: #191919; }
.index-demo9 .grid-products .item .btn-addto-cart i { margin-top: -5px; font-size: 18px; }
.index-demo9 .button-set li .btn-icon { border-radius: 100px; }
.index-demo9 .store-features { background-color: #f6f5f6; border-top: 1px solid #eaeaea; }
.index-demo9 .featured-content.style2 .featured-content-bg1 { background-color: transparent; }
.index-demo9 .footer-3.footer .newsletter-section { background: url(../images/news-bg1.jpg) no-repeat center center; background-attachment: fixed; background-size: cover; }
.index-demo9 .footer-3.footer .info-section { background-color: #a20401; }
.index-demo9 .footer-3.footer .info-section a { color: #fff; }
.index-demo9 .footer-3.footer .footer-top { background-color: #191919; border-color: #191919; }
.index-demo9 .footer-3.footer .footer-bottom { background-color: #191919; border-color: #5b5b5b; }
.index-demo9 .collection-banners .details .btn { padding: 5px 20px 7px; }
.index-demo9 .footer-3.footer .newsletter-section .newsletter-input { border-radius: 100px !important; padding-right: 60px; padding-left: 30px; }
.index-demo9 .footer-3.footer .newsletter-section .btn { color: #191919; font-size: 24px; line-height: normal; border-radius: 100px !important; position: absolute; right: 10px; top: 0; background: transparent; border: 0; padding: 0; width: 50px; height: 50px; }
.index-demo9 .footer-3.footer .newsletter-section .btn:hover, .index-demo9 .footer-3.footer .newsletter-section .btn:focus { color: #a20401; border: 0; box-shadow: none; }

@media only screen and (min-width: 767px) {
    .large-pd { padding:55px 0; }
    .small-pd { padding:30px 0; }
}

/*======================================================================
  9. Homepage Sections
========================================================================*/
/* 9.0 Image Banners */
.custom-text-banner { color:#000; background: #fdfcf2; }
.custom-text-banner-in { padding:30px; }
.custom-text-banner .h1 { color:#000; font-size:30px; }

/* 9.1 Categories Section */
.categories-section { background-color:#f7f7f7; }
.categories-section .category-style1 .slick-list { margin:0 -10px; }
.categories-section .category-style1 .slick-slide { margin:0 10px; }
.categories-section .category-style1 .item { background-color:#fff; padding:20px; margin-bottom:20px; }
.categories-section .category-style1 .item:last-of-type { margin-bottom:0; }
.categories-section .category-style1 .content-image { flex:0 0 42%; -webkit-flex:0 0 42%; -ms-flex:0 0 42%; width:42%; }
.categories-section .category-style1 .content { padding: 0 10px 0 30px; margin-top: 0; }
.categories-section .item-children { margin: 0 0 15px 10px; }
.categories-section .item-title { font-size:16px; font-weight:bold; }

.categories-section.style2 { background-color:transparent; }
.categories-section.style2 .item { text-align:center; }
.categories-section.style2 .item img { margin:0 auto 20px; }
.categories-section.style2 .slick-slide { margin:0 15px; }

/* 9.2 Products With Tab Slider */
.tab-slider-product .tabs { border:0; text-align:center; margin:0; padding:0; }
.tab-slider-product .tabs > li { float:none; display:inline-block; margin:0 2px; cursor:pointer; }
.tab-slider-product .tabs > li { background:none; border:0; text-transform:uppercase; letter-spacing:0; }
.tab-slider-product .tabs > li a { color:#000; font-weight:600; font-size:14px; padding:5px 10px; display: inline-block; }
    .tab-slider-product .tabs > li a.active {
        color: #fff;
        background: var(--theme-color);
    }
    .tab-slider-product .tabs li a:hover, .tab-slider-product .tabs li a:focus {
        color: #fff;
        opacity: 1;
        background: var(--theme-color);
    }

#productTabs.tabs-style2 > li { position:relative; padding:0; margin:0 15px; }
#productTabs.tabs-style2 > li a { padding: 0; font-weight: 700; font-size: 16px; }
#productTabs.tabs-style2 > li a:after { width:0; content:""; display:block; height:2px; position:absolute; bottom:-6px; left:0; z-index:1; background:#111111; transition:all .3s ease-in-out; -webkit-transition:all .3s ease-in-out; -ms-transition:all .3s ease-in-out; }
#productTabs.tabs-style2 > li a.active:after { width:100%; }
#productTabs.tabs-style2 > li a:hover:after { width:100%; opacity:1; }
#productTabs.tabs-style2 > li a.active,
#productTabs.tabs-style2 > li a:hover { color:#000; background-color:transparent; }

.tab-slider-product .tab_container { clear:both; width:100%; background:#fff; }
.tab-slider-product .tab_content { display:none; }
.tab-slider-product .tab_drawer_heading { display:none; }
.tab_container .grid-products .slick-arrow { width:30px; padding:0; }

.tab-slider-product-style1 .tabs > li { color:#000; font-size:16px; letter-spacing:0.02em; margin:0 12px; position:relative; padding:0; }
.tab-slider-product-style1 .tabs > li:after { width:0; content:""; display:block; height:2px; position:absolute; bottom:-6px; left:0; z-index:1; background:#111111; transition:all .3s ease-in-out; -webkit-transition:all .3s ease-in-out; -ms-transition:all .3s ease-in-out; }
.tab-slider-product-style1 .tabs > li.active:after { width:100%; }
.tab-slider-product-style1 .tabs > li:hover:after { width:100%; opacity:1; }
.tab-slider-product-style1 .tabs > li.active,
.tab-slider-product-style1 .tabs > li:hover { color:#000; background-color:transparent; }

.tab-slider-product-style2 .tabs > li { color:#848484; font-size:14px; font-weight:bold; text-transform:uppercase; padding:5px 10px; }
.tab-slider-product-style2 .tabs > li.active { color:#000; background:transparent; }
.tab-slider-product-style2 .tabs > li:hover { color:#000; background-color:transparent; }

/* 9.3 Hero/Parallax Banners */
.parallax-banner-style1 .hero--exlarge { background-attachment:fixed; }
.parallax-banner-style-bg-fixed .hero { background-attachment:fixed !important; }
.parallax-banner-style-bg-fixed .wrap-text { background-color:rgba(255,255,255,0.89); }
.hero-section .hero--large { background-position: top center !important; }
.hero-section .hero .text-small .mega-title { font-size:30px; }
.hero-section .hero .hero__inner span, .hero-section .hero .text-small .mega-subtitle { font-size: 16px; }
.hero-section .hero .hero__inner .wrap-text { background: #ffffff96; }
.hero { position:relative; height:475px; display:table; width:100%; background-size:cover; background-repeat:no-repeat; background-position:50% 50%; background-attachment:scroll; }
.hero__inner { position:relative; display:table-cell; vertical-align:middle; padding:35px 0; color:#000; z-index:2; text-align:center; }
.hero[data-stellar-background-ratio] { background-attachment:fixed; }
.hero .text-small .mega-title { font-size:26px; font-weight:700; }
.hero .text-small .mega-subtitle { font-size:14px; font-weight:normal; margin:10px 0 15px; }
.hero .hero__inner span { font-size:14px; font-weight: 500; letter-spacing: 1px; text-transform:uppercase; letter-spacing:0; margin-bottom:10px; display:inline-block; }
.hero .saleTime { position:static; }
.hero .saleTime .count-inner { color:#fff; font-family:'Hm-Regular','Rajdhani', sans-serif; font-weight:bold; min-width:80px; padding:10px 4px 8px; line-height:normal; display:inline-block; margin:0 0 0 1px; text-align:center; background:#000; border-radius:0; }
.hero .saleTime .time-count { color:#fff; display:block !important; background:none; font-size:22px; font-weight:700; text-transform:uppercase; line-height:28px; margin:0; padding:0; }
.hero .saleTime .time-count + span { color:#fff; font-size:14px; font-weight:600; margin:0; }
@media only screen and (min-width:990px) {
    .hero__inner .wrap-text { max-width:500px; }
    .hero-section .hero .hero__inner .wrap-text { max-width: 500px; }
}
.hero--large { height:600px; }
.hero--exlarge { height:900px; }
.hero--small { height:380px; }
.hero .text-large .mega-title { font-size:45px; }
.hero__inner .center { text-align:center; margin:0 auto; }
.hero .hero__inner .wrap-text.left { float:left; }
.hero .hero__inner .wrap-text.right { float:right; }
.hero .text-large .mega-subtitle { font-size:20px; }
.hero .mega-subtitle { margin-bottom:25px; }
.hero .font-bold .mega-title { font-weight:700; }
.hero__inner .right { float:right; text-align:center; }
.hero .text-medium .mega-title { font-size:35px; }
.hero .text-medium .mega-subtitle { font-size:18px; }
@media (min-width:767px) {
    .hero .hero__inner .wrap-text { max-width:460px; padding:30px; margin:0 auto; }
    .medical-demo .hero .hero__inner .wrap-text { max-width:400px; }
}
.hero .hero__inner .wrap-text:before { position:absolute; height:100%; width:100%; content: ""; left:0px; top:0px; z-index:-1; }
.hero .hero__inner .wrap-text.topleft { position:absolute; left:5%; top:10%; }
.hero .hero__inner .wrap-text.bottomleft { position:absolute; left:5%; bottom:10%; }
.hero .hero__inner .wrap-text.bottomright { position:absolute; right:5%; bottom:10%; }


/* 9.4 Featured Content Section */
.featured-content .col-12 { padding:0; }
.featured-content .featured-text.right { padding-left:60px; }
.featured-content .featured-text h2 { font-size:24px; font-weight:bold; text-transform:none; margin-bottom: 20px; }
.featured-content .featured-text p { font-size:14px; margin-bottom:20px; padding: 0 60px; }
.featured-content .featured-content-bg { margin-top:60px !important; }
.featured-content .featured-content-bg .display-table-cell { padding:0; }
.featured-content .featured-content-bg .display-table-cell:first-of-type { background-color:#f4f4f4; text-align:center; padding:60px; }
.featured-content.style2 .featured-content-bg1 { background-color: #fdfcf2; }

.featuredContent .d-flex { background-color:#67b0ee; }
.featuredContent .row-text { padding:20px; }
.featuredContent h3 { color:#fff; font-size:22px; margin-bottom:15px; }
.featuredContent p { color:#fff; font-size:18px; margin-bottom:25px; }
.featuredContent .btn { color:#67b0ee; background-color:#fff; }

.featuredContentStyle2 { background-color:#fafafa; }
.featuredContentStyle2.featuredContent .d-flex { color:#000; background-color:#fafafa; }
.featuredContentStyle2.featuredContent h3,
.featuredContentStyle2.featuredContent p { color:#000; }

.section.featuredContentStyle3 { padding:0; color:#000; }
.featuredContentStyle3.featuredContent .container-fluid { padding:0; }
.featuredContentStyle3.featuredContent .d-flex { background-color:#fff; }
.featuredContentStyle3 p { color:#000; font-size:15px; }
.featuredContentStyle3 h3 { color:#000; font-size:28px; }
.featuredContentStyle3 .btn { color:#fff; background-color:#000; }
.featuredContentStyle3 .btn:hover { color:#fff; background-color:#333; }
.featuredContentStyle3 img { width:100%; }
.featuredContentStyle3.featuredContent .row-text { padding:30px 7%; }

/* 9.5 Newletter Section */
.newsletter-section { background-color:#f5f5f5; }
.newsletter-section .section-header { margin-bottom:20px; }
.newsletter-section .newsletter-form .newsletter-input { border:0; background-color:#fff; }
.newsletter-form .input-group__field, .newsletter-form .input-group__btn { display:table-cell; vertical-align:middle; margin:0; }
.newsletter-form .input-group__btn { white-space:nowrap; width:1%; }

/* 9.6 Testimonial Slider */
.quote-wraper .slick-list { margin:0 -10px; }
.quote-wraper .slick-slide { margin:0 10px; }
.quote-wraper .quotes-slide { background-color:#f7f7f7; padding:40px 20px; }
.quote-wraper .testimonial-image { text-align:center; margin-bottom:10px; }
.quote-wraper .authour { margin-bottom:0; }
.quote-wraper .testimonial-image img { margin:0 auto; max-height:70px; max-width:70px; border-radius:100%; }
.quote-wraper blockquote { border:0; max-width:700px; margin:0 auto; line-height:26px; }
.quotes-slider__text { font-size:1.13462em;font-weight:400;font-style:normal; padding:0 15px; }
.quote-wraper .authour { color:#555; font-size:14px; font-weight:700; letter-spacing:1px; text-transform:uppercase; color:#000; line-height:18px; }
.quotes-slider__text p { margin-bottom:10px; }
.quote-wraper .product-review { margin-bottom:15px; }
.quote-wraper .slick-arrow { background-color:rgba(255,255,255,0.8); height:35px; line-height:34px; width:35px; text-align:center; font-size:0px; padding:0; opacity:0; visibility:hidden; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
.quote-wraper .slick-arrow:before { line-height:35px; }
.quote-wraper:hover .slick-arrow { opacity:1; visibility:visible; }
.quote-wraper .slick-prev { left:-10px; }
.quote-wraper .slick-next { right:-10px; }
.quote-wraper .rte-setting:before { content: "\f10d"; font-family:"annimex-bold"; color:#cecece; font-size:50px; display:block; margin-bottom:15px; }
.quote-wraper .slick-dots { margin-top:20px; }
.testimonial-style2 { background-color:#f7f7f7; }
.testimonial-style2 .section-header { margin:0; }
.quotes-slider-style2 .slick-list, .quotes-slider-style2 .slick-slide { margin:0; }
.quotes-slider-style2 blockquote { font-size:16px; line-height:30px; border:0; max-width:870px; margin:0 auto; }
.quotes-slider-style2 .quotes-slide { padding:20px; }
.quotes-slider-style2 .rte-setting:before { font-size:60px; }
.quotes-slider-style2 .slick-dots { margin:0; }
.quotes-slider-style2 .slick-prev { left:30px; }
.quotes-slider-style2 .slick-next { right:30px; }

.testimonial-slider-style1 { background:url(../images/slideshow-banner/dome13-banner2.jpg) no-repeat; background-position:center center; background-color:transparent; background-size:cover; background-attachment:fixed; }
.testimonial-slider-style1.quote-wraper { padding:90px 40px; }
.testimonial-slider-style1 .section-header h2 { color:#fff; }
.testimonial-slider-style1 .rte-setting, .testimonial-slider-style1 .quotes-slider .authour { color:#fff; font-size:16px; }

/* 9.7 Info/Simple Text Section */
.section.info-section { padding:15px; }
.info-section { color:#fff; font-size:18px; background-color:#333333; padding:10px; }
.info-section a { color:#fff; letter-spacing:0; }
.info-section a:hover { color:#ff802b; }
.info-section span { display:block; font-size:12px; }
.info-section-in { border:1px solid #555; padding:15px; }

/* 9.8 Instagram Section */
.index-demo4 .home-instagram { padding-top:30px; }
.home-instagram #instafeed .insta-img { position:relative; display:block; padding:0; float:left; width:20%; }
.home-instagram #instafeed .insta-img a { margin:0 1px 2px; display:block; overflow:hidden; }
.home-instagram #instafeed .insta-img img { display:block; width:100%; }
.home-instagram #instafeed .overlay { -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; opacity:0; visibility:hidden; content: ""; height:100%; width:100%; left:0; top:0; background:rgba(0,0,0,0.7); position:absolute; }
.home-instagram #instafeed .insta-img:hover .overlay { opacity:1; visibility:visible; }
.home-instagram #instafeed .counter { position:absolute; left:0; right:0; top:50%; width:100%; text-align:center; }
.home-instagram #instafeed .counter span { padding:0 4px; }
.home-instagram #instafeed .counter .anm { color:#fff; margin-right:5px; }
.home-instagram #instafeed .counter span { padding:0 4px; color:#fff; }
@media (min-width:992px){
    .instagram-section .item { max-width:20%; flex:0 0 20%; -webkit-flex:0 0 20%; }
}
.instagram-section .item a { position:relative; height:100%; display:block; overflow:hidden; transition:all 0.4s ease; -webkit-transition:all 0.4s ease; -ms-transition:all 0.4s ease; }
.instagram-section .item a img { width:100%; height:100%; -o-object-fit:cover; object-fit:cover; transition:800ms ease 0s; -ms-transition:800ms ease 0s; -webkit-transition:800ms ease 0s; opacity:1 !important; }
.instagram-section .item .ins-icon {
    font-size:24px; line-height:1; color:#fff; position:absolute; top:50%; left:50%; transform:translate(-50%, -50%) scale(0); -webkit-transform:translate(-50%, -50%) scale(0); -ms-transform:translate(-50%, -50%) scale(0);
    transition:transform .3s ease; -webkit-transition:transform .3s ease; -ms-transition:transform .3s ease; width:100%; height:100%; 
    display:inline-flex; justify-content:center; -webkit-justify-content:center; -ms-justify-content:center; align-items:center; -webkit-align-items:center; -ms-align-items:center; 
}
.instagram-section .item:hover .ins-icon { opacity:1; transform:translate(-50%, -50%) scale(1); -webkit-transform:translate(-50%, -50%) scale(1); -ms-transform:translate(-50%, -50%) scale(1); z-index:3; }
.instagram-section .item:hover a:before { content: ''; background-color:rgba(0, 0, 0, 0.5); content: ''; position:absolute; top:0; left:0; z-index:2; width:100%; height:100%; }
.instagram-section .item:hover img { transform:scale(1.1, 1.1); -webkit-transform:scale(1.1, 1.1); -ms-transform:scale(1.1, 1.1); }

/* 9.9 Miniproduct List Section */
.mini-product .column-ttl { font-size:17px; text-transform:uppercase; margin:0 0 25px; }
.mini-product .mini-list-item { margin-bottom:20px; display:flex; display:-webkit-flex; }
.mini-product .mini-list-item .mini-view_image { width:35%; max-width:100px; float:left; overflow:hidden; }
.mini-product .mini-list-item .details { margin-left:0; padding-left:15px; }
.mini-product .mini-list-item .mini-view_image img { max-width:100px; }
.mini-product .mini-list-item .product-review .fa { font-size:13px; margin:0; }

/* 9.10 Homepage Slideshow */
.slideshow-wrapper { position:relative; }
.slideshow-wrapper .container-fluid { padding:0; }
.slideshow .slide { position:relative; }
.slideshow .wrap-caption.center { max-width:1200px; text-align:center; }
.slideshow .slideshow__text-content.center { left:0; right:0; margin:0 auto; }
.slideshow .slideshow__text-content.center .wrap-caption.center { display: block; }
.slideshow .center .wrap-caption.white-bg { background-color:rgba(255,255,255,0.7); }
.slideshow .wrap-caption.right { text-align:left; }
.slideshow .wrap-caption.left { float:left; text-align:left; }
.slideshow .center .slideshow__text-content { left:0; right:0; margin:0 auto; -ms-transform:translateY(-50%,-50%); -webkit-transform:translateY(-50%,-50%); transform:translateY(-50%,-50%); } 
.left .slideshow__text-content { left:7%; text-align:center; }
.right .slideshow__text-content { right:7%; }
.slideshow__text-content { position:absolute; max-width:1500px; width:83%; top:50%; z-index:3; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); }
.slideshow .wrap-caption { display:inline-block; padding:25px; }

/* Slide Caption Animation */
.slideshow__text-wrap .anim-tru.style1 { opacity:0; -ms-transition:1s all 100ms; -webkit-transition:1s all 100ms; transition:1s all 100ms; -webkit-transition:1s all 100ms; transform:scale(0.8); -webkit-transform:scale(0.8); }
.slick-active .slideshow__text-content .anim-tru.style1 { opacity:1; transform:scale(1); -webkit-transform:scale(1); -ms-transform:scale(1); }
.slideshow__text-content.bottom { top:auto; bottom:10%; -ms-transform:translateY(0); -webkit-transform:translateY(0); transform:translateY(0); }
.slideshow__text-content.top { top:10%; -ms-transform:translateY(10%); -webkit-transform:translateY(10%); transform:translateY(10%); }
.slick-active .slideshow__text-content { -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); opacity:1; }
.slick-active .slideshow__text-content.bottom { top:auto; -ms-transform:translateY(0); -webkit-transform:translateY(0); transform:translateY(0); }
.slick-active .slideshow__text-content.top { -ms-transform:translateY(-5%); -webkit-transform:translateY(-5%); transform:translateY(-5%); }
/* End Slide Caption Animation */

.slideshow .slideshow__title { color:#000000; font-weight:700; font-size:45px; line-height:1.1; text-shadow:1px 1px 7px rgba(0,0,0,0); }
.slideshow .slideshow__subtitle { color:#000000; font-weight:400; font-size:16px; text-transform:none; margin-bottom:20px; line-height:25px; text-shadow:1px 1px 4px rgba(0,0,0,0); display:block; }
.slideshow__text-wrap { height:100%; }
.slick-active .slideshow__image.img-animate { -ms-transform:scale(1); -webkit-transform:scale(1); transform:scale(1); }
.slideshow__image.img-animate { -ms-transform:scale(1.1); -webkit-transform:scale(1.1); transform:scale(1.1); }
.slideshow__overlay.bottom:before { background:-ms-linear-gradient(bottom, rgba(0,0,0,0) 0%, #000 100%); background:linear-gradient(to bottom, rgba(0,0,0,0) 0%, #000 100%); }
.slideshow__overlay:before { content: ''; position:absolute; top:0; right:0; bottom:0; left:0; opacity:0.5; z-index:3; }
.slideshow .slick-slide img { width:100%; }
.slideshow .slick-prev, .slideshow .slick-next { 
    line-height:normal; font-size:0px; padding:0; border:0; opacity:0; visibility:hidden; position:absolute; z-index:4; top:50%; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); 
    width:40px; height:40px; text-align:center; background-color:rgba(255,255,255,0.1); -ms-transition:all ease-out 0.2s; -webkit-transition:all ease-out 0.2s; transition:all ease-out 0.2s;
}
.slideshow:hover .slick-prev, .slideshow:hover .slick-next { opacity:0.75; visibility:visible; opacity:0.7; background-color:rgba(255,255,255,0.7); box-shadow:0 0 4px rgba(0,0,0,0.4); -webkit-box-shadow:0 0 4px rgba(0,0,0,0.4); }
.slideshow .slick-prev { left:10px; }
.slideshow .slick-next { right:10px; }
.slideshow .slick-next:before { content:"\f105"; font-family:"annimex-bold"; color:#000; }
.slideshow .slick-prev:before { content:"\f104"; font-family:"annimex-bold"; color:#000; }
.slideshow .slick-prev:before, .slideshow .slick-next:before { font-size:20px; line-height:20px; }
.slideshow .btn { color:#fff; background-color:#000; }
.slideshow .btn:hover, .slideshow .btn:focus { opacity:0.8; }
.slideshow .slick-dots { margin:0; width:auto; padding:0; list-style:none; position:absolute; bottom:30px; text-align:center; left:50%; transform:translateX(-50%); -webkit-transform:translateX(-50%); -ms-transform:translateX(-50%); }
.slideshow .slick-dots li { width:12px; height:12px; vertical-align:middle; position:relative; display:inline-block; padding:0; cursor:pointer; margin-right:8px; }
.slideshow .slick-dots li button { color:transparent; line-height:0; font-size:0; border:0; background:transparent; display:block; cursor:pointer; color:#fff; width:12px; height:12px; text-indent:-9999px; padding:0; border-radius: 100%; background-color:#000; transition:all 0.2s; -webkit-transition:all 0.2s; -ms-transition:all 0.2s; opacity:0.5; }
.slideshow .slick-dots li.slick-active button { opacity:1; }
.slideshow img.mobile-hide { display:none; }

.slideshow.style2 .slideshow__title { font-size:42px; font-weight:300;  margin-bottom:5px; }
.slideshow.style2 .mega-small-title {  margin-bottom:5px; }
.slideshow.style2 .slideshow__subtitle { font-size:15px; line-height:normal; }
.slideshow.style2 .btn { color:#fff; padding:5px 15px; border:2px solid #000; }
.slideshow.style2 .btn:hover, .slideshow.style2 .btn:focus { color:#000; border-color:#000; background-color:transparent; }

.slideshow-carousel.slideshow .slick-prev,
.slideshow-carousel.slideshow .slick-next { opacity:0.9; background-color:#fff; border-radius:0; box-shadow:none; -webkit-box-shadow:none; }
.home-slideshow-carousel .slick-slide { margin:0 6px 12px; }
.home-slideshow-carousel .slide .img { position:relative; overflow:hidden; }
.home-slideshow-carousel .slide .img img { display:block; width:100%; -ms-transition:all 0.4s ease-out; -webkit-transition:all 0.4s ease-out; transition:all 0.4s ease-out; }
.home-slideshow-carousel .slide .details { display:block; height:100%; position:absolute; top:0; left:0; opacity:0; visibility:hidden; width:100%; text-align:center; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.home-slideshow-carousel .slide .details h3 { color:#000; font-size:20px; line-height:1.3; margin:0; font-weight:600; text-transform:uppercase; }
.home-slideshow-carousel .slide .details .btn { background-color:#e34848; display:inline-block; margin-top:15px; font-size:12px; padding:10px 25px; }
.home-slideshow-carousel .slide .details .btn:hover { background-color:#000; opacity:1; }
.home-slideshow-carousel .slide .details:before { position:absolute; left:0; top:0; content: ""; width:100%; height:100%; background:#ffffff; opacity:0.8; }
.home-slideshow-carousel .slide:hover .details { opacity:1; visibility:visible; }
.home-slideshow-carousel .slide .inner { position:relative; top:50%; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); padding:15px; }
.home-slideshow-carousel .slide:hover .img img { -ms-transform:scale(1.1); -webkit-transform:scale(1.1); transform:scale(1.1); }

.slideshow-carousel.style2 .slick-slide { margin:0; }
.slideshow-carousel.style2 .slide .details { height:auto; top:auto; bottom:0; opacity:1; visibility:visible; }
.slideshow-carousel.style2 .slide .details h3 { color:#fff; font-size:18px; margin:0; font-weight:700; padding:10px 20px; background: #0000007d; display: inline-block; }
.slideshow-carousel.style2 .slide .details:before { display:none; }
.slideshow-carousel.style2 .slide .inner { transform:none; -webkit-transform:none; -ms-transform:none; }

.mega-subtitle ul { list-style:none; padding:0; margin:15px 0; }
.mega-subtitle li { line-height:1.5; position:relative; padding-left:20px }
.mega-subtitle li:before { position:absolute; left:0; content:"\ea7f"; font-family:"annimex-bold"; font-size:14px; top:50%; margin-top:-10px }

/* 9.11 Collection Slider */
.collection-slider .collection-grid-item { margin:0 10px; position:relative; text-align:center; overflow:hidden; width:auto !important; }
.collection-slider .collection-grid-item .img { position:relative; overflow:hidden; }
.collection-slider .collection-grid-item .img img { display:block; width:100%; -ms-transition:all 0.4s ease-out; -webkit-transition:all 0.4s ease-out; transition:all 0.4s ease-out; }
.collection-slider .collection-grid-item:hover .img img { -ms-transform:scale(1.1); -webkit-transform:scale(1.1);transform:scale(1.1); }
.collection-slider .slick-arrow { opacity:0; visibility:hidden; width:30px; height:30px; }
.collection-slider:hover .slick-arrow { color:#000; opacity:1; visibility:visible; }
.collection-slider .collection-grid .slick-prev { left:10px; }
.collection-slider .collection-grid .slick-next{ right:10px; }

.collection-slider-full .collection-grid-slider { padding:0 40px; }
.collection-slider-full .slick-prev { left:40px; }
.collection-slider-full .slick-next{ right:40px; }
.collection-slider-full .collection-grid-slider .collection-item { position:relative; }
.collection-slider-full .collection-grid-slider .details { position:absolute; bottom:20px; left:20px; right:20px; width:auto; }
.collection-slider-full .collection-grid-slider .details:before { background:#ffffff; opacity:0.9; }
.collection-slider-full .collection-grid-slider .details .collection-item-title { color:#333333; }

/* 9.12 Brands Logo Slider */
.logo-section hr { margin-bottom:70px; }
.logo-bar__item:hover { opacity:0.6; }
.logo-bar .slick-arrow { -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
.logo-bar:hover .slick-arrow { color:#000; opacity:1; }
.logo-bar .slick-prev { left:0; }
.logo-bar .slick-next { right:-5px; }
.logo-bar .slick-slide img { margin:0 auto; }
.logo-bar .logo-bar__item a { border:1px solid #ddd; display:block; }
.logo-bar .slick-arrow { opacity:0; visibility:hidden; }
.logo-bar:hover .slick-arrow { opacity:1; visibility:visible; }
.logo-bar .slick-list { margin:0 -10px; }
.logo-bar .slick-slide { margin:0 10px; }

/* 9.13 Home Blog Post */
.home-blog-post { background-color:#f7f7f7; padding:50px 0; }
.home-blog-post-style1 { background-color:#fff; }
.blog-post-slider .slick-list { margin:0 -10px; }
.blog-post-slider .slick-slide { margin:0 10px; }
.blogpost-item { /*width:auto !important;*/ margin:0; }
.blogpost-item .post-thumb { display:block; margin-bottom:20px; position:relative; overflow:hidden; }
.blogpost-item .post-thumb img { display:block; width:100%; -ms-transition:all 0.4s ease-out; -webkit-transition:all 0.4s ease-out; transition:all 0.4s ease-out; }
.blogpost-item:hover .post-thumb img { -ms-transform:scale(1.1); -webkit-transform:scale(1.1);transform:scale(1.1); }
.blogpost-item .post-detail { text-align:left; }
.blogpost-item .post-title { font-weight:bold; }
.blogpost-item .post-excerpt { margin-top:10px; }
.blogpost-item .article-excerpt { font-size:14px; margin-top:10px; }
.blogpost-item .publish-detail { color:#9d9d9d; font-size:12px; list-style:none; padding:0; margin:0; }
.blogpost-item .publish-detail li { display:inline-block; padding:0; }
.blogpost-item .publish-detail li a { color:#9d9d9d; }
.blogpost-item .publish-detail li a:hover { text-decoration:none; } 
.blogpost-item .post-detail .excerpt { margin:15px 0; }
.home-blog-post .slick-arrow { opacity:0; visibility:hidden; width:30px; height:30px; }
.home-blog-post:hover .slick-arrow { color:#000; opacity:1; visibility:visible; }
.home-blog-post .slick-prev { left:10px; }
.home-blog-post .slick-next{ right:10px; }

.home-blog-post-style1 .blogpost-item .post-detail .post-title { font-weight:normal; font-size:15px; }
.home-blog-post-style1 .blogpost-item .post-thumb { margin-bottom:20px; }
.home-blog-post-style1 .blogpost-item .publish-detail { font-size:13px; }
.home-blog-post-style1 .blogpost-item .publish-detail li { padding:0; }

.latest-blog .wrap-blog {
    display: table;
}
.latest-blog .wrap-blog .article__grid-image, .latest-blog .wrap-blog .article__grid-meta {
    display: table-cell;
    vertical-align: middle;
    float: none;
}
.latest-blog .wrap-blog .article__grid-meta {
    width: 55%;
}
.latest-blog .wrap-blog .article__grid-image, .latest-blog .wrap-blog .article__grid-meta {
    display: table-cell;
    vertical-align: middle;
    float: none;
}
.latest-blog .wrap-blog .wrap-blog-inner {
    background: rgba(255, 255, 255, .8);
    padding: 20px;
    margin-left: -30px;
    position: relative;
}
.latest-blog .article__title.h3 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}
.latest-blog .article__title {
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0;
}
.latest-blog .article__date {
    font-size: 12px;
    color: #666;
    display: inline-block;
    margin-bottom: 15px;
}
.latest-blog .article__grid-excerpt {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
}
.latest-blog .article__grid-excerpt {
    font-size: 12px;
    margin-bottom: 10px;
}
.latest-blog .article__meta-buttons {
    text-transform: uppercase;
}
.list--inline{
    padding: 0;
    margin: 0;
}
/* 9.14 Store Features */
.store-features { background-color:#fff; padding:40px 0; }
.store-info .an { float:left; margin:0 15px 0 0; font-size:40px; vertical-align:middle; display:block; }
.store-info h5, .store-info .h5 { margin-bottom:0; letter-spacing:0.05em; font-size:15px; font-weight:700; text-transform:uppercase; }
.store-info h5, .store-info p, .store-info, .store-info a { color:#000; }
.store-info p { display:block; padding-left:55px; }
.store-features .slick-list { margin-left:0; margin-right:0; }
.store-features .slick-slide { margin-left:-5px; margin-right:-5px; }
.store-features .col-12 { padding:0 20px; }
.store-features .store-info .slick-arrow { opacity:0; visibility:hidden; }
.store-features .store-info:hover .slick-arrow { opacity:1; visibility:visible; }

.store-info-style2 .slick-slide { margin:0 10px; }
.store-info-style2 .item { background-color:#f7f7f7; padding:20px 10px; text-align:center; }
.store-info-style2 .item i { font-size:40px; }
.store-info-style2 .item h5 { margin:10px 0 5px; font-size:15px; text-transform:uppercase; }

.store-info-style3 .slick-slide { text-align: center; padding: 0 10px; margin: 0; }
.store-info-style3 .slick-slide i { display: block; float: none; margin: 0 auto 10px; }
.store-info-style3 h5 { margin-bottom: 10px; font-size: 18px; }
.store-info-style3.store-info p { padding: 0; }
.store-info-style3 .icons { background-color: #efefef; width: 70px; height: 70px; display: block; text-align: center; border-radius: 100px; margin: 0 auto 10px; }
.store-info-style3 .icons i { line-height: 70px; }

.store-info-section { background-color:#fafafa; }
.style2.store-info h5, .style2.store-info span, .style2.store-info, .store-info a, .store-info.style2 .anm { color:#333333; }
.style2.store-info h5, .style2.store-info .h5 { margin-bottom:0; letter-spacing:0; font-size:14px; }
.store-info.style2 .anm { float:left; margin:0 15px 0 0; }

/* 9.15 Promotion Product Popup */
.product-notification { display:block; width:270px; padding:20px; background-color:#fff; -webkit-box-shadow:0px 0 7px 2px rgba(158,158,158,0.2); box-shadow:0px 0 7px 2px rgba(158,158,158,0.2); margin:10px; position:fixed; bottom:0; -webkit-animation:movebottom 15s infinite; animation:movebottom 15s infinite; z-index:99; }
.product-notification img { height:70px; }
.product-notification .title { color:#6a6a6a; font-size:10px; }
.product-notification .pname { font-size:14px; font-weight:600; margin-bottom:5px; }
.product-notification .detail { font-size:11px; line-height:1.2; margin:0; color:#6a6a6a; }
.product-notification .media-body { padding-left:10px; }
.product-notification p { margin:0 0 10px; }
.product-notification .close { font-size:13px !important; cursor:pointer; position:absolute; right:7px; top:6px; z-index:99; }
@-webkit-keyframes movebottom {
    0% { display:block; bottom:-200px; }
    25% { bottom:0px; }
    75% { bottom:0px; }
    100% { display:none; bottom:-200px; }
}
@keyframes movebottom {
    0% { display:block; bottom:-200px; }
    25% { bottom:0px; }
    75% { bottom:0px; }
    100% { display:none; bottom:-200px; }
}

.instagram_gallery img { padding:5px; }

/* 9.16 Custom Content */
.custom-content-section .col-lg-6 { margin-bottom:30px; }
.custom-content-section .section-header { margin-bottom:10px; }
.custom-content-section .custom-details { text-align:center; max-width:80%; margin:0 auto; } 
.custom-content-section .custom-details p { margin-bottom:20px; }

/* 9.17 Instagram Shop */
.instagram-shop { margin-top:20px; }
.instagram-shop .row { margin-left:-8px; margin-right:-8px; }
.instagram-shop .row .col-6 { padding:0 8px; margin-bottom:16px; }
.instagram-shop .insta-item img { display:block; -ms-transition:all ease-out 0.5s; -webkit-transition:all ease-out 0.5s; transition:all ease-out 0.5s; }
.instagram-shop .insta-item { position:relative; display:block; overflow:hidden; }
.instagram-shop .insta-item:hover img { transform:scale(1.1); -webkit-transform:scale(1.1); -ms-transform:scale(1.1); }
.instagram-shop .insta-item:after { content:'\e931'; font-family:"annimex-bold"; display:inline-block; position:absolute; right:10px; bottom:10px; font-size:15px; background-color:rgba(0,0,0,0.18); border-radius:100px; width:30px; height:30px; line-height:30px; text-align:center; }


/*======================================================================
  10. Collection Banner Grid
========================================================================*/
.collection-banners .row { margin-left:-10px; margin-right:-10px; }
.collection-banners .row .banner-item { padding-left:10px; padding-right:10px; }
.collection-banners .collection-grid-item { position:relative; overflow:hidden; }
.collection-banners .collection-grid-item img { display:block; width:100%; -ms-transition:all ease-out 0.5s; -webkit-transition:all ease-out 0.5s; transition:all ease-out 0.5s; }
.collection-banners .collection-grid-item:hover img { transform:scale(1.1); -webkit-transform:scale(1.1); -ms-transform:scale(1.1); }
.collection-banners .details { background-color:#fff; color:#000; width:50%; left:50%; -ms-transform:translateX(-50%); -webkit-transform:translateX(-50%); transform:translateX(-50%); position:absolute; bottom:30px; right:0; padding:20px; text-align:center; }
.collection-banners .details.middle { -ms-transform:translate(-50%,-50%); -webkit-transform:translate(-50%,-50%); transform:translate(-50%,-50%); top: 50%; bottom: auto; }
.collection-banners .details.left { -ms-transform:none; -webkit-transform:none; transform:none; left: 30px; }
.collection-banners .details .title { margin:0; font-size:16px; text-transform:uppercase; font-weight:bold; }
.collection-banners .details p { margin:5px 0 0; }
.collection-banners .details .btn { font-size:12px; padding:5px 15px; margin-top:10px; }
.banner-item:after, .collection-page-item:after { content:''; display:block; clear:both; }
.grid-sizer, .collection-page-item { max-width:50%; }
.grid-sizer.grid-5col, .collection-banners.style4 .collection-page-item { width:20%; }
.grid-sizer.grid-6col, .collection-banners.style5 .collection-page-item { width:16.66667%; }
.grid-sizer.grid-7col, .collection-banners.style6 .collection-page-item { width:14.28571%; }
@media (min-width:576px) and (max-width: 767px) {
    .grid-sizer, .banner-item, .collection-page-item { width:50%; }
    .grid-sizer.grid-5col, .collection-banners.style4 .collection-page-item,
    .grid-sizer.grid-6col, .collection-banners.style5 .collection-page-item,
    .grid-sizer.grid-7col, .collection-banners.style6 .collection-page-item { width:33.33%; }
}
.banner-item, .collection-page-item { float:left; margin-bottom:20px; }
.banner-item img, .collection-page-item img { display:block; max-width:100%; }

.collection-page-grid .collection-page-item { padding-left:5px; padding-right:5px; margin-bottom:10px; }
.collection-grid-item .collection-grid-item__link { opacity:1; }
.collection-grid-item .details { display:block; height:100%; position:absolute; top:0; opacity:0; z-index:1; padding:15px; width:100%; text-align:center; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.collection-grid-item .details:before { position:absolute; top:0; left:0; content:""; width:100%; height:100%; background:#000; z-index:-1; }
.collection-grid-item .details .inner { position:relative; top:50%; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); padding:0; }
.collection-grid-item__title { color:#fff; font-size:17px; text-transform:uppercase; margin:0; font-weight:600; display:block; }
.collection-grid-item .counts { color:#fff; font-size:13px; }
.collection-grid-item:hover .details { opacity:0.8; }

.collection-banners.style0 .collection-grid-item .details { display:-ms-flexbox; display:flex; -ms-flex-align:center; align-items:center; -webkit-justify-content:center; -ms-justify-content:center;justify-content:center; height:100%; position:absolute; top:0; opacity:0; z-index:1; padding:15px; width:100%; text-align:center; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.collection-banners.style0 .collection-grid-item .details:before { position:absolute; top:0; left:0; content:""; width:100%; height:100%; background:#000; z-index:-1; }
.collection-banners.style0 .collection-grid-item:hover .details { opacity:0.8; }
.collection-banners.style0 .collection-grid-item .details .inner { position:static; }

.collection-banners.style1 .collection-grid-item .overlay { display:block; width:100%; height:100%; opacity:0; visibility:hidden; position:absolute; top:0; left:0; z-index:4; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.collection-banners.style1 .collection-grid-item:hover .overlay { visibility:visible; opacity:1; }
.collection-banners.style1 .collection-grid-item .details { opacity:1; visibility:visible; height:auto; top:auto; width:auto; }
.collection-banners.style1 .collection-grid-item .details.rightMiddle { right: 30px; left: auto; top: 50%; bottom: auto; transform: translate(0,-50%); -webkit-transform: translate(0,-50%); -ms-transform: translate(0,-50%); }
.collection-banners.style1 .collection-grid-item .details.leftMiddle { left: 30px; right: auto; top: 50%; bottom: auto; transform: translate(0,-50%); -webkit-transform: translate(0,-50%); -ms-transform: translate(0,-50%); }
.collection-banners.style1 .collection-grid-item .details.transparent { background-color:transparent; }
.collection-banners.style1 .collection-grid-item .details:before { display:none; }
.collection-banners.style1 .collection-grid-item .details.wd-70 { width:70%; }
.collection-banners.style1 .collection-grid-item .details.wd-90 { width:90%; }
.collection-banners.style1 .collection-grid-item .details .inner { position:static; transform:none; -ms-transform:none; -webkit-transform:none; line-height: normal; }
.collection-banners.style1 .collection-banners .details p { margin-top:10px; }

.collection-banners.style2 .collection-grid-item .details { text-align:center; position:absolute; top:inherit; bottom:0; left:0; height:auto; -ms-transform:translateY(100%); -webkit-transform:translateY(100%); transform:translateY(100%); }
.collection-banners.style2 .collection-grid-item:hover .details { -ms-transform:translateY(0); -webkit-transform:translateY(0); transform:translateY(0); }
.collection-banners.style2 .collection-grid-item__title, .collection-banners.style3 .collection-grid-item__title { font-size:15px; }

.collection-banners.style3 .collection-page-item { margin-bottom:30px; }
.collection-banners.style3 .collection-grid-item .details { color:#000000; position:relative; opacity:1; visibility:visible; background:#f5f5f5; }
.collection-banners.style3 .collection-grid-item .details:before, .collection-banners.style4 .collection-grid-item .details:before,
.collection-banners.style5 .collection-grid-item .details:before, .collection-banners.style6 .collection-grid-item .details:before { display:none; }
.collection-banners.style3 .collection-grid-item .collection-grid-item__title, .collection-banners.style3 .collection-grid-item .counts { color:#000000; }

.collection-banners.style4 .collection-grid-item .details,
.collection-banners.style5 .collection-grid-item .details,
.collection-banners.style6 .collection-grid-item .details { padding:10px; width:auto; height:auto; top:auto; bottom:15px; left:15px; right:15px; opacity:1; visibility:visible; background:rgba(255,255,255,0.8); -ms-transform:translateX(0); -webkit-transform:translateX(0); transform:translateX(0); }
.collection-banners.style4 .collection-grid-item__title,
.collection-banners.style5 .collection-grid-item__title,
.collection-banners.style6 .collection-grid-item__title { color:#000000; font-size:15px; }
.collection-banners.style6 .collection-grid-item .details { background:#000; position:static; }
.collection-banners.style6 .collection-grid-item__title { color:#ffffff; }

.collection-banners.style7 .collection-grid-item .details { max-width: 280px; opacity:1; visibility:visible; background-color: transparent; width: auto; height: auto; top: auto !important; bottom: auto !important; left: auto; right: auto; -ms-transform: none; -webkit-transform: none; transform: none; }
.collection-banners.style7 .collection-grid-item .details:before { opacity:0; }
.collection-banners.style7 .collection-grid-item .details .inner { font-size: 15px; letter-spacing: normal; padding: 0; top: auto; -ms-transform: none; -webkit-transform: none; transform: none; }

.collection-banners.style7 .collection-grid-item .black-link .btn--link { color: #444444; border-color: #444444; }
.collection-banners.style7 .details.center-left { text-align: left; top: 50% !important; left: 25px; -ms-transform: translateY(-50%); -webkit-transform: translateY(-50%);  transform: translateY(-50%); }
.collection-banners.style7 .details.center-right { text-align: left; top: 50% !important; right: 20px; -ms-transform: translateY(-50%); -webkit-transform: translateY(-50%);  transform: translateY(-50%); }
.collection-banners.style7 .details.center-bottom { width: 100%; bottom: 20px !important; left: 50%; -ms-transform: translateX(-50%); -webkit-transform: translateX(-50%);  transform: translateX(-50%); }
.collection-banners.style7 .details.center-middle { top: 50% !important; left: 50%; -ms-transform: translate(-50%,-50%); -webkit-transform: translate(-50%,-50%); transform: translate(-50%,-50%); }
.collection-banners.style7 .details.top-left { width: 38%; text-align: left; top: 35px !important; left: 35px; }
.collection-banners.style7 .details.top-center { width: 60%; top: 20px !important; left: 50%; -ms-transform: translateX(-50%); -webkit-transform: translateX(-50%);  transform: translateX(-50%); }
.collection-banners.style7 .details.bottom-left { width: 60%; text-align: left; bottom: 20px !important; left: 20px; }
.collection-banners.style7 .details.bottom-right { width: 50%; text-align: left; bottom: 20px !important; right: 20px; }

.category-6col-page .container-fluid, .category-7col-page .container-fluid { padding:0; }

.collection-box { background-color:#f5f5f5; }
.collection-grid-slider { margin:0 -7.5px; }
.collection-grid-slider .slick-slide { margin:0 7.5px; }
.collection-grid-slider .collection-item .img { position:relative; overflow:hidden; }
.collection-grid-slider .collection-item .img img { display:block; width:100%; -ms-transition:all 0.4s ease-out; -webkit-transition:all 0.4s ease-out; transition:all 0.4s ease-out; }
.collection-grid-slider .collection-item:hover .img img { -ms-transform:scale(1.1); -webkit-transform:scale(1.1); transform:scale(1.1); }
.collection-grid-slider .details { z-index:1; position:relative; padding:15px; width:100%; text-align:center; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.collection-grid-slider .details:before { position:absolute; left:0; top:0; content: ""; width:100%; height:100%; background:#111111; opacity:1; z-index:-1; }
.collection-grid-slider .details .collection-item-title { color:#fff; font-size:14px; text-transform:uppercase; margin:0; }
.collection-grid-slider .slick-arrow { background-color:rgba(255,255,255,0.75); opacity:0; visibility:hidden; }
.collection-grid-slider:hover .slick-arrow { opacity:1; visibility:visible; }

.collection-banner-grid { background-color:inherit; }
.collection-banner-grid .collection-item { margin-bottom:30px; }
.collection-banner-grid .details { background-color:rgba(88, 195, 141, 0.7); padding:15px; text-align:center; }
.collection-banner-grid .collection-item-title { font-size:15px; }
.collection-banners .imgBanner-grid-item { position:relative; }

.collection-slider-4items .slick-list { margin-left:-5px; margin-right:-5px; }
.collection-slider-4items .slick-slide { padding:5px; }
.collection-slider-4items .slick-arrow { background-color:rgba(255,255,255,0.9); }
.collection-slider-4items .slick-prev { left:0; }
.collection-slider-4items .slick-next { right:0; }

.imgBanners .inner * { -ms-transition:all 0.4s ease-in-out; -webkit-transition:all 0.4s ease-in-out; transition:all 0.4s ease-in-out; }
.imgBanners .inner { position:relative; overflow:hidden; }
.imgBanners .inner img { display:block; width:100%; }
.imgBanners .imgBanner-grid-item { position:relative; overflow:hidden; }
.imgBanners .imgBanner-grid-item img { display:block; width:100%; -ms-transition:all ease-out 0.5s; -webkit-transition:all ease-out 0.5s; transition:all ease-out 0.5s; }
.imgBanners .imgBanner-grid-item:hover img { transform:scale(1.1); -webkit-transform:scale(1.1); -ms-transform:scale(1.1); }
.imgBanners .inner .ttl { background-color: rgba(255,255,255,0.89); color:#000; line-height:25px; font-size:15px; text-transform:uppercase; line-height:normal; font-weight:700; display:inline-block; padding:10px 20px; max-width:80%; position:absolute; z-index:1; }
.imgBanners .inner.middle .ttl { left: 50%; top: 50%; transform: translate(-50%, -50%); -webkit-transform: translate(-50%, -50%); -ms-transform: translate(-50%, -50%); }
.imgBanners .inner.btmleft .ttl { left:20px; bottom:20px; text-align:left; }
.imgBanners .inner.btmright .ttl { right:20px; bottom:20px; text-align:left; }
.imgBanners .inner.topleft .ttl { left:20px; top:20px; text-align:left; }
.imgBanners .inner.topright .ttl { right:20px; top:20px; text-align:right; }
.imgBanners .inner.center .ttl { left:0; right:0; bottom:20px; text-align:center; }
.imgBanners .img-banner-item.last .imgBanner-grid-item { margin-bottom:22px; }
.imgBanners .img-banner-item.last .imgBanner-grid-item + .imgBanner-grid-item { margin-bottom:0; }
.imgBanners .details { display:inline-block; position:absolute; z-index:1; padding:15px; width:auto; background-color:#fff; text-align:center; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.imgBanners .details .title { color:#000; margin-bottom:5px; }
.imgBanners .details p { margin-bottom:10px; }
.imgBanners .details .ttl { display:inline-block; }
.imgBanners .details.center { left:0; right:0; bottom:20px; text-align:center; margin:0 auto; }
.imgBanners .details.left { left:20px; right:auto; bottom:20px; text-align:left; margin:0 auto; }
.imgBanners .details.right { left:auto; right:20px; bottom:20px; text-align:right; margin:0 auto; }
.imgBanners .details.left-top { left:20px; right:auto; bottom:auto; top:30px; text-align:left; margin:0 auto; }

.index-demo3 .imgBanners.style2 .row { margin-left:-5px; margin-right:-5px; }
.index-demo3 .imgBanners.style2 .row .img-banner-item { padding-left:5px; padding-right:5px; }
.index-demo3 .imgBanners.style2 .img-banner-item { margin-bottom:10px; }
.index-demo3 .imgBanners.style2 .img-banner-item .title { color:#000; font-size:18px; margin-bottom:5px; text-transform:uppercase; }
.index-demo3 .imgBanners.style2 .details p { margin-bottom:10px; }
.index-demo3 .imgBanners.style2 .btn { font-size:12px; }

.imgBanners.style2 .row { margin-left:-10px; margin-right:-10px; }
.imgBanners.style2 .banner-item { padding-right:5px; }
.imgBanners.style2 .banner-item:nth-of-type(1) { padding-left:0; margin-bottom:0; }
.imgBanners.style2 .banner-item:nth-of-type(2) { padding-right:0; padding-left:5px; margin-bottom:0; }
.imgBanners.style2 .details .title { font-size:20px; margin:0 0 5px; }

.imgBanners.style3 .details { background-color:transparent; }
.imgBanners.style3 .details .title { color:#000; font-size:24px; margin:0; text-transform:uppercase; }
.imgBanners.style3 .details .tt-small { font-size:14px; margin-bottom:5px; }

.imgBanners.style4 .inner .img { position:relative; overflow:hidden; }
.imgBanners.style4 .details { position:static; text-align:center; width:100%; }
.imgBanners.style4 .details .title { font-size:26px; font-weight:normal; }
.imgBanners.style4 .imgBanner-grid-item:hover img { transform:none; -webkit-transform:none; -ms-transform:none; opacity:0.8; }
.imgBanners.style4 .details p { margin-bottom:15px; }

.imgBanners.style5 .details { padding:0; background-color:transparent; transform:none; -webkit-transform:none; -ms-transform:none; }
.imgBanners.style5 .details .ttl { margin:0; background:#fff; font-size:15px; display:inline-block; padding:10px 20px; }
.imgBanners.style5 .row + .row { margin-top:20px; }
.imgBanners.style5 .row + .row .col-12 { margin-bottom:20px; }

.imgBanners.style6 { margin-top:20px; }
.imgBanners.style6 .row .img-banner-item.last { margin-top:20px; }
.imgBanners.style6 .inner .ttl { font-size:15px; font-weight:bold; background-color:transparent; padding:5px 10px; }

.imgBanners.style7 .ttl { background-color:transparent; line-height:normal; }
.imgBanners.style7 .ttl .tt-small { font-size:14px; display:block; }
.imgBanners.style7 .ttl .tt-big { font-size:23px; line-height:35px; }

.lookbook { margin:0 -5px; }
.lookbook .collection-grid-item { position:relative; overflow:visible; }
.lookbook .grid-lookbook { float:left; padding:5px; }
.lookbook .collection-grid-item.gallery { overflow:hidden; }
.lookbook a { opacity:1; }
.lookbook .zoom-img { font-size:16px; text-align:center; border-radius:2px; display:block; width:31px; height:28px; line-height:29px; opacity:0; visibility:hidden; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; cursor:pointer; position:absolute; left:10px; top:10px; z-index:9; background-color:#fff; }
.lookbook .grid-lookbook:hover .zoom-img { opacity:1; visibility:visible; }
.lookbook .zoom-img .anm { color:#111111; font-size:19px; line-height:28px; }
.lookbook .grid-lookbook img { width:100%; display:block; margin:0 auto; }
.lookbook-caption { background:#000000; }
.lookbook .lookbook-caption { font-size:20px; text-align:center; bottom:-100px; position:absolute; left:0; right:0; margin:0; padding:12px 18px; transition-duration:0.5s; -webkit-transition-duration:0.5s; }
.lookbook .lookbook-caption a { color:#fff; }
.lookbook .lookbook-caption .text-1 { font-size:15px; }
.lookbook .lookbook-caption .text-2 { font-size:13px; display:block; }
.lookbook .grid-lookbook:hover .lookbook-caption { bottom:0; }
.lookbook-shop-page .collection-grid-item.gallery { overflow:visible; }
.lookbook-shop-page .lookbook .grid-lookbook { z-index:inherit; }
.lookbook .collection-grid-item:hover img { transform:none; -webkit-transform:none; -ms-transform:none; }
.lookbook .products { position:absolute; }
.lookbook .products .btn-shop { z-index:1; margin:5px; cursor:pointer; background:#2e6ed5; color:#fff; border-radius:50%; display: flex; align-items: center; justify-content: center; height:24px; width:24px; text-align:center; line-height:23px; }
.lookbook .products .btn-shop::before { content: ""; position:absolute; left:5px; top:5px; -webkit-animation: box-shadow 1.5s linear infinite; animation: box-shadow 1.5s linear infinite; border-radius:50%; height:24px; width:24px; color:rgba(255,255,255,0.4); }
.lookbook .products .btn-shop .anm { font-size:10px; }
.lookbook .grid-lb { visibility:hidden; z-index:2; box-shadow: 0 0 2px #bbb; -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; opacity:0; display:table; position:absolute; table-layout:fixed; width:260px; background:#fff; border-radius:0; padding:10px; }
.lookbook .grid-lb.active { opacity:1; visibility:visible; }
.lookbook .grid-lb.left { right:0; }
.lookbook .grid-lb .btn-shop-close { background:#131313; color:#ffffff; position:absolute; right:-8px; top:-8px; padding:1px 6px; cursor:pointer; z-index:3; }
.lookbook .grid-lb .pro-img { display:table-cell; margin:0; width:80px; }
.lookbook .grid-lb .detail { width:160px; display:table-cell; vertical-align:top; text-align:left; margin:0; padding:0 0 0 15px; }
.lookbook .grid-lb .detail .title { color:#000; }
.lookbook .grid-lb .detail .price { margin:5px 0; }
.lookbook .grid-lb .detail .btn { margin-top:5px; font-size:12px; padding:5px 10px; }
@keyframes box-shadow {
    0%, 100% { -webkit-box-shadow:0 0 0 0; -moz-box-shadow:0 0 0 0; box-shadow:0 0 0 0; }
    50% { -webkit-box-shadow:0 0 0 4px; -moz-box-shadow:0 0 0 4px; box-shadow:0 0 0 4px; }
}

.custom-text-masonry-item { float:left; margin-bottom:10px; }
.custom-text-masonry-item .btn { text-decoration:none !important; padding:10px 20px; }

/*======================================================================
  11. Breadcrumbs
========================================================================*/
.breadcrumbs-wrapper .container { padding-top:15px; padding-bottom:15px; }
.breadcrumbs a, .breadcrumbs span { color:#111111; display:inline-block; padding:0 3px 0 0; margin-right:3px; font-size:12px; }
.breadcrumbs .icon-home { font-size:15px; }

.page-header { text-align:center; background-color:#f7f7f7; padding:40px 0; margin-bottom:40px; background-position:center center; background-size:cover; background-repeat:no-repeat; }
.page-title h1 { letter-spacing:normal; font-weight:700; font-size:24px; text-transform:uppercase; position:relative; margin:0; text-align:center; }
.page-header .breadcrumbs-wrapper .container { padding-top:10px; padding-bottom:0px; }

/*======================================================================
  12. Section
========================================================================*/
.section { padding-top:70px; padding-bottom:70px; }
.section-header { margin-bottom:30px; text-align:center; }
.section-header h2 { font-size:24px; font-weight:700; line-height:1.3; margin:0 auto; }
.section-header p { color:#9a9a9a; font-size:13px; margin:5px 0 0; display:block; }
.section-header.style2 h2 { margin-bottom:5px; }
.section-header.style2 p { max-width:550px; margin:0 auto; }
.pb-section { padding-bottom:50px; }
.pt-section { padding-top:50px; }
.no-pb-section { padding-bottom:0 !important; }
.no-pt-section { padding-top:0 !important; }

.section-header.style1 { margin-bottom:30px; }
.section-header-left { text-align:left; flex: 0 0 65%; -webkit-flex: 0 0 65%; -ms-flex: 0 0 65%; padding-right:20px; }
.section-header-right { text-align:right; }

@media only screen and (min-width: 992px) {
    #page-content { min-height:500px; }
}

/*======================================================================
  13. Product Grid
========================================================================*/
.grid-products a { text-decoration:none !important; }
@media only screen and (min-width: 992px) {
    .shop-grid-5 .grid-products .item.col-lg-2 { -ms-flex:0 0 20%; -webkit-flex:0 0 20%; flex:0 0 20%; max-width:20%; }
}
@media only screen and (min-width: 1540px) {
    .shop-grid-7 .grid-products .item.col-lg-2 { -ms-flex:0 0 14.2222%; -webkit-flex:0 0 14.2222%; flex:0 0 14.2222%; max-width:14.2222%; }
}

.grid-products .item .product-image .showVariantImg img { opacity:0; visibility:hidden; }
.grid-products .item .product-image .showVariantImg .variantImg { visibility:visible; opacity:1; }
.grid-products .item .product-image .showLoading { transition: .5s; animation: loader-rotate .8s infinite linear; background: none !important; border: 3px solid rgba(100,100,100,.5);     border-top-color: rgba(100, 100, 100, 0.5); border-radius: 100%; border-top-color: #fff; content: ""; height: 34px !important; left: 50%; line-height: 1; margin-left: -17px; margin-top: -17px; pointer-events: none; position: absolute; top: 50% !important; -webkit-animation: loader-rotate .8s infinite linear; width: 34px !important; z-index: 154 !important; }
/* 产品图片正方形容器样式 - t200主题 */
.grid-products .item .product-image {
    position: relative;
    width: 100%;
    aspect-ratio: 1 / 1; /* 设置为正方形 */
    overflow: hidden;
    margin: 0 auto 15px;
    border-radius: 8px; /* 可选：添加圆角 */
}

/* 兼容不支持 aspect-ratio 的浏览器 */
@supports not (aspect-ratio: 1 / 1) {
    .grid-products .item .product-image {
        padding-bottom: 100%; /* 创建正方形容器 */
        height: 0;
        margin: 0 auto 15px;
    }

    .grid-products .item .product-image > a {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: block;
        white-space: nowrap;
        opacity: 1;
    }
}

.grid-products .item .product-image > a { display:block; white-space:nowrap; opacity:1; }

    .grid-products .item .product-image > a .product-labels {
        background-size: 100% 100%;
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 6;
    }
    /* 产品图片样式 - 居中显示 */
    .grid-products .item .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover; /* 保持比例并填充容器 */
        object-position: center; /* 图片居中 */
        margin: 0 auto;
        vertical-align: middle;
        -ms-transition: all ease-out 0.4s;
        -webkit-transition: all ease-out 0.4s;
        transition: all ease-out 0.4s;
    }

/* 悬停效果图片定位 */
.grid-products .item .product-image .hover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    -ms-transition: all ease-out 0.4s;
    -webkit-transition: all ease-out 0.4s;
    transition: all ease-out 0.4s;
}

.grid-products .item .product-image:hover .primary { opacity:0; }
.grid-products .item .product-image:hover .hover:not(.variantImg) { opacity:1; visibility:visible; }

/* 列表视图产品图片正方形容器样式 - t200主题 */
.list-view-item__image-wrapper {
    position: relative;
    width: 100%;
    max-width: 200px; /* 限制最大宽度 */
    aspect-ratio: 1 / 1; /* 设置为正方形 */
    overflow: hidden;
    border-radius: 8px; /* 可选：添加圆角 */
}

/* 兼容不支持 aspect-ratio 的浏览器 */
@supports not (aspect-ratio: 1 / 1) {
    .list-view-item__image-wrapper {
        padding-bottom: 100%; /* 创建正方形容器 */
        height: 0;
    }

    .list-view-item__image-wrapper > a {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
}

/* 列表视图产品图片样式 */
.list-view-item__image {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 保持比例并填充容器 */
    object-position: center; /* 图片居中 */
    transition: all 0.3s ease;
}

/* 确保所有产品显示 */
.product-grid-view .item,
.product-load-more .list-product {
    display: block !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .list-view-item__image-wrapper {
        max-width: 150px;
    }
}

@media (max-width: 480px) {
    .list-view-item__image-wrapper {
        max-width: 120px;
    }
}
.grid-view_image:hover .primary { opacity:0; visibility:hidden; }
.grid-view_image:hover .hover:not(.variantImg) { opacity:1; visibility:visible; }
.grid-products .item .product-details.text-left .product-name, 
.grid-products .item .product-details.text-left .product-price .price { font-size:14px; }

.add-to-cart-btn { margin:10px 0; }
.add-to-cart-btn i { vertical-align:middle; padding-right:2px; }
.add-to-cart-btn span { vertical-align:middle; }
.tab_container .grid-products.grid-products-style1 .item { padding:0; }
.grid-products-style1 .slick-slide { margin-right:15px; margin-left:15px; }
.grid-products-style1 .item { border:1px solid #eeeeee; padding:0; }
.grid-products-style1 .item:hover { border:1px solid #ddd; }
.grid-products-style1 .item .product-details { padding:10px; }
.grid-products-style1 .item .saleTime span { background-color:#efefef; }
.medical-demo .btn.soldOutBtn, .soldOutBtn { background-color:#f00; }

.grid-products.style2 { position:relative; text-align:center; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.grid-products.style2 .item { position:relative; margin-bottom:25px; }
.grid-products.style2 .item .overlay { display:block; width:100%; height:100%; opacity:0; visibility:hidden; position:absolute; top:0; left:0; z-index:4; background-color:rgba(255,255,255,0.85); -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.grid-products.style2 .item:hover .overlay { visibility:visible; opacity:1; }
.grid-products.style2 .item.product-image { width:100%; overflow:hidden; position:relative; z-index:1; }
.grid-products.style2 .item .product-details { width:100%; height:auto; padding-bottom:30px; opacity:0; visibility:hidden; position:absolute; top:50%; left:0; z-index:5; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.grid-products.style2 .item:hover .product-details { opacity:1; visibility:visible; }
.grid-products.style2 .item .button-set { width:100%; display:block; font-size:0px; position:absolute; bottom:-20px; top:auto; left:0; z-index:444; -ms-transition:all 0.3s ease-out; -webkit-transition:all 0.3s ease-out; transition:all 0.3s ease-out; }
.grid-products.style2 .item:hover .button-set { bottom:-10px; top:auto; }
.grid-products.style2 .item .product-review { margin-bottom:10px; }
.grid-products.style2 .row { margin-left:-10px; margin-right:-10px; }
.grid-products.style2 .row .col-12 { padding-left:10px; padding-right:10px; }

.grid-view_image .product-image > a:after { content: ""; display:inline-block;width:0px; height:100%; vertical-align:middle; }

.slick-prev, .slick-next { width:30px; height:30px; text-align:center; position:absolute; z-index:9; display:block; line-height:normal; font-size:0px; padding:6px 10px; cursor:pointer; background:rgba(255,255,255,0.75); color:transparent; top:50%; -webkit-transform:translate(0, -50%); -ms-transform:translate(0, -50%); transform:translate(0, -50%); padding:0; border:none; opacity:1; }
.slick-prev { left:10px; }
.slick-next { right:10px; }
.slick-prev:before, .slick-next:before { font-family:"annimex-bold"; font-size:20px; line-height:30px; color:#000; -webkit-font-smoothing:antialiased; -moz-osx-font-smoothing:grayscale; vertical-align:middle; display:block; }
.slick-prev:before { content:"\f104"; }
.slick-next:before { content:"\f105"; }

.productSlider:hover .slick-arrow, .productPageSlider:hover .slick-arrow, .productSlider-style1:hover .slick-arrow, .productSlider-style2:hover .slick-arrow,
.productSlider-style2:hover .slick-arrow, .productSlider-fullwidth:hover .slick-arrow { opacity:1; }
.grid-products .slick-arrow { margin-top:-10px; width:30px; }
.productPageSlider .slick-arrow { margin-top:-20px; }
.productSlider-style1 .slick-arrow, .productSlider-style2 .slick-prev, .productSlider-fullwidth .slick-prev { margin-top:-20px; }
.productSlider .slick-arrow, .productPageSlider .slick-arrow, .productSlider-style1 .slick-arrow,
.productSlider-style2 .slick-arrow, .productSlider-fullwidth .slick-arrow { padding:6px 10px; border-radius:0; opacity:0; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
.productSlider .slick-next, .productPageSlider .slick-next, .productSlider-style1 .slick-next,
.productSlider-style2 .slick-next, .productSlider-fullwidth .slick-next { right:-35px; }
.productSlider .slick-prev, .productPageSlider .slick-prev, .productSlider-style1 .slick-prev,
.productSlider-style2 .slick-prev, .productSlider-fullwidth .slick-prev { left:-35px; }
.grid-products .slick-slider .item, .grid-products.slick-slider .item { margin-bottom:0; }

.productSlider.slick-slider .slick-list,
.productPageSlider.slick-slider .slick-list,
.productSlider-style2.slick-slider .slick-list { margin:0 -10px; }
.productSlider.slick-slider .slick-slide,
.productPageSlider.slick-slider .slick-slide,
.productSlider-style2.slick-slider .slick-slide { margin:0 10px; }

.product-labels { position:absolute; left:5px; top:5px; }
.product-labels + .product-labels { top:30px; }
.product-labels.rectangular .lbl { border-radius:0; }
.product-labels.radius .lbl { border-radius:3px; }
.product-labels.round .lbl { border-radius:100px; width:45px; height:43px; line-height:44px; padding:0 10px; }
.product-labels .lbl { display: block; white-space:nowrap; color:#fff; font-size:11px; font-weight:400; text-transform:uppercase; text-align:center; padding:0 5px; height:20px; line-height:20px; margin-bottom:5px; }
.product-labels .on-sale { right:5px; background:#e60f00; }
.product-labels .pr-label1, .product-labels .new { left:5px; background:#01bad4; }
.product-labels .pr-label2, .product-labels .hot { left:5px; background:#e9a400; }
.product-labels .pr-label3 { left:5px; background:#81d53d; }
.product-labels.rounded .lbl { 
    border-radius:50%; -moz-border-radius:50%; display:-webkit-box; display:-webkit-flex; display:-moz-flex; display:-ms-flexbox; display:flex; -webkit-box-align:center; -ms-flex-align:center; -webkit-align-items:center; -moz-align-items:center; 
    align-items:center; white-space:nowrap; word-break:break-all;-webkit-box-pack:center; -ms-flex-pack:center; -webkit-justify-content:center; -moz-justify-content:center; justify-content:center; text-align:center; height:50px; width:50px;
}
.grid-view-item--sold-out .grid-view-item__image { opacity:0.5; }
.sold-out { position:absolute; top:0; width:100%; left:0; height:100%; }
.sold-out span { color:#fff; position:absolute; top:50%; left:0; right:0; text-transform:uppercase; letter-spacing:0.08em; text-align:center; background-color:#f00; width:80%; margin:-20px auto; padding:10px }
.product-image:hover .variants.add { bottom:5px; }
.button-set { position:absolute; right:5px; top:30px; opacity:0; visibility:hidden; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
.button-set.style1 { right:0; left:0; width:100%; text-align:center; bottom:-10px; top:auto; }
    .button-set.style3 {
        right: 0;
        left: 0;
        width: 100%;
        text-align: center;
        bottom: -10px;
        top: auto;
        z-index: 11;
    }
.button-set.style4 { right:0; left:0; width:100%; text-align:center; bottom:-10px; top:auto; }
.button-set.style2  { right:auto; left:10px; width:35px; text-align:center; bottom:10px; top:auto; }
.button-set.style2 ul li { display:block; }
.button-set ul { list-style:none; padding:0; margin:0; }
.button-set ul li { display:inline-block; vertical-align:middle; }
.button-set li .btn-icon { color:#ffffff; display:inline-block; border:0; background-color:#333; position:relative; font-size:19px; padding:0; margin:2px; width:100%; height:35px; width:35px; line-height:32px; text-align:center; border:2px solid transparent; }
.button-set li .btn-icon.btn-square { line-height:30px; }
.grid-products .item:hover .button-set.style1 { bottom:10px; top:auto; -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; }
.grid-products .item:hover .button-set.style2 { bottom:10px; top:auto; -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; }
.grid-products .item:hover .button-set.style3 { bottom:10px; top:auto; -ms-transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; transition:all 0.3s ease-in-out; }
.grid-products .item .button-set.style3 .btn-icon { -webkit-transform: none; -moz-transform: none; transform: none; }
.grid-products .item .button-set.style3 .btn-addto-cart i { vertical-align: initial; }
.grid-products .item:hover .button-set { top:5px; opacity:1; visibility:visible; }
a.quick-view, a.wishlist, .cartIcon, .add-to-compare { color: #000000; background-color:#ffffff; border:0; width:35px; height:35px; line-height:34px; display:block; text-transform:uppercase; text-align:center; padding:0; margin-bottom:5px; }
a.quick-view:hover, a.wishlist:hover, .cartIcon:hover, .variants.add button:hover, .add-to-compare:hover { color:#ffffff; background-color:#000000; opacity:0.8; }
.button-set .tooltip-label { 
    height:23px; position:absolute; top:-20px; left:50%; font-size:10px; text-transform:uppercase; line-height:21px; -ms-transition:all 0.2s ease-in-out; -webkit-transition:all 0.2s ease-in-out; transition:all 0.2s ease-in-out; visibility:hidden; opacity:0; background:#000000; color:#ffffff; border-radius:0; padding:1px 7px; 
    white-space:nowrap; -ms-transform:translateX(-50%); -webkit-transform:translateX(-50%); transform:translateX(-50%); border-radius:0; letter-spacing:0;
}
.button-set .tooltip-label:before { content: ""; border:5px solid transparent; border-top:6px solid #000000; bottom:-10px; margin-left:-3px; left:50%; position:absolute; }
.button-set .btn-icon:hover .tooltip-label { opacity:1; visibility:visible; top:-33px; }
.button-set.style2 .tooltip-label { left:30px; top:50% !important; height:24px; top:50%; -ms-transform:translateY(-50%); -webkit-transform:translateY(-50%); transform:translateY(-50%); }
.button-set.style2 .btn-icon:hover .tooltip-label { top:50% !important; left:40px; }
.button-set.style2 .tooltip-label:before { border:5px solid transparent; border-right:6px solid #000000; bottom:auto; left:-7px; top:7px; }
.button-set .btn-icon.btn-square .tooltip-label { line-height:18px; }

.button-style2, .button-style2 .variants.add { position:static; opacity:1; }
.button-style2 .btn-style2 { display:block; float:left; width:25%; }
.button-style2 .cartIcon, .button-style2 .quick-view-popup, .button-style2 .wishlist, .button-style2 .compare { color:#fff; background-color:#000; border-right:1px solid #fff; }
.button-style2 .compare { border-right: 0; }
.button-style2 .wishlist, .button-style2 .compare { width:100%; }
.button-style2 .variants.add button { color:#fff; background-color:#000; }

.button-set.style4 { position:absolute; bottom:30px; -webkit-transition:.3s; -moz-transition:.3s; transition:.3s; z-index:2; opacity:0; visibility:hidden; left:0; right:0; }
.button-set.style4 .btn-icon { font-size:20px; display:inline-block; padding:0; margin:0 2px; border:0; vertical-align:middle; height:40px; width:40px; line-height:40px; text-align:center; -webkit-transform:scaleX(0); -moz-transform:scaleX(0); transform:scaleX(0); border-radius:50px; }
.grid-products .item:hover .btn-icon { -webkit-transform:scaleX(1); -moz-transform:scaleX(1); transform:scaleX(1); }
.grid-products .item:hover .button-set.style4 { opacity:1; visibility:visible; top:auto; }

.button-set li .btn-icon.btn-square { border-radius:0; }
.button-set li .btn-icon.btn-square .tooltip-label { border-radius:0; }

#pro-addtocart-popup .modal-dialog { max-width: 430px; }
#pro-addtocart-popup .title { margin-bottom:15px; font-weight:bold; }
#pro-addtocart-popup .pro-img { max-width:400px; margin-bottom:15px !important; }
#pro-addtocart-popup .pro-name { font-weight:600; }
#pro-addtocart-popup .sku { color:#888; }

.grid-products .item { margin:0 0 30px; text-align:center; }
.grid-products .item .product-name a {
    color:#000;
    font-size:1em;
    line-height:1.2;
    margin-bottom:0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}
.grid-products .item .product-name a:hover { color: #333; }
.brand-name { color:#9d9d9d; font-size:12px; text-transform:uppercase; }
.grid-products .item .product-review { margin-bottom:10px; }
.grid-products .item .swatches { margin: 8px 0 7px; }
.grid-products .item .product-price { margin:5px 0 10px; color:#000000; font-weight:600; }
.product-price .old-price {
    color: #555;
    opacity: 0.8;
    text-decoration: line-through;
    padding-left: 5px;
}
    .product-price .old-price + .price {
        padding-left: 5px;
        padding-right: 5px;
        color: #e95144 !important;
    }
.product-price .price { color:#000000; }
.product-price__sale, .product__price--sale { color:#000 !important; }

.product-review .an { font-size:16px; opacity:1; color:#ffb503; margin:0; }
.product-review .an.gray-star { color:#ddd; }
.swatches { margin:8px 0 0; list-style:none; padding:0; }
.swatches li { position:relative; display:inline-block; height:17px; width:17px; margin:3px 1px; cursor:pointer; box-shadow:0 0 1px 1px #ddd; -webkit-box-shadow:0 0 1px 1px #ddd; border:1px solid #ffffff; }
.swatches li.large { width:25px; height:25px; }
.swatches li.large .swacth-btn { width:23px; height:23px; }
.tooltip-label { 
    z-index:2; -ms-transition:all 0.15s ease-in-out; -webkit-transition:all 0.15s ease-in-out; transition:all 0.15s ease-in-out; opacity:0; visibility:hidden; position:absolute; top:-38px; bottom:auto; left:50%; 
    background:#000000; color:#ffffff; border-radius:3px; padding:2px 6px; white-space:nowrap; font-size:10px; transform:translateX(-50%); text-transform:uppercase;
}
.tooltip-label:before { content: ""; border:5px solid transparent; border-top:5px solid #000000; position:absolute; bottom:-9px; left:50%; margin-left:-5px; }
.swatches li:hover .tooltip-label { opacity:1; top:-28px; visibility:visible; }
.swatches li img { display:block; border-radius:50%; max-height:30px; margin:0 auto; }
.swatches li.square img { border-radius:0; }
.swatches li.radius img { border-radius:5px; }
.swatches li:hover { box-shadow:0 0 1px 1px #000; -webkit-box-shadow:0 0 1px 1px #000; }
.swatches li.rounded { border-radius:50% !important; }
.swatches li.radius { border-radius:5px !important; }
.swatches li.medium { height:30px; width:30px; }
.swatches li.navy { background-color:navy; }
.swatches li.green { background-color:green; }
.swatches li.gray { background-color:gray; }
.swatches li.aqua { background-color:aqua; }
.swatches li.orange { background-color:orange; }
.swatches li.purple { background-color:purple; }
.swatches li.teal { background-color:teal; }
.swatches li.black { background-color:black; }
.swatches li.red { background-color:red; }
.swatches li.yellow { background-color:yellow; }
.swatches li.darkgreen { background-color:darkgreen; }
.swatches li.maroon { background-color:maroon; }
.image-swatches li { width:32px; height:32px; }
.image-swatches li.rounded, .image-swatches li .rounded { border-radius:30px !important; }
.image-swatches li.radius, .image-swatches li .radius { border-radius:5px !important; }
.image-swatches li img { max-height:26px; }
.image-swatches .swacth-btn { display:block; float:left; margin-bottom:10px; position:relative; height:30px; width:30px; border:1px solid transparent; background-color:#fff; text-align:center; cursor:pointer; }
.image-swatches li.small, .image-swatches li .small { width:17px; height:17px; }
.image-swatches li .blue-red { background: url(../images/swatches/blue-red.jpg) no-repeat center center; }
.image-swatches li .black-grey { background: url(../images/swatches/black-gray.jpg) no-repeat center center; }
.image-swatches li .grey-black { background: url(../images/swatches/grey-black.jpg) no-repeat center center; }
.image-swatches li .pink-black { background: url(../images/swatches/pink-black.jpg) no-repeat center center; }

.grid-view-item__title { color:#000; font-size:1em; line-height:1.2; margin-bottom:0; }
.grid-view-item__meta { margin:5px 0; }
.product-price__price { color:#000000; font-weight:600; display:inline-block; }

.grid-products-hover-btn a.quick-view, .grid-products-hover-btn a.wishlist, .grid-products-hover-btn .variants.add button,
.grid-products-hover-btn .cartIcon, .grid-products-hover-btn .add-to-compare { color:#ffffff; background-color:#000000; }

.grid-products-hover-gry a.quick-view, .grid-products-hover-gry a.wishlist, .grid-products-hover-gry .variants.add button,
.grid-products-hover-gry .cartIcon, .grid-products-hover-gry .add-to-compare { color:#ffffff; background-color:#555555; }

.grid-products .item .btn-addto-cart i { font-size:21px; }
.brand-name a { color:#555; font-size:12px; text-transform:uppercase; }

/* Countdown Timer On listing */
.grid-products .item:hover .saleTime { display:none }
.saleTime { position:absolute; bottom:5px; left:0; right:0; z-index:111 }
.saleTime .time-count { font-weight:700; font-size:14px; }
.saleTime span>span { color:#141414; font-weight:700; min-width:30px; padding:6px 4px 4px; line-height:12px; display:inline-block; margin:0 0 0 1px; text-align:center; background:rgba(255, 255, 255, 0.7); }
.saleTime span>span span { display:block; background:none; font-size:10px; font-weight:400; margin-top:-3px; text-transform:uppercase; line-height:8px }
.timermobile { margin:0 -10px; display:none }
.timermobile .saleTime { position:relative; margin-top:20px }

.countdown-deals { line-height:35px; text-align:center; width:100%; margin-bottom:10px; }
.countdown-deals .cdown { background:#efefef; display:inline-block; height:50px; width:44px; }
.countdown-deals .cdown span { font-size:14px; font-weight:500; }
.countdown-deals .cdown > p { font-size:12px; text-transform:uppercase; line-height:0; margin:0; }
.grid-products .countdown-deals { position:absolute; bottom:-10px; }
.grid-products .countdown-deals .cdown { color:#fff; background-color:#000; }
.product-list .countdown-deals { line-height:40px; text-align:left; }
.product-list .countdown-deals .cdown { font-size:14px; height:59px; width:65px; text-align:center; color:#fff; background-color:#000; }

.product-load-more .list-product, .product-load-more .item { display:none; }

.grid-products .item .brands { margin:10px 0; font-size:13px; }
.grid-products .item .brands p { margin:0; }
.grid-products .item .brands .label { font-weight:600; }

/* Quickview Popup */
#quickview_popup .modal-dialog { max-width:800px; }
#quickView .quickview-in { position: relative; }
#quickView .np-btns { position: absolute; width: 100%; transform: translate(-50%,-50%); -webkit-transform: translate(-50%,-50%); top: 50%; left: 50%; }
#quickView .np-btns .carousel-control { position: absolute; width: 25px; height: 25px; text-align: center; background-color: #ffffffd4; line-height: 25px; font-size: 16px; }
#quickView .np-btns .left { left: 0; }
#quickView .np-btns .right { right: 0; }
.model-thumbnail-img .carousel-indicators { position:static; left:initial; width:auto; margin:0; }
.model-thumbnail-img .carousel-indicators > li { width:100px; height:auto; text-indent:initial; margin-bottom: 5px; }
.model-thumbnail-img .carousel-indicators > li img { width:100px; }
.model-thumbnail-img .carousel-indicators > li.active img { opacity: 1; }
.model-thumbnail-img { position:relative; margin:10px auto 0 auto; }
.model-thumbnail-img .carousel-control { color:#000; position:absolute; top:45%; }
.model-thumbnail-img .carousel-control.left { left:10px; }
.model-thumbnail-img .carousel-control.right { right:10px; }
#quickview_popup .product-brand { text-transform:uppercase; }
#quickview_popup .product-title { font-size:20px; font-weight:700; letter-spacing:0; margin:0 0 10px; }
#quickview_popup .product-info .product-stock { color:#447900; font-weight:600; text-transform:uppercase; margin-bottom:4px; }
#quickview_popup .product-review { margin-bottom:15px; }
#quickview_popup .product-review .rating { display:inline-block; vertical-align:middle; padding-right:5px; }
#quickview_popup .product-review .reviews { display:inline-block; vertical-align:middle; }
#quickview_popup .pricebox { margin:10px 0; }
#quickview_popup .pricebox .price { font-size:18px; font-weight:600; display:inline-block; }
#quickview_popup .pricebox .old-price { color:#888; text-decoration:line-through; font-weight:400; padding-right:20px; }
#quickview_popup .sort-description { margin-bottom:20px; padding-bottom:20px; border-bottom:1px dotted #939393; }
#quickview_popup .wishlist-btn a.wishlist { width:auto; display:inline-flex; align-items:center; }
#quickview_popup .wishlist-btn a.wishlist:hover { color:#000; background-color:transparent; }
#quickview_popup .wishlist-btn a.wishlist .icon {font-size: 16px; vertical-align: middle; margin-right:5px; }
#quickview_popup .product-options { display:flex; display:-webkit-flex; display:-ms-flex; flex-wrap:wrap; -wekit-flex-wrap:wrap; }
#quickview_popup .product-options .swatch { width: 100%; }
/* End Quickview Popup */

.product-with-colletion-bnr .grid-products .item { margin-bottom:0; }
.product-with-colletion-bnr a { position:relative; display:block; }
.product-with-colletion-bnr .btn-inner { position:absolute; bottom:20px; left:0; right:0; }
.product-with-colletion-bnr .btn-inner span { font-weight:700; margin:0; background:#fff; font-size:14px; display:inline-block; padding:8px 25px; word-wrap:break-word; color:#111; letter-spacing:0.02em; line-height:normal; }
.product-with-colletion-bnr .item:hover .btn-inner span { background-color:rgba(255,255,255,0.8); }

.products-grid-section .grid-products .button-set.style3 { position:static; opacity:1; visibility:visible; margin-top:10px; }
.products-grid-section .grid-products .row .item { margin-bottom:40px; }
.products-grid-section .grid-products .row:last-of-type .item { margin-bottom:20px; }

.modal-content { border-radius: 0; -webkit-border-radius: 0; }
.modal-header { border: 0; padding: 0; }
.modal-header .btn-close { padding: 0; margin: 0; position: absolute; right: 10px; top: 10px; z-index: 100; }

/*======================================================================
  14. Product Listview
========================================================================*/
.list-view-item { display:table; table-layout:fixed; margin-bottom:15px; padding-bottom:15px; width:100%; border-bottom:1px solid #e8e9eb; text-decoration:none; }
.list-view-item:hover { text-decoration:none; }
.list-view-item p { color:#555; margin-bottom: 10px; }
.list-view-item .brand-name { margin-bottom:10px; }
.list-view-item__image-column { display:table-cell; vertical-align:middle; width:280px; }
.list-view-item__image-wrapper { position:relative; margin-right:20px; }
.list-view-item__title-column { display:table-cell; vertical-align:middle; }
.list-view-items .grid-view-item__title { font-size:15px; font-weight:700; margin-bottom:10px; text-align:left; }
.list-view-items .product-price { font-size:16px; margin-bottom:10px; }
.list-view-items .product-price__sale { padding-left:5px; }
.list-view-items .variants { margin-top:10px; display:inline-block; vertical-align:middle; }
.list-view-items .button-box { display:inline-block; vertical-align:middle; margin-top:0; }
.list-view-items .button-box > div { display:inline-block; }
.list-view-items .button-box > div .btn-icon { color:#fff; font-size:18px; background-color:#2e6ed5; width:40px; height:39px; line-height:39px; padding:0 10px; display:inline-block; vertical-align:middle; margin:0; }
.list-view-items .button-box > div .btn-icon.wishlist { margin:0 7px; }

/*======================================================================
  15. Products Detail Page
========================================================================*/
.product-form .swatch { margin-bottom:10px; }
.product-form .swatch .product-form__item { margin-bottom:0; padding-bottom:0; padding-top:0; }
.product-form__item { -webkit-flex:1 1 200px; -moz-flex:1 1 200px; -ms-flex:1 1 200px; flex:1 1 200px; margin-bottom:10px; padding:5px 5px 0; }
.product-form .swatch label { display:block; text-transform:uppercase; font-weight:600; margin-bottom:8px; }
.product-form .swatch label .required { color:#F00; }
.product-form .swatch label .slVariant { font-weight:normal; padding-right: 20px; }
.product-template__container label .slVariant { font-weight:700; }
.product-form .swatch .swatch-element { display:inline-block; margin-right:8px; cursor:pointer; }
.product-form .swatch .swatchInput + .swatchLbl.color.medium { width:50px; height:50px; }
.product-form .swatch .swatchInput:checked + .swatchLbl { border:2px solid #111111; box-shadow:none; }
.product-form .swatch .swatchInput + .swatchLbl.color { width:30px; padding:0; height:30px; background-repeat:no-repeat; background-position:50% 50%; background-size:100% auto; cursor:pointer; }
.product-form .swatch .swatchInput + .swatchLbl.large { width:40px; height:40px; }
.product-form .swatch .swatchInput + .swatchLbl.large:not(.color) { line-height:36px; }
.product-form .swatch .swatchInput + .swatchLbl {
    color:#333; font-size:12px; font-weight:400; line-height:25px; text-transform:capitalize; display:inline-block; margin:0; min-width:30px; height:30px; overflow:hidden; text-align:center; background-color:#f9f9f9; padding:0 10px; border:2px solid #fff; box-shadow:0 0 0 1px #ddd; 
    border-radius:0; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; cursor:pointer;
}
.product-form .swatch .swatchInput { display:none; }

.product-action { width:100%; display:block; margin:5px 0 10px; padding:0 5px; }
.product-action .quantity { float:left; margin:0 10px 10px 0; }
.product-action .add-to-cart { width:auto; overflow:hidden; }
.product-action .add-to-cart .button-cart { width:100%; padding:6px 15px; min-height:42px; }
.product-action .wishlist-btn .wishlist { width:auto; float:left; padding:0; line-height:normal;  }
.product-action .wishlist-btn .wishlist i { vertical-align:middle; }
.product-action .wishlist-btn .wishlist:hover { color:#555; background-color:transparent; }

.share-icon { clear:both; }
.share-icon span { display:inline-block; font-weight:600; text-transform:uppercase; margin-right:5px; }
.share-icon .social-icons {  display:inline-block; }
.share-icon .social-icons li { margin-right:6px; }

.product-single-1 .product-details-img .product-zoom-right { width:100%; }
.product-details-img:before, .product-details-img:after { content:''; clear:both; display:block; }
.product-details-img .product-thumb {
    display: inline-block;
    padding: 0 10px 0 0;
    width: 15%;
    margin: 0;
    float: left;
    position: relative;
    z-index: 10;
}
.product-details-img .product-thumb-1 { display:block; padding:0; width:100%; margin:0; float:left; padding-right:0; }
.product-details-img .product-zoom-right { display:inline-block; position:relative; width:85%; float:left; }
.product-thumb .product-dec-slider-2 a { cursor:pointer;}
.product-thumb .slick-slide { margin-bottom:3px; }
.product-details-img .product-thumb .slick-arrow { top:auto; background-color:rgba(255,255,255,0.8); padding:5px 0; opacity:0; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
.product-details-img:hover .product-thumb .slick-arrow { opacity:1; }
.product-details-img .product-thumb .slick-prev { top:16px; left:50%; margin-left:-15px; }
.product-details-img .product-thumb .slick-next { bottom:-10px; left:50%; right:auto; margin-left:-15px; }
.product-details-img .product-thumb .slick-prev:before { content:"\f106"; font-family:"annimex-bold"; font-size:15px; line-height:20px; }
.product-details-img .product-thumb .slick-next:before { content:"\f107"; font-family:"annimex-bold"; font-size:15px; line-height:20px; }
.product-details-img .product-labels { z-index:99; }
.product-details-img .product-labels.product-labels-img {
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    top: 0;
    left: 0;
}
.product-details-img.product-single__photos.bottom .product-thumb .slick-arrow { background:transparent; }
.product-details-img.product-single__photos.bottom .product-thumb .slick-next { right:0; top:50%; left:inherit; }
.product-details-img.product-single__photos.bottom .product-thumb .slick-prev { left:15px; top:50%; bottom:0; }
.product-details-img.product-single__photos.bottom .product-thumb .slick-prev:before { content:"\ea8b"; font-family:"annimex-bold"; font-size:16px; }
.product-details-img.product-single__photos.bottom .product-thumb .slick-next:before { content:"\ea8c"; font-family:"annimex-bold"; font-size:16px; }

.product-details-img .social-sharing { text-align:center; clear:both; padding-top:20px; }
.product-details-img .social-sharing .share-title { display:none; }

.trustseal-img { margin:20px 0; }
.zoomContainer { z-index:0; }
.product-buttons > a { position:relative; }
.product-buttons .tooltip-label { padding:4px 6px; line-height:normal; top:-42px; left:50%; -ms-transform:translate(-50%); -webkit-transform:translate(-50%); transform:translate(-50%); border-radius:0; }
.product-buttons .btn:hover .tooltip-label { opacity:1; visibility:visible; top:-30px; z-index:1000; }

.product-form .swatch .swatchInput + .swatchLbl.rounded { border-radius:50% !important; }
.product-form .swatch .swatchInput + .swatchLbl.rectangle { border-radius:7px !important; }

.product-buttons { position:absolute; right:10px; bottom:10px; z-index:10; }
.product-buttons .btn.popup-video i, .product-buttons .btn i { line-height:33px; }
.product-buttons .btn { font-size:19px; height:36px; width:36px; text-align:center; margin-top:5px; clear:both; padding:0; line-height:33px; color:#ffffff; opacity:0.9; }
#videoPopup .modal-dialog { padding: 0; max-width: 700px; }
#videoPopup .modal-header .btn-close { right: 5px; top: 5px; font-size: 11px; }

.product-template__container .product-single { margin-bottom:20px; }
.product-template__container .product-single__meta { position:relative; margin-bottom:20px; }
h1.product-single__title, .product-single__title.h1 { color:#000; font-size:26px; margin-bottom:10px; font-weight:bold; }
.product-template__container .product-nav { position:absolute; right:0; top:10px; }
.product-template__container .product-nav .next { float:right; }
.product-template__container .product-nav .prev, .product-template__container .product-nav .next { font-size:20px; display:block; line-height:22px; text-align:center; height:20px; width:20px; padding:0; color:#000; }

.product-detail-container .prInfoRow { margin-bottom:10px; }
.product-detail-container .prInfoRow > div { display:inline-block; margin-right:5%; }
.product-detail-container .prInfoRow .instock { color:#447900; }
.product-detail-container .prInfoRow .outstock { color:#F00; }
.product-detail-container .prInfoRow .spr-badge-caption { color:#424242; padding-left:5px; }
.product-detail-container .prInfoRow a:hover { text-decoration:none; }
.product-single__price .product-price__price { font-size:22px; padding-left:3px; }
.product-single__price .product-price__old { font-size:18px; color:#888; text-decoration:line-through; padding-right:10px; } 
.discount-badge { display:inline-block; vertical-align:middle; margin:-2px 0 0 5px; font-size:13px; }
.discount-badge .product-single__save-amount { font-weight:700; }
.discount-badge .off, .discount-badge .product-single__save-amount { color:#e95144; }
.product-single__price { display:inline-block; margin-right:10px; font-weight:600; margin-bottom:15px; margin-top:10px; }
.orderMsg { color:#fff; margin-bottom:20px; display:block; }
.orderMsg p { color:#f00; line-height:normal; background-color:#fff1f1; padding:8px 20px; display:inline-block; text-align:center; }
@-webkit-keyframes blinker {
    from { opacity: 1.0; }
    to { opacity:0.0; } 
}
.product-description ul, .product-single__description ul { margin-left:0; }
.product-single__description ul { text-align:left; }
.product-description ul li, .product-single__description ul li { position:relative; margin-left:15px; list-style:disc; }

.rte { margin-bottom:20px; }
.rte li { margin-bottom:4px; list-style:inherit; }
.rte h1, .rte .h1, .rte h2, .rte .h2, .rte h3, .rte .h3, .rte h4, .rte .h4, .rte h5, .rte .h5, .rte h6, .rte .h6 { margin-top:30px; margin-bottom:15px; }
.rte h1:first-child, .rte .h1:first-child, .rte h2:first-child, .rte .h2:first-child, .rte h3:first-child, .rte .h3:first-child, .rte h4:first-child, .rte .h4:first-child, .rte h5:first-child, .rte .h5:first-child, .rte h6:first-child, .rte .h6:first-child { margin-top:0 }
.rte:last-child { margin-bottom:0; }

.product-template__container #quantity_message { color:#31a3a3; font-size:16px; text-align:center; padding:5px 9px; margin-bottom:15px; border:1px dashed #31a3a3; }
.product-template__container #quantity_message .items { font-weight:700; }

.product-form { display:-webkit-flex; display:-ms-flexbox; display:flex; width:100%; -webkit-flex-wrap:wrap; -moz-flex-wrap:wrap; -ms-flex-wrap:wrap; flex-wrap:wrap; -ms-flex-align:end; -webkit-align-items:flex-end; -moz-align-items:flex-end; -ms-align-items:flex-end; -o-align-items:flex-end; align-items:flex-end; width:auto; margin:0 -5px -10px; }
.product-detail-container .product-form .swatch { width:100%; }
.product-form .swatch { margin-bottom:10px; }
.product-form .swatch .product-form__item { margin-bottom:0; padding-bottom:0; padding-top:0; }
.product-form .swatch .sizelink { float:right; }
.product-form__item { -webkit-flex:1 1 200px; -moz-flex:1 1 200px; -ms-flex:1 1 200px; flex:1 1 200px; margin-bottom:10px; padding:5px; }
.product-form .swatch label { display:block; text-transform:uppercase; font-weight:600; }
.product-template__container label .slVariant { font-weight:700; }
.product-form .swatch .swatch-element { display:inline-block; margin-right:5px; cursor:pointer; }
.product-form .swatch .swatchInput + .swatchLbl.color.medium { width:40px; height:40px; }
.product-form .swatch .swatchInput:checked + .swatchLbl, .product-form .swatch .swatchInput:hover + .swatchLbl {
    border: 2px solid var( --theme-color);
    box-shadow: none;
}
.product-form .swatch .swatchInput + .swatchLbl.color { width:30px; padding:0; height:30px; background-repeat:no-repeat; background-position:0 0; background-size:100% auto; cursor:pointer; }
.product-form .swatch .swatchInput + .swatchLbl.large { width:40px; height:40px; }
.product-form .swatch .swatchInput + .swatchLbl.large:not(.color) { line-height:36px; }
.product-form .swatch .swatchInput + .swatchLbl { 
    color:#000; font-size:12px; font-weight:400; line-height:26px; text-transform:capitalize; display:inline-block; margin:0; min-width:30px; height:30px; overflow:hidden; text-align:center; background-color:#f9f9f9; padding:0 10px; border:2px solid #fff; box-shadow:0 0 0 1px #ddd; 
    border-radius:0; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; cursor:pointer; 
}
.product-form .swatch .swatchInput { display:none; }
.product-form .swatch .swatch-element { position:relative; }
.product-form .swatch .swatch-element:hover .tooltip-label { opacity:1; top:-28px; visibility:visible; }
.product-form .swatch .swatch-element.unavailable { opacity:0.6; cursor:default; }
.product-form .swatch .swatch-element.unavailable:before { display:block; position:absolute; content:""; border-radius:1000px; transform:scale(1.33) rotate(45deg); -webkit-transform:scale(1.33) rotate(45deg); left:18px; top:-1px; width:2px; height:calc(90% + 2px); background:#F00; }

.product-form .swatch .swatch-element .black { background-color:#000; }
.product-form .swatch .swatch-element .white { background-color:#fff; border:1px solid #ddd; }
.product-form .swatch .swatch-element .red { background-color:#fe0000; }
.product-form .swatch .swatch-element .blue { background-color:#0000fe; }
.product-form .swatch .swatch-element .pink { background-color:#ffc1cc; }
.product-form .swatch .swatch-element .gray { background-color:#818181; }
.product-form .swatch .swatch-element .green { background-color:#027b02; }
.product-form .swatch .swatch-element .orange { background-color:#fca300; }
.product-form .swatch .swatch-element .yellow { background-color:#f9f900; }
.product-form .swatch .swatch-element .blueviolet { background-color:#8A2BE2; }
.product-form .swatch .swatch-element .brown { background-color:#A52A2A; }

.infolinks { margin:5px 0 20px; padding:0; width:100%; }
.infolinks .btn, .infolinks .wishlist { border:0; background-color:transparent; color:#000; padding:0; margin-right:15px; display:inline-block; vertical-align:top; }
.infolinks .btn:focus, .infolinks .wishlist:focus { outline:0; box-shadow:none; }
.infolinks .wishlist { background:none !important; color:#000 !important; width:auto; text-align:left; line-height:inherit; height:auto; text-decoration:none; margin-bottom:0; }
.infolinks a { flex:1; -ms-flex:1; -webkit-flex:1; margin-right:0 !important; padding-right:5px; }

#sizechart .modal-dialog { max-width: 650px; }
#sizechart table tr th { background:#000; color:#fff; border:0 !important; white-space: nowrap; }
#sizechart table tr th, #sizechart table tr td { padding:7px 12px; text-align:center; font-size:12px; border:1px solid #e8e9eb; }
table { margin-bottom:15px; width:100%; border-collapse:collapse; border-spacing:0; }
#sizechart ul, #ShippingInfo ul { margin:0 0 20px 15px; }

#productInquiry .modal-dialog, #ShippingInfo .modal-dialog { max-width: 700px; }
#productInquiry .page-title .an { font-size:26px; margin-bottom:10px; background-color:#f7f7f7; width:50px; height:50px; line-height:45px; border-radius:100px; }
#productInquiry h3 { font-size:20px; font-weight:bold; margin-bottom:20px; }
#productInquiry input[type="tel"], #productInquiry input[type="email"], #productInquiry input[type="text"], #productInquiry textarea { background-color:#f4f4f4; border-color:#f4f4f4; margin-bottom:20px; }
#productInquiry textarea { padding:10px; }

.product-template__container .product-action { width:100%; display:block; margin-bottom:15px; padding:0 5px; }
.product-template__container .product-form__item--quantity { float:left; margin:0 10px 10px 0; }
.wrapQtyBtn { float:left; }
.product-action .qtyField { position: relative; display:table; margin:0 auto; border:1px solid #ddd; }
.product-action .qtyField .qtyBtn, 
.product-action .qtyField .qty { padding:10px 6px; width:45px; height:40px; border-radius:0; float:left; border:0; }
.product-action .qtyField .qty { border-left: 1px solid #ddd; border-right: 1px solid #ddd; }
.product-action .qtyField .qtyBtn { background-color:#f7f7f7; width: 30px; }
.product-action .qtyField a { background-color:#fff; color:#000; border:0; }
.product-action .qtyField a .fa { font-size:12px; line-height:21px; }
.product-action .qtyField > a, .product-action .qtyField > span, .qtyField input { display:table-cell; line-height:normal; text-align:center; padding:3px 6px; border:1px solid #f5f5f5; }
.product-form__item--submit { width:auto; overflow:hidden; padding-left:15px; }
.product-form__item--submit .btn { font-size:16px; width:100%; padding:8px 15px 9px 15px; min-height:42px; }
.agree-check { margin:15px 0 10px; }
.agree-check .checkbox { margin-right:3px; }
.buy-it-btn .btn:hover {
    background: #fff;
    color: var( --theme-color);
}
.buy-it-btn .btn {
    border-radius: 0;
    -ms-transition: all 0.3s ease-in-out;
    -webkit-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
    color: #fff;
    background-color: var( --theme-color);
    float: right;
    cursor: pointer;
    display: block;
    font-size: 14px;
    font-weight: 400;
    line-height: 1;
    text-align: center;
    width: 100%;
    padding: 1em 2em;
    margin: 0 0 15px;
    letter-spacing: 0.02em;
}
.buy-it-btn .btn[disabled] { opacity:0.6; cursor:default; }
.social-sharing { display: inline-flex; }
.social-sharing .btn { color:#000 !important; padding:0 5px; margin-bottom:0; background:none !important; border:0; letter-spacing:normal; text-transform:capitalize; }
.btn--share .fa { color:#222222; font-size:14px; }

.freeShipMsg { margin-bottom:12px; }
.freeShipMsg .fa { font-size:20px; width:25px; vertical-align:middle; }
.shippingMsg { margin-bottom:12px; }
.shippingMsg .fa { font-size:18px; width:25px; vertical-align:middle; }

#quantity_message { color:#e00; font-size:14px; font-weight:600; text-align:center; padding:5px 9px; margin-bottom:15px; border:1px dashed #e00; text-transform:uppercase; }
#quantity_message .items { font-weight:700; }

.userViewMsg { clear:both; margin-bottom:12px; }
.userViewMsg .fa, .userViewMsg .uersView { color:#e95144; }
.userViewMsg .fa { font-size:18px; width:25px; }

.prFeatures { padding:20px 0; }
.prFeatures .feature { margin-bottom:20px; }
.prFeatures img { float:left; }
.prFeatures .details { margin-left:65px; line-height:1.5; }
.prFeatures .details h3, .prFeatures .details .h3 { margin-bottom:5px; text-transform:uppercase; }

.template-product .tabs-listing { margin-top:40px; margin-bottom:20px; }
.template-product .tabs-listing .tabs-ac-style { display:none; }
.template-product .tabs-listing .product-tabs { border-bottom:1px solid #e8e9eb; width:100%; margin-bottom:0; text-align:center; }
.template-product .tabs-listing .product-tabs li { float:none; display:inline-block; cursor:pointer; margin-right:25px; }
.template-product .tabs-listing .product-tabs a { color:#000; font-size:14px; line-height:normal; position:relative; border-bottom:0; letter-spacing:0; display:block; border:0; border-bottom:3px solid transparent; padding:10px 0; background:transparent; text-transform:uppercase; font-weight:600; border-radius:0; outline:none; text-decoration:none; }
.template-product .product-tabs li.active a, .template-product .product-tabs li.active a:focus { background:transparent; text-decoration:none; color:#2358ad; border-color:#2358ad; opacity:1; }
.template-product .product-tabs li.active a:before, .template-product .product-tabs li.active a:before { content: ""; width:100%; height:2px; background-color:#fff; position:absolute; bottom:-1px; left:0; right:0; }
.tabs-listing.tabs-listing-style2 .product-tabs { margin-top:60px; }
.tabs-listing.tabs-listing-style2 .product-tabs li { margin-right:1px; }
.tabs-listing.tabs-listing-style2 .product-tabs a { color:#000; font-weight:600; font-size:13px; background:#f7f7f7; padding:10px 20px; border:0 !important; }
    .tabs-listing.tabs-listing-style2 .product-tabs li.active a, .tabs-listing.tabs-listing-style2 .product-tabs li.active a:focus {
        color: #fff;
        background: var(--theme-color);
        border: 0 !important;
    }
.tabs-listing.tabs-listing-style2 .product-tabs li.active a:before, .tabs-listing.tabs-listing-style2 .product-tabs li.active a:before { display:none; }
.acor-ttl.active { border-bottom:1px solid #000; }
.acor-ttl { display:block; padding:15px 0; position:relative; font-weight:700; letter-spacing:0; border-bottom:1px solid #e8e9eb; margin:0; font-size:15px; cursor:pointer; }
.template-product .tabs-listing .tab-container { padding:30px 0; text-align:left; }
.tab-container .tab-content { display:none; }
.product-template__container .product-single-1 .tab-container .tab-content { padding-top:20px; }
#shopify-product-reviews { *zoom:1; display:block; clear:both; overflow:hidden; margin:1em 0; }
.spr-container:before, .spr-container:after { content:" "; display:table; }
.spr-summary-starrating { margin:0 6px 0 0; }
.spr-summary-actions-newreview { float:right; background:#000; color:#fff !important; font-size:12px; font-weight:700; padding:8px 10px; text-transform:uppercase; }
.spr-form-title { font-size:18px; font-weight:700; line-height:24px; margin-top:0; text-transform:uppercase; }
.spr-form-contact-name, .spr-form-contact-email, .spr-form-contact-location, .spr-form-review-rating, .spr-form-review-title, .spr-form-review-body { *zoom:1; margin:0 0 15px 0; }
.spr-container input, .spr-container select, .spr-container textarea { border-color:#d3d3d3; }
.product-template__container label { font-weight:400; text-transform:uppercase; letter-spacing:0.02em; }
.spr-reviews { padding:0 0 20px; }
.spr-reviews .review-inner { max-height:525px; overflow-y:auto; }
.spr-reviews .spr-review:not(:last-of-type) { border-bottom:1px dotted #a6a6a6; padding-bottom:15px; margin-bottom:15px; }
.spr-review-header-starratings { margin:0; display:inline-block; }
.spr-review-header-title { font-size:15px; line-height:24px; margin:0; padding:0; border:none; }
.spr-review-header-byline { font-weight:normal; font-size:12px; opacity:0.7; display:inline-block; margin:0 0 1em 0; }
.spr-review-header-byline strong { font-weight:normal; }
.spr-review-content { *zoom:1; margin:0; }
.spr-form-actions .spr-button-primary { float:left; }
.new-review-form .spr-form-label { display:block; margin-bottom:3px; }
.new-review-form .spr-form-label .required { color:#F00; }

.tabs-listing .tab-container table { max-width:100%; }
.tabs-listing .tab-container table tr th { background-color: #f7f7f7; }
.tabs-listing .tab-container table tr th, .tabs-listing .tab-container table tr td { padding:7px 12px; font-size:14px; border:1px solid #e8e9eb; }

.related-product { margin-bottom:70px; }
.related-product .section-header { margin-bottom:20px; }
.related-product .section-header p { margin-left:auto; margin-right:auto; }
.related-product .button-set.style1 li .btn-icon { font-size:14px; margin:1px; width:30px; height:30px; line-height:28px; }
.related-product .button-set.style1 li .btn-icon .an-shopping-cart{
    font-size:18px;
}
.product-template__container .section-header { margin-bottom:40px; }
.sub-heading { text-align:center; max-width:500px; margin:0 auto; }
.related-product .grid--view-items { overflow:visible; }
.recently-product .grid-products .item { float:left; }

.product-single__photos.bottom .product-dec-slider-1 { padding:8px 0; margin-left:-4px; }
.product-single__photos.bottom .product-dec-slider-1 .slick-list { margin:0 -2px; }
.product-single__photos.bottom .product-dec-slider-1 .slick-slide { margin:0 4px; }
.product-info .lbl { font-weight:700; }

.left-content-product { float:left; width:80%; padding-right:30px; }
.sidebar-product { float:left; width:20%; }
.sidebar-product .prFeatures { padding-top:0; }
.sidebar-product .prFeatures h5 { font-size:1.07692em; font-family:'Hm-Regular','Poppins', sans-serif; font-weight:600; }
.template-product-right-thumb .sidebar-product .prFeatures { padding-top:0; }
.sidebar-product .section-header { margin-bottom:20px; }

.prstyle3 .related-product { margin-bottom:20px; }
.prstyle3 .related-product:before, .prstyle3 .related-product:after { content:''; clear:both; display:block; }
.prstyle3 .related-product .section-header .h2, .prstyle3 .related-product .section-header .sub-heading { text-align:left; }
.prstyle3 .related-product .section-header { margin-bottom:12px; }
.prSidebar .section-header h2, .prSidebar .section-header .h2 { font-size:130%; text-align:left !important; }
.prstyle3 .mini-list-item .mini-view_image img { max-width:110px; }
.prstyle3 .mini-list-item .mini-view_image { width:28%; }
.prstyle3 .mini-list-item .details { margin-left:32%; }

.template-product-right-thumb .product-details-img .product-thumb { padding-right:0; padding-left:5px; }
.template-product-right-thumb .product-thumb .product-dec-slider-2 a { padding-bottom:3px; }
.template-product-right-thumb .prFeatures { padding:40px 0 20px; }

.product-countdown { position:static; margin:15px 0; }
.product-countdown:before, .product-countdown:after { content:''; clear:both; display:block; }
.product-countdown .time-count { font-weight:700; font-size:24px; display:block; width:100%; text-align:center; margin:0; }
.saleTime.product-countdown { position:static; margin:0 0 15px 0; display:inline-flex; width:100%; }
.saleTime.product-countdown > * { width: 100%; margin: 3px; }
.saleTime.product-countdown .count-inner { background-color:#f5f5f5; width:100%; border:1px solid #f0f0f0; padding:10px; margin:0; }
.saleTime.product-countdown .count-inner .time-count { color:#000; font-size:18px; line-height:30px; font-weight:bold; }
.saleTime.product-countdown span>span { margin-top:0; }
.saleTime.product-countdown span>span span { color:#000; font-size:13px; line-height:16px; display:block; background-color:transparent; border:0; padding:0; margin:0 auto; }

.product-right-sidebar .product-details-img { width:50%; float:left; padding-right:10px; }
.product-right-sidebar .product-information { width:50%; float:left; padding-left:10px; }
.product-right-sidebar .sidebar-product { width:100%; }
.product-right-sidebar .tabs-listing { clear:both; padding-top:30px; }
.product-right-sidebar .sub-heading { text-align:left; }
.product-right-sidebar .related-product { margin-bottom:20px; }

.product-labels .pr-label3 { left:5px; background:#fb6c3e; }

.product-single .product-single__meta { position:relative; margin-bottom:10px; }
.product-single .product-featured-img { width:100%; display:block; margin:0 auto; }
.product-single .grid_item-title { font-size:26px; margin-bottom:25px; }

.mfpbox { margin:0 auto; padding:20px; max-width:800px; position:relative; background:#fff; box-shadow:0 0 20px rgba(51,51,51,0.3); -webkit-box-shadow:0 0 20px rgba(51,51,51,0.3); }
.mfpbox .mfp-close { right:0; opacity:1; top:0; color:#333; line-height:30px; height:30px; width:30px }

.product-nav { 
    color:#333333; font-size:12px; line-height:1.2; max-width:200px; padding:5px; opacity:0.3; display:-webkit-flex; display:-ms-flexbox; display:flex; width:100%; -ms-flex-align:center; -webkit-align-items:center; -moz-align-items:center; 
    -ms-align-items:center; -o-align-items:center; align-items:center; -webkit-justify-content:space-between; -ms-justify-content:space-between; justify-content:space-between; position:fixed; top:45%; z-index:99; background-color:#ffffff; box-shadow:0 0 10px rgba(0,0,0,0.2);
}
.product-nav:hover { color:#333; opacity:1; }
.product-nav.prev-pro { left:-130px; }
.product-nav.next-pro { right:-130px; }
.product-nav .details { width:125px; padding:5px; }
.product-nav span.img { width:60px; }
.product-nav:hover.prev-pro { left:0; }
.product-nav:hover.next-pro { right:0; }
.product-nav .price { margin-top:10px; display:block; }

.product-horizontal-style .product-zoom-right { width:100%; }
.product-horizontal-style .product-horizontal-thumb { width:100%; padding:10px 0; }
.product-horizontal-style .product-horizontal-thumb .slick-slider { margin:0 -10px 0 0; }
.product-horizontal-style .product-horizontal-thumb .slick-slide { margin:0 10px 0 0; cursor:pointer; }
.product-horizontal-style .product-horizontal-thumb .slick-prev { left:0; top:50%; margin:0; }
.product-horizontal-style .product-horizontal-thumb .slick-prev:before { content: "\f104"; font-family:"annimex-bold"; font-size:14px; line-height:18px; }
.product-horizontal-style .product-horizontal-thumb .slick-next { right:9px; left:auto; bottom:auto; top:50%; margin:0; }
.product-horizontal-style .product-horizontal-thumb .slick-next:before { content: "\f105"; font-family:"annimex-bold"; font-size:14px; line-height:18px; }

.product-sticky-style .product-horizontal-style { position: relative; z-index: 1; width: 100%; }
.product-360-degree-layout .trustseal-img { margin-top:20px; }
.product-single .type-product { display:block; margin:10px 0; }
.social-sharing .btn .share-title { display:none; }

.stickyCart { display:none; color:#f5f5f5; position:fixed; bottom:0; left:0; right:0; z-index:50; text-align:center; width:100%; height:80px; padding:15px 5px; background-color:#000000; }
.stickyCart .img, .stickyCart .sticky-title { display:inline-block; vertical-align:middle }
.stickyCart .product-featured-img { display:block; margin:0 auto; max-width:40px; max-height:40px; object-fit:cover; }
.stickyCart .sticky-title { padding:0 60px 0 8px; text-align:left; }
    .stickyCart .sticky-title .old-price {
        color: #eee;
        opacity: 0.8;
        text-decoration: line-through;
        margin-right: 10px;
    }
.stickyCart .sticky-title .price{color: #ed0f0f;}
.stickyCart .stickyOptions { margin-right:10px; border:1px solid #535353; position:relative; display:inline-block; vertical-align:middle; text-align:left; }
.stickyCart .stickyOptions .selectedOpt { padding:8px 30px 0 15px; font-size:13px; height:35px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; width:220px; text-overflow:ellipsis; cursor:pointer; position:relative; }
.stickyCart .stickyOptions .selectedOpt:after { content:"\f107"; font-family:"annimex-bold"; position:absolute; top:8px; right:10px; }
.stickyCart .stickyOptions ul { position:absolute; bottom:100%; left:0; display:none; min-width:100%; max-height:300px; overflow:auto; background-color:#000; }
.stickyCart .stickyOptions ul li { font-size:90%; padding:10px; white-space:nowrap; text-overflow:ellipsis; cursor:pointer; }
.stickyCart .stickyOptions ul li.soldout { opacity:0.5; text-decoration:line-through; }
.stickyCart .product-form__cart-submit { padding:0 20px; line-height:37px; border:0; }
.stickyCart .wrapQtyBtn { display:inline-block; vertical-align:middle; margin-right:10px; float:none; }
.stickyCart .qtyField { border:1px solid #535353; }
.stickyCart .qtyField .qtyBtn, .stickyCart .qtyField .qty { color:#f5f5f5; height:35px; line-height:15px; background-color:#000; border:0; }
.stickyCart.popup { padding-right:16px; }
.stickyCart .product-form__variants.selectbox.no-js { display:none; }

/* 新增的sticky cart样式 */
    .stickyCart .sticky-product-name {
        font-weight: 500;
        font-size: 14px;
        line-height: 1.1;
        margin-bottom: 2px;
        display: block;
        max-width: 45vw;
    }

.stickyCart .sticky-price-container {
    display: flex;
    align-items: center;
    gap: 6px;
}

.stickyCart .sticky-old-price {
    color: #888;
    text-decoration: line-through;
    font-size: 13px;
    opacity: 0.8;
    margin-right: 8px;
}

.stickyCart .sticky-current-price {
    color: #ed0f0f;
    font-weight: 600;
    font-size: 14px;
    text-decoration: none; /* 确保当前价格没有下划线 */
}

.stickyCart .sticky-current-price.promotion-price {
    color: #e95144;
    text-decoration: none;
}

.stickyCart .sticky-current-price.sale-price {
    color: #ed0f0f;
    text-decoration: none;
}

.stickyCart .sticky-buttons {
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
}

.stickyCart .sticky-buttons .btn {
    margin-left: 5px;
    font-size: 13px;
    padding: 8px 15px;
    line-height: 1.2;
}

.template-product .tabs-listing.tab-accordian-style { margin-top:10px; }
.tab-accordian-style .acor-ttl { line-height:normal; text-transform:uppercase; color:#000; background-color:#f3f3f3; border:1px solid #f3f3f3; display:block; padding:10px 30px 10px 15px; margin-bottom:5px; }
    .tab-accordian-style .acor-ttl.active {
        color: #fff;
        background-color: var(--theme-color);
        border: 1px solid var(--theme-color);
    }
.tab-accordian-style .acor-ttl:before { font-family:"annimex-bold"; content: "\f107"; font-size:14px; position:absolute; right:10px; top:50%; margin-top:-8px; }
.tab-accordian-style .acor-ttl.active:before { content: "\f106"; }
.tab-accordian-style .tab-container .tab-content { padding:20px 0; }

.product-single-style2 .product-sticky-style { margin-bottom:45px; position:sticky; position:-webkit-sticky; top:70px; }
.product-single-style2 .product-details-img .product-zoom-right { width:100%; }
.product-single-style2 .product-details-img .zoompro-wrap { margin-bottom:10px; }
.product-single-style2 .product-form__item .sizelink,
.product-single-style3 .product-form__item .sizelink { padding-left:10px; }

.product-single-style3 .product-form { margin-left:0; margin-right:0; }
.product-single-style3 .product-form .col-12 { padding:0 5px; }
.product-single-style3 .product-form .row { width:100%; margin:0 -5px; }
.product-single-style3 .infolinks { margin:10px 0 30px; }
.product-single-style3 .orderMsg { color:#000; }
.product-single-style3 .orderMsg .anm { font-size:20px; padding-right:5px; }
.product-single-style3 .product-action { margin:10px 0; padding:0; }
.product-single-style3 .userViewMsg .anm { font-size:16px; padding-right:5px; }
.product-single-style3 .userViewMsg .anm, .product-single-style3 .userViewMsg .uersView { color:#000; }
.product-single-style3 .product-sticky-style { position:sticky; position:-webkit-sticky; top:70px; }
.product-single-style3 .product-single { margin-bottom:40px; }

.storeFeatures.storeFeaturesProStyle1 { color:#000; margin-bottom:40px; }
.storeFeatures.storeFeaturesProStyle1 .items { padding:15px 15px 15px 70px; margin:0; background-color:#f7f7f7; position:relative; }
.storeFeatures.storeFeaturesProStyle1 .items .an { font-size:35px; line-height:20px; position:absolute; left:25px; top:25px; }
.storeFeatures.storeFeaturesProStyle1 .items h4 { margin:0; font-size:15px; }
.storeFeatures.storeFeaturesProStyle1 .items span { color:#888; }

.product-single-style4 .product-sticky-style { position:sticky; position:-webkit-sticky; top:70px; }
.product-single-style4 .product-single { margin-bottom:40px; }
.product-single-style4 .product-details-img .product-zoom-right { width:100%; margin-bottom:10px; }
.product-single-style4 .product-details-img > .row { margin-left:-5px; margin-right:-5px; }
.product-single-style4 .product-details-img .col-md-6 { padding-left:5px; padding-right:5px; }

.template-product .list-sidebar-products .grid__item { margin-bottom:15px; }
.template-product .mini-list-item .mini-view_image img { max-width:90px; }

ol#compare-items { list-style:none; padding:0; margin:0; }
ol#compare-items li { margin-bottom:10px; padding-bottom:10px; border-bottom:1px solid #ECECEC; position:relative; }
ol#compare-items li .btn-remove1 { position:absolute; right:5px; top:5px; }

.template-product .block-cart .list-sidebar-products .grid__item { margin-bottom:0; }
.block { background-color:#f5f5f5; padding:15px; }
.block-cart .block-subtitle { font-weight:600; }
.block-cart .list-sidebar-products { margin-top:15px; }

.block-cart .summary { padding-bottom:15px; border-bottom:1px solid #ECECEC; }
.block-cart .summary p { margin-bottom:5px; }
.block-cart .summary .subtotal .price { font-weight:600; }
.block-cart .product-name { display:block; margin:5px 0; }
.block-cart .details { position:relative; }
.block-cart .btn-remove1 { position:absolute; right:5px; top:5px; }

.product-single-center-mode .product-details-img { margin-bottom:30px; position:relative; } 
.product-single-center-mode .product-buttons { bottom:15px; right:10px; }
.product-single-center-mode .product-center-style3 .slick-slide { opacity:0.5; }
.product-single-center-mode .product-center-style3 .slick-slide.slick-active.slick-center { opacity:1; }
.product-single-center-mode .tabs-listing { margin-bottom:20px; }
.product-single-center-mode .tabs-listing .product-tabs { text-align:center; }
.product-single-center-mode .product-info-center { max-width:860px; margin:0 auto 30px; text-align:center; }

.product-simple-layout #freeShipMsg { margin-top:20px; }
.product-simple-layout .product-form-product-template { border-top:1px dotted #a0a0a0; border-bottom:1px dotted #a0a0a0; padding:20px 0 10px; margin:20px 0 10px; }
.product-simple-layout .tabs-listing .product-tabs { text-align:center; }

.product-variable-layout .product-form-product-template { border-top:1px dotted #a0a0a0; border-bottom:1px dotted #a0a0a0; padding:20px 0; margin:20px 0 30px; }
.product-variable-layout .infolinks .btn { margin-right:10px; }

.product-grouped-layout .product-single__meta .product-single__price .money { font-size:18px; }
.product-grouped-layout .product-form__item--submit { padding-left:0; }
.product-grouped-layout .product-form__item--submit .btn { width:100%; }
.product-grouped-layout .infolinks { margin-bottom:25px; }
.grouped-product-list { border-collapse:collapse; margin-top:10px; }
.grouped-product-list tr { border-bottom:1px dotted #b7b7b7; }
.grouped-product-list td { padding:10px 5px; vertical-align:middle; }
.grouped-product-list td.grouped-product-list-item__thumb img { max-width:70px; }
.grouped-product-list td .qtyField a { background:#f4f4f4; }
.grouped-product-list td .qtyField .qtyBtn, 
.grouped-product-list td .qtyField .qty { height:35px; padding:7px 6px; }
.grouped-product-list td .qtyField .qtyBtn { width: 25px; }
.grouped-product-list td .qtyField .qty { width: 40px; }
.grouped-product-list td.grouped-product-list-item__price .old-price { color:#999; padding-right:10px; text-decoration:line-through; }

#threesixty { max-width:600px; background:#fff; margin:0 auto; padding:10px; position: relative; }
.threesixty { overflow:hidden; }
.threesixty .nav_bar { position:absolute; bottom:20px; left:50%; z-index:11; transform:translateX(-50%); -webkit-transform:translateX(-50%); -ms-transform:translateX(-50%); }
.threesixty .nav_bar a { font-size:0; width:40px; line-height:40px; height:40px; float:left; background-color:#fff; text-align:center; }
.threesixty .nav_bar a::before { display:inline-block; font-size:24px; font-family:"annimex-bold"; }
.threesixty .nav_bar a.nav_bar_previous::before { content: "\f048"; }
.threesixty .nav_bar a.nav_bar_play::before { content:"\f04b"; }
.threesixty .nav_bar a.nav_bar_next::before { content: "\f051"; }
.threesixty .nav_bar a.nav_bar_stop::before { content: "\f04d"; }
.threesixty .spinner { width:60px; display:block; margin:0 auto; height:30px; background:#333; border-radius:5px; }
.threesixty .spinner span { font-family:Arial, "MS Trebuchet", sans-serif; font-size:12px; font-weight:bolder; color:#FFF; text-align:center; line-height:30px; display:block; }
.threesixty .threesixty_images { display:none; list-style:none; margin:0; padding:0; }
.threesixty .threesixty_images img { position:absolute; top:0; left:50%; height:auto; max-height:780px; transform:translateX(-50%); -webkit-transform:translateX(-50%); -ms-transform:translateX(-50%); }
.threesixty .threesixty_images img.current-image { visibility:visible; }
.threesixty .threesixty_images img.previous-image { visibility:hidden; }

.out-of-stock-layout .product-form__item--submit { padding-left:0; }
.out-of-stock-layout .out-of-stock-btn { background-color:#ff0000; opacity:1; cursor:default; }
.out-of-stock-layout .product-form-product-template { border-bottom:0; }
.out-of-stock-form { background-color:#f7f7f7; padding:20px; }
.out-of-stock-form input[type="text"] { background-color:#fff; }

/*======================================================================
  16. Sidebar
========================================================================*/
.sidebar .sidebar_widget { margin-bottom:35px;clear:both; width:100%; }
.sidebar .sidebar_widget:last-of-type { margin-bottom:0; }
.sidebar h2, .sidebar .h2 { margin-bottom:15px; font-size:15px; }
.sidebar h2:after { content: ''; width:50px; height:3px; display:block; background-color:var( --theme-color); margin:10px 0; }
.sidebar .sidebar_widget .widget-content ul { margin:0 0 15px; list-style:none; }
.sidebar .sidebar_widget.sidePro .widget-content ul { margin:0 0 8px; }
.sidebar .sidebar_widget .widget-content ul li { list-style:none; padding:3px 0; font-size:12px; }

.filterBox ul:not(.filter-color) { list-style:none; }
.filterBox ul:not(.filter-color) input[type="checkbox"] { width:20px; height:auto; margin:0; padding:0; font-size:1em; opacity:0; }
.filterBox ul:not(.filter-color) input[type="checkbox"] + label { display:inline-block; margin-left:-20px; line-height:1.5em; cursor:pointer; margin-bottom:0; }
.filterBox ul:not(.filter-color) li label { font-weight:400; font-size:12px; }
.filterBox ul:not(.filter-color) input[type="checkbox"] + label > span { display:inline-block; width:15px;height:15px; margin:0 7px 0 0; border:1px solid #d0d0d0; vertical-align:middle; }
.filterBox ul:not(.filter-color) input[type="checkbox"]:checked + label > span:before { content:"\f00c"; font-family: Hm-Bold; font-weight: 900; display:block; width:14px; color:#000; font-size:13px; line-height:13px; text-align:center; }

.filterBox .filter-color { display:table; list-style:none; width:100%; }
.filterBox .filter-color .swacth-btn { display:block; float:left; margin-bottom:10px; position:relative; height:25px; width:25px; border:1px solid transparent; background-color:#fff; margin-right:5px; text-align:center; font-size:10px; line-height:21px; color:#000; cursor:pointer; }
.filterBox .filter-color .swacth-btn.checked { border-color:#000; }
.filterBox .filter-color .black { background-color:#000; }
.filterBox .filter-color .white { background-color:#fff; border:1px solid #ddd; }
.filterBox .filter-color .red { background-color:#fe0000; }
.filterBox .filter-color .blue { background-color:#0000fe; }
.filterBox .filter-color .pink { background-color:#ffc1cc; }
.filterBox .filter-color .gray { background-color:#818181; }
.filterBox .filter-color .green { background-color:#027b02; }
.filterBox .filter-color .orange { background-color:#fca300; }
.filterBox .filter-color .yellow { background-color:#f9f900; }
.filterBox .filter-color .blueviolet { background-color:#8A2BE2; }
.filterBox .filter-color .brown { background-color:#A52A2A; }
.filterBox .filter-color .darkGoldenRod { background-color:#B8860B; }
.filterBox .filter-color .darkGreen { background-color:#006400; }
.filterBox .filter-color .darkRed { background-color:#8B0000; }
.filterBox .filter-color .khaki { background-color:#F0E68C; }
.filterBox .filter-color .blue-red { background:url(../images/swatches/blue-red.jpg) no-repeat center center; }
.filterBox .filter-color .black-grey { background:url(../images/swatches/grey-black.jpg) no-repeat center center; }
.filterBox .filter-color .pink-black { background:url(../images/swatches/pink-black.jpg) no-repeat center center; }

.shop-fullwidth-layout.shop-listing .page-title h1 { text-align:center; }
.shop-fullwidth-layout .filterbar { padding:20px; opacity:0; visibility:hidden; width:300px; height:100%; overflow:auto; background-color:#fff; box-shadow:0 0 5px rgba(0,0,0,0.3); position:fixed; top:0; left:-240px; z-index:1000; -ms-transition:0.5s; -webkit-transition:0.5s; transition:0.5s; }
.shop-fullwidth-layout .btn-filter { margin-bottom:0; margin-right:10px; padding:5px 10px; font-size:13px; line-height:18px; }
.shop-fullwidth-layout .btn-filter i { vertical-align:middle; }
.shop-fullwidth-layout .filterbar .sidebar_widget:not(.filterBox), .filterbar .static-banner-block { display:none; }
.shop-fullwidth-layout .filterbar.active { left:0; opacity:1; visibility:visible }
.shop-fullwidth-layout .filterbar .closeFilter { color:#fff; font-size:16px; line-height:29px; height:30px; width:30px; text-align:center; visibility:hidden; cursor:pointer; position:fixed; top:15px; left:-30px; background-color:#000; -ms-transition:0.5s; -webkit-transition:0.5s; transition:0.5s; }
.shop-fullwidth-layout .filterbar.active .closeFilter { visibility:visible; left:300px; }
.shop-fullwidth-layout .filterbar .size-swacthes .swacth-list .swacth-btn { background-color: #f5f5f5; }
.brand-filter li { margin-bottom:4px; }

.filter-sidebar { background-color:#f7f7f7; padding:20px; margin-bottom:30px; }
.shop-fullwidth-layout .filter-sidebar { background-color:#fff; padding:0; margin-bottom:0; }
.sidebar .sidebar_widget.categories .sub-level { position:relative; }
.sidebar .sidebar_widget.categories .sub-level > a:after { content:"\f067"; font-family:"annimex-bold"; display:inline-block; position:absolute; right:0; top:3px; }
.sidebar .sidebar_widget.categories .sub-level > a.active:after { content:"\f068"; font-family:"annimex-bold"; display:inline-block; }
.sidebar .sidebar_widget.categories .sub-level ul { margin-left:15px; margin-bottom:0; display:none; }
.sidebar .sidebar_widget.categories li a { color:#111; font-size:14px; display:block; }
.sidebar .sidebar_widget.categories li a:focus { outline:0; }
.filter-by .filter-by-content { margin-bottom:10px; position:relative; }
.filter-by .close-icon { position:absolute; right:0; top:20px; }

.filter-widget .widget-title { position:relative; cursor:pointer; }
.filter-widget .widget-title:after { content:"\f106"; font-family:"annimex-bold"; display:inline-block; position:absolute; right:0; top:1px; font-size:14px; }
.filter-widget .widget-title.active:after { content:"\f107"; font-family:"annimex-bold"; display:inline-block; }
.filter-widget .price-filter { margin-top: 25px; }

.size-swacthes .swacth-list ul { margin-left:0; }
.size-swacthes .swacth-list li { float:left; display:block; }
.size-swacthes .swacth-list .swacth-btn { font-size:11px; display:block; margin-bottom:5px; width:30px; height:30px; line-height:28px; }

/* Price Range */
.price-filter input[type="text"] { height:30px; padding:0 10px; text-align:center; font-size:12px; width:100px; }
#slider-range.ui-slider-horizontal { background:#e9e9e9; border:none; border-radius:0; height:3px; margin-bottom:20px; }
#slider-range .ui-slider-handle { background:#2358ad; border:2px solid #2358ad; height:12px; outline:none; top:-5px; width:12px; cursor:w-resize; margin-left:-1px; border-radius:0; }
#slider-range.ui-slider-horizontal .ui-slider-range { background:#2e6ed5; border:0; }
#slider-range.ui-slider-horizontal .ui-slider-range ~ .ui-slider-range { background:#000; }

/* Color Swatches */
.sidebar .swacth-list ul { margin:0; }
.sidebar .swacth-list ul:before,
.sidebar .swacth-list ul:after { content:''; clear:both; display:table; }
.sidebar .swacth-list li { position:relative; float:left; }
.sidebar .swacth-list li .tooltip-label { top:-23px; left:50%; transform:translateX(-50%); margin-left:-3px; }
.sidebar .swacth-list li:hover .tooltip-label { opacity:1; top:-28px; visibility:visible; }
.grid-products .item .swatches.color-style li { box-shadow:none; -webkit-box-shadow:none; }
.grid-products .item .swatches.color-style li input[type="checkbox"] { display:none; }
.grid-products .item .swatches.color-style li input[type="checkbox"] + label.color { margin:0; cursor:pointer; border:1px solid #ccc; }
.grid-products .item .swatches.color-style li input[type="checkbox"] + label.color span { display:block; height:25px; width:25px; }
.grid-products .item .swatches.color-style li input[type="checkbox"]:checked + label.color { border:1px solid #000; box-shadow:0 0 1px #000; }
.grid-products .item .swatches.color-style li .black { background-color:#000; }
.grid-products .item .swatches.color-style li .white { background-color:#fff; }
.grid-products .item .swatches.color-style li .red { background-color:#fe0000; }
.grid-products .item .swatches.color-style li .blue { background-color:#0000fe; }
.grid-products .item .swatches.color-style li.rounded { width:25px; height:25px; border-radius:50% !important; }
.grid-products .item .swatches.color-style li.rounded input[type="checkbox"] + label.color,
.grid-products .item .swatches.color-style li.rounded input[type="checkbox"] + label.color span,
.grid-products .item .swatches.color-style li.rounded input[type="checkbox"]:checked + label.color { border-radius:50% !important; }
.grid-products .item .swatches.color-style li.radius input[type="checkbox"] + label.color,
.grid-products .item .swatches.color-style li.radius input[type="checkbox"] + label.color span,
.grid-products .item .swatches.color-style li.radius input[type="checkbox"]:checked + label.color { border-radius:5px !important; }
.grid-products .item .swatches.color-style li.small,
.grid-products .item .swatches.color-style li.small input[type="checkbox"] + label.color span { width:15px; height:15px; }
/* End Color Swatches */

.list-sidebar-products { margin-top:30px; }
.list-sidebar-products:before, .list-sidebar-products:after,
.sidebar .sidebar_widget:before, .sidebar .sidebar_widget:after { content:''; clear:both; display:block; }
.list-sidebar-products .grid__item { margin-bottom:5px; } 
.mini-list-item { margin-bottom:10px; display:-ms-flexbox; display:-webkit-flex; display:flex; -ms-flex-wrap:wrap; -webkit-flex-wrap:wrap; flex-wrap:wrap; }
.mini-list-item:before, .mini-list-item:after { content:''; clear:both; display:block; }
.mini-list-item .mini-view_image { float:left; margin-right:15px; }
.mini-list-item .mini-view_image img { width:100%; max-width:70px; }
.mini-list-item .details { flex:6; -ms-flex:6; }
.product-tags li { display:inline-block; border:1px solid #e8e9eb; margin-bottom:5px; height:30px; line-height:20px; }
.product-tags li a { padding:5px 10px; font-size:11px; }
.btnview { background:none; color:#000; padding:5px 0; border-bottom:1px solid #000; -ms-transition:all 0.5s ease-in-out; -webkit-transition:all 0.5s ease-in-out; transition:all 0.5s ease-in-out; }
.btnview:hover { background:none; border-color:#fff; color:#000; }

.filters-toolbar-wrapper { border:0; margin:-10px 0 20px 0; padding:5px; }
.filters-toolbar-wrapper .change-view { color:#000; font-size:18px; cursor:pointer; background:none; border:0; padding:0 3px; }
.filters-toolbar-wrapper .change-view--active { color:#000; }
.filters-toolbar__product-count { font-size:0.92308em; line-height:35px; margin-bottom:0; overflow:hidden; text-overflow:ellipsis; white-space:nowrap; }
.filters-toolbar-wrapper select { font-size:12px; margin-top:2px; }
.filters-toolbar__input { padding:0 5px; -ms-transition: all ease-out 0.15s; -webkit-transition:all ease-out 0.15s; transition:all ease-out 0.15s; background-color: #fff; overflow:hidden; text-overflow:ellipsis; white-space:nowrap; max-width:100%; height:30px; opacity:1; }

.toolbar { margin-top:30px; }
.infinitpagin { clear:both; padding:15px 0 0; text-align:center; }
.loadMore { color:#fff !important; }

.pagination { width:100%; text-align:center; list-style:none; font-size:1.15385em; }
.pagination ul { display:block; margin:0 auto; }
.pagination li { display:inline-block; }
.pagination li a { font-size:12px; color:#8e8e8e; height:35px; width:35px; line-height:33px; display:inline-block; border:2px solid #e3e3e3; vertical-align:middle; }
.pagination li.active a, .pagination li a:hover { border:2px solid var( --theme-color); color:var( --theme-color); }

/* Countdown Timer On listing */
.grid-products .item:hover .saleTime { display:none }
.saleTime { position:absolute; bottom:5px; left:0; right:0; z-index:111 }
.saleTime .time-count { font-weight:700; font-size:14px; }
.saleTime span>span { font-weight:700; min-width:30px; padding:6px 4px 4px; line-height:12px; display:inline-block; margin:0 0 0 1px; text-align:center; background:rgba(255, 255, 255, 0.9); color:#141414 }
.saleTime span>span span { display:block; background:none; font-size:10px; font-weight:400; margin-top:-3px; text-transform:uppercase; line-height:8px }
.timermobile { margin:0 -10px; display:none }
.timermobile .saleTime { position:relative; margin-top:20px }

.countdown-deals { line-height:35px; text-align:center; width:100%; margin-bottom:10px; }
.countdown-deals .cdown { background:#efefef; display:inline-block; height:50px; width:44px; }
.countdown-deals .cdown span { font-size:14px; font-weight:500; }
.countdown-deals .cdown > p { font-size:12px; text-transform:uppercase; line-height:0; margin:0; }
.grid-products .countdown-deals { position:absolute; bottom:-10px; }
.grid-products .countdown-deals .cdown { color:#fff; background-color:#000; }
.product-list .countdown-deals { line-height:40px; text-align:left; }
.product-list .countdown-deals .cdown { font-size:14px; height:59px; width:65px; text-align:center; color:#fff; background-color:#000; }

.product-load-more .item { display:none; }

.sideProSlider .slick-next { right:0; }
.sideProSlider .slick-prev { left:0; }
.sideProSlider .slick-arrow { opacity:0; visibility:hidden; width:35px; height:35px; background:#fff; }
.sideProSlider:hover .slick-arrow { opacity:1; visibility:visible; }

.sidebar .storeFeatures p { width:100%; }
.sidebar .storeFeatures .anm { font-size:16px; min-width:25px; display:inline-block; }
.sidebar .storeFeatures { color:#000; margin-bottom:40px; background-color:#f7f7f7; padding:10px; }
.sidebar .storeFeatures .items { padding:15px 15px 15px 50px; margin:0; background-color:#f7f7f7; position:relative; border-bottom:1px solid #ddd; }
.sidebar .storeFeatures .items.last { border-bottom:0; }
.sidebar .storeFeatures .items .an { font-size:35px; line-height:20px; position:absolute; left:5px; top:25px; }
.sidebar .storeFeatures .items h4 { margin:0; font-size:15px; }
.sidebar .storeFeatures .items span { color:#888; }
/*======================================================================
  End Sidebar
========================================================================*/

/*======================================================================
  17. Shop Pages
========================================================================*/
.category-banner, .category-description { margin-bottom:20px; }

.small-heading .page-title { background-color:#f5f5f5; padding:30px 0; }
.small-heading .page-title h1 { text-align:center; margin-bottom:0; }
.small-heading .breadcrumbs-wrapper { margin-bottom:0; }

.category-text-banner { background:url(../images/collection-banner/1025x300-4.jpg) no-repeat 50% 50%; background-size:cover; width:100%; height:230px; position:relative; }
.category-text-banner .page-title { color:#fff; position:absolute; top:50%; left:0; margin-top:-30px; padding:0 50px; }
.category-text-banner .page-title h1 { margin-bottom:10px; color:#fff; }
.category-text-banner .block-ttl { width:100%; }

.category-banner-slider { margin-bottom:20px; }
.category-banner-slider .slick-arrow { opacity:0; visibility:hidden; width:40px; border-radius:5px; height:40px; line-height:38px; text-align:center; background-color:rgba(255,255,255,0.5); }
.category-banner-slider .slick-arrow:before { line-height:40px; }
.category-banner-slider:hover .slick-arrow { opacity:1; visibility:visible; }
.category-banner-slider .slick-arrow:hover { background-color:rgba(255,255,255,0.5); box-shadow:0 0 4px rgba(0,0,0,0.3); -webkit-box-shadow:0 0 4px rgba(0,0,0,0.3); }
.category-banner-slider .slick-prev { left:10px; }
.category-banner-slider .slick-next { right:10px; }
/*======================================================================
  End Shop Pages
========================================================================*/

/*======================================================================
  18. CMS Page
========================================================================*/
.social-url a .an { font-size: 16px; margin-right: 5px; }
.team-img:hover { opacity: 0.8; }

.error-404-page .page-title h1 { font-size:40px; margin:0 0 10px; }
.error-404-page #page-content .error-content p { color:#000; font-size:15px; }

.my-account ul.nav { padding:0; border:1px solid #ebebeb; }
.dashboard-upper-info { border-bottom:1px solid #ebebeb; border-top:1px solid #ebebeb; margin-bottom:40px; }
.dashboard-upper-info p{ margin-bottom:0; font-size:15px; }
.dashboard-upper-info .d-single-info { border-right:1px solid #ebebeb; padding:30px 20px; }
.dashboard-upper-info [class*="col-"]:last-child .d-single-info { border-right:0; }
.dashboard-list li a { border-bottom:1px solid #ebebeb; color:#000; display:block; font-size:13px; font-weight:500; padding:10px 15px; text-transform:capitalize;}
.dashboard-list li a.active, .dashboard-list li a:hover { color:var(--theme-color); }
.dashboard-list li:last-of-type a { border-bottom:0; }
.dashboard-content { border:1px solid #ebebeb; padding:20px; }
.dashboard-content h3 { font-size:20px; line-height:24px; margin-bottom:15px; padding-bottom:15px; padding-top:0 !important; text-transform:uppercase; font-weight:700; padding:10px 0; letter-spacing:0; }
.dashboard-content p { margin:0; }
.product-order .table thead th { font-size:14px; padding:10px; font-weight:600;}
.product-order .table tbody tr td { line-height:18px; font-weight:500; }
.product-order .table tbody tr td a:hover { text-decoration:underline; }
.dashboard-content .billing-address {  font-size:16px; font-weight:700; line-height:normal; margin:15px 0 10px; }
.dashboard-content .address .view:hover { text-decoration:underline; }

.block-box .block-title { background-color:#f7f7f7; padding:10px; text-transform:uppercase; position:relative; }
.box.box-information .box-title { text-transform:uppercase; border-bottom:1px solid #f7f7f7; display:block; padding-bottom:10px; margin-bottom:10px; }
.block-box .block-title .action { position:absolute; right:10px; top:10px; }
.box.box-information .box-content p { margin-bottom:5px; }
.box.box-information .box-actions .action { margin-right:10px; }

.contact-details { padding:60px 30px; background:#f8f8f8; color:#111111; }
.contact-details .social-icons li { padding-right:10px; }
.contact-details .social-icons .icon { font-size:18px; }
.visually-hidden, .icon__fallback-text { position:absolute !important; overflow:hidden; clip:rect(0 0 0 0); height:1px; width:1px; margin:-1px; padding:0; border:0; }
.addressFooter { list-style:none; padding:0; margin:0; }
.addressFooter .icon { color:#000 !important; font-size:18px; float:left; margin-top:2px; }
.addressFooter li { padding-bottom:10px; }
.addressFooter li p { padding-left:30px; }
.d-app-col a img { margin: 5px; }

.contact-us-page .section-header { margin-bottom:0; }
.contact-us-page .map-section { margin-bottom:0; overflow:hidden; height:350px; }
.contact-us-page .map-section .container { position:relative; height:100%; }
.contact-us-page .map-section__overlay { left:auto; width:300px; padding:20px; display:inline-block; text-align:center; z-index:3; position:absolute; left:30px; top:0; transform:translateY(-135%); -webkit-transform:translateY(-135%); margin-top:0; background:rgba(255,255,255,0.8); }
.contact-us-page .map-section__overlay-wrapper { position:static; text-align:left; height:100%; }
.rte-setting { margin-bottom:11.11111px; }
.contact-us-page .btn--secondary { background-color:#ededed; color:#000; border:1px solid #000; }
.contact-form textarea { height:120px; }

.contact-us-page2 .contact-form-in { padding:30px; background:#f8f8f8; color:#111111; }
.contact-us-page2 .contact-form-in .input-text, .contact-us-page2 .contact-form-in textarea { background-color:#fff; }
.contact-us-page2 .contact-form-in textarea { height: 100px; }
.contact-us-page2 .contact-details { background-color:#fff; padding:0; }

/* Social Icon */
.site-footer__social-icons li { padding:0 10px; }
.social-icons .icon { color:#111111; font-size:15px; }
.site-footer__social-icons .icon { width:16px; }

.faqs-style1 #accordion .card { border:0; margin:0; }
.faqs-style1 #accordion .card-header { line-height:22px; cursor:pointer; font-weight:600; font-size:105%; text-transform:uppercase; background:none; padding:18px 40px 0 0; margin:0; border-bottom:1px solid #eeeeee; position:relative; border-radius:0; }
.faqs-style1 #accordion .card-header:before { display:none; }
.faqs-style1 #accordion .card-header a:before { content:"\f068"; font-family:'Hm-Bold'; font-weight:900; font-size:18px; position:absolute; top:18px; right:0px; }
.faqs-style1 #accordion .card-header a.collapsed:before { content:"\f067"; }
.faqs-style1 #accordion .card-header a { color:#000; display:block; margin:0 0 15px; }
.faqs-style1 #accordion .card-header a:focus { outline:0; }
.faqs-style1 #accordion .card-header a:hover,
.faqs-style1 #accordion .card-header a[aria-expanded="true"] { color: #2e6ed5; }
.faqs-style1 #accordion .collapse.show { margin:0; } 

.faq-style2 h3 { color:#eeeeee; font-size:20px; font-weight:600; background:#333333; padding:12px 20px; margin-top:15px; }
.faq-style2 .panel-title { line-height:22px; font-weight:600; font-size:16px; text-transform:uppercase; background:none; padding:15px 0; margin:0; border-bottom:1px solid #eeeeee; position:relative; }
.faq-style2 .panel-content { padding:20px 0; }

.coming-soon-page { height:100vh; }
.password-page { display:table; height:100%; width:100%; background-image:url(../images/coming-soon.jpg); background-repeat:no-repeat; background-position:50% 50%; background-size:cover; }
.password-main__inner { display:table-cell; vertical-align:middle; padding:15px; height:100vh; }
.coming-soon-form { color:#000000; display:inline-block; max-width:600px; padding:35px; margin:0 auto; position:relative; background-color:#ffffff; }
.coming-soon-form h2 { font-size:22px; font-weight:700; letter-spacing:0.2px; }
.coming-soon-form p { font-size:16px; font-weight:400; letter-spacing:0.2px; } 
.coming-soon-page .site-header__logo-image { max-width:150px; display:block; margin:0 auto 20px; }
.coming-soon-page .input-group__field { background-color:#fff; margin-bottom:10px; }
.coming-soon-page .input-group__btn { margin:0 auto !important; }
.coming-soon-page .saleTime { position:static; margin-bottom:20px; }
.coming-soon-page .saleTime .count-inner { color:#fff; font-family:'Hm-Regular','Hm-Regular','Rajdhani', sans-serif; font-weight:bold; min-width:80px; padding:10px 4px 8px; line-height:normal; display:inline-block; margin:0 0 0 1px; text-align:center; background:#000000; border-radius:0; }
.coming-soon-page .saleTime .time-count { color:#fff; display:block !important; background:none; font-size:22px; font-weight:700; text-transform:uppercase; line-height:28px; margin:0; padding:0; }
.coming-soon-page .saleTime .time-count + span { color:#fff; font-size:14px; font-weight:600; margin:0; }
.social-sharing .icon, .social-sharing .an { font-size:20px; }
.social-sharing .icon { padding:0 2px; vertical-align: middle; }

/* Wishlist Page & Compare page */
.wishlist-table { margin:30px 0; }
.wishlist-table tr, .wishlist-table td { vertical-align:middle; }
.wishlist-table .product-thumbnail a img { max-width:100px; }
.wishlist-table .in-stock { color:#01BD01; text-transform:uppercase; font-weight:600; }
.wishlist-table .out-stock { color:#DD0101; text-transform:uppercase; font-weight:600; }

.compare-page .table { border:1px solid #ddd; }
.compare-page .table th { background-color:#efefef; border-color: #ddd !important; vertical-align:middle; }
.compare-page .table th.product-name { min-width: 150px; }
.compare-page .table td { border:1px solid #ddd !important; vertical-align:middle; }
.compare-page .table .remove-compare { border:0; cursor:pointer; }
.compare-page .table .remove-compare:hover { color:#000000; }
.compare-page .table .featured-image { max-width:100%; margin-bottom:15px; }
.compare-page .table .product-price.product_price { margin-bottom:15px; display:block; }
.compare-page .table .available-stock p { color:#090; text-transform:uppercase; }

.compare-page2 .table .featured-image { max-width: 230px; display: block; margin: 0 auto; }
.compare-page2 .table .remove-compare { float:right; width:100%; text-align:right; margin:0 0 5px; }
.compare-page2 .table .grid-link__title { margin-bottom:10px; }
.compare-page2 .table .product-price.product_price { margin-bottom: 0; }

/* My Account Pages */
.login-page .box { margin-bottom:30px; }
.login-page .box h3, .register-page .box h3 { font-size:20px; font-weight:bold; text-transform:uppercase; margin:20px 0; }
.login-page form .required, .register-page .required { color:#F00; }

/*======================================================================
  19. Blog Pages
========================================================================*/
.loadmore-post { text-align:center; }
.blog--grid-load-more .article { display:none; padding:0 0 30px; margin-bottom:30px; border-bottom:1px dotted #a6a6a6; }
.no-border .article { border-bottom:0; padding-bottom:0; }

.custom-search { margin-bottom:20px; }
.custom-search .search { opacity:1; border:1px solid #ddd; max-width:100%; padding:8px 10px; border-radius:0; box-shadow:none; -webkit-box-shadow:none; display:table; top:0; transform:none; -webkit-transform:none; visibility:visible; }
.custom-search .search__input { font-size:13px; border:none; display:table-cell; width:100%; padding:0 10px; }
.custom-search .input-group__field, .custom-search .input-group__btn { display:table-cell; vertical-align:middle; margin:0; }
.custom-search .input-group__btn { text-align:center; white-space:nowrap; width:1%; }
.custom-search .btnSearch { border:0; cursor:pointer; font-size:21px; }

.article_featured-image { display:-webkit-box; display:-moz-box; display:-ms-flexbox; display:-webkit-flex; display:flex; align-items:center; justify-content:center; min-height:140px; }
.article_featured-image img { margin-bottom:15px; }
.blog-list-view .article_featured-image img { margin-bottom:0; }
.publish-detail { margin:0 0 10px 0; display: flex; flex-wrap: wrap; }
.publish-detail .an { font-size: 16px; margin-right: 5px; }
.publish-detail li { list-style:none; display:inline-flex; align-items: center; }
.publish-detail > li:after { content:'|'; display:inline-block; padding:0 10px; vertical-align:middle; }
.publish-detail > li:last-of-type:after { content:""; }
.article .rte { margin-bottom:15px; }
.article .h3 { font-size:20px; font-weight:bold; text-transform:none }
.article .rte h2, .article h2, .blog-single-page h2 { text-transform:none; }

.featured-content .list-items { margin-left:10px; }

#comment_form { padding:30px; background:#faf9f9; margin-top:40px; }
#comment_form input[type="text"], #comment_form input[type="email"], #comment_form textarea { background:#fff; }
#comment_form h2 { font-size:24px; margin:0 0 20px; }
.blog-nav { margin-top:40px; } 
.blog-nav .icon { vertical-align:middle; padding-right:4px; }
.blog-nav .text-right .icon { padding-left:4px; }
.list-sidebar-products .article__date { color:#888; }

.tags-clouds li { display:inline-block; margin-bottom:6px; margin-right:6px; }
.tags-clouds li a { color:#000000; display:block; border:1px solid #ddd; padding:5px 9px !important; text-transform:uppercase; }
.tags-clouds li a:hover { background-color:#efefef; }

/* 标签云滚动样式 */
.tags-scrollable {
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 5px;
}

/* 自定义滚动条样式 */
.tags-scrollable::-webkit-scrollbar {
    width: 6px;
}

.tags-scrollable::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.tags-scrollable::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.tags-scrollable::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Firefox滚动条样式 */
.tags-scrollable {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 标签云激活状态样式增强 */
.tags-clouds li a.active {
    background-color: #2e6ed5;
    color: #fff !important;
    border-color: #2e6ed5;
}

.blog-fullwidth-page .article .article_featured-image img { width:100%; }

.blog-comment { margin-top:40px; margin-bootom:40px; }
.blog-single-page .article blockquote { background:#f8f8f8; text-align:center; font-weight:600; font-size:15px; font-style:normal; padding:30px; margin:0 0 20px; }
.blog-single-page .article .btn-link { color:#000; padding-right:10px; text-decoration:none; display:inline-flex; align-items:center; }
.blog-single-page .article .btn-link:hover { color: #2e6ed5; text-decoration:none; }
.blog-single-page .blog-nav a { color:#000; font-size:15px; text-transform:uppercase; padding:10px 0; }
.blog-single-page .blog-nav a i, .blog-single-page .blog-nav span { vertical-align:middle; font-weight:600; }
.blog-single-page .blog-nav .nav-prev { float:left; }
.blog-single-page .blog-nav .nav-next { float:right; }
.blog-single-page .comments-list { list-style:none; margin:0; padding:0; }
.blog-single-page .comment { display:-ms-flexbox; display:-webkit-flexbox; display:flex; }
.blog-single-page .blog-comment h2 { font-size:24px; margin:20px 0 30px; }
.blog-single-page .comment__avatar { width:70px; -ms-flex-negative:0; flex-shrink:0; margin-left:20px; margin-right:24px; }
.blog-single-page .comment__content { -ms-flex-positive:1; flex-grow:1; }
.blog-single-page .comment__avatar img { max-width:100%; border-radius:222px; }
.blog-single-page .comment__header { margin-top:-4px; display:-ms-flexbox; display:flex; -ms-flex-pack:justify; justify-content:space-between; -ms-flex-align:center;align-items:center; }
.blog-single-page .comments-list--level--1 { border-top: 1px solid #ebebeb; margin-top:24px; padding-top:28px; }
.blog-single-page .comments-list__item + .comments-list__item { border-top: 1px solid #ebebeb; margin-top:24px; padding-top:28px; }
.blog-single-page .comments-list--level--1 > .comments-list__item { margin-left:46px; }
.blog-single-page .comment__author { color:#000; font-size:14px; font-weight:600; margin-bottom:10px; }
.blog-single-page .comment__date { margin-top:10px; color:#999; font-size:14px; }
.blog-single-page .comment__reply .btn { color:#555; height:22px; padding:.125rem .375rem; display:block; background-color:transparent; border:0; }
.blog-single-page .comment__reply .btn:hover {  }

/*======================================================================
  20. Cart Pages
========================================================================*/
.cart-col h5 { font-size:18px; font-weight:700; text-transform:uppercase; }
.cart__row { position:relative; }
.cart th { padding:10px 0 8px; background:#f2f2f2; text-transform:uppercase; padding-left:15px; padding-right:15px; }
.cart td { padding:10px; }
.cart .cart__meta { padding-right:15px; }
.cart th.cart__meta, .cart td.cart__meta { text-align:left; }
.cart__image-wrapper a { display:block; }
.cart .list-view-item__title { color:#000; font-size:1.15385em; min-width:100px; }
.cart__image-wrapper { width:120px; }
.cart .qtyField a { background-color: #f7f7f7; height:36px; width: 26px; line-height:34px; padding:0; }
.cart .qtyField a:hover { background-color: #f0f0f0; }
.cart .qtyField .cart__qty-input { height:36px; width:40px; float:left; }
.cart .qtyField a .icon { line-height:33px; font-size:10px; }
.cart .cart__remove { border:0; font-size: 20px; line-height: 25px; padding:0; height:25px; width:25px; text-align:center; display: block; margin: 0 auto; }
.cart table tfoot .icon { vertical-align:middle; }
.style2 .cart__footer .cart-note { margin-bottom:30px; }
.cart__footer .solid-border { border:1px solid #e8e9eb; padding:20px; margin-bottom:20px; }
.cart__footer h5, .cart__footer .h5, .cart__footer h5 label, .cart__footer .h5 label, .cart__footer .cart__subtotal-title { color:#000; text-transform:uppercase; font-size:14px;font-family:'Hm-Regular',"Poppins",Helvetica,Tahoma,Arial,sans-serif; letter-spacing:0.02em; }
.cart-note__input { min-height:50px; width:100%; height:178px; }
.cart-note__label, .cart-note__input { display:block; }
.cart__subtotal { font-weight:700; padding-left:15px; display:inline-block; }
.cart__shipping { font-style:italic; font-size:13px; padding:12px 0; }
.cart_tearm label { cursor:pointer; }
input.checkbox { height:auto; vertical-align:middle; padding:0; box-shadow:none; }
#cartCheckout { width:100%; padding:15px; }
.cart-variant1 .cart .cart__price-wrapper{ text-align:center; }
.cart-variant1 .cart table { border:1px solid #f2f2f2; }
.cart-variant1 .cart table td { border:1px solid #f2f2f2; }
.cart-variant1 .cart th.text-right,
.cart-variant1 .cart .text-right.cart-price { text-align:center !important; }
.cart__meta-text { color:#a2a2a2; font-size:12px; margin-top: 5px; }

.wrapQtyBtn { float:left; }
.qtyField { display:flex; justify-content: center; margin:0 auto; }
.qtyField .qty { width:40px; }
.qtyField .qtyBtn, .qtyField .qty { padding:10px 6px; width:30px; height:42px; border-radius:0; float:left; }
.qtyField a { color:#333; }
.qtyField a .fa { font-size:12px; line-height:21px; }
.qtyField > a, .qtyField > span, .qtyField input { display:table-cell; line-height:normal; text-align:center; padding:3px 6px; border:1px solid #f5f5f5; }

.cart-col .cart-col-in { background-color:#f9f9f9; padding:20px; }
.cart-col .cart-col-in input:not(.btn), 
.cart-col .cart-col-in select { background-color: #fff; }

#shipping-calculator { background-color:#f9f9f9; padding:20px; }
#shipping-calculator input:not(.btn), 
#shipping-calculator select { background-color: #fff; }
#shipping-calculator h5 { font-size:18px; font-weight:bold; text-transform:uppercase; }

.my-cart-page .section { padding:40px 0; }

/*======================================================================
  21. Checkout Page Styles
========================================================================*/
.login-title, .order-title, .payment-title, .checkout-page-style2 .title { font-size:18px; font-weight:700; }
.customer-box h3 { color:#fff; font-size:15px; font-weight:400; line-height:normal; margin:0; padding:15px; text-transform:uppercase; background-color:#464646; }
.customer-box h3 i { font-size:22px; vertical-align:middle; line-height:20px; margin-right:5px; }
.customer-box h3 a { font-weight:700; text-decoration:none; }
.billing-fields { margin-bottom:30px; }
.order-table .table thead th { background-color:#f0f0f0; font-size:13px; padding:7px 10px; border-bottom:1px solid #ddd !important; }
.order-table .table td { font-size:13px; padding:8px 10px; }
.card { border-radius:0; margin-bottom:10px; }
.card-header { position:relative; padding:10px 15px; }
.payment-accordion .card .card-header { background-color:#f9f9f9; border-radius:0; }
.payment-accordion .card .card-header a { color:#000; display:block; font-size:14px; font-weight:600; text-transform:uppercase; }
.payment-accordion .card .card-header a:after { content: "\f107"; font-family: "annimex-bold"; font-weight: 900; font-size: 14px; position: absolute; right: 15px; top: 50%; -webkit-transform: translateY(-50%); transform: translateY(-50%); }
.order-button-payment .btn { font-size:18px; font-size:700; padding: 10px 20px; }
.customer-box .discount-coupon, .customer-box .customer-info { background-color:#f7f7f7; padding:20px; }
.create-ac-content, .your-order-payment { border:1px solid #ddd; padding:20px; }
.create-ac-content .form-check { margin-left:15px; }
.customer-box input[type="email"], .customer-box input[type="text"], .customer-box input[type="password"] { background-color:#fff; }
.order-button-payment { margin-top:30px; }
.checkout-page .form-control, .checkout-page input, .checkout-page select, .checkout-page textarea { font-size:13px; border-radius:0; }
.checkout-page .required-f { color:#F00; }

.card-header { border-bottom:1px solid #ddd; }
.card.card--grey { background-color:#f7f7f7; }
.card { background-color:#fff; border-color:#f7f7f7; border-radius:0; }
.checkout-page-style2 input[type="checkbox"] + label,
.checkout-page-style2 input[type="radio"] + label { font-size:13px; font-weight:normal; line-height:24px; margin:0 10px 12px 0; min-width:100px; padding-left:5px; position:relative; }

.checkout-page-style2 .card-body h2 { font-size:18px; font-weight:700; }
.checkout-page-style2 .table-bordered .thumb { width:70px; display:block; text-align:center; margin:0 auto; }
.checkout-page-style2 .order-table .table td { vertical-align:middle; }

/*======================================================================
  22. Nesletter Popup Styles
========================================================================*/
.newsletter-section .input-group { position:relative; display:table; width:100%; border-collapse:separate; max-width:500px; margin:0 auto; }
#newsletter-modal { text-align:center; width:90%; position:fixed; left:50%; top:50%; background:#fafafa; color:#111; margin:20px auto; z-index:1045; transform:translate(-50%, -50%) scale(0.9); -webkit-transform:translate(-50%, -50%) scale(0.9); -ms-transform:translate(-50%, -50%) scale(0.9); }
#newsletter-modal .newsltr-text { padding:40px 30px; }
#newsletter-modal .newsltr-text h2, #newsletter-modal .newsltr-text .h2 { font-weight:700; font-size:18px; text-transform:uppercase; margin:0 0 15px 0; color:#111; letter-spacing:0.05px; }
#newsletter-modal .newsltr-text .input-group { display:block; margin:0 auto 20px; position:relative; width:100%; }
#newsletter-modal .newsltr-text .input-group input { width:100%; margin:0 0 10px 0; }
#newsletter-modal .newsltr-text p.sub-text { max-width:400px; margin:0 auto 20px; font-size:13px; line-height:1.3; }
#newsletter-modal .newsltr-text .btn.mcNsBtn { width:100%; }
#newsletter-modal .newsltr-text .social-icons { margin:0 0 20px; display: inline-flex; align-items: center; }
#newsletter-modal .newsltr-text .social-icons li { display:inline-block; margin:0 5px; list-style:none; }
#newsletter-modal .newsltr-text .social-icons li a { color:#111111; font-size:18px; display:inline-block; width:auto; height:auto; line-height:1; text-align:center; }
#newsletter-modal .newsltr-text .social-icons li a:hover { opacity:0.5; }
#newsletter-modal .newsltr-text .social-icons li a .anm { font-size:14px; }
#newsletter-modal .newsltr-text #Subscribe { width:100%; border:0; background:#111111; color:#ffffff; }
#newsletter-modal .newsltr-text #Subscribe:hover { opacity:0.9; }
#newsletter-modal .checkboxlink { font-size:11px; text-transform:uppercase; }
#newsletter-modal .checkboxlink input[type="checkbox"] { vertical-align:middle; }
#newsletter-modal .checkboxlink label { margin-bottom: 0; padding-left: 3px; }
#newsletter-modal .wraptext { max-width:300px; margin:0 auto; }
#newsletter-modal.style2 { max-width:650px; }
#newsletter-modal.style2 .newsltr-tbl { display:table; table-layout:fixed; vertical-align:middle; margin:0 auto; }
#newsletter-modal.style2 .newsltr-tbl .newsltr-img,
#newsletter-modal.style2 .newsltr-tbl .newsltr-text { display:table-cell; vertical-align:middle; }
#newsletter-modal.style2 .newsltr-tbl .newsltr-img img { float:left; width:100%; }
#newsletter-modal.style2 .newsltr-tbl .wraptext { padding:0 20px; }
#newsletter-modal.style2 .newsletter__submit { width:100%; }
#newsletter-modal.style3 { max-width:600px; }
#newsletter-modal.style3 .wraptext { max-width: 100%; }
#newsletter-modal.style3 .newsltr-text { padding:30px; }
#newsletter-modal.style3 .input-group input { margin-bottom: 0; }
#newsletter-modal.style3 .inputbox { flex: 1; -webkit-flex: 1; -ms-flex: 1; }
#newsletter-modal.style3 .btn { padding: 9px 20px; }
.mfp-ready #newsletter-modal { transform:translate(-50%, -50%) scale(1); -webkit-transform:translate(-50%, -50%) scale(1); -ms-transform:translate(-50%, -50%) scale(1); }
#newsletter-modal .mfp-close { color:#111111; font-size:30px; opacity:1; }
.mfp-hide { display:none !important; }
.mfp-zoom-in .mfp-with-anim { opacity:0; transform:scale(0.9); -webkit-transform:scale(0.9); -ms-transform:scale(0.9); transition:all 0.3s ease-in-out; -webkit-transition:all 0.3s ease-in-out; -ms-transition:all 0.3s ease-in-out; }
.mfp-zoom-in.mfp-ready .mfp-with-anim { opacity:1; transform:scale(1); -webkit-transform:scale(1); -ms-transform:scale(1); }
.mfp-zoom-in.mfp-removing .mfp-with-anim { opacity:0; transform:scale(0.9); -webkit-transform:scale(0.9); -ms-transform:scale(0.9); }
@-webkit-keyframes ad-spin {
    0% { -webkit-transform:rotate(0deg); transform:rotate(0deg); }
    100% { -webkit-transform:rotate(359deg); transform:rotate(359deg); }
}
@keyframes ad-spin {
    0% { -webkit-transform:rotate(0deg); transform:rotate(0deg); }
    100% { -webkit-transform:rotate(359deg); transform:rotate(359deg); }
}
.ad-spin { animation:ad-spin 1.5s infinite linear; -webkit-animation:ad-spin 1.5s infinite linear; }
@keyframes scaleimg {
    0%, 100% { transform:scale(1); -webkit-transform:scale(1); -ms-transform:scale(1) }
    50% { transform:scale(1.2); -webkit-transform:scale(1); -ms-transform:scale(1); }
}

/*======================================================================
  23. Footer
========================================================================*/
#site-scroll { color:#fff; line-height:35px; cursor:pointer; font-size:20px; width:35px; height:35px; right:30px; position:fixed; text-align:center; transition:all 0.3s ease 0s; -moz-transition:all 0.3s ease 0s; -webkit-transition:all 0.3s ease 0s; bottom:40px; z-index:444; display:none; background:#333; }
#site-scroll i { line-height:35px; }

.template-index .footer { margin-top:0; }
.footer { margin-top:70px; }
.footer .footer-top { color:#fff; background-color:#2358ad; border-top: 1px solid #2358ad; padding-top:70px; padding-bottom:70px; }
.footer .footer-top .h4 { color:#fff; font-size:15px; letter-spacing:0.5px; margin:0 0 15px; text-transform:uppercase; font-weight:700; }
.footer .about-us-col img { margin-bottom:20px; max-width:150px; }
.footer .social-icons li { padding-right:10px; }
.footer .social-icons li a, .footer .social-icons .icon { color:#fff; font-size:18px; }

.footer-links ul { list-style:none; padding:0; margin:0; }
.footer-links li { margin:5px 0; }
.footer-links a, .footer a { color:#fff; }
.footer-links a:hover, .footer a:hover { color:#fff; }
.footer-links a:before { content: ""; display:inline-block; width:0px; height:3px; vertical-align:middle; background-color:#c1c1c1; }
.footer-links a:hover:before { width:4px; margin-right:3px; }

.footer-newsletter .newsletter-input { background:#fff; margin-bottom:10px; border:0; }
.footer-newsletter .btn { font-size:12px; }

.footer-bottom { background:#ffffff; color:#111111; clear:both; padding:20px 0; border-top:1px dotted #eeeeee; }
.footer-bottom .payment-icons { color:#999; float:right; font-size:28px; line-height:30px; text-align:right; }
.footer-bottom .copytext { float:left; line-height:30px; }
.footer-bottom .copytext a { color:#111111; }
.footer-bottom .copytext a:hover { color:#2358ad; }

.footer.footer-1 .about-us-col p { margin-bottom: 15px; }
@media (min-width:992px){
    .footer.footer-1 .col-lg-3 { max-width:20%; flex:0 0 20%; -webkit-flex:0 0 20%; }
}

.footer-2 .footer-top { background-color:#333333; color:#fff; border-top:1px solid #333; }
.footer-2 .footer-links a, .footer-2 .footer a, .footer-2 .footer-bottom a { color:#fff; }
.footer-2 .footer-bottom { background-color:#333333; color:#fff; border-top:1px solid #555; }
.footer-2 .footer-bottom .copytext a:hover { color:#ff802b; }
.footer-2 .contact-us-col .footer-logo { margin-bottom:20px; display:block; max-width:160px; }
.footer-2 .contact-us-col p { position:relative; padding-left:25px; }
.footer-2 .contact-us-col p i { position:absolute; left:0; top:3px; }

.footer-3.footer .newsletter-section { background:url(../images/news-bg.jpg) no-repeat 50% 50%; background-size:cover; background-attachment: fixed; }
.footer-3.footer .newsletter-section .btn { padding: 14px 20px 13px; font-weight: 600; }
.footer-3.footer .newsletter-section .newsletter-input { height: 50px; padding: 0 15px; }
.footer-3.footer .newsletter-section h2 { margin-bottom: 10px; }
.footer-3.footer .newsletter-section h2, .footer-3.footer .newsletter-section .section-header p { color: #fff; }
.footer-3.footer .info-section { background-color: #ffc64e; }
.footer-3.footer .info-section a { color: #000; font-weight: 600; }
.footer-3.footer .footer-top { background-color: #1f3f55; border-color:#1f3f55; }
.footer-3.footer .social-icons li { display:block; margin-bottom:5px }
.footer-3.footer .social-icons li a { font-size:13px; }
.footer-3.footer .social-icons li a i { font-size:13px; margin-right:7px; }
.footer-3.footer .footer-bottom { color: #fff; background-color: #1f3f55; border-color:#5d707d; }
.footer-3.footer .footer-bottom a { color: #fff; }
.footer-3.footer .footer-bottom .payment-icons { color: #fff; }

.footer-4.footer .footer-top, .footer-4 .footer-bottom { background-color: #3754b2; }
.footer-4.footer .footer-bottom { color: #fff; background-color: #3754b2; border-color:#7897fb; }
.footer-4.footer .footer-bottom a { color: #fff; }
.footer-4.footer .footer-bottom .payment-icons { color: #fff; }

.footer-5.footer .footer-top { border-color: #ececec; }
.footer-5.footer .footer-bottom { border-color: #b9b9b9; }
.footer-5.footer .footer-top, .footer-5 .footer-bottom { color: #000; background-color: #fff; }
.footer-5 .footer-links a, .footer-5.footer a { color: #000; }
.footer-5 .footer-links a:hover, .footer-5.footer a:hover { color: #ee307c; }
.footer-5.footer .footer-top .h4 { color: #000; }
.footer-5 .footer-newsletter .newsletter-input { border: 1px solid #d7d7d7; }

.footer-6.footer .footer-top, .footer-6 .footer-bottom { background-color: #333e48; }
.footer-6.footer .footer-bottom { color: #fff; background-color: #333e48; border-color:#56616a; }
.footer-6.footer .footer-bottom a { color: #fff; }
.footer-6.footer .footer-bottom .payment-icons { color: #fff; }

.footer-7.footer .footer-top { background-color: #000; }
.footer-7.footer .footer-bottom { color: #000; background-color: #fff; border-color:#000; border: 0; }

.footer-8.footer .footer-top, .footer-8 .footer-bottom { background-color: #333; }
.footer-8.footer .footer-bottom { color: #fff; background-color: #2f2f2f; border-color:#2f2f2f; }
.footer-8.footer .footer-bottom a { color: #fff; }
.footer-8.footer .footer-bottom .payment-icons { color: #fff; }

.blur-up.lazyloaded { -webkit-filter:blur(0); filter:blur(0); }
.blur-up { -webkit-filter:blur(5px); filter:blur(5px); transition:filter 400ms, -webkit-filter 400ms; -webkit-transition:filter 400ms, -webkit-filter 400ms; }

.modal-backdrop.show { opacity: .8; }

.home-policy {
    text-align: center;
}
.home-policy i{
    font-size: 40px;
}
.home-policy h5 {
    margin: 10px 0 5px;
    font-size: 15px;
    text-transform: uppercase;

}
.my-address .my-address-item:not(:last-child) {
    border-bottom: 1px solid #ebebeb;
}
.product-slider.tab-slider-product .tab-slider-ul {
    border-bottom: 1px solid #dee2e6;
    padding: 4px 0;
}
.table-responsive.my-review .product-info {
    max-width: 400px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
}
.review-detail .user-review {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
/*======================================================================
  24. Checkout Progress
========================================================================*/
.user-track-order .track-order-step {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

    .user-track-order .track-order-step .step-item {
        text-align: center;
        padding: 10px;
        flex: 1;
        position: relative;
        z-index: 1;
    }

        .user-track-order .track-order-step .step-item.completed::before {
            background: var(--bs-danger);
        }

        .user-track-order .track-order-step .step-item::before {
            content: "";
            position: absolute;
            width: 100%;
            height: 5px;
            background: var(--theme-color);
            left: 0;
            top: 50px;
            z-index: -1;
        }

        .user-track-order .track-order-step .step-item:last-child:before {
            width: 50%;
        }

        .user-track-order .track-order-step .step-item:first-child:before {
            width: 50%;
            left: unset;
            right: 0;
        }

    .user-track-order .track-order-step .step-icon {
        width: 80px;
        height: 80px;
        line-height: 80px;
        text-align: center;
        background: var(--theme-color);
        color: var(--bs-white);
        font-size: 35px;
        border-radius: 50%;
        margin: 0 auto;
    }

    .user-track-order .track-order-step .step-item h6 {
        margin-top: 15px;
        font-size: 14px;
    }
    .user-track-order .track-order-step .step-item p{
        color:#999;
    }

    .user-track-order .track-order-step .step-item.completed .step-icon {
        background: var(--bs-danger);
    }
@media all and (max-width:767px) {
    .user-track-order .track-order-step {
        flex-wrap: nowrap;
        flex-direction: column
    }

        .user-track-order .track-order-step .step-item {
            display: flex;
            text-align: left;
        }

            .user-track-order .track-order-step .step-item::before {
                width: 5px;
                height: 50%;
                left: 38px;
                top: 53px;
            }

            .user-track-order .track-order-step .step-item:first-child:before {
                width: 5px;
                left: 38px;
                right: 0;
            }

            .user-track-order .track-order-step .step-item:last-child:before {
                display: none;
            }

        .user-track-order .track-order-step .step-icon {
            width: 60px;
            height: 60px;
            line-height: 60px;
            margin: 0 10px 0 0;
        }
}

.package-info .package-item{
    margin-right:10px;
}
.package-info .package-item .package-item-title{
    color:#999;
}