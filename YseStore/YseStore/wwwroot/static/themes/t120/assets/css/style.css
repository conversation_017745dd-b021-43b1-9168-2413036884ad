/*============================================
	Project Name : Inspina
	Company Name : G-Projects
	Company URL: https://themeforest.net/user/g-projects
    Project Description: Inspina is a Universal Shop HTML Template
============================================*/
/*============================================
    START TABLE OF CONTENT
==============================================
    1.  Template Fonts
    2.  Menu Styles
    3.  Standart Styles
    4.  Forms Styles
    5.  Tables Styles
    6.  Buttons Styles
    7.  Tabs Styles
    8.  Animation Styles
    9.  Card Styles
    10. Accordion Styles
    11. Pagination Styles
    12. Comments Styles
    13. Tooltip Styles
    14. Bad<PERSON> & Alerts Styles
    15. IsiToast Styles
    16. Carousel & Slider Styles
    17. Sidebar & Widgets Styles
    18. Steps Styles
    19. Isotope Styles
    20. Topbar & Navbar Styles
    21. Titles & Breadcrumbs Styles
    22. Account Styles
    23. Footer Styles
    24. Products Styles
    25. Checkout Styles
    26. Blog Styles
    27. Icons Styles
    28. Inner Styles
==============================================
    END TABLE OF CONTENT
============================================*/
/*--------------------------------------------
1. Template Fonts
--------------------------------------------*/
@import url(../../../../../../fonts.googleapis.com/css�family=Maven+Pro_400,500,700,900.css0");

/*--------------------------------------------
2. Menu Styles
--------------------------------------------*/
.sub-menu, .toolbar-dropdown {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    width: 240px;
    margin-top: -7px !important;
    padding: 10px 0 !important;
    border: 1px solid #e1e7ec;
    border-radius: 5px;
    background-color: #fff;
    line-height: 1.5;
    box-shadow: 0 7px 22px -5px rgba(47, 56, 68, 0.2)
}

.sub-menu > li, .toolbar-dropdown > li, .mobile-account-menu > li {
    display: block;
    position: relative
}

.sub-menu > li > a, .toolbar-dropdown > li > a, .mobile-account-menu > li > a {
    display: block;
    padding: 5px 20px !important;
    transition: color .3s;
    color: #606975;
    font-size: 14px;
    font-weight: 500;
    text-align: left;
    text-decoration: none
}

.sub-menu > li > a > i, .toolbar-dropdown > li > a > i {
    display: inline-block;
    margin-top: -4px;
    margin-right: 4px;
    font-size: 15px;
    vertical-align: middle;
}

.sub-menu > li.sub-menu-user, .toolbar-dropdown > li.sub-menu-user {
    display: table;
    width: 100%;
    padding: 4px 17px 6px
}

.sub-menu > li.sub-menu-user .user-ava, .toolbar-dropdown > li.sub-menu-user .user-ava, .sub-menu > li.sub-menu-user .user-info, .toolbar-dropdown > li.sub-menu-user .user-info {
    display: table-cell;
    vertical-align: middle
}

.sub-menu > li.sub-menu-user .user-ava, .toolbar-dropdown > li.sub-menu-user .user-ava {
    width: 44px
}

.sub-menu > li.sub-menu-user .user-ava > img, .toolbar-dropdown > li.sub-menu-user .user-ava > img {
    display: block;
    width: 44px;
    padding: 3px;
    border: 1px solid #e1e7ec;
    border-radius: 50%
}

.sub-menu > li.sub-menu-user .user-info, .toolbar-dropdown > li.sub-menu-user .user-info {
    padding-left: 8px
}

.sub-menu > li.sub-menu-user .user-info > .user-name, .toolbar-dropdown > li.sub-menu-user .user-info > .user-name {
    margin-bottom: 2px;
    font-size: 14px
}

.sub-menu > li.sub-menu-user .user-info > span, .toolbar-dropdown > li.sub-menu-user .user-info > span {
    display: block
}

.sub-menu > li.sub-menu-separator, .toolbar-dropdown > li.sub-menu-separator {
    margin-top: 5px;
    margin-bottom: 3px;
    border-top: 1px solid #e1e7ec
}

.sub-menu > li:hover > a, .toolbar-dropdown > li:hover > a {
    color: #fa6800
}

.sub-menu > li.active > a, .toolbar-dropdown > li.active > a {
    color: #fa6800
}

.sub-menu > li.has-children > a, .toolbar-dropdown > li.has-children > a {
    padding-right: 35px !important
}

.sub-menu > li.has-children > a::after, .toolbar-dropdown > li.has-children > a::after {
    display: block;
    position: absolute;
    top: 50%;
    right: 18px;
    width: 0;
    height: 0;
    margin-top: -4px;
    border-top: .3em solid transparent;
    border-bottom: .3em solid transparent;
    border-left: .32em solid;
    content: ''
}

.sub-menu > li > .sub-menu, .toolbar-dropdown > li > .sub-menu {
    top: -4px;
    left: 100%;
    margin-left: -5px
}

.mega-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    margin-top: -1px !important;
    border: 1px solid #e1e7ec;
    background-color: #fff;
    box-shadow: 0 7px 22px -5px rgba(47, 56, 68, 0.2);
    table-layout: fixed
}

.mega-menu > li {
    display: table-cell !important;
    position: relative;
    padding: 25px !important;
    border-left: 1px solid #e1e7ec;
    vertical-align: top
}

.mega-menu > li .mega-menu-title {
    display: block;
    margin-bottom: 16px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e1e7ec;
    color: #9da9b9;
    font-weight: 500;
    text-transform: uppercase;
    text-align: left
}

.mega-menu > li:first-child {
    border-left: 0
}

.mega-menu .sub-menu {
    display: block !important;
    position: relative;
    width: 100%;
    padding: 0 !important;
    border: 0;
    border-radius: 0;
    background-color: transparent;
    box-shadow: none
}

.mega-menu .sub-menu > li > a {
    padding: 5px 0 !important
}

.offcanvas-menu {
    position: relative;
    width: 100%;
    overflow: hidden
}

.offcanvas-menu ul {
    margin: 0;
    padding: 0;
    list-style: none
}

.offcanvas-menu ul.menu {
    position: relative;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    transition: all 0.4s cubic-bezier(0.86, 0, 0.07, 1)
}

.offcanvas-menu ul.menu.off-view {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0)
}

.offcanvas-menu ul.menu.in-view {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

.offcanvas-menu ul li {
    display: block
}

.offcanvas-menu ul li a {
    display: block;
    padding: 15px 20px;
    transition: color .3s;
    border-bottom: 1px solid rgba(255, 255, 255, 0.12);
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: .05em;
    text-transform: uppercase;
    text-decoration: none
}

.offcanvas-menu ul li a:hover {
    color: #fa6800
}

.offcanvas-menu ul li.back-btn > a {
    background-color: #313b47;
    color: #fff
}

.offcanvas-menu ul li.back-btn > a::before {
    display: inline-block;
    width: 0;
    height: 0;
    margin-right: 8px;
    border-top: .32em solid transparent;
    border-right: .37em solid;
    border-bottom: .32em solid transparent;
    content: '';
    opacity: .75
}

.offcanvas-menu ul li.active > a {
    color: #fa6800
}

.offcanvas-menu ul li.has-children > span {
    display: block;
    position: relative;
    width: 100%
}

.offcanvas-menu ul li.has-children > span > a {
    padding-right: 65px
}

.offcanvas-menu ul li.has-children.active > span > a {
    color: #fa6800
}

.offcanvas-menu ul li.has-children .sub-menu-toggle {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 100%;
    height: calc(100% - 1px);
    transition: background-color .3s;
    border-left: 1px solid rgba(255, 255, 255, 0.12);
    color: rgba(255, 255, 255, 0.75);
    cursor: pointer;
    z-index: 1
}

.offcanvas-menu ul li.has-children .sub-menu-toggle::before {
    display: block;
    position: absolute;
    top: 50%;
    right: 25px;
    width: 0;
    height: 0;
    margin-top: -5px;
    border-top: .35em solid transparent;
    border-bottom: .35em solid transparent;
    border-left: .4em solid;
    content: ''
}

.offcanvas-menu ul li.has-children .sub-menu-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1)
}

.offcanvas-menu ul li.has-children > .offcanvas-submenu {
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: auto;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    transition: all 0.4s cubic-bezier(0.86, 0, 0.07, 1)
}

.offcanvas-menu ul li.has-children > .offcanvas-submenu.in-view {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

.offcanvas-menu ul li.has-children > .offcanvas-submenu.off-view {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0)
}

@-webkit-keyframes submenu-show {
    from {
        -webkit-transform: scale(0.85);
        transform: scale(0.85);
        opacity: 0
    }
    to {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
    }
}

@keyframes submenu-show {
    from {
        -webkit-transform: scale(0.85);
        transform: scale(0.85);
        opacity: 0
    }
    to {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
    }
}

@-webkit-keyframes megamenu-show {
    from {
        -webkit-transform: translate3d(0, -9px, 0);
        transform: translate3d(0, -9px, 0);
        opacity: 0
    }
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

@keyframes megamenu-show {
    from {
        -webkit-transform: translate3d(0, -9px, 0);
        transform: translate3d(0, -9px, 0);
        opacity: 0
    }
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

/*--------------------------------------------
3. Standart Styles
--------------------------------------------*/
html * {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

body {
    background-position: center;
    background-color: #fff;
    background-repeat: no-repeat;
    background-size: cover;
    color: #606975;
    font-family: "Maven Pro", Helvetica, Arial, sans-serif;
    font-size: 14px;
    font-weight: normal;
    text-transform: none;
    line-height: 1.5;
}

a {
    color: #fa6800;
    text-decoration: underline
}

a:hover {
    color: #fa6800;
    text-decoration: none
}

a:focus {
    outline: none
}

.small, small {
    font-size: 85%
}

.navi-link, .navi-link-light {
    transition: color .3s;
    color: #606975;
    text-decoration: none
}

.navi-link:hover, .navi-link-light:hover {
    color: #fa6800
}

.navi-link-light {
    color: #fff
}

img, figure {
    max-width: 100%;
    height: auto;
    vertical-align: middle
}

svg {
    max-width: 100%
}

iframe {
    width: 100%
}

* {
    box-sizing: border-box
}

*::before, *::after {
    box-sizing: border-box
}

hr {
    margin: 0;
    border: 0;
    border-top: 1px solid #e1e7ec
}

hr.hr-light {
    border-top-color: rgba(255, 255, 255, 0.12)
}

pre {
    display: block;
    padding: 15px;
    border: 1px solid #e1e7ec;
    border-radius: 7px;
    background-color: #f5f5f5
}

::-moz-selection {
    background: #374250;
    color: #fff
}

::selection {
    background: #374250;
    color: #fff
}

::-moz-selection {
    background: #374250;
    color: #fff
}

figure {
    position: relative;
    margin: 0
}

figure figcaption {
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    margin: 0;
    padding: 12px;
    font-size: 14px
}

@media (min-width: 1200px) {
    .container {
        width: 1170px;
        max-width: 1170px
    }
}

@media (max-width: 1200px) {
    .container {
        width: 100% !important;
        max-width: 100% !important
    }
}
@media (min-width: 1500px) {
    .container {
        width: 1300px;
        max-width: 1300px;
    }
}
@media (min-width: 1600px) {
    .container {
        width: 1500px;
        max-width: 1500px;
    }
}
.container-fluid {
    max-width: 1920px;
    margin-right: auto;
    margin-left: auto;
    padding-right: 30px;
    padding-left: 30px
}

@media (max-width: 1200px) {
    .container-fluid {
        padding: 0 15px
    }
}

.caret {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 2px;
    border-top: 4px dashed;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
    vertical-align: middle
}

.close {
    transition: opacity .25s;
    border: 0;
    background: 0;
    color: #606975;
    font-family: sans-serif;
    font-size: 18px;
    cursor: pointer
}

.close:hover {
    opacity: .6
}

.icon-medal {
    display: inline-block;
    width: 14px;
    height: 14px;
    background-position: center;
    background-image: url(data:image/svg+xml;utf8;base64,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);
    background-repeat: no-repeat;
    background-size: 14px;
    line-height: 1
}

.position-relative {
    position: relative !important
}

.position-absolute {
    position: absolute !important
}

.position-fixed {
    position: fixed !important
}

.position-static {
    position: static !important
}

.top-0 {
    top: 0
}

.right-0 {
    right: 0
}

.bottom-0 {
    bottom: 0
}

.left-0 {
    left: 0
}

.w-90 {
    width: 90px !important
}

.w-110 {
    width: 110px !important
}

.w-150 {
    width: 150px !important
}

.w-200 {
    width: 200px !important
}

.w-250 {
    width: 250px !important
}

.w-270 {
    width: 270px !important
}

.w-300 {
    width: 300px !important
}

.col-sm-6.horizontal-center {
    margin: 0 auto !important;
}

.border-default {
    border: 1px solid #e1e7ec
}

.border-default.border-light {
    border-color: rgba(255, 255, 255, 0.12)
}

.border-0 {
    border: 0 !important
}

.border-top-0 {
    border-top: 0 !important
}

.border-right-0 {
    border-right: 0 !important
}

.border-bottom-0 {
    border-bottom: 0 !important
}

.border-left-0 {
    border-left: 0 !important
}

.rounded {
    border-radius: 7px
}

.rounded-top {
    border-top-left-radius: 7px;
    border-top-right-radius: 7px
}

.rounded-right {
    border-top-right-radius: 7px;
    border-bottom-right-radius: 7px
}

.rounded-bottom {
    border-bottom-left-radius: 7px;
    border-bottom-right-radius: 7px
}

.rounded-left {
    border-top-left-radius: 7px;
    border-bottom-left-radius: 7px
}

.rounded-circle {
    border-radius: 50%
}

.rounded-0 {
    border-radius: 0 !important
}

.rounded-top-0 {
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important
}

.rounded-right-0 {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important
}

.rounded-bottom-0 {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important
}

.rounded-left-0 {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important
}

.box-shadow {
    box-shadow: 0 7px 22px -5px rgba(47, 56, 68, 0.2)
}

.img-thumbnail {
    padding: 5px !important;
    border: 1px solid #e1e7ec;
    border-radius: 7px;
    background-color: #fff
}

.img-thumbnail.rounded-circle {
    border-radius: 50%
}

.img-cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover
}

[class^='col-'] .img-cover {
    left: 15px;
    width: calc(100% - 30px)
}

.opacity-100 {
    opacity: 1 !important
}

.opacity-90 {
    opacity: .9 !important
}

.opacity-80 {
    opacity: .8 !important
}

.opacity-75 {
    opacity: .75 !important
}

.opacity-60 {
    opacity: .6 !important
}

.opacity-50 {
    opacity: .5 !important
}

.opacity-25 {
    opacity: .25 !important
}

.opacity-15 {
    opacity: .15 !important
}

.clearfix::after {
    display: block;
    clear: both;
    content: ''
}

.text-muted {
    color: #9da9b9 !important
}

.text-primary {
    color: #fa6800 !important
}

.text-info {
    color: #50c6e9 !important
}

.text-warning {
    color: #ffb74f !important
}

.text-success {
    color: #43d9a3 !important
}

.text-danger {
    color: #ff5252 !important
}

.text-gray-dark {
    color: #374250 !important
}

.text-body {
    color: #606975 !important
}

.text-light, .text-white {
    color: #fff !important
}

.text-highlighted {
    background-color: #fff8b0 !important
}

.text-decoration-none {
    text-decoration: none !important
}

.text-crossed {
    text-decoration: line-through !important
}

.text-shadow {
    text-shadow: 0 1px rgba(0, 0, 0, 0.5) !important
}

.text-black {
    font-weight: 900 !important
}

.text-bold {
    font-weight: bold !important
}

.text-medium {
    font-weight: 500 !important
}

h2.text-normal.with-side, span.h2.with-side {
    margin-bottom: 10px;
    font-size: 22px;
}

h2.padding-top-1x.with-side {
    padding-top: 10px;
}

.text-normal {
    font-weight: normal !important
}

.text-thin {
    font-weight: 300 !important
}

.text-uppercase {
    letter-spacing: .04em
}

.bg-primary {
    background-color: #fa6800 !important
}

.bg-success {
    background-color: #43d9a3 !important
}

.bg-info {
    background-color: #50c6e9 !important
}

.bg-warning {
    background-color: #ffb74f !important
}

.bg-danger {
    background-color: #ff5252 !important
}

.bg-inverse {
    background-color: #374250 !important
}

.bg-faded, .bg-secondary {
    background-color: #f5f5f5 !important
}

.bg-dark {
    background-color: #374250 !important
}

.bg-white {
    background-color: #fff !important
}

.bg-no-repeat {
    background-repeat: no-repeat
}

.bg-repeat {
    background-repeat: repeat
}

.bg-center {
    background-position: center
}

.bg-cover {
    background-size: cover
}

.border-primary {
    border-color: #fa6800 !important
}

.border-success {
    border-color: #43d9a3 !important
}

.border-info {
    border-color: #50c6e9 !important
}

.border-warning {
    border-color: #ffb74f !important
}

.border-danger {
    border-color: #ff5252 !important
}

.border-secondary {
    border-color: #f5f5f5 !important
}

.border-dark {
    border-color: #374250 !important
}

.padding-top-1x {
    padding-top: 24px !important
}

@media (max-width: 768px) {
    .padding-top-1x {
        padding-top: 16px !important
    }
    .home-cat{
        margin-bottom: 30px;
    }
    .shop-cat{
        margin: 0 auto;
    }
    .single-shop{
        margin-top: 20px;
    }
    .order-sum{
        margin-top: 20px;
    }
}

.padding-top-2x {
    padding-top: 48px !important
}

@media (max-width: 768px) {
    .padding-top-2x {
        padding-top: 32px !important
    }
}

.padding-top-3x {
    padding-top: 72px !important
}

@media (max-width: 768px) {
    .padding-top-3x {
        padding-top: 48px !important
    }
}

.padding-top-4x {
    padding-top: 96px !important
}

@media (max-width: 768px) {
    .padding-top-4x {
        padding-top: 64px !important
    }
}

.padding-top-5x {
    padding-top: 120px !important
}

@media (max-width: 768px) {
    .padding-top-5x {
        padding-top: 80px !important
    }
}

.padding-top-6x {
    padding-top: 144px !important
}

@media (max-width: 768px) {
    .padding-top-6x {
        padding-top: 96px !important
    }
}

.padding-top-7x {
    padding-top: 168px !important
}

@media (max-width: 768px) {
    .padding-top-7x {
        padding-top: 112px !important
    }
}

.padding-top-8x {
    padding-top: 192px !important
}

@media (max-width: 768px) {
    .padding-top-8x {
        padding-top: 128px !important
    }
}

.padding-top-9x {
    padding-top: 216px !important
}

@media (max-width: 768px) {
    .padding-top-9x {
        padding-top: 144px !important
    }
}

.padding-top-10x {
    padding-top: 240px !important
}

@media (max-width: 768px) {
    .padding-top-10x {
        padding-top: 160px !important
    }
}

.padding-bottom-1x {
    padding-bottom: 24px !important
}

@media (max-width: 768px) {
    .padding-bottom-1x {
        padding-bottom: 16px !important
    }
}

.padding-bottom-2x {
    padding-bottom: 48px !important
}

@media (max-width: 768px) {
    .padding-bottom-2x {
        padding-bottom: 32px !important
    }
}

.padding-bottom-3x {
    padding-bottom: 72px !important
}

@media (max-width: 768px) {
    .padding-bottom-3x {
        padding-bottom: 48px !important
    }
}

.padding-bottom-4x {
    padding-bottom: 96px !important
}

@media (max-width: 768px) {
    .padding-bottom-4x {
        padding-bottom: 64px !important
    }
}

.padding-bottom-5x {
    padding-bottom: 120px !important
}

@media (max-width: 768px) {
    .padding-bottom-5x {
        padding-bottom: 80px !important
    }
}

.padding-bottom-6x {
    padding-bottom: 144px !important
}

@media (max-width: 768px) {
    .padding-bottom-6x {
        padding-bottom: 96px !important
    }
}

.padding-bottom-7x {
    padding-bottom: 168px !important
}

@media (max-width: 768px) {
    .padding-bottom-7x {
        padding-bottom: 112px !important
    }
}

.padding-bottom-8x {
    padding-bottom: 192px !important
}

@media (max-width: 768px) {
    .padding-bottom-8x {
        padding-bottom: 128px !important
    }
}

.padding-bottom-9x {
    padding-bottom: 216px !important
}

@media (max-width: 768px) {
    .padding-bottom-9x {
        padding-bottom: 144px !important
    }
}

.padding-bottom-10x {
    padding-bottom: 240px !important
}

@media (max-width: 768px) {
    .padding-bottom-10x {
        padding-bottom: 160px !important
    }
}

.margin-top-1x {
    margin-top: 24px !important
}

@media (max-width: 768px) {
    .margin-top-1x {
        margin-top: 16px !important
    }
}

.margin-top-2x {
    margin-top: 48px !important
}

@media (max-width: 768px) {
    .margin-top-2x {
        margin-top: 32px !important
    }
}

.margin-top-3x {
    margin-top: 72px !important
}

@media (max-width: 768px) {
    .margin-top-3x {
        margin-top: 48px !important
    }
}

.margin-top-4x {
    margin-top: 96px !important
}

@media (max-width: 768px) {
    .margin-top-4x {
        margin-top: 64px !important
    }
}

.margin-top-5x {
    margin-top: 120px !important
}

@media (max-width: 768px) {
    .margin-top-5x {
        margin-top: 80px !important
    }
}

.margin-top-6x {
    margin-top: 144px !important
}

@media (max-width: 768px) {
    .margin-top-6x {
        margin-top: 96px !important
    }
}

.margin-top-7x {
    margin-top: 168px !important
}

@media (max-width: 768px) {
    .margin-top-7x {
        margin-top: 112px !important
    }
}

.margin-top-8x {
    margin-top: 192px !important
}

@media (max-width: 768px) {
    .margin-top-8x {
        margin-top: 128px !important
    }
}

.margin-top-9x {
    margin-top: 216px !important
}

@media (max-width: 768px) {
    .margin-top-9x {
        margin-top: 144px !important
    }
}

.margin-top-10x {
    margin-top: 240px !important
}

@media (max-width: 768px) {
    .margin-top-10x {
        margin-top: 160px !important
    }
}

.margin-bottom-1x {
    margin-bottom: 24px !important
}

@media (max-width: 768px) {
    .margin-bottom-1x {
        margin-bottom: 16px !important
    }
}

.margin-bottom-2x {
    margin-bottom: 48px !important
}

@media (max-width: 768px) {
    .margin-bottom-2x {
        margin-bottom: 32px !important
    }
}

.margin-bottom-3x {
    margin-bottom: 72px !important
}

@media (max-width: 768px) {
    .margin-bottom-3x {
        margin-bottom: 48px !important
    }
}

.margin-bottom-4x {
    margin-bottom: 96px !important
}

@media (max-width: 768px) {
    .margin-bottom-4x {
        margin-bottom: 64px !important
    }
}

.margin-bottom-5x {
    margin-bottom: 120px !important
}

@media (max-width: 768px) {
    .margin-bottom-5x {
        margin-bottom: 80px !important
    }
}

.margin-bottom-6x {
    margin-bottom: 144px !important
}

@media (max-width: 768px) {
    .margin-bottom-6x {
        margin-bottom: 96px !important
    }
}

.margin-bottom-7x {
    margin-bottom: 168px !important
}

@media (max-width: 768px) {
    .margin-bottom-7x {
        margin-bottom: 112px !important
    }
}

.margin-bottom-8x {
    margin-bottom: 192px !important
}

@media (max-width: 768px) {
    .margin-bottom-8x {
        margin-bottom: 128px !important
    }
}

.margin-bottom-9x {
    margin-bottom: 216px !important
}

@media (max-width: 768px) {
    .margin-bottom-9x {
        margin-bottom: 144px !important
    }
}

.margin-bottom-10x {
    margin-bottom: 240px !important
}

@media (max-width: 768px) {
    .margin-bottom-10x {
        margin-bottom: 160px !important
    }
}

.mb-30 {
    margin-bottom: 30px !important
}

.mt-30 {
    margin-top: 30px !important
}

.margin-top-none {
    margin-top: 0 !important
}

.margin-bottom-none {
    margin-bottom: 0 !important
}

.padding-top-none {
    padding-top: 0 !important
}

.padding-bottom-none {
    padding-bottom: 0 !important
}

.margin-right-none {
    margin-right: 0 !important
}

.margin-left-none {
    margin-left: 0 !important
}

.padding-right-none {
    padding-right: 0 !important
}

.padding-left-none {
    padding-left: 0 !important
}

.hidden-xs-up {
    display: none !important
}

@media (max-width: 575px) {
    .hidden-xs-down {
        display: none !important
    }
}

@media (min-width: 576px) {
    .hidden-sm-up {
        display: none !important
    }
}

@media (max-width: 767px) {
    .hidden-sm-down {
        display: none !important
    }
}

@media (min-width: 768px) {
    .hidden-md-up {
        display: none !important
    }
}

@media (max-width: 991px) {
    .hidden-md-down {
        display: none !important
    }
}

@media (min-width: 992px) {
    .hidden-lg-up {
        display: none !important
    }
}

@media (max-width: 1199px) {
    .hidden-lg-down {
        display: none !important
    }
}

@media (min-width: 1200px) {
    .hidden-xl-up {
        display: none !important
    }
}

.hidden-xl-down {
    display: none !important
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
    margin: 0;
    color: #374250;
    font-family: inherit;
    font-style: normal;
    font-weight: 500;
    text-transform: none
}

h1 small, h2 small, h3 small, h4 small, h5 small, h6 small, .h1 small, .h2 small, .h3 small, .h4 small, .h5 small, .h6 small {
    display: block;
    padding-top: 3px;
    color: #9da9b9
}

h1, .h1 {
    margin-bottom: 24px;
    font-size: 36px;
    line-height: 1.15
}

@media (max-width: 768px) {
    h1, .h1 {
        font-size: 31px
    }
}

h2, .h2 {
    margin-bottom: 16px;
    font-size: 30px;
    line-height: 1.2
}

@media (max-width: 768px) {
    h2, .h2 {
        font-size: 28px
    }
}

h3, .h3 {
    margin-bottom: 12px;
    font-size: 24px;
    line-height: 1.25
}

@media (max-width: 768px) {
    h3, .h3 {
        font-size: 22px
    }
}

h4, .h4 {
    margin-bottom: 8px;
    font-size: 20px;
    line-height: 1.3
}

h5, .h5 {
    margin-bottom: 8px;
    font-size: 18px;
    line-height: 1.35
}

h6, .h6 {
    margin-bottom: 6px;
    font-size: 16px;
    line-height: 1.4
}

.display-1, .display-2, .display-3, .display-4 {
    font-weight: normal;
    line-height: 1.15
}

.display-1 {
    font-size: 72px
}

@media (max-width: 576px) {
    .display-1 {
        font-size: 57px
    }
}

.display-2 {
    font-size: 60px
}

@media (max-width: 576px) {
    .display-2 {
        font-size: 48px
    }
}

.display-3 {
    font-size: 50px
}

@media (max-width: 576px) {
    .display-3 {
        font-size: 42px
    }
}

.display-4 {
    font-size: 40px
}

@media (max-width: 576px) {
    .display-4 {
        font-size: 36px
    }
}

p {
    margin: 0 0 16px
}

.lead {
    font-size: 18px
}

.text-lg {
    font-size: 16px
}

.text-sm {
    font-size: 13px
}

.text-xs {
    font-size: 12px
}

ul, ol {
    margin-top: 0;
    margin-bottom: 16px;
    padding-left: 18px;
    line-height: 1.8
}

ul ul, ul ol, ol ul, ol ol {
    margin-bottom: 0
}

.list-unstyled {
    padding-left: 0;
    list-style: none
}

.list-inline {
    padding-left: 0;
    list-style: none
}

.list-inline > li {
    display: inline-block;
    padding-right: 5px;
    padding-left: 5px
}

.list-icon {
    padding: 0;
    list-style: none
}

.list-icon > li {
    position: relative;
    margin-bottom: 6px;
    padding-left: 22px
}

.list-icon > li > i {
    display: block;
    position: absolute;
    left: 0;
    line-height: inherit;
    top: 2px;
}

.list-icon.text-lg > li {
    padding-left: 25px
}

.list-icon.text-sm > li {
    padding-left: 18px
}

.list-icon.lead > li {
    padding-left: 26px
}

dl {
    margin-top: 0;
    margin-bottom: 16px
}

dt, dd {
    line-height: 1.5
}

dt {
    padding-top: 9px;
    border-top: 1px solid #e1e7ec;
    color: #374250;
    font-weight: 500
}

dt:first-child {
    padding-top: 0;
    border: 0
}

dd {
    margin-top: 3px;
    margin-bottom: 15px;
    margin-left: 0
}

blockquote {
    position: relative;
    margin: 0;
    margin-bottom: 16px;
    padding: 24px 0 24px 36px;
    border-top: 1px solid #e1e7ec;
    border-bottom: 1px solid #e1e7ec;
    color: #606975;
    font-size: 18px
}

blockquote::before {
    position: absolute;
    top: 24px;
    left: -17px;
    color: #9da9b9;
    font-size: 82px;
    font-style: italic;
    line-height: .8;
    content: '"'
}

blockquote cite {
    display: block;
    margin-top: 9px;
    color: #9da9b9;
    font-size: 14px;
    font-style: normal;
    font-weight: normal
}

blockquote cite::before {
    display: inline-block;
    margin-top: -2px;
    margin-right: 8px;
    color: #9da9b9;
    font-family: feather;
    font-size: .95em;
    content: '\e074';
    vertical-align: middle
}

blockquote p {
    margin-bottom: 0
}

blockquote.blockquote-reverse {
    padding-right: 36px;
    padding-left: 0;
    border-left: 0;
    text-align: right
}

blockquote.blockquote-reverse::before {
    right: 10px;
    left: auto
}

kbd {
    background-color: #606975
}

/*--------------------------------------------
4. Forms Styles
--------------------------------------------*/
.form-control {
    padding: 0 18px 3px;
    border: 1px solid #dbe2e8;
    border-radius: 22px;
    background-color: #fff;
    color: #606975;
    font-family: "Maven Pro", Helvetica, Arial, sans-serif;
    font-size: 14px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.form-control:not(textarea) {
    height: 44px
}

.form-control::-moz-placeholder {
    color: #9da9b9;
    opacity: 1
}

.form-control:-ms-input-placeholder {
    color: #9da9b9
}

.form-control::-webkit-input-placeholder {
    color: #9da9b9
}

.form-control:focus {
    border-color: #fa6800;
    outline: none;
    color: #606975;
    box-shadow: none !important
}

.form-control[type='color'] {
    padding-bottom: 0 !important
}

.form-control:disabled, .form-control[readonly] {
    background-color: #f5f5f5;
    cursor: not-allowed
}

textarea.form-control {
    padding-top: 12px;
    padding-bottom: 12px
}

select.form-control {
    padding-right: 38px;
    background-position: center right 17px;
    background-image: url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMTYuMC4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogNi4wMCBCdWlsZCAwKSAgLS0+CjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiBpZD0iQ2FwYV8xIiB4PSIwcHgiIHk9IjBweCIgd2lkdGg9IjE2cHgiIGhlaWdodD0iMTZweCIgdmlld0JveD0iMCAwIDI5Mi4zNjIgMjkyLjM2MiIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgMjkyLjM2MiAyOTIuMzYyOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxnPgoJPHBhdGggZD0iTTI4Ni45MzUsNjkuMzc3Yy0zLjYxNC0zLjYxNy03Ljg5OC01LjQyNC0xMi44NDgtNS40MjRIMTguMjc0Yy00Ljk1MiwwLTkuMjMzLDEuODA3LTEyLjg1LDUuNDI0ICAgQzEuODA3LDcyLjk5OCwwLDc3LjI3OSwwLDgyLjIyOGMwLDQuOTQ4LDEuODA3LDkuMjI5LDUuNDI0LDEyLjg0N2wxMjcuOTA3LDEyNy45MDdjMy42MjEsMy42MTcsNy45MDIsNS40MjgsMTIuODUsNS40MjggICBzOS4yMzMtMS44MTEsMTIuODQ3LTUuNDI4TDI4Ni45MzUsOTUuMDc0YzMuNjEzLTMuNjE3LDUuNDI3LTcuODk4LDUuNDI3LTEyLjg0N0MyOTIuMzYyLDc3LjI3OSwyOTAuNTQ4LDcyLjk5OCwyODYuOTM1LDY5LjM3N3oiIGZpbGw9IiM2MDY5NzUiLz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8L3N2Zz4K);
    background-repeat: no-repeat;
    background-size: 9px 9px
}

select.form-control:not([size]):not([multiple]) {
    height: 44px
}

.form-group {
    margin-bottom: 20px !important
}

.form-group label {
    margin-bottom: 8px;
    padding-left: 18px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer
}

.form-group label.col-form-label {
    -ms-flex-item-align: center !important;
    align-self: center !important;
    margin-bottom: 0
}

.form-control-lg {
    border-radius: 27px;
    font-size: 16px
}

.form-control-lg:not(textarea) {
    height: 54px
}

.form-control-lg.form-control-rounded {
    border-radius: 7px
}

.form-control-lg.form-control-square {
    border-radius: 0
}

select.form-control.form-control-lg:not([size]):not([multiple]) {
    height: 54px
}

.form-control-sm {
    padding-bottom: 2px;
    border-radius: 18px
}

.form-control-sm:not(textarea) {
    height: 36px
}

.form-control-sm.form-control-rounded {
    border-radius: 3px
}

.form-control-sm.form-control-square {
    border-radius: 0
}

select.form-control.form-control-sm:not([size]):not([multiple]) {
    height: 36px
}

.form-text {
    padding-left: 0px
}

.custom-control {
    margin-bottom: 6px !important;
    padding-left: 1.5rem !important
}

.custom-control:focus {
    outline: 0
}

.custom-control .custom-control-label {
    margin: 0;
    padding-left: 0;
    font-size: 14px !important;
    font-weight: normal !important;
    text-transform: none
}

.custom-control .custom-control-label::before {
    box-shadow: none !important
}

.custom-control .custom-control-input:checked ~ .custom-control-label::before {
    background-color: #fa6800
}

.custom-control .custom-control-input:disabled ~ .custom-control-label {
    color: #9da9b9;
    cursor: not-allowed
}

.custom-control-inline {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.custom-file, .custom-file-input {
    height: 44px;
    border-radius: 22px;
    cursor: pointer
}

.custom-file:focus ~ .custom-file-label, .custom-file-input:focus ~ .custom-file-label {
    box-shadow: none
}

.custom-file-label {
    font-weight: normal !important
}

.custom-file-label, .custom-file-label::after {
    height: 44px;
    border-radius: 22px;
    border-color: #dbe2e8;
    color: #606975;
    line-height: 2.2
}

.custom-file-label::after {
    height: 42px;
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    background-color: #f5f5f5;
    font-weight: 500 !important
}

.form-control-rounded {
    border-radius: 5px
}

.form-control-rounded .custom-file-control, .form-control-rounded .custom-file-control::before {
    border-radius: 5px
}

.form-control-square {
    border-radius: 0
}

.form-control-square .custom-file-control, .form-control-square .custom-file-control::before {
    border-radius: 0
}

.input-group {
    display: block;
    position: relative
}

.input-group .input-group-addon, .input-group .input-group-btn {
    display: inline-block;
    position: absolute;
    top: 50%;
    margin-top: -3px;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%)
}

.input-group .input-group-addon {
    left: 15px;
    transition: color .3s;
    background-color: transparent !important;
    color: #9da9b9
}

.input-group .form-control {
    padding-left: 35px;
    width: 100%;
}

.input-group > .form-control:focus,
.input-group > .form-control:active {
    transition: all .4s;
}

.input-group .form-control:focus ~ .input-group-addon {
    color: #fa6800
}

.input-group .input-group-btn {
    right: 10px;
    margin-top: -2px
}

.input-group .input-group-btn button {
    transition: color .3s;
    border: 0;
    background: 0;
    color: #606975;
    font-size: 1.2em;
    cursor: pointer
}

.input-group .input-group-btn button:hover {
    color: #fa6800
}

.input-group .input-group-btn .btn {
    margin: 0;
    margin-right: 3px;
    padding: 0
}

.input-group .input-group-btn ~ .form-control {
    padding-right: 35px;
    padding-left: 18px
}

.has-success label, .has-success .form-control-feedback, .has-success .custom-control, .has-success .input-group .input-group-addon, .has-success.input-group .input-group-addon, .has-success .form-control:focus ~ .input-group-addon {
    color: #43d9a3
}

.has-success .form-control, .has-success .form-control:focus {
    border-color: #43d9a3
}

.has-success .form-control-success {
    padding-right: 42px;
    background-position: center right 15px;
    background-image: url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMTkuMC4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogNi4wMCBCdWlsZCAwKSAgLS0+CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiBpZD0iTGF5ZXJfMSIgeD0iMHB4IiB5PSIwcHgiIHZpZXdCb3g9IjAgMCA0MDYuODM0IDQwNi44MzQiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDQwNi44MzQgNDA2LjgzNDsiIHhtbDpzcGFjZT0icHJlc2VydmUiIHdpZHRoPSIxNnB4IiBoZWlnaHQ9IjE2cHgiPgo8cG9seWdvbiBwb2ludHM9IjM4NS42MjEsNjIuNTA3IDE0Ni4yMjUsMzAxLjkwMSAyMS4yMTMsMTc2Ljg5MSAwLDE5OC4xMDQgMTQ2LjIyNSwzNDQuMzI3IDQwNi44MzQsODMuNzIgIiBmaWxsPSIjNDNkOWEzIi8+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+Cjwvc3ZnPgo=);
    background-repeat: no-repeat
}

.has-warning label, .has-warning .form-control-feedback, .has-warning .custom-control, .has-warning .input-group .input-group-addon, .has-warning.input-group .input-group-addon, .has-warning .form-control:focus ~ .input-group-addon {
    color: #ffb74f
}

.has-warning .form-control, .has-warning .form-control:focus {
    border-color: #ffb74f
}

.has-warning .form-control-warning {
    padding-right: 42px;
    background-position: center right 15px;
    background-image: url(data:image/svg+xml;utf8;base64,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);
    background-repeat: no-repeat
}

.has-danger label, .has-danger .form-control-feedback, .has-danger .custom-control, .has-danger .input-group .input-group-addon, .has-danger.input-group .input-group-addon, .has-danger .form-control:focus ~ .input-group-addon {
    color: #ff5252
}

.has-danger .form-control, .has-danger .form-control:focus {
    border-color: #ff5252
}

.has-danger .form-control-danger {
    padding-right: 42px;
    background-position: center right 15px;
    background-image: url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMTkuMC4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogNi4wMCBCdWlsZCAwKSAgLS0+CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiBpZD0iQ2FwYV8xIiB4PSIwcHgiIHk9IjBweCIgdmlld0JveD0iMCAwIDI0MS4xNzEgMjQxLjE3MSIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgMjQxLjE3MSAyNDEuMTcxOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSIgd2lkdGg9IjE2cHgiIGhlaWdodD0iMTZweCI+CjxnPgoJPHBhdGggaWQ9IkNsb3NlIiBkPSJNMTM4LjEzOCwxMjAuNzU0bDk5LjExOC05OC41NzZjNC43NTItNC43MDQsNC43NTItMTIuMzE5LDAtMTcuMDExYy00Ljc0LTQuNzA0LTEyLjQzOS00LjcwNC0xNy4xNzksMCAgIGwtOTkuMDMzLDk4LjQ5MkwyMS4wOTUsMy42OTljLTQuNzQtNC43NTItMTIuNDM5LTQuNzUyLTE3LjE3OSwwYy00Ljc0LDQuNzY0LTQuNzQsMTIuNDc1LDAsMTcuMjI3bDk5Ljg3Niw5OS44ODhMMy41NTUsMjIwLjQ5NyAgIGMtNC43NCw0LjcwNC00Ljc0LDEyLjMxOSwwLDE3LjAxMWM0Ljc0LDQuNzA0LDEyLjQzOSw0LjcwNCwxNy4xNzksMGwxMDAuMTUyLTk5LjU5OWw5OS41NTEsOTkuNTYzICAgYzQuNzQsNC43NTIsMTIuNDM5LDQuNzUyLDE3LjE3OSwwYzQuNzQtNC43NjQsNC43NC0xMi40NzUsMC0xNy4yMjdMMTM4LjEzOCwxMjAuNzU0eiIgZmlsbD0iI2ZmNTI1MiIvPgoJPGc+Cgk8L2c+Cgk8Zz4KCTwvZz4KCTxnPgoJPC9nPgoJPGc+Cgk8L2c+Cgk8Zz4KCTwvZz4KCTxnPgoJPC9nPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+CjxnPgo8L2c+Cjwvc3ZnPgo=);
    background-size: 13px 13px;
    background-repeat: no-repeat
}

.form-control-feedback {
    padding-top: 2px;
    padding-left: 18px
}

.input-light.form-control, .input-light .form-control {
    border-color: rgba(255, 255, 255, 0.17);
    background-color: transparent;
    color: #fff
}

.input-light.form-control::-moz-placeholder, .input-light .form-control::-moz-placeholder {
    color: rgba(255, 255, 255, 0.5);
    opacity: 1
}

.input-light.form-control:-ms-input-placeholder, .input-light .form-control:-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.5)
}

.input-light.form-control::-webkit-input-placeholder, .input-light .form-control::-webkit-input-placeholder {
    color: rgba(255, 255, 255, 0.5)
}

.input-light.form-control:focus, .input-light .form-control:focus {
    border-color: #fa6800;
    color: #fff
}

.input-light.input-group .input-group-addon {
    color: rgba(255, 255, 255, 0.5)
}

.subscribe-form .input-group {
    width: calc(100% - 88px);
    float: left
}

.subscribe-form .btn {
    margin: 0;
    float: right
}

.subscribe-form .btn > i {
    margin-top: -6px;
    font-size: 1.5em
}

.subscribe-form .form-text {
    padding-top: 12px
}

.coupon-form .form-control {
    display: inline-block;
    width: 100%;
    max-width: 235px;
    margin-right: 12px
}

.coupon-form .btn {
    margin-right: 0
}

@media (max-width: 768px) {
    .coupon-form .form-control {
        display: block;
        max-width: 100%
    }
}

.login-box {
    display: block;
    width: 100%;
    padding: 30px 24px;
    border: 1px solid #e1e7ec;
    border-radius: 7px
}

.card-wrapper {
    margin: 30px 0
}

@media (max-width: 576px) {
    .jp-card-container {
        width: 285px !important
    }

    .jp-card {
        min-width: 250px !important
    }
}

/*--------------------------------------------
5. Tables Styles
--------------------------------------------*/
.table thead th, .table td, .table th {
    border-color: #e1e7ec
}

.table.table-inverse {
    background-color: #374250;
    color: #fff
}

.table.table-inverse thead th, .table.table-inverse td, .table.table-inverse th {
    border-color: rgba(255, 255, 255, 0.12)
}

.thead-inverse th {
    background-color: #374250;
    color: #fff
}

.thead-default th {
    background-color: #f5f5f5;
    color: #606975
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: #f5f5f5
}

.table-striped.table-inverse tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.08)
}

.table-hover tbody tr:hover {
    background-color: #f5f5f5
}

.table-hover.table-inverse tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.08)
}

.table-active, .table-active td, .table-active th {
    background-color: rgba(0, 0, 0, 0.05)
}

.table-success, .table-success td, .table-success th {
    background-color: rgba(67, 217, 163, 0.09)
}

.table-info, .table-info td, .table-info th {
    background-color: rgba(80, 198, 233, 0.09)
}

.table-warning, .table-warning td, .table-warning th {
    background-color: rgba(255, 183, 79, 0.09)
}

.table-danger, .table-danger td, .table-danger th {
    background-color: rgba(255, 82, 82, 0.09)
}

/*--------------------------------------------
6. Buttons Styles
--------------------------------------------*/
.btn {
    display: inline-block;
    position: relative;
    height: 44px;
    margin-top: 8px;
    margin-right: 12px;
    margin-bottom: 8px;
    padding: 0 22px;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    transition: all .4s;
    border: 1px solid transparent;
    border-radius: 22px;
    background-color: transparent;
    background-image: none;
    color: #606975;
    font-family: "Maven Pro", Helvetica, Arial, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    letter-spacing: .07em;
    line-height: 42px;
    white-space: nowrap;
    cursor: pointer;
    vertical-align: middle;
    text-transform: uppercase;
    text-decoration: none;
    text-align: center;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.btn:hover, .btn:focus .btn:active, .btn.active {
    outline: none;
    background-image: none;
    text-decoration: none;
    box-shadow: none
}

.btn:hover {
    color: #606975
}

.btn > i {
    display: inline-block;
    margin-top: -2px;
    vertical-align: middle
}

.btn[disabled], .btn.disabled {
    cursor: not-allowed;
    opacity: .55;
    pointer-events: none
}

button:focus {
    outline: none
}

.btn-lg {
    height: 54px;
    border-radius: 27px;
    line-height: 52px
}

.btn-sm {
    height: 36px;
    padding: 0 18px;
    border-radius: 18px;
    font-size: 12px;
    line-height: 34px
}

.btn-rounded {
    border-radius: 5px
}

.btn-rounded.btn-lg {
    border-radius: 7px
}

.btn-rounded.btn-sm {
    border-radius: 3px
}

.btn-square {
    border-radius: 0
}

.btn-secondary {
    background-color: #f5f5f5;
    border-color: #e1e7ec
}

.btn-secondary:hover {
    background-color: #dcdbdb
}

.btn-primary, .btn-success, .btn-info, .btn-warning, .btn-danger {
    color: #fff
}

.btn-primary:hover, .btn-primary:active, .btn-primary:focus, .btn-success:hover, .btn-success:active, .btn-success:focus, .btn-info:hover, .btn-info:active, .btn-info:focus, .btn-warning:hover, .btn-warning:active, .btn-warning:focus, .btn-danger:hover, .btn-danger:active, .btn-danger:focus {
    color: #fff
}

.btn-primary {
    background-color: #fa6800
}

.btn-primary:hover {
    background-color: #db5c00;
    border-color: #fa6800;
}

.btn-success {
    background-color: #43d9a3
}

.btn-success:hover {
    background-color: #27c28a
}

.btn-info {
    background-color: #50c6e9
}

.btn-info:hover {
    background-color: #23b7e3
}

.btn-warning {
    background-color: #ffb74f
}

.btn-warning:hover {
    background-color: #ffa21c
}

.btn-danger {
    background-color: #ff5252
}

.btn-danger:hover {
    background-color: #ff1f1f
}

.btn-white {
    background-color: #fff
}

.btn-white:hover {
    background-color: #e6e5e5
}

.btn-outline-secondary {
    border-color: #e1e7ec
}

.btn-outline-secondary:hover {
    background-color: #f5f5f5
}

.btn-outline-primary {
    border-color: #fa6800;
    background-color: transparent;
    color: #fa6800
}

.btn-outline-primary:hover {
    background-color: #fa6800;
    color: #fff;
    border-color: #db5c00;

}

.btn-outline-success {
    border-color: #43d9a3;
    background-color: transparent;
    color: #43d9a3
}

.btn-outline-success:hover {
    background-color: #43d9a3;
    color: #fff
}

.btn-outline-info {
    border-color: #50c6e9;
    background-color: transparent;
    color: #50c6e9
}

.btn-outline-info:hover {
    background-color: #50c6e9;
    color: #fff
}

.btn-outline-warning {
    border-color: #ffb74f;
    background-color: transparent;
    color: #ffb74f
}

.btn-outline-warning:hover {
    background-color: #ffb74f;
    color: #fff
}

.btn-outline-danger {
    border-color: #ff5252;
    background-color: transparent;
    color: #ff5252
}

.btn-outline-danger:hover {
    background-color: #ff5252;
    color: #fff
}

.btn-outline-white {
    border-color: #fff;
    background-color: transparent;
    color: #fff
}

.btn-outline-white:hover {
    background-color: #fff;
    color: #fff
}

.btn-outline-white:hover {
    color: #606975
}

.btn-link-secondary {
    color: #606975
}

.btn-link-secondary:hover {
    color: #495059
}

.btn-link-primary {
    color: #fa6800
}

.btn-link-primary:hover {
    color: #0a87bf
}

.btn-link-success {
    color: #43d9a3
}

.btn-link-success:hover {
    color: #27c28a
}

.btn-link-info {
    color: #50c6e9
}

.btn-link-info:hover {
    color: #23b7e3
}

.btn-link-warning {
    color: #ffb74f
}

.btn-link-warning:hover {
    color: #ffa21c
}

.btn-link-danger {
    color: #ff5252
}

.btn-link-danger:hover {
    color: #ff1f1f
}

.btn-link-white {
    color: #fff
}

.btn-link-white:hover {
    color: #e6e5e5
}

.btn-link-secondary > i, .btn-link-primary > i, .btn-link-success > i, .btn-link-info > i, .btn-link-warning > i, .btn-link-danger > i, .btn-link-white > i {
    margin-top: -4px
}

.btn-group {
    display: -ms-inline-flexbox;
    display: inline-flex;
    position: relative;
    margin-top: 8px;
    margin-right: 12px;
    margin-bottom: 8px;
    vertical-align: middle
}

.btn-group .btn {
    position: relative;
    -webkit-box-flex: 0;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto;
    margin: 0
}

.btn-group .btn:first-child:not(:last-child):not(.dropdown-toggle) {
    margin-right: -1px;
    padding-right: 12px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.btn-group .btn:first-child:not(:last-child):not(.dropdown-toggle).btn-secondary, .btn-group .btn:first-child:not(:last-child):not(.dropdown-toggle).btn-outline-secondary {
    border-right: 0
}

.btn-group > .dropdown-toggle:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.btn + .dropdown-toggle-split {
    padding-right: 15px;
    padding-left: 8px
}

.text-center .btn, .text-center .btn-group {
    margin-right: 6px;
    margin-left: 6px
}

.text-right .btn, .text-right .btn-group {
    margin-right: 0;
    margin-left: 12px
}

.btn-block {
    display: block;
    width: 100%;
    margin-top: 12px;
    margin-right: 0 !important;
    margin-bottom: 12px;
    margin-left: 0 !important;
    padding-right: 15px !important;
    padding-left: 15px !important
}

.scroll-to-top-btn {
    display: block;
    position: fixed;
    right: 16px;
    bottom: -88px;
    width: 44px;
    height: 44px;
    transition: bottom 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55), opacity 0.3s, background-color 0.3s, border-color 0.3s;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
    color: #fff;
    font-size: 18px;
    line-height: 39px;
    opacity: 0;
    z-index: 2000;
    text-align: center;
    text-decoration: none
}

.scroll-to-top-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff
}

.scroll-to-top-btn:focus, .scroll-to-top-btn:active {
    color: #fff
}

.scroll-to-top-btn.visible {
    bottom: 14px;
    opacity: 1
}

@media (max-width: 768px) {
    .scroll-to-top-btn {
        bottom: -72px;
        width: 36px;
        height: 36px;
        line-height: 31px
    }
}

.market-button {
    display: inline-block;
    margin-right: 14px;
    margin-bottom: 14px;
    padding: 5px 14px 5px 45px;
    transition: background-color .3s;
    border: 1px solid #e1e7ec;
    border-radius: 5px;
    background-position: center left 12px;
    background-color: #fff;
    background-size: 24px 24px;
    background-repeat: no-repeat;
    text-decoration: none
}

.market-button:hover {
    background-color: #f5f5f5
}

.market-button .mb-subtitle {
    display: block;
    margin-bottom: -4px;
    color: #9da9b9;
    font-size: 12px
}

.market-button .mb-title {
    display: block;
    color: #606975;
    font-size: 18px
}

.market-button.apple-button {
    background-image: url(data:image/svg+xml;utf8;base64,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)
}

.market-button.google-button {
    background-image: url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMTkuMC4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogNi4wMCBCdWlsZCAwKSAgLS0+CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiBpZD0iTGF5ZXJfMSIgeD0iMHB4IiB5PSIwcHgiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA1MTIgNTEyOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSIgd2lkdGg9IjUxMnB4IiBoZWlnaHQ9IjUxMnB4Ij4KPHBvbHlnb24gc3R5bGU9ImZpbGw6IzVDREFERDsiIHBvaW50cz0iMjkuNTMsMCAyOS41MywyNTEuNTA5IDI5LjUzLDUxMiAyOTkuMDA0LDI1MS41MDkgIi8+Cjxwb2x5Z29uIHN0eWxlPSJmaWxsOiNCREVDQzQ7IiBwb2ludHM9IjM2OS4wNjcsMTgwLjU0NyAyNjIuMTc1LDExOS40NjcgMjkuNTMsMCAyOTkuMDA0LDI1MS41MDkgIi8+Cjxwb2x5Z29uIHN0eWxlPSJmaWxsOiNEQzY4QTE7IiBwb2ludHM9IjI5LjUzLDUxMiAyOS41Myw1MTIgMjYyLjE3NSwzODMuNTUxIDM2OS4wNjcsMzIyLjQ3IDI5OS4wMDQsMjUxLjUwOSAiLz4KPHBhdGggc3R5bGU9ImZpbGw6I0ZGQ0E5NjsiIGQ9Ik0zNjkuMDY3LDE4MC41NDdsLTcwLjA2Myw3MC45NjFsNzAuMDYzLDcwLjk2MWwxMDguNjg4LTYyLjg3N2M2LjI4OC0zLjU5Myw2LjI4OC0xMS42NzcsMC0xNS4yNyAgTDM2OS4wNjcsMTgwLjU0N3oiLz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPC9zdmc+Cg==)
}

.market-button.windows-button {
    background-image: url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMTYuMC4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogNi4wMCBCdWlsZCAwKSAgLS0+CjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiBpZD0iQ2FwYV8xIiB4PSIwcHgiIHk9IjBweCIgd2lkdGg9IjI0cHgiIGhlaWdodD0iMjRweCIgdmlld0JveD0iMCAwIDQ4MCA0ODAiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDQ4MCA0ODA7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPGc+Cgk8cGF0aCBkPSJNMC4xNzYsMjI0TDAuMDAxLDY3Ljk2M2wxOTItMjYuMDcyVjIyNEgwLjE3NnogTTIyNC4wMDEsMzcuMjQxTDQ3OS45MzcsMHYyMjRIMjI0LjAwMVYzNy4yNDF6IE00NzkuOTk5LDI1NmwtMC4wNjIsMjI0ICAgbC0yNTUuOTM2LTM2LjAwOFYyNTZINDc5Ljk5OXogTTE5Mi4wMDEsNDM5LjkxOEwwLjE1Nyw0MTMuNjIxTDAuMTQ3LDI1NmgxOTEuODU0VjQzOS45MTh6IiBmaWxsPSIjMDBiY2YyIi8+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPC9zdmc+Cg==)
}

.market-button.blackberry-button {
    background-image: url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMTkuMC4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogNi4wMCBCdWlsZCAwKSAgLS0+CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiBpZD0iTGF5ZXJfMSIgeD0iMHB4IiB5PSIwcHgiIHZpZXdCb3g9IjAgMCA1MDMuMzIyIDUwMy4zMjIiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDUwMy4zMjIgNTAzLjMyMjsiIHhtbDpzcGFjZT0icHJlc2VydmUiIHdpZHRoPSIyNHB4IiBoZWlnaHQ9IjI0cHgiPgo8Zz4KCTxnPgoJCTxwYXRoIGQ9Ik0xMTYuMjg1LDYwLjc0Nkg0NS45OTNsLTIwLjgyNyw5NS40NThoNzMuNzYzYzU3LjI3NSwwLDczLjc2My0yOC42MzcsNzMuNzYzLTUzLjgwMyAgICBDMTczLjU1OSw4NC4xNzYsMTYyLjI3OCw2MC43NDYsMTE2LjI4NSw2MC43NDZ6IiBmaWxsPSIjMmUyZTJlIi8+Cgk8L2c+CjwvZz4KPGc+Cgk8Zz4KCQk8cGF0aCBkPSJNMjM4LjY0NCwzNDcuMTE5aC03MS4xNTlsLTE5Ljk1OSw5NS40NThoNzMuNzYzYzU3LjI3NSwwLDczLjc2My0yOC42MzcsNzMuNzYzLTUzLjgwMyAgICBDMjk1LjA1MSwzNzAuNTQ5LDI4NC42MzcsMzQ3LjExOSwyMzguNjQ0LDM0Ny4xMTl6IiBmaWxsPSIjMmUyZTJlIi8+Cgk8L2c+CjwvZz4KPGc+Cgk8Zz4KCQk8cGF0aCBkPSJNOTEuMTE5LDE5OS41OTNIMTkuOTU5TDAsMjk1LjA1MWg3My43NjNjNTcuMjc1LDAsNzMuNzYzLTI4LjYzNyw3My43NjMtNTMuODAzICAgIEMxNDcuNTI1LDIyMy4wMjQsMTM3LjExMiwxOTkuNTkzLDkxLjExOSwxOTkuNTkzeiIgZmlsbD0iIzJlMmUyZSIvPgoJPC9nPgo8L2c+CjxnPgoJPGc+CgkJPHBhdGggZD0iTTQyMC44ODEsMjk1LjA1MWgtNzEuMTU5bC0xOS45NTksODYuNzhoNzMuNzYzYzU3LjI3NSwwLDczLjc2My0yNC4yOTgsNzMuNzYzLTQ5LjQ2NCAgICBDNDc3LjI4OCwzMTQuMTQyLDQ2Ni44NzUsMjk1LjA1MSw0MjAuODgxLDI5NS4wNTF6IiBmaWxsPSIjMmUyZTJlIi8+Cgk8L2c+CjwvZz4KPGc+Cgk8Zz4KCQk8cGF0aCBkPSJNNDQ2LjkxNSwxNDcuNTI1aC03MS4xNTlsLTE5Ljk1OSw4Ni43OGg3My43NjNjNTcuMjc1LDAsNzMuNzYzLTI0LjI5OCw3My43NjMtNDkuNDY0ICAgIEM1MDMuMzIyLDE2Ni42MTcsNDkyLjkwOCwxNDcuNTI1LDQ0Ni45MTUsMTQ3LjUyNXoiIGZpbGw9IiMyZTJlMmUiLz4KCTwvZz4KPC9nPgo8Zz4KCTxnPgoJCTxwYXRoIGQ9Ik0yNjUuNTQ2LDE5OS41OTNoLTcxLjE1OWwtMTkuOTU5LDk1LjQ1OGg3My43NjNjNTcuMjc1LDAsNzMuNzYzLTI4LjYzNyw3My43NjMtNTMuODAzICAgIEMzMjIuODIsMjIzLjAyNCwzMTEuNTM5LDE5OS41OTMsMjY1LjU0NiwxOTkuNTkzeiIgZmlsbD0iIzJlMmUyZSIvPgoJPC9nPgo8L2c+CjxnPgoJPGc+CgkJPHBhdGggZD0iTTI5MS41OCw2MC43NDZIMjIwLjQybC0xOS45NTksOTUuNDU4aDczLjc2M2M1Ny4yNzUsMCw3My43NjMtMjguNjM3LDczLjc2My01My44MDMgICAgQzM0Ny45ODYsODQuMTc2LDMzNy41NzMsNjAuNzQ2LDI5MS41OCw2MC43NDZ6IiBmaWxsPSIjMmUyZTJlIi8+Cgk8L2c+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPC9zdmc+Cg==)
}

.market-button.amazon-button {
    background-image: url(data:image/png;base64,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)
}

.market-button.mb-light-skin {
    border-color: rgba(255, 255, 255, 0.12);
    background-color: transparent
}

.market-button.mb-light-skin .mb-subtitle, .market-button.mb-light-skin .mb-title {
    color: #fff
}

.market-button.mb-light-skin .mb-subtitle {
    opacity: .55
}

.market-button.mb-light-skin:hover {
    background-color: rgba(255, 255, 255, 0.06)
}

.market-button.mb-light-skin.apple-button {
    background-image: url(data:image/svg+xml;utf8;base64,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)
}

.market-button.mb-light-skin.blackberry-button {
    background-image: url(data:image/svg+xml;utf8;base64,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)
}

.market-button.mb-light-skin.amazon-button {
    background-image: url(data:image/png;base64,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)
}

.facebook-btn, .twitter-btn, .google-btn {
    text-transform: none
}

.facebook-btn > i, .twitter-btn > i, .google-btn > i {
    margin-top: 0
}

.facebook-btn {
    border-color: #3b5998;
    background-color: transparent;
    color: #3b5998
}

.facebook-btn:hover {
    background-color: #3b5998;
    color: #fff
}

.twitter-btn {
    border-color: #55acee;
    background-color: transparent;
    color: #55acee
}

.twitter-btn:hover {
    background-color: #55acee;
    color: #fff
}

.google-btn {
    border-color: #dd4b39;
    background-color: transparent;
    color: #dd4b39
}

.google-btn > i {
    font-size: 1.3em
}

.google-btn:hover {
    background-color: #dd4b39;
    color: #fff
}

.topbar-ghost .social-button {
    color: #FFFFFF;
}

.social-button {
    display: inline-block;
    margin-top: 5px;
    margin-right: 10px;
    margin-bottom: 5px;
    transition: color .3s;
    color: #606975;
    font-size: 13px;
    text-decoration: none;
    vertical-align: middle
}

.soc-border {
    border-left: 1px solid #e1e7ec;
    padding: 4px 0 4px 12px;
}

.social-button.shape-circle, .social-button.shape-rounded, .social-button.shape-square {
    width: 36px;
    height: 36px;
    margin-right: 8px;
    border: 1px solid #e1e7ec;
    line-height: 35px;
    text-align: center
}

.social-button.shape-circle {
    border-radius: 50%
}

.social-button.shape-rounded {
    border-radius: 5px
}

.social-button:focus {
    text-decoration: none
}

.text-center .social-button {
    margin-right: 9px;
    margin-left: 9px
}

.text-center .social-button.shape-circle, .text-center .social-button.shape-rounded, .text-center .social-button.shape-square {
    margin-right: 4px;
    margin-left: 4px
}

.text-right .social-button {
    margin-right: 0;
    margin-left: 18px
}

.text-right .social-button.shape-circle, .text-right .social-button.shape-rounded, .text-right .social-button.shape-square {
    margin-right: 0;
    margin-left: 8px
}

.sb-amazon {
    font-size: 1.15em !important
}

.sb-amazon:hover, .sb-amazon:focus {
    color: #ff9900 !important
}

.sb-airbnb:hover, .sb-airbnb:focus {
    color: #fd5c63 !important
}

.sb-behance {
    font-size: 1.1em !important
}

.sb-behance:hover, .sb-behance:focus {
    color: #1769ff !important
}

.sb-deviantart {
    font-size: 1.2em !important
}

.sb-deviantart:hover, .sb-deviantart:focus {
    color: #4e6252 !important
}

.sb-digg {
    font-size: 1.2em !important
}

.sb-digg:hover, .sb-digg:focus {
    color: #000000 !important
}

.sb-disqus {
    font-size: 1.1em !important
}

.sb-disqus:hover, .sb-disqus:focus {
    color: #2e9fff !important
}

.sb-dribbble:hover, .sb-dribbble:focus {
    color: #ea4c89 !important
}

.sb-drupal {
    font-size: 1.1em !important
}

.sb-drupal:hover, .sb-drupal:focus {
    color: #0077c0 !important
}

.sb-email:hover, .sb-email:focus {
    color: #fa6800 !important
}

.sb-facebook:hover, .sb-facebook:focus {
    color: #3b5998 !important
}

.sb-flickr:hover, .sb-flickr:focus {
    color: #0063dc !important
}

.sb-foursquare:hover, .sb-foursquare:focus {
    color: #ef4b78 !important
}

.sb-github:hover, .sb-github:focus {
    color: #4183c4 !important
}

.sb-google-plus {
    font-size: 1.2em !important
}

.sb-google-plus:hover, .sb-google-plus:focus {
    color: #dd4b39 !important
}

.sb-instagram:hover, .sb-instagram:focus {
    color: #3f729b !important
}

.sb-lastfm {
    font-size: 1.1em !important
}

.sb-lastfm:hover, .sb-lastfm:focus {
    color: #e31b23 !important
}

.sb-linkedin:hover, .sb-linkedin:focus {
    color: #0976b4 !important
}

.sb-odnoklassniki {
    font-size: 1.1em !important
}

.sb-odnoklassniki:hover, .sb-odnoklassniki:focus {
    color: #ed812b !important
}

.sb-paypal {
    font-size: .9em !important
}

.sb-paypal:hover, .sb-paypal:focus {
    color: #253b80 !important
}

.sb-pinterest:hover, .sb-pinterest:focus {
    color: #cc2127 !important
}

.sb-reddit {
    font-size: 1.1em !important
}

.sb-reddit:hover, .sb-reddit:focus {
    color: #ff4500 !important
}

.sb-rss {
    font-size: .9em !important
}

.sb-rss:hover, .sb-rss:focus {
    color: #f26522 !important
}

.sb-skype {
    font-size: .9em !important
}

.sb-skype:hover, .sb-skype:focus {
    color: #00aff0 !important
}

.sb-soundcloud {
    font-size: 1.2em !important
}

.sb-soundcloud:hover, .sb-soundcloud:focus {
    color: #ff8800 !important
}

.sb-stackoverflow:hover, .sb-stackoverflow:focus {
    color: #fe7a15 !important
}

.sb-steam:hover, .sb-steam:focus {
    color: #7da10e !important
}

.sb-stumbleupon:hover, .sb-stumbleupon:focus {
    color: #eb4924 !important
}

.sb-tumblr:hover, .sb-tumblr:focus {
    color: #35465c !important
}

.sb-twitch:hover, .sb-twitch:focus {
    color: #6441a5 !important
}

.sb-twitter:hover, .sb-twitter:focus {
    color: #55acee !important
}

.sb-vimeo:hover, .sb-vimeo:focus {
    color: #1ab7ea !important
}

.sb-vine:hover, .sb-vine:focus {
    color: #00b488 !important
}

.sb-vk {
    font-size: 1.1em !important
}

.sb-vk:hover, .sb-vk:focus {
    color: #45668e !important
}

.sb-wordpress:hover, .sb-wordpress:focus {
    color: #21759b !important
}

.sb-xing:hover, .sb-xing:focus {
    color: #026466 !important
}

.sb-yahoo {
    font-size: 1.1em !important
}

.sb-yahoo:hover, .sb-yahoo:focus {
    color: #400191 !important
}

.sb-yelp:hover, .sb-yelp:focus {
    color: #af0606 !important
}

.sb-youtube:hover, .sb-youtube:focus {
    color: #e52d27 !important
}

.sb-light-skin {
    transition: all .3s;
    background-color: transparent;
    color: rgba(255, 255, 255, 0.6) !important
}

.sb-light-skin:hover, .sb-light-skin:focus {
    color: #fff !important
}

.sb-light-skin.shape-circle, .sb-light-skin.shape-rounded, .sb-light-skin.shape-square {
    border-color: rgba(255, 255, 255, 0.12)
}

.sb-light-skin.shape-circle:hover, .sb-light-skin.shape-rounded:hover, .sb-light-skin.shape-square:hover {
    background-color: rgba(255, 255, 255, 0.06)
}

/*--------------------------------------------
7. Tabs Styles
--------------------------------------------*/
.nav-tabs {
    border-bottom-color: #e1e7ec
}

.nav-tabs .nav-link {
    padding: 10px 20px;
    transition: color .3s;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    color: #606975;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: .05em;
    text-transform: uppercase;
    text-decoration: none
}

.nav-tabs .nav-link:hover {
    color: #fa6800
}

.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
    border-color: transparent
}

.nav-tabs .nav-link.disabled {
    cursor: not-allowed
}

.nav-tabs .nav-link.disabled, .nav-tabs .nav-link.disabled:hover {
    color: #9da9b9
}

.nav-tabs .nav-link > i {
    margin-top: -3px;
    margin-right: 5px
}

.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
    border-color: #e1e7ec #e1e7ec #fff;
    color: #94a1b3
}

.tab-content {
    padding: 24px;
    border-right: 1px solid #e1e7ec;
    border-bottom: 1px solid #e1e7ec;
    border-left: 1px solid #e1e7ec;
    border-bottom-left-radius: 7px;
    border-bottom-right-radius: 7px;
    overflow: hidden
}

.tab-content p:last-child, .tab-content ul:last-child, .tab-content ol:last-child {
    margin-bottom: 0
}

.nav-pills .nav-link {
    margin-right: 5px;
    padding: 8px 16px;
    transition: all .3s;
    border-radius: 22px;
    color: #606975;
    font-size: 13px;
    font-weight: 500;
    letter-spacing: .07em;
    text-transform: uppercase;
    text-decoration: none
}

.nav-pills .nav-link:hover:not(.disabled) {
    background-color: #f5f5f5
}

.nav-pills .nav-link.disabled {
    color: #9da9b9;
    cursor: not-allowed
}

.nav-pills .nav-link > i {
    margin-top: -4px;
    margin-right: 5px
}

.nav-pills .nav-item.show .nav-link, .nav-pills .nav-link.active {
    background-color: #fa6800 !important;
    color: #fff;
    cursor: default
}

.nav-pills + .tab-content {
    padding: 24px 0 0;
    border: 0;
    border-radius: 0
}

.nav-pills.nav-justified .nav-link {
    margin-right: 0
}

.nav-pills.justify-content-center .nav-link {
    margin: 0 3px
}

.nav-pills.justify-content-end .nav-link {
    margin: 0 0 0 5px
}

.nav-pills.flex-column .nav-link {
    margin: 0 0 5px
}

/*--------------------------------------------
8. Animation Styles
--------------------------------------------*/
.transition.fade {
    transition: all .4s ease-in-out;
    opacity: 0;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden
}

.transition.fade.show {
    opacity: 1
}

.transition.scale.fade {
    -webkit-transform: scale(0.9);
    -ms-transform: scale(0.9);
    transform: scale(0.9)
}

.transition.scaledown.fade {
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1)
}

.transition.scale.fade.show, .transition.scaledown.fade.show {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1)
}

.transition.left.fade {
    -webkit-transform: translateX(40px);
    -ms-transform: translateX(40px);
    transform: translateX(40px)
}

.transition.right.fade {
    -webkit-transform: translateX(-40px);
    -ms-transform: translateX(-40px);
    transform: translateX(-40px)
}

.transition.left.fade.show, .transition.right.fade.show {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0)
}

.transition.top.fade {
    -webkit-transform: translateY(-40px);
    -ms-transform: translateY(-40px);
    transform: translateY(-40px)
}

.transition.bottom.fade {
    -webkit-transform: translateY(40px);
    -ms-transform: translateY(40px);
    transform: translateY(40px)
}

.transition.top.fade.show, .transition.bottom.fade.show {
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0)
}

.transition.flip.fade {
    -webkit-transform: rotateY(-90deg) scale(1.1);
    transform: rotateY(-90deg) scale(1.1);
    -webkit-transform-origin: 50% 50%;
    -ms-transform-origin: 50% 50%;
    transform-origin: 50% 50%
}

.transition.flip.fade.show {
    -webkit-transform: rotateY(0deg) scale(1);
    transform: rotateY(0deg) scale(1);
    -webkit-transform-origin: 50% 50%;
    -ms-transform-origin: 50% 50%;
    transform-origin: 50% 50%
}

/*--------------------------------------------
9. Card Styles
--------------------------------------------*/
.card {
    border-radius: 7px;
    border-color: #e1e7ec
}

.card .google-map {
    border-top-left-radius: 7px;
    border-top-right-radius: 7px
}

.card-header {
    border-bottom-color: #e1e7ec
}

.card-header h1, .card-header .h1, .card-header h2, .card-header .h2, .card-header h3, .card-header .h3, .card-header h4, .card-header .h4, .card-header h5, .card-header .h5, .card-header h6, .card-header .h6 {
    margin-bottom: 0
}

.card-header:first-child {
    border-radius: 7px 7px 0 0
}

.card-header, .card-footer {
    background-color: #f5f5f5
}

.card-footer {
    border-top-color: #e1e7ec
}

.card-footer:last-child {
    border-radius: 0 0 7px 7px
}

.card-body p:last-child, .card-body ol:last-child, .card-body ul:last-child {
    margin-bottom: 0
}

.card-body .tab-content {
    padding: 0;
    border: 0
}

.card.bg-primary {
    border-color: #0c98d7
}

.card.bg-primary .card-header {
    border-bottom-color: #0c98d7;
    background-color: #0ca2e5
}

.card.bg-secondary .card-header, .card.bg-faded .card-header {
    background-color: #ededed
}

.card.bg-success {
    border-color: #29ca90
}

.card.bg-success .card-header {
    border-bottom-color: #29ca90;
    background-color: #2bd396
}

.card.bg-info {
    border-color: #1cb3e0
}

.card.bg-info .card-header {
    border-bottom-color: #1cb3e0;
    background-color: #35bde6
}

.card.bg-warning {
    border-color: #ff9e12
}

.card.bg-warning .card-header {
    border-bottom-color: #ff9e12;
    background-color: #ffaa30
}

.card.bg-danger {
    border-color: #ff1515
}

.card.bg-danger .card-header {
    border-bottom-color: #ff1515;
    background-color: #f33
}

.card.bg-dark {
    border-color: #262e38
}

.card.bg-dark .card-header {
    border-bottom-color: #262e38;
    background-color: #2b333e
}

.text-white .card-title, .text-light .card-title {
    color: #fff
}

.card-group .card .card-footer {
    border-radius: 0
}

.card-group .card:first-child .card-footer {
    border-bottom-left-radius: 7px
}

.card-group .card:last-child .card-footer {
    border-bottom-right-radius: 7px
}

.card-img-tiles {
    display: block;
    border-bottom: 1px solid #e1e7ec
}

.card-img-tiles > .inner {
    display: table;
    width: 100%
}

.card-img-tiles .main-img, .card-img-tiles .thumblist {
    display: table-cell;
    width: 65%;
    padding: 15px;
    vertical-align: middle
}

.card-img-tiles .main-img > img, .card-img-tiles .thumblist > img {
    display: block;
    width: 100%;
    margin-bottom: 6px
}

.card-img-tiles .main-img > img:last-child, .card-img-tiles .thumblist > img:last-child {
    margin-bottom: 0
}

.card-img-tiles .thumblist {
    width: 35%;
    border-left: 1px solid #e1e7ec
}

.category-card .view-button {
    -webkit-transform: translateY(10px);
    -ms-transform: translateY(10px);
    transform: translateY(10px);
    transition: all .4s;
    opacity: 0
}

.category-card:hover .view-button {
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
    opacity: 1
}

.touchevents .category-card .view-button {
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
    opacity: 1
}

@media (max-width: 768px) {
    .category-card .view-button {
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
        opacity: 1
    }
}

/*--------------------------------------------
10. Accordion Styles
--------------------------------------------*/
.accordion .card {
    margin-bottom: 8px
}

.accordion [data-toggle='collapse'] {
    display: block;
    position: relative;
    color: #606975;
    text-decoration: none
}

.accordion [data-toggle='collapse']::after {
    position: absolute;
    top: 50%;
    right: 0;
    width: 0;
    height: 0;
    margin-top: -2px;
    transition: -webkit-transform .25s;
    transition: transform .25s;
    border-right: 5px solid transparent;
    border-bottom: 5px dashed;
    border-left: 5px solid transparent;
    content: ''
}

.accordion [data-toggle='collapse'].collapsed::after {
    -webkit-transform: rotate(-180deg);
    -ms-transform: rotate(-180deg);
    transform: rotate(-180deg)
}

.accordion [data-toggle='collapse'] > i {
    margin-top: -4px;
    margin-right: 7px
}

.accordion [data-toggle='collapse'] > i.socicon-paypal {
    display: inline-block;
    margin-top: 1px;
    font-size: .8em;
    vertical-align: middle
}

.accordion [data-toggle='collapse'] > i.icon-medal {
    width: 16px;
    height: 16px;
    background-size: 16px
}

.pagination {
    display: table;
    width: 100%;
    border-top: 1px solid #e1e7ec
}

/*--------------------------------------------
11. Pagination Styles
--------------------------------------------*/
.pagination > .column {
    display: table-cell;
    padding-top: 16px;
    vertical-align: middle
}

.pagination .pages {
    display: block;
    margin: 0;
    padding: 0;
    list-style: none
}

.pagination .pages > li {
    display: inline-block;
    width: 36px;
    height: 36px;
    font-size: 14px;
    font-weight: 500;
    line-height: 34px;
    text-align: center
}

.pagination .pages > li > a {
    display: block;
    width: 36px;
    height: 36px;
    transition: all .3s;
    border: 1px solid transparent;
    border-radius: 50%;
    color: #606975;
    line-height: 34px;
    text-decoration: none
}

.pagination .pages > li > a:hover {
    border-color: #e1e7ec;
    background-color: #f5f5f5
}

.pagination .pages > li.active > a {
    border-color: #fa6811;
    background-color: #fa6800;
    color: #fff
}

.pagination .btn > i {
    margin-top: -5px
}

.entry-navigation {
    display: table;
    width: 100%;
    border-top: 1px solid #e1e7ec;
    border-bottom: 1px solid #e1e7ec;
    table-layout: fixed
}

.entry-navigation > .column {
    display: table-cell;
    padding-top: 15px;
    padding-bottom: 15px;
    text-align: center;
    vertical-align: middle
}

.entry-navigation .btn {
    margin: 0
}

.entry-navigation .btn > i {
    margin-top: -4px
}

.entry-navigation .btn.view-all {
    width: 44px;
    padding-right: 0;
    padding-left: 1px
}

.entry-navigation .btn.view-all > i {
    margin-top: -6px;
    font-size: 1.4em
}

/*--------------------------------------------
12. Comments Styles
--------------------------------------------*/
.comment-list {
    max-height: 65vh;
    overflow-y: scroll;
    border-bottom: 1px solid #f5f5f5;
}
.comment.my-comment {
    padding-left: 0;
    padding-right: 66px;
}
.comment.my-comment .comment-author-ava{
    left: calc(100% - 50px);
}
.comment.my-comment .comment-body::after{
    right: -20px;
}
.comment.my-comment .comment-body::before{
    right: -20px;
    transform: rotate(180deg);
}
.comment {
    display: block;
    position: relative;
    margin-bottom: 30px;
    padding-left: 66px
}

.comment .comment-author-ava {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 50px;
    border-radius: 50%;
    overflow: hidden
}

.comment .comment-author-ava > img {
    display: block;
    width: 100%
}

.comment .comment-body {
    position: relative;
    padding: 24px;
    border: 1px solid #e1e7ec;
    border-radius: 7px;
    background-color: #fff
}

.comment .comment-body::after, .comment .comment-body::before {
    position: absolute;
    top: 12px;
    right: 100%;
    width: 0;
    height: 0;
    border: solid transparent;
    content: '';
    pointer-events: none
}

.comment .comment-body::after {
    border-width: 9px;
    border-color: transparent;
    border-right-color: #fff
}

.comment .comment-body::before {
    margin-top: -1px;
    border-width: 10px;
    border-color: transparent;
    border-right-color: #e1e7ec
}

.comment .comment-title {
    margin-bottom: 8px;
    color: #606975;
    font-size: 14px;
    font-weight: 500
}

.comment .comment-text {
    margin-bottom: 12px
}

.comment .comment-footer {
    display: table;
    width: 100%
}

.comment .comment-footer > .column {
    display: table-cell;
    vertical-align: middle
}

.comment .comment-footer > .column:last-child {
    text-align: right
}

.comment .comment-meta {
    color: #9da9b9;
    font-size: 13px
}

.comment .reply-link {
    transition: color .3s;
    color: #606975;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: .07em;
    text-transform: uppercase;
    text-decoration: none
}

.comment .reply-link > i {
    display: inline-block;
    margin-top: -3px;
    margin-right: 4px;
    vertical-align: middle
}

.comment .reply-link:hover {
    color: #fa6800
}

.comment.comment-reply {
    margin-top: 30px;
    margin-bottom: 0
}

@media (max-width: 576px) {
    .comment {
        padding-left: 0
    }

    .comment .comment-author-ava {
        display: none
    }

    .comment .comment-body {
        padding: 15px
    }

    .comment .comment-body::before, .comment .comment-body::after {
        display: none
    }
}

/*--------------------------------------------
13. Tooltip Styles
--------------------------------------------*/
.tooltip {
    font-family: "Maven Pro", Helvetica, Arial, sans-serif
}

.tooltip.bs-tooltip-top .arrow::before {
    border-top-color: #2f3844
}

.tooltip.bs-tooltip-right .arrow::before {
    border-right-color: #2f3844
}

.tooltip.bs-tooltip-bottom .arrow::before {
    border-bottom-color: #2f3844
}

.tooltip.bs-tooltip-left .arrow::before {
    border-left-color: #2f3844
}

.tooltip.show {
    opacity: 1
}

.tooltip-inner {
    border-radius: 3px;
    background-color: #2f3844;
    color: #fff;
    font-size: 13px
}

.popover {
    border-radius: 7px;
    border-color: #e1e7ec;
    font-family: "Maven Pro", Helvetica, Arial, sans-serif
}

.popover.bs-popover-top .arrow::before {
    border-top-color: #d5dde4
}

.popover.bs-popover-right .arrow::before {
    border-right-color: #d5dde4
}

.popover.bs-popover-bottom .arrow::before {
    border-bottom-color: #d5dde4
}

.popover.bs-popover-bottom .arrow::after {
    border-bottom-color: #f7f7f7
}

.popover.bs-popover-left .arrow::before {
    border-left-color: #d5dde4
}

.popover-header {
    color: #374250;
    font-family: inherit
}

.popover-body {
    color: #606975
}

.example-tooltip .tooltip {
    display: inline-block;
    position: relative;
    margin: 10px 20px;
    opacity: 1
}

.example-popover .popover {
    display: block;
    position: relative;
    width: 260px;
    margin: 1.25rem;
    float: left
}

.bs-tooltip-bottom-demo .arrow, .bs-tooltip-top-demo .arrow {
    left: 50%;
    margin-left: -2px
}

.bs-tooltip-left-demo .arrow, .bs-tooltip-right-demo .arrow {
    top: 50%;
    margin-top: -2px
}

.bs-popover-bottom-demo .arrow, .bs-popover-top-demo .arrow {
    left: 50%;
    margin-left: -11px
}

.bs-popover-left-demo .arrow, .bs-popover-right-demo .arrow {
    top: 50%;
    margin-top: -8px
}

.dropdown-menu {
    border-color: #e1e7ec;
    border-radius: 5px;
    font-size: 14px;
    box-shadow: 0 7px 22px -5px rgba(47, 56, 68, 0.2)
}

.dropdown-menu .dropdown-item {
    padding-right: 20px;
    padding-left: 20px;
    transition: color .3s;
    color: #606975;
    text-decoration: none
}

.dropdown-menu .dropdown-item:hover, .dropdown-menu .dropdown-item.active, .dropdown-menu .dropdown-item:focus, .dropdown-menu .dropdown-item:active {
    background: 0
}

.dropdown-menu .dropdown-item:hover {
    color: #fa6800
}

.dropdown-menu .dropdown-item.active {
    color: #fa6800
}

.dropdown-menu a.dropdown-item {
    font-weight: 500
}

.dropdown-toggle::after {
    margin-top: 1px;
    margin-left: .3em;
    vertical-align: .2em
}

.btn.dropdown-toggle::after {
    vertical-align: .2em
}

.show .dropdown-menu {
    -webkit-animation: dropdown-show .25s;
    animation: dropdown-show .25s
}

@-webkit-keyframes dropdown-show {
    from {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

@keyframes dropdown-show {
    from {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

.list-group-item {
    border-color: #e1e7ec;
    background-color: #fff;
    text-decoration: none
}

.list-group-item:first-child {
    border-top-left-radius: 7px;
    border-top-right-radius: 7px
}

.list-group-item:last-child {
    border-bottom-left-radius: 7px;
    border-bottom-right-radius: 7px
}

.list-group-item i {
    margin-top: -4px;
    margin-right: 8px;
    font-size: 1.1em
}

.list-group-item p, .list-group-item ul, .list-group-item ol, .list-group-item li, .list-group-item span {
    font-weight: normal !important
}

a.list-group-item, .list-group-item-action {
    transition: all .25s;
    color: #606975;
    font-weight: 500
}

a.list-group-item:hover, a.list-group-item:focus, a.list-group-item:active, .list-group-item-action:hover, .list-group-item-action:focus, .list-group-item-action:active {
    background-color: #f5f5f5;
    color: #606975
}

a.list-group-item {
    padding-top: .87rem;
    padding-bottom: .87rem
}

/*--------------------------------------------
14. Badges & Alerts Styles
--------------------------------------------*/
.with-badge {
    position: relative;
    padding-right: 3.3rem
}

.with-badge .badge {
    position: absolute;
    top: 50%;
    right: 1.15rem;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%)
}

.badge {
    color: #fff;
    font-size: 90%;
    font-weight: 500
}

.badge.badge-default {
    background-color: #eaeef2;
    color: #606975
}

.badge.badge-primary {
    background-color: #fa6800
}

.badge.badge-info {
    background-color: #50c6e9
}

.badge.badge-success {
    background-color: #43d9a3
}

.badge.badge-warning {
    background-color: #ffb74f
}

.badge.badge-danger {
    background-color: #ff5252
}

.list-group-item.active {
    border-color: #fa6800;
    background-color: #fa6800;
    color: #fff;
    cursor: default;
    pointer-events: none
}

.list-group-item.active h1, .list-group-item.active .h1, .list-group-item.active h2, .list-group-item.active .h2, .list-group-item.active h3, .list-group-item.active .h3, .list-group-item.active h4, .list-group-item.active .h4, .list-group-item.active h5, .list-group-item.active .h5, .list-group-item.active h6, .list-group-item.active .h6 {
    color: #fff
}

.list-group-item.active .badge {
    background-color: #fff !important;
    color: #606975 !important
}

.list-group-item-info {
    background-color: rgba(80, 198, 233, 0.12);
    color: #50c6e9 !important
}

.list-group-item-info > *, .list-group-item-info h1, .list-group-item-info h2, .list-group-item-info h3, .list-group-item-info h4, .list-group-item-info h5, .list-group-item-info h6, .list-group-item-info p, .list-group-item-info ul, .list-group-item-info ol, .list-group-item-info a {
    color: #50c6e9 !important
}

.list-group-item-success {
    background-color: rgba(67, 217, 163, 0.12);
    color: #36d69d !important
}

.list-group-item-success > *, .list-group-item-success h1, .list-group-item-success h2, .list-group-item-success h3, .list-group-item-success h4, .list-group-item-success h5, .list-group-item-success h6, .list-group-item-success p, .list-group-item-success ul, .list-group-item-success ol, .list-group-item-success a {
    color: #36d69d !important
}

.list-group-item-warning {
    background-color: rgba(255, 183, 79, 0.12);
    color: #ffb140 !important
}

.list-group-item-warning > *, .list-group-item-warning h1, .list-group-item-warning h2, .list-group-item-warning h3, .list-group-item-warning h4, .list-group-item-warning h5, .list-group-item-warning h6, .list-group-item-warning p, .list-group-item-warning ul, .list-group-item-warning ol, .list-group-item-warning a {
    color: #ffb140 !important
}

.list-group-item-danger {
    background-color: rgba(255, 82, 82, 0.12);
    color: #ff5252 !important
}

.list-group-item-danger > *, .list-group-item-danger h1, .list-group-item-danger h2, .list-group-item-danger h3, .list-group-item-danger h4, .list-group-item-danger h5, .list-group-item-danger h6, .list-group-item-danger p, .list-group-item-danger ul, .list-group-item-danger ol, .list-group-item-danger a {
    color: #ff5252 !important
}

.list-group-item-action:hover.list-group-item-info, .list-group-item-action.active.list-group-item-info {
    background-color: rgba(80, 198, 233, 0.24)
}

.list-group-item-action:hover.list-group-item-success, .list-group-item-action.active.list-group-item-success {
    background-color: rgba(67, 217, 163, 0.24)
}

.list-group-item-action:hover.list-group-item-warning, .list-group-item-action.active.list-group-item-warning {
    background-color: rgba(255, 183, 79, 0.24)
}

.list-group-item-action:hover.list-group-item-danger, .list-group-item-action.active.list-group-item-danger {
    background-color: rgba(255, 82, 82, 0.24)
}

.card:not([class*='mb-']):not([class*='margin-bottom-']) + .list-group {
    margin-top: -1px
}

.card:not([class*='mb-']):not([class*='margin-bottom-']) + .list-group .list-group-item:first-child {
    border-radius: 0
}

.alert {
    position: relative;
    padding: 24px;
    border: 5px solid transparent;
    border-radius: 5px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    background-clip: padding-box
}

.alert i {
    display: inline-block;
    margin-top: -2px;
    vertical-align: middle
}

.alert > *:last-child:not(.btn), .alert h1:last-child:not(.btn), .alert h2:last-child:not(.btn), .alert h3:last-child:not(.btn), .alert h4:last-child:not(.btn), .alert h5:last-child:not(.btn), .alert h6:last-child:not(.btn), .alert p:last-child:not(.btn), .alert ul:last-child:not(.btn), .alert ol:last-child:not(.btn), .alert a:last-child:not(.btn) {
    margin: 0
}

.alert::before {
    display: block;
    position: absolute;
    top: -6px;
    right: -6px;
    bottom: -6px;
    left: -6px;
    border: 1px solid transparent;
    border-radius: 5px;
    content: '';
    z-index: -1
}

.alert .alert-dismissible {
    transition: opacity .4s
}

.alert .alert-dismissible.fade.show {
    opacity: 1
}

.alert .alert-close {
    display: block;
    position: absolute;
    top: 10px;
    right: 12px;
    font-family: feather;
    font-size: 14px;
    cursor: pointer
}

.alert .alert-close::before {
    content: '\e117'
}

.alert-default {
    background-color: rgba(96, 105, 117, 0.05);
    color: #606975
}

.alert-default::before {
    border-color: rgba(96, 105, 117, 0.13)
}

.alert-default > *:not(.text-white), .alert-default > *:not(.text-light), .alert-default h1:not(.text-white), .alert-default h1:not(.text-light), .alert-default h2:not(.text-white), .alert-default h2:not(.text-light), .alert-default h3:not(.text-white), .alert-default h3:not(.text-light), .alert-default h4:not(.text-white), .alert-default h4:not(.text-light), .alert-default h5:not(.text-white), .alert-default h5:not(.text-light), .alert-default h6:not(.text-white), .alert-default h6:not(.text-light), .alert-default p:not(.text-white), .alert-default p:not(.text-light), .alert-default ul:not(.text-white), .alert-default ul:not(.text-light), .alert-default ol:not(.text-white), .alert-default ol:not(.text-light), .alert-default a:not(.text-white), .alert-default a:not(.text-light) {
    color: #606975
}

.alert-default .alert-close {
    color: #606975
}

p.primary-alert {
    color: #0da9ef !important;
}

.alert-primary {
    background-color: rgba(13, 169, 239, 0.12);
    color: #fa6800
}

.alert-primary::before {
    border-color: rgba(13, 169, 239, 0.25)
}

.alert-primary > *:not(.text-white), .alert-primary > *:not(.text-light), .alert-primary h1:not(.text-white), .alert-primary h1:not(.text-light), .alert-primary h2:not(.text-white), .alert-primary h2:not(.text-light), .alert-primary h3:not(.text-white), .alert-primary h3:not(.text-light), .alert-primary h4:not(.text-white), .alert-primary h4:not(.text-light), .alert-primary h5:not(.text-white), .alert-primary h5:not(.text-light), .alert-primary h6:not(.text-white), .alert-primary h6:not(.text-light), .alert-primary p:not(.text-white), .alert-primary p:not(.text-light), .alert-primary ul:not(.text-white), .alert-primary ul:not(.text-light), .alert-primary ol:not(.text-white), .alert-primary ol:not(.text-light), .alert-primary a:not(.text-white), .alert-primary a:not(.text-light) {
    color: #fa6800
}

.alert-primary .alert-close {
    color: #fa6800
}

.alert-info {
    background-color: rgba(219, 92, 0, 0.12);
    color: #fa6800
}

.alert-info::before {
    border-color: rgba(185, 54, 3, 0.3)
}

.alert-info > *:not(.text-white), .alert-info > *:not(.text-light), .alert-info h1:not(.text-white), .alert-info h1:not(.text-light), .alert-info h2:not(.text-white), .alert-info h2:not(.text-light), .alert-info h3:not(.text-white), .alert-info h3:not(.text-light), .alert-info h4:not(.text-white), .alert-info h4:not(.text-light), .alert-info h5:not(.text-white), .alert-info h5:not(.text-light), .alert-info h6:not(.text-white), .alert-info h6:not(.text-light), .alert-info p:not(.text-white), .alert-info p:not(.text-light), .alert-info ul:not(.text-white), .alert-info ul:not(.text-light), .alert-info ol:not(.text-white), .alert-info ol:not(.text-light), .alert-info a:not(.text-white), .alert-info a:not(.text-light) {
    color: #fa6800
}

.alert-info .alert-close {
    color: #fa6800
}

.alert-success {
    background-color: rgba(67, 217, 163, 0.12);
    color: #36d69d
}

.alert-success::before {
    border-color: rgba(67, 217, 163, 0.25)
}

.alert-success > *:not(.text-white), .alert-success > *:not(.text-light), .alert-success h1:not(.text-white), .alert-success h1:not(.text-light), .alert-success h2:not(.text-white), .alert-success h2:not(.text-light), .alert-success h3:not(.text-white), .alert-success h3:not(.text-light), .alert-success h4:not(.text-white), .alert-success h4:not(.text-light), .alert-success h5:not(.text-white), .alert-success h5:not(.text-light), .alert-success h6:not(.text-white), .alert-success h6:not(.text-light), .alert-success p:not(.text-white), .alert-success p:not(.text-light), .alert-success ul:not(.text-white), .alert-success ul:not(.text-light), .alert-success ol:not(.text-white), .alert-success ol:not(.text-light), .alert-success a:not(.text-white), .alert-success a:not(.text-light) {
    color: #36d69d
}

.alert-success .alert-close {
    color: #36d69d
}

.alert-warning {
    background-color: rgba(255, 183, 79, 0.12);
    color: #ffb140
}

.alert-warning::before {
    border-color: rgba(255, 183, 79, 0.25)
}

.alert-warning > *:not(.text-white), .alert-warning > *:not(.text-light), .alert-warning h1:not(.text-white), .alert-warning h1:not(.text-light), .alert-warning h2:not(.text-white), .alert-warning h2:not(.text-light), .alert-warning h3:not(.text-white), .alert-warning h3:not(.text-light), .alert-warning h4:not(.text-white), .alert-warning h4:not(.text-light), .alert-warning h5:not(.text-white), .alert-warning h5:not(.text-light), .alert-warning h6:not(.text-white), .alert-warning h6:not(.text-light), .alert-warning p:not(.text-white), .alert-warning p:not(.text-light), .alert-warning ul:not(.text-white), .alert-warning ul:not(.text-light), .alert-warning ol:not(.text-white), .alert-warning ol:not(.text-light), .alert-warning a:not(.text-white), .alert-warning a:not(.text-light) {
    color: #ffb140
}

.alert-warning .alert-close {
    color: #ffb140
}

.alert-danger {
    background-color: rgba(255, 82, 82, 0.12);
    color: #ff5252
}

.alert-danger::before {
    border-color: rgba(255, 82, 82, 0.25)
}

.alert-danger > *:not(.text-white), .alert-danger > *:not(.text-light), .alert-danger h1:not(.text-white), .alert-danger h1:not(.text-light), .alert-danger h2:not(.text-white), .alert-danger h2:not(.text-light), .alert-danger h3:not(.text-white), .alert-danger h3:not(.text-light), .alert-danger h4:not(.text-white), .alert-danger h4:not(.text-light), .alert-danger h5:not(.text-white), .alert-danger h5:not(.text-light), .alert-danger h6:not(.text-white), .alert-danger h6:not(.text-light), .alert-danger p:not(.text-white), .alert-danger p:not(.text-light), .alert-danger ul:not(.text-white), .alert-danger ul:not(.text-light), .alert-danger ol:not(.text-white), .alert-danger ol:not(.text-light), .alert-danger a:not(.text-white), .alert-danger a:not(.text-light) {
    color: #ff5252
}

.alert-danger .alert-close {
    color: #ff5252
}

.alert-image-bg {
    border: 0
}

.alert-image-bg::before {
    display: none
}

/*--------------------------------------------
15. IsiToast Styles
--------------------------------------------*/
.iziToast {
    border: 1px solid #e1e7ec;
    background: #f5f5f5
}

.iziToast::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 4px;
    box-shadow: 0 8px 10px -4px rgba(0, 0, 0, 0.2);
    content: '';
    z-index: -1
}

.iziToast > .iziToast-body {
    margin-left: 15px
}

.iziToast > .iziToast-close {
    width: 40px;
    transition: opacity .25s;
    background: none;
    background-position: center;
    background-color: transparent;
    background-image: url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHZlcnNpb249IjEuMSIgdmlld0JveD0iMCAwIDE1LjY0MiAxNS42NDIiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDE1LjY0MiAxNS42NDIiIHdpZHRoPSIxNnB4IiBoZWlnaHQ9IjE2cHgiPgogIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgZD0iTTguODgyLDcuODIxbDYuNTQxLTYuNTQxYzAuMjkzLTAuMjkzLDAuMjkzLTAuNzY4LDAtMS4wNjEgIGMtMC4yOTMtMC4yOTMtMC43NjgtMC4yOTMtMS4wNjEsMEw3LjgyMSw2Ljc2TDEuMjgsMC4yMmMtMC4yOTMtMC4yOTMtMC43NjgtMC4yOTMtMS4wNjEsMGMtMC4yOTMsMC4yOTMtMC4yOTMsMC43NjgsMCwxLjA2MSAgbDYuNTQxLDYuNTQxTDAuMjIsMTQuMzYyYy0wLjI5MywwLjI5My0wLjI5MywwLjc2OCwwLDEuMDYxYzAuMTQ3LDAuMTQ2LDAuMzM4LDAuMjIsMC41MywwLjIyczAuMzg0LTAuMDczLDAuNTMtMC4yMmw2LjU0MS02LjU0MSAgbDYuNTQxLDYuNTQxYzAuMTQ3LDAuMTQ2LDAuMzM4LDAuMjIsMC41MywwLjIyYzAuMTkyLDAsMC4zODQtMC4wNzMsMC41My0wLjIyYzAuMjkzLTAuMjkzLDAuMjkzLTAuNzY4LDAtMS4wNjFMOC44ODIsNy44MjF6IiBmaWxsPSIjMzc0MjUwIi8+Cjwvc3ZnPgo=);
    background-size: 8px;
    background-repeat: no-repeat
}

.iziToast.iziToast-info {
    border-color: rgba(80, 198, 233, 0.3);
    background-color: #e6f7fc;
    color: #39bfe6
}

.iziToast.iziToast-info > .iziToast-close {
    background-image: url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHZlcnNpb249IjEuMSIgdmlld0JveD0iMCAwIDE1LjY0MiAxNS42NDIiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDE1LjY0MiAxNS42NDIiIHdpZHRoPSIxNnB4IiBoZWlnaHQ9IjE2cHgiPgogIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgZD0iTTguODgyLDcuODIxbDYuNTQxLTYuNTQxYzAuMjkzLTAuMjkzLDAuMjkzLTAuNzY4LDAtMS4wNjEgIGMtMC4yOTMtMC4yOTMtMC43NjgtMC4yOTMtMS4wNjEsMEw3LjgyMSw2Ljc2TDEuMjgsMC4yMmMtMC4yOTMtMC4yOTMtMC43NjgtMC4yOTMtMS4wNjEsMGMtMC4yOTMsMC4yOTMtMC4yOTMsMC43NjgsMCwxLjA2MSAgbDYuNTQxLDYuNTQxTDAuMjIsMTQuMzYyYy0wLjI5MywwLjI5My0wLjI5MywwLjc2OCwwLDEuMDYxYzAuMTQ3LDAuMTQ2LDAuMzM4LDAuMjIsMC41MywwLjIyczAuMzg0LTAuMDczLDAuNTMtMC4yMmw2LjU0MS02LjU0MSAgbDYuNTQxLDYuNTQxYzAuMTQ3LDAuMTQ2LDAuMzM4LDAuMjIsMC41MywwLjIyYzAuMTkyLDAsMC4zODQtMC4wNzMsMC41My0wLjIyYzAuMjkzLTAuMjkzLDAuMjkzLTAuNzY4LDAtMS4wNjFMOC44ODIsNy44MjF6IiBmaWxsPSIjMTg5NmJiIi8+Cjwvc3ZnPgo=)
}

.iziToast.iziToast-success {
    border-color: rgba(67, 217, 163, 0.3);
    background-color: #dcf8ee;
    color: #2bd396
}

.iziToast.iziToast-success > .iziToast-close {
    background-image: url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHZlcnNpb249IjEuMSIgdmlld0JveD0iMCAwIDE1LjY0MiAxNS42NDIiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDE1LjY0MiAxNS42NDIiIHdpZHRoPSIxNnB4IiBoZWlnaHQ9IjE2cHgiPgogIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgZD0iTTguODgyLDcuODIxbDYuNTQxLTYuNTQxYzAuMjkzLTAuMjkzLDAuMjkzLTAuNzY4LDAtMS4wNjEgIGMtMC4yOTMtMC4yOTMtMC43NjgtMC4yOTMtMS4wNjEsMEw3LjgyMSw2Ljc2TDEuMjgsMC4yMmMtMC4yOTMtMC4yOTMtMC43NjgtMC4yOTMtMS4wNjEsMGMtMC4yOTMsMC4yOTMtMC4yOTMsMC43NjgsMCwxLjA2MSAgbDYuNTQxLDYuNTQxTDAuMjIsMTQuMzYyYy0wLjI5MywwLjI5My0wLjI5MywwLjc2OCwwLDEuMDYxYzAuMTQ3LDAuMTQ2LDAuMzM4LDAuMjIsMC41MywwLjIyczAuMzg0LTAuMDczLDAuNTMtMC4yMmw2LjU0MS02LjU0MSAgbDYuNTQxLDYuNTQxYzAuMTQ3LDAuMTQ2LDAuMzM4LDAuMjIsMC41MywwLjIyYzAuMTkyLDAsMC4zODQtMC4wNzMsMC41My0wLjIyYzAuMjkzLTAuMjkzLDAuMjkzLTAuNzY4LDAtMS4wNjFMOC44ODIsNy44MjF6IiBmaWxsPSIjMWY5NzZjIi8+Cjwvc3ZnPgo=)
}

.iziToast.iziToast-warning {
    border-color: rgba(255, 183, 79, 0.3);
    background-color: #fff6e8;
    color: #ffb140
}

.iziToast.iziToast-warning > .iziToast-close {
    background-image: url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHZlcnNpb249IjEuMSIgdmlld0JveD0iMCAwIDE1LjY0MiAxNS42NDIiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDE1LjY0MiAxNS42NDIiIHdpZHRoPSIxNnB4IiBoZWlnaHQ9IjE2cHgiPgogIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgZD0iTTguODgyLDcuODIxbDYuNTQxLTYuNTQxYzAuMjkzLTAuMjkzLDAuMjkzLTAuNzY4LDAtMS4wNjEgIGMtMC4yOTMtMC4yOTMtMC43NjgtMC4yOTMtMS4wNjEsMEw3LjgyMSw2Ljc2TDEuMjgsMC4yMmMtMC4yOTMtMC4yOTMtMC43NjgtMC4yOTMtMS4wNjEsMGMtMC4yOTMsMC4yOTMtMC4yOTMsMC43NjgsMCwxLjA2MSAgbDYuNTQxLDYuNTQxTDAuMjIsMTQuMzYyYy0wLjI5MywwLjI5My0wLjI5MywwLjc2OCwwLDEuMDYxYzAuMTQ3LDAuMTQ2LDAuMzM4LDAuMjIsMC41MywwLjIyczAuMzg0LTAuMDczLDAuNTMtMC4yMmw2LjU0MS02LjU0MSAgbDYuNTQxLDYuNTQxYzAuMTQ3LDAuMTQ2LDAuMzM4LDAuMjIsMC41MywwLjIyYzAuMTkyLDAsMC4zODQtMC4wNzMsMC41My0wLjIyYzAuMjkzLTAuMjkzLDAuMjkzLTAuNzY4LDAtMS4wNjFMOC44ODIsNy44MjF6IiBmaWxsPSIjZTg4OTAwIi8+Cjwvc3ZnPgo=)
}

.iziToast.iziToast-danger {
    border-color: rgba(255, 82, 82, 0.3);
    background-color: #ffebeb;
    color: #ff5252
}

.iziToast.iziToast-danger > .iziToast-close {
    background-image: url(data:image/svg+xml;utf8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHZlcnNpb249IjEuMSIgdmlld0JveD0iMCAwIDE1LjY0MiAxNS42NDIiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDE1LjY0MiAxNS42NDIiIHdpZHRoPSIxNnB4IiBoZWlnaHQ9IjE2cHgiPgogIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgZD0iTTguODgyLDcuODIxbDYuNTQxLTYuNTQxYzAuMjkzLTAuMjkzLDAuMjkzLTAuNzY4LDAtMS4wNjEgIGMtMC4yOTMtMC4yOTMtMC43NjgtMC4yOTMtMS4wNjEsMEw3LjgyMSw2Ljc2TDEuMjgsMC4yMmMtMC4yOTMtMC4yOTMtMC43NjgtMC4yOTMtMS4wNjEsMGMtMC4yOTMsMC4yOTMtMC4yOTMsMC43NjgsMCwxLjA2MSAgbDYuNTQxLDYuNTQxTDAuMjIsMTQuMzYyYy0wLjI5MywwLjI5My0wLjI5MywwLjc2OCwwLDEuMDYxYzAuMTQ3LDAuMTQ2LDAuMzM4LDAuMjIsMC41MywwLjIyczAuMzg0LTAuMDczLDAuNTMtMC4yMmw2LjU0MS02LjU0MSAgbDYuNTQxLDYuNTQxYzAuMTQ3LDAuMTQ2LDAuMzM4LDAuMjIsMC41MywwLjIyYzAuMTkyLDAsMC4zODQtMC4wNzMsMC41My0wLjIyYzAuMjkzLTAuMjkzLDAuMjkzLTAuNzY4LDAtMS4wNjFMOC44ODIsNy44MjF6IiBmaWxsPSIjZWIwMDAwIi8+Cjwvc3ZnPgo=)
}

.modal {
    z-index: 9200
}

.modal-content {
    border-radius: 7px;
    border-color: #e1e7ec
}

.modal-header, .modal-body, .modal-footer {
    padding-right: 20px;
    padding-left: 20px
}

.modal-footer {
    padding-top: 12px;
    padding-bottom: 12px
}

.modal-footer .btn {
    margin-right: 0;
    margin-left: 12px
}

.modal-open.hasScrollbar .navbar-stuck {
    width: calc(100% - 15px)
}

.modal-backdrop {
    z-index: 9100
}

.example-modal .modal {
    display: block;
    position: relative;
    top: auto;
    right: auto;
    bottom: auto;
    left: auto;
    z-index: 1
}

.progress {
    height: auto;
    border-radius: 9px;
    background-color: #f0f0f0;
    font-size: 13px;
    font-weight: 500;
    line-height: 18px
}

.progress-bar {
    height: 18px;
    background-color: #fa6800
}

/*--------------------------------------------
16. Carousel & Slider Styles
--------------------------------------------*/
.owl-carousel {
    display: none;
    position: relative;
    width: 100%;
    z-index: 1;
    -webkit-tap-highlight-color: transparent
}

.owl-carousel .owl-stage {
    position: relative;
    -ms-touch-action: pan-Y;
    -moz-backface-visibility: hidden
}

.owl-carousel .owl-stage::after {
    display: block;
    height: 0;
    clear: both;
    line-height: 0;
    content: '.';
    visibility: hidden
}

.owl-carousel .owl-stage-outer {
    position: relative;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    overflow: hidden
}

.owl-carousel .owl-wrapper, .owl-carousel .owl-item {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden
}

.owl-carousel .owl-item {
    position: relative;
    min-height: 1px;
    float: left;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none
}

.owl-carousel .owl-item .owl-lazy {
    transition: opacity 400ms ease;
    opacity: 0
}

.owl-carousel .owl-item .from-top, .owl-carousel .owl-item .from-bottom, .owl-carousel .owl-item .from-left, .owl-carousel .owl-item .from-right, .owl-carousel .owl-item .scale-up, .owl-carousel .owl-item .scale-down {
    transition: all .45s .3s ease-in-out;
    opacity: 0;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden
}

.owl-carousel .owl-item .from-top {
    -webkit-transform: translateY(-45px);
    -ms-transform: translateY(-45px);
    transform: translateY(-45px)
}

.owl-carousel .owl-item .from-bottom {
    -webkit-transform: translateY(45px);
    -ms-transform: translateY(45px);
    transform: translateY(45px)
}

.owl-carousel .owl-item .from-left {
    -webkit-transform: translateX(-45px);
    -ms-transform: translateX(-45px);
    transform: translateX(-45px)
}

.owl-carousel .owl-item .from-right {
    -webkit-transform: translateX(45px);
    -ms-transform: translateX(45px);
    transform: translateX(45px)
}

.owl-carousel .owl-item .scale-up {
    -webkit-transform: scale(0.8);
    -ms-transform: scale(0.8);
    transform: scale(0.8)
}

.owl-carousel .owl-item .scale-down {
    -webkit-transform: scale(1.2);
    -ms-transform: scale(1.2);
    transform: scale(1.2)
}

.owl-carousel .owl-item .delay-1 {
    transition-delay: .5s
}

.owl-carousel .owl-item .delay-2 {
    transition-delay: .7s
}

.owl-carousel .owl-item .delay-3 {
    transition-delay: .9s
}

.owl-carousel .owl-item .delay-4 {
    transition-delay: 1.1s
}

.owl-carousel .owl-item.active .from-top, .owl-carousel .owl-item.active .from-bottom {
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
    opacity: 1
}

.owl-carousel .owl-item.active .from-left, .owl-carousel .owl-item.active .from-right {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
    opacity: 1
}

.owl-carousel .owl-item.active .scale-up, .owl-carousel .owl-item.active .scale-down {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
    opacity: 1
}

.owl-carousel .owl-item > img {
    display: block;
    width: 100%
}

.owl-carousel .owl-item > img.owl-lazy {
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d
}

.owl-carousel .owl-nav.disabled, .owl-carousel .owl-dots.disabled {
    display: none
}

.owl-carousel .owl-nav .owl-prev, .owl-carousel .owl-nav .owl-next, .owl-carousel .owl-dot {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.owl-carousel.owl-loaded {
    display: block
}

.owl-carousel.owl-loading {
    display: block;
    opacity: 0
}

.owl-carousel.owl-hidden {
    opacity: 0
}

.owl-carousel.owl-refresh .owl-item {
    visibility: hidden
}

.owl-carousel.owl-drag .owl-item {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.owl-carousel.owl-grab {
    cursor: move;
    cursor: grab
}

.owl-carousel.owl-rtl {
    direction: rtl
}

.owl-carousel.owl-rtl .owl-item {
    float: right
}

.owl-carousel .animated {
    -webkit-animation-duration: 1000ms;
    animation-duration: 1000ms;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.owl-carousel .owl-animated-in {
    z-index: 0
}

.owl-carousel .owl-animated-out {
    z-index: 1
}

.owl-carousel .fadeOut {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut
}

.owl-carousel .owl-dots {
    display: block;
    width: 100%;
    margin-top: 18px;
    text-align: center
}

.owl-carousel .owl-dots .owl-dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin: 0 6px;
    transition: opacity .25s;
    border-radius: 50%;
    background-color: #606975;
    opacity: .3
}

.owl-carousel .owl-dots .owl-dot.active {
    opacity: .9
}

.owl-carousel .owl-prev, .owl-carousel .owl-next {
    display: block;
    position: absolute;
    top: 50%;
    width: 44px;
    height: 44px;
    margin-top: -42px;
    transition: opacity .3s;
    border: 1px solid #e1e7ec;
    border-radius: 50%;
    background-color: #fff;
    color: #374250;
    line-height: 40px;
    text-align: center;
    opacity: .7
}

.owl-carousel .owl-prev:hover, .owl-carousel .owl-next:hover {
    opacity: 1
}

.owl-carousel .owl-prev::before, .owl-carousel .owl-next::before {
    font-family: feather;
    font-size: 19px;
    content: '\f115'
}

.owl-carousel .owl-prev {
    left: 20px
}

.owl-carousel .owl-prev::before {
    content: '\e094'
}

.owl-carousel .owl-next {
    right: 20px
}
@media only screen and (min-width:1200px) {
    .owl-carousel .owl-prev {
        left: -50px
    }
    .owl-carousel .owl-next {
        right: -50px
    }
}

.owl-carousel .owl-next::before {
    content: '\e095'
}

.owl-carousel.large-controls .owl-prev, .owl-carousel.large-controls .owl-next {
    width: 54px;
    height: 54px;
    margin-top: -47px;
    line-height: 50px
}

.owl-carousel.large-controls .owl-prev {
    left: 30px
}

.owl-carousel.large-controls .owl-next {
    right: 30px
}

.owl-carousel.dots-inside .owl-dots {
    position: absolute;
    bottom: 0;
    margin: 0;
    padding-bottom: 24px
}

.owl-carousel.dots-inside .owl-dots .owl-dot {
    background-color: #fff;
    opacity: .5
}
.home-4-hero .owl-carousel.dots-inside .owl-dots .owl-dot {
    background-color: #000;
}
.owl-carousel.dots-inside .owl-dots .owl-dot.active {
    opacity: 1;
    width: 40px;
    border-radius: 4px;
}

.owl-carousel.dots-inside .owl-prev, .owl-carousel.dots-inside .owl-next {
    margin-top: -22px
}

.owl-carousel.dots-inside.large-controls .owl-prev, .owl-carousel.dots-inside.large-controls .owl-next {
    margin-top: -27px
}

.owl-carousel .widget {
    margin-bottom: 0
}

.owl-carousel .widget.widget-featured-posts > .entry {
    margin-bottom: 0
}

.no-js .owl-carousel {
    display: block
}

@-webkit-keyframes fadeOut {
    0% {
        opacity: 1
    }
    100% {
        opacity: 0
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1
    }
    100% {
        opacity: 0
    }
}

.owl-height {
    transition: height 500ms ease-in-out
}

.hero-slider {
    width: 100%;
    /* min-height: 580px;
    background-position: center;
    background-color: #f5f5f5;
    background-repeat: no-repeat;
    background-size: cover; */
    overflow: hidden
}
.hero-item{
    position: relative;
    width: 100%;
}
.hero-slider-container{
    position: absolute;
    bottom: 0;
    right: 0;
}
.hero-slider > .owl-carousel {
    min-height: 580px
}

.hero-slider > .owl-carousel.dots-inside .owl-dots {
    display: inline-block;
    left: 50%;
    width: auto;
    padding: 10px 22px 14px;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    border-top-left-radius: 7px;
    border-top-right-radius: 7px;
}

.hero-slider > .owl-carousel .owl-prev, .hero-slider > .owl-carousel .owl-next {
    transition: all .35s ease-in-out
}

.hero-slider > .owl-carousel .owl-prev {
    -webkit-transform: translateX(-150px);
    -ms-transform: translateX(-150px);
    transform: translateX(-150px)
}

.hero-slider > .owl-carousel .owl-next {
    -webkit-transform: translateX(150px);
    -ms-transform: translateX(150px);
    transform: translateX(150px)
}

.hero-slider > .owl-carousel:hover .owl-prev, .hero-slider > .owl-carousel:hover .owl-next {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0)
}

@media (max-width: 991px) {
    .hero-slider > .owl-carousel .owl-prev, .hero-slider > .owl-carousel .owl-next {
        display: none
    }
}

@media (max-width: 1100px) {
    .hero-slider {
        min-height: 430px
    }

    .hero-slider > .owl-carousel {
        min-height: 430px
    }
}
@media (max-width: 990px) {
    .hero-slider {
        min-height: auto;
    }

    .hero-slider > .owl-carousel {
        min-height: auto;
    }
}

.gallery-item {
    margin-bottom: 30px
}

.gallery-item > a {
    display: block;
    position: relative;
    width: 100%;
    border-radius: 7px;
    overflow: hidden
}

.gallery-item > a > img {
    display: block;
    width: 100%
}

.gallery-item > a::before {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    content: '';
    opacity: 0;
    z-index: 1;
    transition: opacity .3s
}

.gallery-item > a::after {
    display: block;
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    margin-top: -19px;
    -webkit-transform: translateY(15px);
    -ms-transform: translateY(15px);
    transform: translateY(15px);
    transition: all .35s;
    color: #fff;
    font-family: feather;
    font-size: 26px;
    text-align: center;
    content: '\e036';
    opacity: 0;
    z-index: 5
}

.gallery-item > a:hover::before {
    opacity: .45
}

.gallery-item > a:hover::after {
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
    opacity: 1
}

.gallery-item > a[data-type='video']::after {
    left: 50%;
    width: 46px;
    height: 46px;
    margin-top: -22px;
    margin-left: -22px;
    padding-left: 5px;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
    border-radius: 50%;
    background-color: #fff;
    color: #606975;
    font-size: 27px;
    line-height: 42px;
    box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.25);
    content: '\e052';
    opacity: 1
}

.gallery-item .caption {
    display: none
}

.gallery-item.no-hover-effect > a::before {
    display: none
}

.grid-no-gap .gallery-item {
    margin-bottom: 0
}

.grid-no-gap .gallery-item > a {
    border-radius: 0
}

.owl-carousel .gallery-item {
    margin-bottom: 0
}

.pswp__zoom-wrap {
    text-align: center
}

.pswp__zoom-wrap::before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle
}

.wrapper {
    line-height: 0;
    width: 100%;
    max-width: 900px;
    position: relative;
    display: inline-block;
    vertical-align: middle;
    margin: 0 auto;
    text-align: left;
    z-index: 1045
}

.video-wrapper {
    position: relative;
    padding-bottom: 56.25%;
    padding-top: 25px;
    height: 0;
    width: 100%
}

.video-wrapper iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

video {
    width: 100% !important;
    height: auto !important
}

.pswp__caption__center {
    padding: 20px 10px;
    font-size: 14px;
    font-weight: 500;
    text-align: center
}

.countdown {
    display: inline-block
}

.countdown::after {
    display: block;
    clear: both;
    content: ''
}

.countdown .item {
    display: block;
    margin: 7px;
    float: left;
    text-align: center
}

.countdown .item .days, .countdown .item .hours, .countdown .item .minutes, .countdown .item .seconds {
    width: 48px;
    height: 48px;
    margin-bottom: 5px;
    border: 1px solid #e1e7ec;
    border-radius: 7px;
    font-size: 22px;
    line-height: 46px
}

.countdown .item .days_ref, .countdown .item .hours_ref, .countdown .item .minutes_ref, .countdown .item .seconds_ref {
    font-size: 12px;
    text-transform: uppercase
}

.countdown.countdown-inverse .item .days, .countdown.countdown-inverse .item .hours, .countdown.countdown-inverse .item .minutes, .countdown.countdown-inverse .item .seconds {
    border-color: rgba(255, 255, 255, 0.12);
    color: #fff
}

.countdown.countdown-inverse .item .days_ref, .countdown.countdown-inverse .item .hours_ref, .countdown.countdown-inverse .item .minutes_ref, .countdown.countdown-inverse .item .seconds_ref {
    color: rgba(255, 255, 255, 0.8)
}

/*--------------------------------------------
17. Sidebar & Widgets Styles
--------------------------------------------*/
.sidebar {
    width: 100%;
    max-width: 400px
}

.widget-title {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e1e7ec;
    color: #9da9b9;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase
}

.widget {
    margin-bottom: 38px
}

.widget .form-group {
    margin-bottom: 0
}

.widget ul {
    margin-bottom: 12px
}

.widget .market-button:last-child {
    margin-bottom: 0
}

.widget .custom-control:last-child {
    margin-bottom: 0 !important
}

.widget-categories ul, .widget-links ul {
    margin: 0;
    padding: 0;
    list-style: none
}

.widget-categories ul > li, .widget-links ul > li {
    position: relative;
    margin-bottom: 6px;
    padding-left: 14px
}

.widget-categories ul > li:last-child, .widget-links ul > li:last-child {
    margin-bottom: 0
}

.widget-categories ul > li::before, .widget-links ul > li::before {
    display: block;
    position: absolute;
    top: 12px;
    left: 0;
    width: 0;
    height: 0;
    -webkit-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotate(-90deg);
    transition: -webkit-transform .35s;
    transition: transform .35s;
    border-top: 4px dashed;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
    color: #9da9b9;
    content: ''
}

.widget-categories ul > li > a, .widget-links ul > li > a {
    display: inline-block;
    transition: color .3s;
    color: #606975;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none
}
.widget-categories ul > li.has-children > a:nth-child(1){
    background: #e6e6e6;
    padding: 4px 18px;
    border-radius: 20px;
    margin-bottom: 8px;
    font-size: 14px;
}
.widget-categories ul > li > a:hover, .widget-links ul > li > a:hover {
    color: #fa6800
}

.widget-categories ul > li.active > a, .widget-links ul > li.active > a {
    color: #fa6800
}

.widget-categories ul > li > span, .widget-links ul > li > span {
    margin-left: 4px;
    color: #9da9b9
}

.widget-categories ul > li.has-children ul, .widget-links ul > li.has-children ul {
    border-left: 1px solid #dee5ea
}

.widget-categories ul > li.has-children ul li::before, .widget-links ul > li.has-children ul li::before {
    top: 14px;
    width: 8px;
    height: 1px;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
    border: 0;
    background-color: #dee5ea;
    color: transparent
}

.widget-categories ul > li.has-children ul li a, .widget-links ul > li.has-children ul li a {
    font-size: 13px
}

.widget-categories ul > li.has-children ul ul > li, .widget-links ul > li.has-children ul ul > li {
    margin-bottom: 0
}

.widget-categories ul > li.has-children > ul, .widget-links ul > li.has-children > ul {
    max-height: 0;
    transition: max-height .6s;
    overflow: hidden
}

.widget-categories ul > li.has-children.expanded::before, .widget-links ul > li.has-children.expanded::before {
    -webkit-transform: rotate(0);
    -ms-transform: rotate(0);
    transform: rotate(0)
}

.widget-categories ul > li.has-children.expanded > ul, .widget-links ul > li.has-children.expanded > ul {
    max-height: 800px
}

.widget-featured-posts > .entry, .widget-featured-products > .entry {
    display: table;
    width: 100%;
    margin-bottom: 18px
}

.widget-featured-posts > .entry .entry-thumb, .widget-featured-posts > .entry .entry-content, .widget-featured-products > .entry .entry-thumb, .widget-featured-products > .entry .entry-content {
    display: table-cell;
    vertical-align: middle
}

.widget-featured-posts > .entry .entry-thumb, .widget-featured-products > .entry .entry-thumb {
    width: 62px;
    padding-right: 12px
}

.widget-featured-posts > .entry .entry-thumb > a, .widget-featured-products > .entry .entry-thumb > a {
    display: block;
    border-radius: 50%;
    overflow: hidden
}

.widget-featured-posts > .entry .entry-thumb > a > img, .widget-featured-products > .entry .entry-thumb > a > img {
    width: 100%
}

.widget-featured-posts > .entry .entry-title, .widget-featured-products > .entry .entry-title {
    margin-bottom: 0;
    font-size: 14px
}

.widget-featured-posts > .entry .entry-title > a, .widget-featured-products > .entry .entry-title > a {
    transition: color .3s;
    color: #606975;
    font-weight: 500;
    text-decoration: none
}

.widget-featured-posts > .entry .entry-title > a:hover, .widget-featured-products > .entry .entry-title > a:hover {
    color: #fa6800
}

.widget-featured-posts > .entry .entry-meta, .widget-featured-products > .entry .entry-meta {
    display: block;
    margin-bottom: 0;
    padding-top: 4px;
    color: #9da9b9;
    font-size: 12px
}

.widget-featured-products > .entry {
    margin-bottom: 12px
}

.widget-featured-products > .entry .entry-thumb > a {
    border-radius: 0
}

.widget-featured-products > .entry .entry-meta {
    color: #606975;
    font-size: 13px
}

.tag {
    display: inline-block;
    height: 28px;
    margin-right: 4px;
    margin-bottom: 8px;
    padding: 0 12px;
    transition: all .3s;
    border: 1px solid #e1e7ec;
    border-radius: 14px;
    color: #606975 !important;
    font-size: 13px;
    font-weight: 500;
    line-height: 25px;
    text-decoration: none !important;
    white-space: nowrap
}

.tag:hover {
    background-color: #f5f5f5;
    color: #606975 !important
}

.tag.active {
    border-color: #fa6811;
    background-color: #fa6800;
    color: #fff !important;
    cursor: default
}

.text-right .tag {
    margin-right: 0;
    margin-left: 4px
}

.text-center .tag {
    margin-right: 2px;
    margin-left: 2px
}

.widget-order-summary .table td {
    padding: 6px 0;
    border: 0
}

.widget-order-summary .table td:last-child {
    text-align: right
}

.widget-order-summary .table tr:first-child > td {
    padding-top: 0
}

.widget-order-summary .table tr:last-child > td {
    padding-top: 12px;
    border-top: 1px solid #e1e7ec
}

.widget-order-summary .table tr:nth-last-child(2) > td {
    padding-bottom: 12px
}

.noUi-target, .noUi-target * {
    -ms-touch-action: none;
    touch-action: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.noUi-target {
    position: relative;
    border-radius: 3px;
    direction: ltr
}

.noUi-base {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 1
}

.noUi-origin {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

.noUi-handle {
    position: relative;
    z-index: 1
}

.noUi-stacking .noUi-handle {
    z-index: 10
}

.noUi-state-tap .noUi-origin {
    transition: left .3s, top .3s
}

.noUi-state-drag * {
    cursor: inherit !important
}

.noUi-base, .noUi-handle {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

.noUi-horizontal {
    height: 4px
}

.noUi-horizontal .noUi-handle {
    top: -6px;
    left: -8px;
    width: 16px;
    height: 16px
}

.noUi-background {
    background: #ededed
}

.noUi-connect {
    transition: background .45s;
    background: #9da9b9
}

.noUi-draggable {
    cursor: w-resize
}

.noUi-vertical .noUi-draggable {
    cursor: n-resize
}

.noUi-handle {
    border: 4px solid #9da9b9;
    border-radius: 50%;
    background: #fff;
    cursor: default
}

.price-range-slider {
    padding-top: 9px
}

.price-range-slider .ui-range-slider-footer {
    display: table;
    width: 100%;
    padding-top: 30px
}

.price-range-slider .ui-range-slider-footer > .column {
    display: table-cell;
    vertical-align: middle
}

.price-range-slider .ui-range-slider-footer > .column:first-child {
    width: 40%
}

.price-range-slider .ui-range-slider-footer > .column:last-child {
    width: 60%;
    padding-left: 15px;
    text-align: right
}

.price-range-slider .ui-range-slider-footer .btn {
    height: 28px;
    margin: 0;
    padding: 0 15px;
    line-height: 26px
}

.price-range-slider .ui-range-values {
    display: inline-block
}

.price-range-slider .ui-range-values .ui-range-value-min, .price-range-slider .ui-range-values .ui-range-value-max {
    display: inline-block;
    font-size: 14px
}

.widget-light-skin .widget-title {
    border-color: rgba(255, 255, 255, 0.12);
    color: rgba(255, 255, 255, 0.5)
}

.widget-light-skin.widget-categories ul > li::before, .widget-light-skin.widget-links ul > li::before {
    color: rgba(255, 255, 255, 0.15)
}

.widget-light-skin.widget-categories ul > li > a, .widget-light-skin.widget-links ul > li > a {
    color: #fff
}

.widget-light-skin.widget-categories ul > li > a:hover, .widget-light-skin.widget-links ul > li > a:hover {
    color: #fa6800
}

.widget-light-skin.widget-categories ul > li.active > a, .widget-light-skin.widget-links ul > li.active > a {
    color: #fa6800
}

.widget-light-skin.widget-categories ul > li > span, .widget-light-skin.widget-links ul > li > span {
    color: rgba(255, 255, 255, 0.5)
}

.widget-light-skin.widget-featured-posts > .entry .entry-title > a, .widget-light-skin.widget-featured-products > .entry .entry-title > a {
    color: #fff
}

.widget-light-skin.widget-featured-posts > .entry .entry-title > a:hover, .widget-light-skin.widget-featured-products > .entry .entry-title > a:hover {
    color: #fa6800
}

.widget-light-skin.widget-featured-posts > .entry .entry-meta, .widget-light-skin.widget-featured-products > .entry .entry-meta {
    color: rgba(255, 255, 255, 0.5)
}

.widget-light-skin .tag {
    border-color: rgba(255, 255, 255, 0.12);
    color: #fff !important
}

.widget-light-skin .tag:hover {
    background-color: #f5f5f5;
    color: #606975 !important
}

.widget-light-skin .tag.active {
    border-color: #fa6811;
    background-color: #fa6800;
    color: #fff !important
}

/*--------------------------------------------
18. Steps Styles
--------------------------------------------*/
.steps .step {
    display: block;
    width: 100%;
    margin-bottom: 35px;
    text-align: center
}

.steps .step .step-icon-wrap {
    display: block;
    position: relative;
    width: 100%;
    height: 80px;
    text-align: center
}

.steps .step .step-icon-wrap::before, .steps .step .step-icon-wrap::after {
    display: block;
    position: absolute;
    top: 50%;
    width: 50%;
    height: 3px;
    margin-top: -1px;
    background-color: #e1e7ec;
    content: '';
    z-index: 1
}

.steps .step .step-icon-wrap::before {
    left: 0
}

.steps .step .step-icon-wrap::after {
    right: 0
}

.steps .step .step-icon {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
    border: 1px solid #e1e7ec;
    border-radius: 50%;
    background-color: #f5f5f5;
    color: #374250;
    font-size: 38px;
    line-height: 81px;
    z-index: 5
}

.steps .step .step-title {
    margin-top: 16px;
    margin-bottom: 0;
    color: #606975;
    font-size: 14px;
    font-weight: 500
}

.steps .step:first-child .step-icon-wrap::before {
    display: none
}

.steps .step:last-child .step-icon-wrap::after {
    display: none
}

.steps .step.completed .step-icon-wrap::before, .steps .step.completed .step-icon-wrap::after {
    background-color: #fa6800
}

.steps .step.completed .step-icon {
    border-color: #fa6811;
    background-color: #fa6800;
    color: #fff
}

@media (max-width: 576px) {
    .flex-sm-nowrap .step .step-icon-wrap::before, .flex-sm-nowrap .step .step-icon-wrap::after {
        display: none
    }
}

@media (max-width: 768px) {
    .flex-md-nowrap .step .step-icon-wrap::before, .flex-md-nowrap .step .step-icon-wrap::after {
        display: none
    }
}

@media (max-width: 991px) {
    .flex-lg-nowrap .step .step-icon-wrap::before, .flex-lg-nowrap .step .step-icon-wrap::after {
        display: none
    }

    .sidebar {
        max-width: 100%;
    }
}

@media (max-width: 1200px) {
    .flex-xl-nowrap .step .step-icon-wrap::before, .flex-xl-nowrap .step .step-icon-wrap::after {
        display: none
    }
}

.promo-box {
    position: relative;
    padding-right: 15px;
    padding-left: 15px;
    background-position: center;
    background-color: #f5f5f5;
    background-repeat: no-repeat;
    background-size: cover
}

.promo-box .overlay-dark, .promo-box .overlay-light {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    content: '';
    opacity: .5;
    z-index: 1
}

.promo-box .overlay-light {
    background-color: #fff
}

.promo-box .promo-box-content {
    position: relative;
    z-index: 5
}

.fw-section, .fh-section {
    position: relative;
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-size: cover
}

.fw-section > .overlay, .fh-section > .overlay {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    content: '';
    opacity: .6;
    z-index: 1
}

.fw-section > .container, .fw-section > .container-fluid, .fw-section > div, .fh-section > .container, .fh-section > .container-fluid, .fh-section > div {
    position: relative;
    z-index: 5
}

.fw-section.bg-fixed, .fh-section.bg-fixed {
    background-attachment: fixed
}

.fw-section.no-cover-bg, .fh-section.no-cover-bg {
    background-size: auto
}

.fw-section {
    width: 100%
}

.fh-section {
    height: 100vh
}

/*--------------------------------------------
19. Isotope Styles
--------------------------------------------*/
.isotope-grid {
    position: relative;
    overflow: hidden
}

.isotope-grid:not(.filter-grid) {
    min-height: 180px;
    background-position: center;
    background-image: url(../images/loading.gif);
    background-repeat: no-repeat;
    background-size: 74px
}

.isotope-grid:not(.filter-grid) > .grid-item {
    display: none
}

.isotope-grid:not(.filter-grid)[style] {
    background: none
}

.isotope-grid:not(.filter-grid)[style] > .grid-item {
    display: inline-block;
    -webkit-animation: showGrid .6s;
    animation: showGrid .6s
}

.isotope-grid .gutter-sizer {
    width: 30px
}

.isotope-grid .grid-item {
    margin-bottom: 30px
}

.isotope-grid .grid-item .post-tile, .isotope-grid .grid-item .portfolio-tile {
    margin-bottom: 0
}

.isotope-grid.cols-1 .gutter-sizer {
    width: 0
}

.isotope-grid.cols-1 .grid-sizer, .isotope-grid.cols-1 .grid-item {
    width: 100%
}

.isotope-grid.cols-2 .grid-sizer, .isotope-grid.cols-2 .grid-item {
    width: calc((100% / 2) - 15px)
}

.isotope-grid.cols-3 .grid-sizer, .isotope-grid.cols-3 .grid-item {
    width: calc((100% / 3) - 20px)
}

.isotope-grid.cols-4 .grid-sizer, .isotope-grid.cols-4 .grid-item {
    width: calc((100% / 4) - 22.5px)
}

.isotope-grid.cols-5 .grid-sizer, .isotope-grid.cols-5 .grid-item {
    width: calc((100% / 5) - 24px)
}

.isotope-grid.cols-6 .grid-sizer, .isotope-grid.cols-6 .grid-item {
    width: calc((100% / 6) - 25px)
}

@media (max-width: 1200px) {
    .isotope-grid.cols-6 .grid-sizer, .isotope-grid.cols-6 .grid-item, .isotope-grid.cols-5 .grid-sizer, .isotope-grid.cols-5 .grid-item, .isotope-grid.cols-4 .grid-sizer, .isotope-grid.cols-4 .grid-item, .isotope-grid.cols-3 .grid-sizer, .isotope-grid.cols-3 .grid-item {
        width: calc((100% / 3) - 22.5px)
    }
}

@media (max-width: 768px) {
    .isotope-grid.cols-6 .grid-sizer, .isotope-grid.cols-6 .grid-item, .isotope-grid.cols-5 .grid-sizer, .isotope-grid.cols-5 .grid-item, .isotope-grid.cols-4 .grid-sizer, .isotope-grid.cols-4 .grid-item, .isotope-grid.cols-3 .grid-sizer, .isotope-grid.cols-3 .grid-item {
        width: calc((100% / 2) - 15px)
    }
}

@media (max-width: 576px) {
    .isotope-grid.cols-6 .gutter-sizer, .isotope-grid.cols-5 .gutter-sizer, .isotope-grid.cols-4 .gutter-sizer, .isotope-grid.cols-3 .gutter-sizer, .isotope-grid.cols-2 .gutter-sizer {
        width: 0
    }

    .isotope-grid.cols-6 .grid-sizer, .isotope-grid.cols-6 .grid-item, .isotope-grid.cols-5 .grid-sizer, .isotope-grid.cols-5 .grid-item, .isotope-grid.cols-4 .grid-sizer, .isotope-grid.cols-4 .grid-item, .isotope-grid.cols-3 .grid-sizer, .isotope-grid.cols-3 .grid-item, .isotope-grid.cols-2 .grid-sizer, .isotope-grid.cols-2 .grid-item {
        width: 100%
    }
}

.isotope-grid.grid-no-gap .gutter-sizer {
    width: 0
}

.isotope-grid.grid-no-gap .grid-item {
    margin-bottom: 0
}

.isotope-grid.grid-no-gap.cols-2 .grid-sizer, .isotope-grid.grid-no-gap.cols-2 .grid-item {
    width: 50%
}

.isotope-grid.grid-no-gap.cols-3 .grid-sizer, .isotope-grid.grid-no-gap.cols-3 .grid-item {
    width: 33.3333333333%
}

.isotope-grid.grid-no-gap.cols-4 .grid-sizer, .isotope-grid.grid-no-gap.cols-4 .grid-item {
    width: 25%
}

.isotope-grid.grid-no-gap.cols-5 .grid-sizer, .isotope-grid.grid-no-gap.cols-5 .grid-item {
    width: 20%
}

.isotope-grid.grid-no-gap.cols-6 .grid-sizer, .isotope-grid.grid-no-gap.cols-6 .grid-item {
    width: 16.6666666667%
}

@media (max-width: 1200px) {
    .isotope-grid.grid-no-gap.cols-6 .grid-sizer, .isotope-grid.grid-no-gap.cols-6 .grid-item, .isotope-grid.grid-no-gap.cols-5 .grid-sizer, .isotope-grid.grid-no-gap.cols-5 .grid-item, .isotope-grid.grid-no-gap.cols-4 .grid-sizer, .isotope-grid.grid-no-gap.cols-4 .grid-item, .isotope-grid.grid-no-gap.cols-3 .grid-sizer, .isotope-grid.grid-no-gap.cols-3 .grid-item {
        width: 33.3333333333%
    }
}

@media (max-width: 768px) {
    .isotope-grid.grid-no-gap.cols-6 .grid-sizer, .isotope-grid.grid-no-gap.cols-6 .grid-item, .isotope-grid.grid-no-gap.cols-5 .grid-sizer, .isotope-grid.grid-no-gap.cols-5 .grid-item, .isotope-grid.grid-no-gap.cols-4 .grid-sizer, .isotope-grid.grid-no-gap.cols-4 .grid-item, .isotope-grid.grid-no-gap.cols-3 .grid-sizer, .isotope-grid.grid-no-gap.cols-3 .grid-item {
        width: 50%
    }
}

@media (max-width: 576px) {
    .isotope-grid.grid-no-gap.cols-6 .grid-sizer, .isotope-grid.grid-no-gap.cols-6 .grid-item, .isotope-grid.grid-no-gap.cols-5 .grid-sizer, .isotope-grid.grid-no-gap.cols-5 .grid-item, .isotope-grid.grid-no-gap.cols-4 .grid-sizer, .isotope-grid.grid-no-gap.cols-4 .grid-item, .isotope-grid.grid-no-gap.cols-3 .grid-sizer, .isotope-grid.grid-no-gap.cols-3 .grid-item, .isotope-grid.grid-no-gap.cols-2 .grid-sizer, .isotope-grid.grid-no-gap.cols-2 .grid-item {
        width: 100%
    }
}

@-webkit-keyframes showGrid {
    from {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

@keyframes showGrid {
    from {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

/*--------------------------------------------
20. Topbar & Navbar Styles
--------------------------------------------*/
.topbar {
    display: table;
    position: relative;
    width: 100%;
    height: 40px;
    padding: 0 30px;
    border-bottom: 1px solid #e1e7ec;
    background-color: #f5f5f5;
    z-index: 9010
}

.topbar .topbar-column {
    display: table-cell;
    vertical-align: middle
}

.topbar .topbar-column:last-child {
    text-align: right;
    max-width: 240px;
}

.topbar .topbar-column:first-child {
    text-align: left
}

.topbar .topbar-column a:not(.social-button), .topbar .topbar-column span, .topbar .topbar-column p {
    color: #606975;
    font-size: 13px
}

.topbar .topbar-column > a:not(.social-button), .topbar .topbar-column > span, .topbar .topbar-column > p {
    display: inline-block;
    margin-top: 5px;
    margin-bottom: 5px
}

.topbar .topbar-column > a:not(.social-button) > i, .topbar .topbar-column > span > i, .topbar .topbar-column > p > i {
    margin-top: -3px
}

.topbar .topbar-column > a:not(.social-button) > i.icon-download, .topbar .topbar-column > span > i.icon-download, .topbar .topbar-column > p > i.icon-download {
    margin-top: -4px
}

.topbar .topbar-column a:not(.social-button) {
    transition: color .3s;
    text-decoration: none
}

.topbar .topbar-column a:not(.social-button):hover {
    color: #fa6800
}

.topbar .topbar-column:last-child > a:not(.social-button), .topbar .topbar-column:last-child > span, .topbar .topbar-column:last-child > p {
    margin-left: 20px
}

.topbar .topbar-column:first-child > a:not(.social-button), .topbar .topbar-column:first-child > span, .topbar .topbar-column:first-child > p {
    margin-right: 20px
}

.topbar.topbar-ghost {
    position: absolute;
    top: 0;
    left: 0;
    border-bottom-color: rgba(255, 255, 255, 0.15);
    background-color: rgba(255, 255, 255, 0.05)
}

.topbar.topbar-ghost .topbar-column a:not(.social-button):not(.dropdown-item), .topbar.topbar-ghost .topbar-column span, .topbar.topbar-ghost .topbar-column p {
    color: #fff
}

.topbar.topbar-ghost .topbar-column a:not(.social-button):not(.dropdown-item):hover {
    color: #fa6800
}

.topbar.topbar-ghost .lang-currency-switcher-wrap .lang-currency-switcher > .currency {
    border-left-color: rgba(255, 255, 255, 0.15)
}

.topbar.topbar-ghost .dropdown-toggle::after {
    color: #fff
}

.lang-currency-switcher-wrap {
    display: inline-block;
    position: relative;
    margin-left: 22px;
    cursor: pointer;
    vertical-align: middle
}

.lang-currency-switcher-wrap .lang-currency-switcher > .language, .lang-currency-switcher-wrap .lang-currency-switcher > .currency, .lang-currency-switcher-wrap .lang-currency-switcher::after {
    display: inline-block;
    transition: color .3s;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    vertical-align: middle
}

.lang-currency-switcher-wrap .lang-currency-switcher > .language {
    width: 20px
}

.lang-currency-switcher-wrap .lang-currency-switcher > .currency {
    margin-left: 12px;
    padding: 4px 0 4px 12px;
}

.lang-currency-switcher-wrap .lang-currency-switcher::after {
    margin-left: 6px;
    margin-right: 8px;
    transition: all .3s
}

.lang-currency-switcher-wrap .lang-currency-switcher:hover > .language, .lang-currency-switcher-wrap .lang-currency-switcher:hover > .currency, .lang-currency-switcher-wrap .lang-currency-switcher:hover::after {
    color: #fa6800
}

.lang-currency-switcher-wrap.show > .lang-currency-switcher .currency, .lang-currency-switcher-wrap.show > .lang-currency-switcher::after {
    color: #fa6800
}

.lang-currency-switcher-wrap .dropdown-menu {
    right: 0;
    left: auto;
    padding-bottom: 12px
}

.lang-currency-switcher-wrap .dropdown-item {
    padding-top: 4px;
    padding-bottom: 4px
}

.lang-currency-switcher-wrap .dropdown-item > img {
    display: inline-block;
    width: 17px;
    margin-top: -1px;
    margin-right: 7px;
    vertical-align: middle
}

.lang-currency-switcher-wrap .currency-select {
    padding-top: 7px;
    padding-right: 20px;
    padding-bottom: 10px;
    padding-left: 20px
}
.mobile-account .dropdown-toggle {
    display: inline-block;
    position: relative;
    width: 44px;
    height: 44px;
    margin-left: 6px;
    transition: background-color .35s;
    border: 1px solid #e1e7ec;
    border-radius: 50%;
    color: #606975;
    font-size: 17px;
    cursor: pointer;
    vertical-align: middle;
    text-align: center;
    text-decoration: none;
}
    .lang-currency-switcher-wrap.mobile-account .lang-currency-switcher::after, .mobile-account .dropdown-toggle::after {
        display: none;
    }

.site-search {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transition: .3s;
    background-color: #fff;
    opacity: 0;
    visibility: hidden;
    z-index: 10
}

.site-search > input {
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    padding-right: 120px;
    padding-left: 30px;
    border: 0;
    background-color: #fff;
    color: #606975;
    font-size: 16px
}

.site-search > input:focus {
    outline: 0
}

.site-search > input::-moz-placeholder {
    color: #9da9b9;
    opacity: 1
}

.site-search > input:-ms-input-placeholder {
    color: #9da9b9
}

.site-search > input::-webkit-input-placeholder {
    color: #9da9b9
}

.site-search .search-tools {
    position: absolute;
    top: 50%;
    right: 30px;
    margin-top: -20px;
    z-index: 5
}

.site-search .search-tools .clear-search, .site-search .search-tools .close-search {
    display: inline-block;
    transition: color .25s;
    cursor: pointer;
    vertical-align: middle
}

.site-search .search-tools .clear-search {
    padding: 10px 0;
    color: #9da9b9;
    font-size: 11px;
    font-weight: 500;
    letter-spacing: .1em;
    text-transform: uppercase
}

.site-search .search-tools .clear-search:hover {
    color: #606975
}

.site-search .search-tools .close-search {
    margin-left: 10px;
    padding-top: 4px;
    padding-bottom: 10px;
    padding-left: 10px
}

.site-search .search-tools .close-search:hover {
    color: #9da9b9
}

.site-search.search-visible {
    opacity: 1;
    visibility: visible
}

@media (max-width: 1200px) {
    .site-search > input {
        padding-right: 105px;
        padding-left: 15px
    }

    .site-search .search-tools {
        right: 15px
    }
}

.navbar {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    min-height: 84px;
    border-bottom: 1px solid #e1e7ec;
    background-color: #fff;
    z-index: 9000
}

.navbar.navbar-stuck {
    position: fixed;
    box-shadow: 0 3px 25px 0 rgba(47, 56, 68, 0.22);
    z-index: 9010
}

.site-branding {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 5
}

.site-branding .inner {
    display: table;
    height: 100%;
    min-height: 100%
}

.site-logo, .offcanvas-toggle {
    display: table-cell;
    vertical-align: middle
}

.site-logo {
    width: 154px;
    padding: 12px 0 12px 25px;
    text-decoration: none
}

.site-logo > img {
    display: block;
    width: 100%
}

.site-logo.logo-stuck {
    display: none
}

.navbar-ghost.navbar-stuck .site-logo {
    display: none
}

.navbar-ghost.navbar-stuck .site-logo.logo-stuck {
    display: table-cell
}

.offcanvas-toggle {
    width: 74px;
    transition: color .3s;
    border-right: 1px solid #e1e7ec;
    color: #606975;
    font-size: 20px;
    line-height: 1;
    text-align: center;
    cursor: pointer
}

.offcanvas-toggle::before {
    display: inline-block;
    font-family: feather;
    content: '\e120'
}

.offcanvas-toggle:hover {
    color: #fa6800
}

.offcanvas-toggle.menu-toggle {
    display: none
}

.site-menu {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    z-index: 1
}

.site-menu ul {
    margin: 0 auto;
    padding: 0;
    list-style: none
}

.site-menu ul > li > a {
    padding: 0 15px;
    transition: color .3s;
    color: #000;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none
}

.site-menu ul > li:hover > a {
    color: #fa6800
}

.site-menu ul > li.active > a {
    color: #fa6800
}

.site-menu > ul {
    display: table;
    height: 100%;
    min-height: 100%
}

.site-menu > ul > li {
    display: table-cell;
    position: relative;
    vertical-align: middle
}

.site-menu > ul > li > a {
    display: table;
    height: 100%;
    min-height: 100%;
    border-top: 1px solid transparent;
    letter-spacing: .05em;
    text-transform: uppercase
}

.site-menu > ul > li > a > span {
    display: table-cell;
    vertical-align: middle
}

.site-menu > ul > li.active > a {
    border-top-color: #fa6800
}

.site-menu ul > li:hover > .sub-menu {
    display: block;
    -webkit-animation: submenu-show 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    animation: submenu-show 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55)
}

.site-menu ul > li:hover > .mega-menu {
    display: table;
    -webkit-animation: megamenu-show 0.45s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    animation: megamenu-show 0.45s cubic-bezier(0.68, -0.55, 0.265, 1.55)
}

.site-menu ul > li:hover > .mega-menu .sub-menu {
    -webkit-animation: none;
    animation: none
}

.site-menu > ul > li.has-megamenu {
    position: static
}

.toolbar {
    position: absolute;
    top: 0;
    right: 30px;
    height: 100%;
    z-index: 5
}

.toolbar .inner {
    display: table;
    height: 100%;
    min-height: 100%
}

.toolbar .tools {
    display: table-cell;
    vertical-align: middle
}

.toolbar .search, .toolbar .account, .toolbar .cart {
    display: inline-block;
    position: relative;
    width: 44px;
    height: 44px;
    margin-left: 6px;
    transition: background-color .35s;
    border: 1px solid #e1e7ec;
    border-radius: 50%;
    color: #606975;
    font-size: 17px;
    cursor: pointer;
    vertical-align: middle;
    text-align: center;
    text-decoration: none
}

.toolbar .search > i, .toolbar .account > i, .toolbar .cart > i {
    line-height: 39px
}

.toolbar .search > a, .toolbar .account > a, .toolbar .cart > a {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%
}

.toolbar .search:hover, .toolbar .account:hover, .toolbar .cart:hover {
    background-color: #f5f5f5
}

.toolbar .account > a::after, .toolbar .cart > a::after {
    display: block;
    position: absolute;
    top: 100%;
    width: 100%;
    height: 18px;
    margin-top: -10px;
    content: ''
}

.toolbar .search > i {
    line-height: 40px
}

.toolbar .cart {
/*    width: auto;
    padding: 0 16px;*/
    border-radius: 22px
}

.toolbar .cart > a {
    border-radius: 22px
}

.toolbar .cart > .count, .toolbar .cart > .subtotal {
    display: inline-block;
    margin-top: 2px;
    font-size: 14px;
    vertical-align: middle
}

.toolbar .cart > .count {
    margin-left: 5px;
    position: absolute;
    bottom: 0;
    right: 0px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #000;
    color: #fff;
}

.toolbar .cart > .subtotal {
    margin-left: 11px;
    padding: 1px 0 1px 12px;
    border-left: 1px solid #e1e7ec
}

@media (max-width: 1200px) {
    .toolbar .cart {
        padding: 0 14px
    }

    .toolbar .cart > .subtotal {
        display: none
    }
}

.toolbar-dropdown {
    margin-top: 7px !important;
    text-align: left
}

.toolbar .account > .toolbar-dropdown {
    left: 50%;
    margin-left: -100px
}

.toolbar .cart > .toolbar-dropdown {
    right: 0;
    left: auto;
    width: 260px;
    padding-right: 18px !important;
    padding-left: 18px !important
}

.toolbar .cart > .toolbar-dropdown > .toolbar-dropdown-group {
    display: table;
    width: 100%;
    padding-top: 9px;
    padding-bottom: 9px;
    border-top: 1px solid #e1e7ec
}

.toolbar .cart > .toolbar-dropdown > .toolbar-dropdown-group:last-child {
    border-top: 0
}

.toolbar .cart > .toolbar-dropdown > .toolbar-dropdown-group > .column {
    display: table-cell;
    vertical-align: top
}

.toolbar .cart > .toolbar-dropdown > .toolbar-dropdown-group > .column:first-child {
    padding-right: 6px
}

.toolbar .cart > .toolbar-dropdown > .toolbar-dropdown-group > .column:last-child {
    padding-left: 6px
}

.toolbar .cart > .toolbar-dropdown > .toolbar-dropdown-group .btn {
    margin: 0
}

.dropdown-product-item {
    display: table;
    position: relative;
    width: 100%;
    padding: 9px 0;
    border-top: 1px dashed #d8e0e6
}

.dropdown-product-item:first-child {
    border-top: 0
}

.dropdown-product-item > .dropdown-product-thumb, .dropdown-product-item > .dropdown-product-info {
    display: table-cell;
    vertical-align: top
}

.dropdown-product-item > .dropdown-product-thumb {
    width: 55px;
    border-radius: 50%;
    overflow: hidden
}

.dropdown-product-item > .dropdown-product-thumb > img {
    display: block;
    width: 100%
}

.dropdown-product-item > .dropdown-product-remove {
    display: block;
    position: absolute;
    top: 12px;
    right: 0;
    color: #ff5252;
    font-size: 13px;
    line-height: 1;
    cursor: pointer
}

.dropdown-product-item > .dropdown-product-info {
    padding-right: 15px;
    padding-left: 12px
}

.dropdown-product-item .dropdown-product-title {
    display: block;
    padding-top: 2px;
    transition: color .3s;
    color: #606975;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none
}

.dropdown-product-item .dropdown-product-title:hover {
    color: #fa6800
}

.dropdown-product-item .dropdown-product-details {
    color: #9da9b9;
    font-size: 14px
}

.toolbar .account:hover > .toolbar-dropdown, .toolbar .cart:hover > .toolbar-dropdown {
    display: block;
    -webkit-animation: submenu-show 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    animation: submenu-show 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55)
}

.navbar-ghost:not(.navbar-stuck) {
    position: absolute;
    top: 40px;
    border-bottom-color: rgba(255, 255, 255, 0.15);
    background-color: transparent
}

.navbar-ghost:not(.navbar-stuck) .offcanvas-toggle {
    border-right-color: rgba(255, 255, 255, 0.15);
    color: #fff
}

.navbar-ghost:not(.navbar-stuck) .offcanvas-toggle:hover {
    color: #fa6800
}

.navbar-ghost:not(.navbar-stuck) .site-menu > ul > li > a {
    color: #fff
}

.navbar-ghost:not(.navbar-stuck) .site-menu > ul > li:hover > a {
    color: #fa6800
}

.navbar-ghost:not(.navbar-stuck) .site-menu > ul > li.active > a {
    color: #fa6800
}

.navbar-ghost:not(.navbar-stuck) .toolbar .search, .navbar-ghost:not(.navbar-stuck) .toolbar .account, .navbar-ghost:not(.navbar-stuck) .toolbar .cart {
    border-color: rgba(255, 255, 255, 0.15);
    color: #fff
}

.navbar-ghost:not(.navbar-stuck) .toolbar .search:hover, .navbar-ghost:not(.navbar-stuck) .toolbar .account:hover, .navbar-ghost:not(.navbar-stuck) .toolbar .cart:hover {
    background-color: rgba(255, 255, 255, 0.07)
}

.navbar-ghost:not(.navbar-stuck) .toolbar .cart > .subtotal {
    border-left-color: rgba(255, 255, 255, 0.15)
}

.navbar-ghost:not(.navbar-stuck) .toolbar .toolbar-dropdown-group {
    color: #606975
}

@media (max-width: 1070px) {
    .navbar {
        min-height: 78px
    }

    .toolbar {
        right: 15px
    }

    .offcanvas-toggle.menu-toggle {
        display: table-cell
    }

    .site-menu, .offcanvas-toggle.cats-toggle, .toolbar .account, .toolbar-dropdown {
        display: none !important
    }
}

@media (max-width: 576px) {
    .topbar {
        padding-right: 15px;
        padding-left: 15px
    }

    .offcanvas-toggle {
        width: 56px
    }

    .site-logo {
        width: 145px;
        padding-left: 16px
    }
}

/*--------------------------------------------
21. Titles & Breadcrumbs Styles
--------------------------------------------*/
.page-title {
    width: 100%;
    margin-bottom: 60px;
    padding: 36px 0;
    border-bottom: 1px solid #e1e7ec;
    background-color: #f5f5f5
}

.page-title > .container, .page-title > .container-fluid {
    display: table
}

.page-title .column {
    display: table-cell;
    vertical-align: middle
}

.page-title .column:first-child {
    padding-right: 20px
}

.page-title h1, .page-title h2, .page-title h3 {
    margin: 0;
    font-size: 24px;
    font-weight: normal;
    line-height: 1.25
}

@media (max-width: 576px) {
    .page-title {
        margin-bottom: 53px
    }
}

.breadcrumbs {
    display: block;
    margin: 0;
    padding: 0;
    list-style: none;
    text-align: right
}

.breadcrumbs > li {
    display: inline-block;
    margin-left: 5px;
    padding: 5px 0;
    color: #9da9b9;
    font-size: 14px;
    cursor: default;
    vertical-align: middle
}

.breadcrumbs > li.separator {
    width: 3px;
    height: 3px;
    margin-top: 2px;
    padding: 0;
    border-radius: 50%;
    background-color: #9da9b9
}

.breadcrumbs > li > a {
    transition: color .25s;
    color: #606975;
    text-decoration: none
}

.breadcrumbs > li > a:hover {
    color: #fa6800
}

@media (max-width: 768px) {
    .page-title > .container, .page-title > .container-fluid {
        display: block
    }

    .page-title .column {
        display: block;
        width: 100%;
        text-align: center
    }

    .page-title .column:first-child {
        padding-right: 0
    }

    .breadcrumbs {
        padding-top: 10px;
        text-align: center
    }

    .breadcrumbs > li {
        margin-left: 3px;
        margin-right: 3px
    }
}

.offcanvas-wrapper {
    position: relative;
    min-height: 100vh;
    background-color: #fff;
    z-index: 10
}

.offcanvas-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 290px;
    height: 100%;
    background-color: #000;
    box-shadow: inset -4px 0 17px 0 rgba(0, 0, 0, 0.35);
    visibility: hidden;
    z-index: 2025;
    overflow-y: auto
}

.offcanvas-container.active {
    visibility: visible
}

.offcanvas-header {
    padding: 28px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.12)
}

.offcanvas-header .offcanvas-title {
    margin-bottom: 0;
    color: rgba(255, 255, 255, 0.5);
    font-size: 16px;
    font-weight: 500
}

/*--------------------------------------------
22. Account Styles
--------------------------------------------*/
.account-link {
    display: table;
    width: 100%;
    padding: 20px 18px;
    transition: background-color .3s;
    border-bottom: 1px solid rgba(255, 255, 255, 0.12);
    background-color: #313b47;
    text-decoration: none
}

.account-link .user-ava, .account-link .user-info {
    display: table-cell;
    vertical-align: middle
}

.account-link .user-ava {
    width: 48px
}

.account-link .user-ava > img {
    display: block;
    width: 48px;
    padding: 3px;
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 50%
}

.account-link .user-info {
    padding-left: 8px
}

.account-link .user-info > .user-name {
    margin-bottom: 2px;
    color: #fff
}

.account-link .user-info > span {
    display: block
}

.account-link:hover {
    background-color: #394453
}

.site-backdrop {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #fff;
    content: '';
    opacity: 0;
    z-index: 9980;
    position: fixed;
    transition: opacity .35s, visibility .35s;
    cursor: pointer;
    visibility: hidden
}

.no-csstransforms3d .offcanvas-wrapper, .no-csstransforms3d .navbar, .no-csstransforms3d .topbar {
    transition: left .4s ease-in-out, background-color .2s
}

.no-csstransforms3d .site-backdrop {
    transition: left .4s ease-in-out, opacity .35s, visibility .35s
}

.no-csstransforms3d .offcanvas-open .offcanvas-wrapper, .no-csstransforms3d .offcanvas-open .site-backdrop, .no-csstransforms3d .offcanvas-open .navbar, .no-csstransforms3d .offcanvas-open .topbar {
    left: 290px
}

.no-csstransforms3d .offcanvas-open .site-backdrop {
    opacity: .2;
    visibility: visible
}

.csstransforms3d .offcanvas-wrapper, .csstransforms3d .navbar, .csstransforms3d .topbar {
    transition: -webkit-transform .4s ease-in-out, background-color .2s;
    transition: transform .4s ease-in-out, background-color .2s
}

.csstransforms3d .site-backdrop {
    transition: -webkit-transform .4s ease-in-out, opacity .35s, visibility .35s;
    transition: transform .4s ease-in-out, opacity .35s, visibility .35s
}

.csstransforms3d .offcanvas-open .offcanvas-wrapper, .csstransforms3d .offcanvas-open .site-backdrop, .csstransforms3d .offcanvas-open .navbar, .csstransforms3d .offcanvas-open .topbar {
    -webkit-transform: translate3d(290px, 0, 0);
    transform: translate3d(290px, 0, 0)
}

.csstransforms3d .offcanvas-open .site-backdrop {
    opacity: .2;
    visibility: visible
}

/*--------------------------------------------  
23. Footer Styles
--------------------------------------------*/
.site-footer {
    padding-top: 72px;
    background-color: #000
}

.site-footer .widget .text-white i {
    margin-top: -4px;
    margin-right: 8px;
}
.site-footer .widget-categories ul > li::before, .site-footer .widget-links ul > li::before{
    display: none;
}

@media (max-width: 768px) {
    .site-footer {
        padding-top: 48px
    }
}

.footer-copyright {
    margin: 0;
    padding-top: 30px;
    padding-bottom: 30px;
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
    font-weight: normal
}

.footer-copyright > a {
    transition: color .25s;
    color: rgba(255, 255, 255, 0.5);
    text-decoration: none
}

.footer-copyright > a:hover {
    color: #fa6800
}

.footer-light {
    background-color: #f5f5f5
}

.footer-light .footer-copyright {
    color: #9da9b9
}

.footer-light .footer-copyright > a {
    color: #9da9b9
}

.footer-light .footer-copyright > a:hover {
    color: #fa6800
}

.user-info-wrapper {
    position: relative;
    width: 100%;
    margin-bottom: -1px;
    padding-top: 90px;
    padding-bottom: 30px;
    border: 1px solid #e1e7ec;
    border-top-left-radius: 7px;
    border-top-right-radius: 7px;
    overflow: hidden
}

.user-info-wrapper .user-cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 120px;
    background-position: center;
    background-color: #f5f5f5;
    background-repeat: no-repeat;
    background-size: cover
}

.user-info-wrapper .user-cover .tooltip .tooltip-inner {
    width: 230px;
    max-width: 100%;
    padding: 10px 15px
}

.user-info-wrapper .info-label {
    display: block;
    position: absolute;
    top: 18px;
    right: 18px;
    height: 26px;
    padding: 0 12px;
    border-radius: 13px;
    background-color: #fff;
    color: #606975;
    font-size: 12px;
    line-height: 26px;
    box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.18);
    cursor: pointer
}

.user-info-wrapper .info-label > i {
    display: inline-block;
    margin-right: 3px;
    font-size: 1.2em;
    vertical-align: middle
}

.user-info-wrapper .user-info {
    display: table;
    position: relative;
    width: 100%;
    padding: 0 18px;
    z-index: 5
}

.user-info-wrapper .user-info .user-avatar, .user-info-wrapper .user-info .user-data {
    display: table-cell;
    vertical-align: top
}

.user-info-wrapper .user-info .user-avatar {
    position: relative;
    width: 115px
}

.user-info-wrapper .user-info .user-avatar > img {
    display: block;
    width: 100%;
    border: 5px solid #fff;
    border-radius: 50%
}

.user-info-wrapper .user-info .user-avatar .edit-avatar {
    display: block;
    position: absolute;
    top: -2px;
    right: 2px;
    width: 36px;
    height: 36px;
    transition: opacity .3s;
    border-radius: 50%;
    background-color: #fff;
    color: #606975;
    line-height: 34px;
    box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.2);
    cursor: pointer;
    opacity: 0;
    text-align: center;
    text-decoration: none
}

.user-info-wrapper .user-info .user-avatar .edit-avatar::before {
    font-family: feather;
    font-size: 17px;
    content: '\e058'
}

.user-info-wrapper .user-info .user-avatar:hover .edit-avatar {
    opacity: 1
}

.user-info-wrapper .user-info .user-data {
    padding-top: 48px;
    padding-left: 12px
}

.user-info-wrapper .user-info .user-data h4 {
    margin-bottom: 2px
}

.user-info-wrapper .user-info .user-data span {
    display: block;
    color: #9da9b9;
    font-size: 13px
}

.user-info-wrapper + .list-group .list-group-item:first-child {
    border-radius: 0
}

/*--------------------------------------------
24. Products Styles
--------------------------------------------*/
.product-card {
    display: block;
    position: relative;
    width: 100%;
    padding: 18px;
    border: 1px solid #e1e7ec;
    border-radius: 7px;
    background-color: #fff
}

.product-card .product-thumb {
    position: relative;
    display: block;
    width: 100%;
    padding-bottom: 100%;
}

.product-card .product-thumb > img {
    max-width: 100%;
}
.product-grid .product-card .product-thumb > img, .product-list .product-card .product-thumb > img {
    position: absolute;
    max-width: 100%;
    display: block;
    -webkit-transition: all ease-out 0.4s;
    transition: all ease-out 0.4s;
}
.product-list .product-card .product-thumb > img{
    top:0;
    left:0;
}
.product-card .product-thumb > img.hover {
    visibility: hidden;
    opacity: 0;
}
.product-card:hover .product-thumb > img.hover{
    visibility:visible;
    opacity:1;
}
.product-card:hover .product-thumb > img.primary{
    opacity:0;
}
.product-labels-img.product-labels {
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
}
.product-single-img .product-labels-img.product-labels {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
}
.product-labels .lbl {
    display: block;
    white-space: nowrap;
    color: #fff;
    font-size: 11px;
    font-weight: 400;
    text-transform: uppercase;
    text-align: center;
    padding: 0 5px;
    height: 20px;
    line-height: 20px;
    margin-bottom: 5px;
}
.product-labels .on-sale {
    background: #f54337;
}
.product-labels .pr-label1 {
    background: #198754;
}
.product-card .product-thumb .product-labels, .product-single.product-labels {
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    z-index:20;
}
.product-single.product-labels {
    left: 15px;
    top: 15px;
}
.product-labels.rectangular .lbl {
    display: block;
    width: 50px;
}


.product-card .product-title {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: normal;
    text-align: center
}

.product-card .product-title > a {
    transition: color .3s;
    color: #374250;
    text-decoration: none
}

.product-card .product-title > a:hover {
    color: #fa6800
}

.product-card .product-price {
    margin-bottom: 10px;
    color: #606975;
    font-size: 16px;
    font-weight: 500;
    text-align: center
}

.product-card .product-price > del {
    margin-right: 5px;
    color: #9da9b9
}

.product-card .product-buttons {
    padding: 12px 0 8px;
    text-align: center
}

.product-card .product-buttons > .btn {
    margin: 0 4px
}

.product-card .product-buttons > .btn.btn-wishlist {
    width: 36px;
    padding: 0;
    padding-left: 1px;
    border-radius: 50%
}

.product-card .product-buttons > .btn.btn-wishlist > i {
    font-size: 1.2em
}

.product-card .product-buttons > .btn.btn-wishlist.active {
    color: #ff5252
}

.product-card .product-badge {
    top: 15px;
    left: 18px
}

.product-card .rating-stars {
    position: absolute;
    top: 15px;
    right: 18px;
    z-index:20;
}

.product-card.product-list {
    margin-bottom: 30px
}

@media (min-width: 576px) {
    .product-card.product-list {
        display: table;
        width: 100%;
    }

    .product-card.product-list .product-thumb, .product-card.product-list .product-info {
        display: table-cell;
        vertical-align: middle
    }

    .product-card.product-list .product-thumb {
        position: relative;
        width: 250px;
        height:250px;
        padding: 20px 18px;
        border-right: 1px solid #e1e7ec
    }

    .product-card.product-list .product-info {
        padding: 20px 22px
    }

    .product-card.product-list .product-info .product-title, .product-card.product-list .product-info .product-price, .product-card.product-list .product-info .product-buttons {
        text-align: left
    }

    .product-card.product-list .product-info .product-buttons {
        padding-top: 20px;
        padding-bottom: 0;
        border-top: 1px solid #e1e7ec
    }

    .product-card.product-list .product-info .product-buttons > .btn {
        margin: 0;
        margin-right: 8px
    }

    .product-card.product-list .product-title {
        font-size: 18px
    }
}

.product-badge {
    position: absolute;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: .07em;
    line-height: 2;
    margin-left: 10px;
}

.rating-stars {
    display: inline-block
}

.rating-stars > i {
    display: inline-block;
    margin-right: 2px;
    color: #cad0d9;
    font-size: 14px
}

.rating-stars > i.filled {
    color: #ffb74f
}

.rating-stars > i:last-child {
    margin-right: 0
}

.shop-toolbar {
    display: table;
    width: 100%
}

.shop-toolbar > .column {
    display: table-cell;
    vertical-align: middle
}

.shop-toolbar > .column:last-child {
    text-align: right
}

@media (max-width: 576px) {
    .shop-toolbar > .column {
        display: block;
        width: 100%;
        text-align: center
    }

    .shop-toolbar > .column:last-child {
        padding-top: 24px;
        text-align: center
    }
}

.shop-sorting label, .shop-sorting .form-control, .shop-sorting span {
    display: inline-block;
    vertical-align: middle
}

.shop-sorting span {
    padding: 8px 0
}

.shop-sorting label {
    margin: 0;
    padding: 8px 5px 8px 0;
    color: #9da9b9;
    font-size: 14px;
    font-weight: normal
}

.shop-sorting .form-control {
    width: 100%;
    max-width: 186px;
    margin-right: 10px
}

@media (max-width: 576px) {
    .shop-sorting label, .shop-sorting .form-control {
        display: block;
        width: 100%;
        max-width: 100%;
        margin: 0;
        padding-top: 0;
        padding-right: 0
    }
}

.shop-view {
    display: inline-block
}

.shop-view::after {
    display: block;
    clear: both;
    content: ''
}

.shop-view > a {
    display: block;
    width: 43px;
    height: 43px;
    margin-left: 10px;
    padding: 13px;
    float: left;
    transition: background-color .35s;
    border: 1px solid #e1e7ec;
    border-radius: 50%;
    background-color: #fff
}

.shop-view > a span {
    display: block;
    position: relative;
    width: 3px;
    height: 3px;
    margin-bottom: 3px;
    background-color: #606975
}

.shop-view > a span::before, .shop-view > a span::after {
    display: block;
    position: absolute;
    background-color: #606975
}

.shop-view > a span:last-child {
    margin-bottom: 0
}

.shop-view > a:hover {
    background-color: #f5f5f5
}

.shop-view > a.active {
    border-color: #fa6811;
    background-color: #fa6800;
    cursor: default;
    pointer-events: none
}

.shop-view > a.active span, .shop-view > a.active span::before, .shop-view > a.active span::after {
    background-color: #fff
}

.shop-view > a.grid-view span::before, .shop-view > a.grid-view span::after {
    top: 0;
    width: 3px;
    height: 3px;
    content: ''
}

.shop-view > a.grid-view span::before {
    left: 6px
}

.shop-view > a.grid-view span::after {
    left: 12px
}

.shop-view > a.list-view span::before {
    top: 1px;
    left: 6px;
    width: 9px;
    height: 1px;
    content: ''
}

.shopping-cart, .wishlist-table, .order-table {
    margin-bottom: 20px
}

.shopping-cart .table, .wishlist-table .table, .order-table .table {
    margin-bottom: 0
}

.shopping-cart .btn, .wishlist-table .btn, .order-table .btn {
    margin: 0
}

.shopping-cart > table > thead > tr > th, .shopping-cart > table > thead > tr > td, .shopping-cart > table > tbody > tr > th, .shopping-cart > table > tbody > tr > td, .wishlist-table > table > thead > tr > th, .wishlist-table > table > thead > tr > td, .wishlist-table > table > tbody > tr > th, .wishlist-table > table > tbody > tr > td, .order-table > table > thead > tr > th, .order-table > table > thead > tr > td, .order-table > table > tbody > tr > th, .order-table > table > tbody > tr > td {
    vertical-align: middle !important
}

.shopping-cart > table thead th, .wishlist-table > table thead th, .order-table > table thead th {
    padding-top: 17px;
    padding-bottom: 17px;
    border-width: 1px
}

.shopping-cart .remove-from-cart, .wishlist-table .remove-from-cart, .order-table .remove-from-cart {
    display: inline-block;
    color: #ff5252;
    font-size: 18px;
    line-height: 1;
    text-decoration: none
}

.shopping-cart .count-input, .wishlist-table .count-input, .order-table .count-input {
    display: inline-block;
    width: 100%;
    width: 86px
}

.shopping-cart .product-item, .order-table .product-item {
    display: table;
    width: 100%;
    min-width: 150px;
    margin-top: 5px;
    margin-bottom: 3px
}
.wishlist-table .product-item{
    display: table;
    width: 100%;
    min-width: 150px;
}

.shopping-cart .product-item .product-thumb, .shopping-cart .product-item .product-info, .wishlist-table .product-item .product-thumb, .wishlist-table .product-item .product-info, .order-table .product-item .product-thumb, .order-table .product-item .product-info {
    display: table-cell;
    vertical-align: top
}

.shopping-cart .product-item .product-thumb, .wishlist-table .product-item .product-thumb, .order-table .product-item .product-thumb {
    width: 120px;
    padding-right: 20px
}
.wishlist-table table .rating-stars{
    min-width: 100px;
}
.shopping-cart .product-item .product-thumb > img, .wishlist-table .product-item .product-thumb > img, .order-table .product-item .product-thumb > img {
    display: block;
    width: 100%
}

@media screen and (max-width: 860px) {
    .shopping-cart .product-item .product-thumb, .wishlist-table .product-item .product-thumb, .order-table .product-item .product-thumb {
        display: none
    }
    .wishlist-table table .rating-stars {
        min-width: auto;
    }
}

.shopping-cart .product-item .product-info span, .wishlist-table .product-item .product-info span, .order-table .product-item .product-info span {
    display: block;
    font-size: 13px
}

.shopping-cart .product-item .product-info span > em, .wishlist-table .product-item .product-info span > em, .order-table .product-item .product-info span > em {
    font-weight: 500;
    font-style: normal
}

.shopping-cart .product-item .product-title, .wishlist-table .product-item .product-title, .order-table .product-item .product-title {
    margin-bottom: 6px;
    padding-top: 5px;
    font-size: 16px;
    font-weight: 500
}

.shopping-cart .product-item .product-title > a, .wishlist-table .product-item .product-title > a, .order-table .product-item .product-title > a {
    transition: color .3s;
    color: #374250;
    line-height: 1.5;
    text-decoration: none
}

.shopping-cart .product-item .product-title > a:hover, .wishlist-table .product-item .product-title > a:hover, .order-table .product-item .product-title > a:hover {
    color: #fa6800
}

.shopping-cart .product-item .product-title small, .wishlist-table .product-item .product-title small, .order-table .product-item .product-title small {
    display: inline;
    margin-left: 6px;
    font-weight: 500
}

.wishlist-table .product-item .product-thumb {
    display: table-cell !important
}

@media screen and (max-width: 576px) {
    .wishlist-table .product-item .product-thumb {
        display: none !important
    }
}

.shopping-cart-footer {
    display: table;
    width: 100%;
    padding: 10px 0;
    border-top: 1px solid #e1e7ec
}

.shopping-cart-footer > .column {
    display: table-cell;
    padding: 5px 0;
    vertical-align: middle
}

.shopping-cart-footer > .column:last-child {
    text-align: right
}

.shopping-cart-footer > .column:last-child .btn {
    margin-right: 0;
    margin-left: 15px
}

@media (max-width: 768px) {
    .shopping-cart-footer > .column {
        display: block;
        width: 100%
    }

    .shopping-cart-footer > .column:last-child {
        text-align: center
    }

    .shopping-cart-footer > .column .btn {
        width: 100%;
        margin: 12px 0 !important
    }
}

/*--------------------------------------------
25. Checkout Styles
--------------------------------------------*/
.checkout-steps {
    margin-bottom: 40px
}

.checkout-steps::after {
    display: block;
    clear: both;
    content: ''
}

.checkout-steps > a {
    display: block;
    position: relative;
    width: 25%;
    height: 55px;
    float: right;
    transition: color .3s;
    border-top: 1px solid #e1e7ec;
    border-bottom: 1px solid #e1e7ec;
    background-color: #fff;
    color: #606975;
    font-size: 14px;
    font-weight: 500;
    line-height: 53px;
    text-decoration: none;
    text-align: center
}

.checkout-steps > a > .angle {
    display: block;
    position: absolute;
    top: 0;
    right: -13px;
    width: 27px;
    height: 53px;
    background-color: #fff
}

.checkout-steps > a > .angle::before, .checkout-steps > a > .angle::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 0;
    border: solid transparent;
    content: '';
    pointer-events: none
}

.checkout-steps > a > .angle::after {
    border-width: 26px;
    border-color: transparent;
    border-left-color: #fff
}

.checkout-steps > a > .angle::before {
    margin-top: -1px;
    border-width: 27px;
    border-color: transparent;
    border-left-color: #d8e0e6
}

.checkout-steps > a:hover {
    color: #fa6800
}

.checkout-steps > a.active {
    background-color: #fa6800;
    color: #fff;
    cursor: default;
    pointer-events: none
}

.checkout-steps > a.active > .angle::after {
    border-left-color: #fa6800
}

.checkout-steps > a.active + a > .angle {
    background-color: #fa6800
}

.checkout-steps > a.completed > .step-indicator {
    display: inline-block;
    margin-top: -5px;
    margin-right: 7px;
    border-radius: 50%;
    color: #43d9a3;
    font-size: 18px;
    line-height: 20px;
    text-align: center;
    vertical-align: middle
}

.checkout-steps > a.completed:hover {
    color: #606975
}

.checkout-steps > a:first-child {
    border-right: 1px solid #e1e7ec;
    border-top-right-radius: 7px;
    border-bottom-right-radius: 7px
}

.checkout-steps > a:last-child {
    border-left: 1px solid #e1e7ec;
    border-top-left-radius: 7px;
    border-bottom-left-radius: 7px
}

@media (max-width: 576px) {
    .checkout-steps > a {
        width: 100%;
        margin-bottom: 10px;
        float: none;
        border: 1px solid #e1e7ec;
        border-radius: 7px
    }

    .checkout-steps > a > .angle {
        display: none
    }
}

.checkout-footer {
    display: table;
    width: 100%;
    margin-top: 28px;
    padding-top: 5px;
    padding-bottom: 5px;
    border: 1px solid #e1e7ec;
    border-radius: 7px;
    table-layout: fixed
}

.checkout-footer > .column {
    display: table-cell;
    padding: 10px 15px;
    vertical-align: middle
}

.checkout-footer > .column:last-child {
    text-align: right
}

.checkout-footer > .column:first-child {
    text-align: left
}

.checkout-footer .btn {
    margin: 0
}

.sp-buttons .btn {
    margin: 0;
    margin-left: 8px
}

.sp-buttons .btn.btn-wishlist {
    width: 36px;
    padding: 0;
    padding-left: 1px;
    border-radius: 50%
}

.sp-buttons .btn.btn-wishlist > i {
    font-size: 1.2em
}

.sp-buttons .btn.btn-wishlist.active {
    color: #ff5252
}

.product-gallery {
    position: relative;
    padding-top: 15px;
    padding-right: 15px;
    padding-bottom: 15px;
    padding-left: 15px;
    border: 1px solid #e1e7ec;
    border-radius: 7px
}

.product-gallery .gallery-item {
    position: absolute;
    top: 15px;
    right: 15px;
    margin-bottom: 0;
    z-index:20;
}

.product-gallery .gallery-item > a {
    display: none;
    width: 44px;
    height: 44px;
    transition: background-color .3s;
    border: 1px solid #e1e7ec;
    border-radius: 50%;
    text-decoration: none
}

.product-gallery .gallery-item > a::before {
    display: none !important
}

.product-gallery .gallery-item > a::after {
    position: static;
    top: 0;
    margin-top: 0;
    margin-left: 1px;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
    transition: none;
    color: #606975;
    font-size: 22px;
    line-height: 40px;
    content: '\e037';
    opacity: 1
}

.product-gallery .gallery-item > a:hover {
    background-color: #f5f5f5
}

.product-gallery .gallery-item.active > a {
    display: block
}

.product-gallery .product-badge {
    top: 25px;
    left: 15px
}

.product-gallery .product-thumbnails.with-sidebar > li a {
    width: 70px;
    border-radius: 2px;
}

.product-gallery .product-thumbnails {
    display: block;
    margin: 0;
    margin-top: 24px;
    padding: 0;
    list-style: none;
    text-align: center
}

.product-gallery .product-thumbnails.with-sidebar > li {
    margin: 0
}

.product-gallery .product-thumbnails > li {
    display: inline-block;
    margin: 0 3px 10px
}

.product-gallery .product-thumbnails > li > a {
    display: block;
    width: 94px;
    transition: border-color .25s;
    border: 1px solid #e1e7ec;
    border-radius: 5px;
    overflow: hidden
}

.product-gallery .product-thumbnails > li.active > a {
    border-color: #fa6800;
    cursor: default
}

@media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none) {
    .product-gallery .gallery-item {
        display: none !important
    }
}

/*--------------------------------------------
26. Blog Styles
--------------------------------------------*/
.post-meta {
    display: block;
    margin-bottom: 24px;
    padding: 0;
    border: 1px solid #e1e7ec;
    border-radius: 7px;
    list-style: none
}

.post-meta > li {
    display: block;
    margin: 0;
    padding: 9px 12px;
    border-bottom: 1px solid #e1e7ec;
    color: #606975;
    font-size: 13px;
    cursor: default
}

.post-meta > li:last-child {
    border-bottom: 0
}

.post-meta > li > i, .post-meta > li > a {
    display: inline-block;
    vertical-align: middle
}

.post-meta > li > i {
    margin-top: -2px;
    margin-right: 3px;
    color: #9da9b9
}

.post-meta > li > a {
    transition: color .25s;
    color: #606975;
    text-decoration: none
}

.post-meta > li > a:hover {
    color: #fa6800
}

.blog-post {
    margin-bottom: 48px
}

.blog-post p {
    margin-bottom: 0
}

.post-thumb {
    display: block;
    margin-bottom: 20px
}

.post-title > a {
    transition: color .3s;
    color: #374250;
    text-decoration: none
}

.post-title > a:hover {
    color: #fa6800
}

.single-post-meta {
    display: table;
    width: 100%;
    padding-bottom: 24px
}

.single-post-meta .column {
    display: table-cell;
    vertical-align: top
}

.single-post-meta .column:last-child {
    text-align: right
}

.single-post-meta .meta-link {
    display: inline-block;
    margin-right: 10px;
    color: #606975;
    font-size: 13px
}

.single-post-meta .meta-link > span {
    margin-right: 4px;
    color: #9da9b9
}

.single-post-meta .meta-link > a {
    transition: color .25s;
    color: #606975;
    text-decoration: none
}

.single-post-meta .meta-link > a > i {
    display: inline-block;
    margin-top: -3px;
    margin-right: 5px;
    color: #9da9b9 !important;
    font-size: 1.1em;
    vertical-align: middle
}

.single-post-meta .meta-link > a:hover {
    color: #fa6800
}

.single-post-meta .column:last-child .meta-link {
    margin-right: 0;
    margin-left: 10px
}

@media (max-width: 768px) {
    .single-post-meta {
        display: block
    }

    .single-post-meta .column {
        display: block;
        width: 100%
    }

    .single-post-meta .column:last-child {
        padding-top: 5px;
        text-align: left
    }

    .single-post-meta .column:last-child .meta-link {
        margin-right: 10px;
        margin-left: 0
    }
}

.single-post-footer {
    padding-top: 12px;
    padding-bottom: 30px
}

.single-post-footer > .column {
    padding-top: 4px;
    padding-bottom: 6px
}

@media (min-width: 576px) {
    .single-post-footer {
        display: table;
        width: 100%
    }

    .single-post-footer > .column {
        display: table-cell;
        vertical-align: middle
    }

    .single-post-footer > .column:last-child {
        text-align: right
    }
}

.single-post-footer .sp-tag {
    transition: color .25s;
    color: #606975;
    font-size: 13px;
    font-weight: 500;
    text-decoration: none
}

.single-post-footer .sp-tag:hover {
    color: #fa6800
}

.entry-share span, .entry-share .share-links {
    display: inline-block;
    vertical-align: middle
}

.entry-share .social-button {
    margin-right: 0;
    margin-left: 8px
}

/*--------------------------------------------
28. Inner Styles
--------------------------------------------*/
.home-1-hero {
    background-image: url(../images/hero-slider/main-bg.jpg);
}

.home-1-hero-item {
    width: 125px;
}

.footer-payment-items {
    max-width: 615px;
}
.footer-subs{
    display: none;
}

.home-2-hero-cat-1 {
    background-image: url(../images/banners/home06.jpg);
}

.home-2-hero-cat-2 {
    background-image: url(../images/banners/home05.jpg);
}

.home-2-hero-cat-3 {
    background-image: url(../images/banners/home04.jpg);
}

.home-2-hero-cat-4 {
    background-image: url(../images/banners/home03.jpg);
}

.home-2-hero-cat-5 {
    background-image: url(../images/banners/home02.jpg);
}

.home-2-hero-cat-6 {
    background-image: url(../images/banners/home01.jpg);
}

.home-2-hero-sub {
    background-color: #000;
    opacity: .5;
}

.home-3-hero {
    background-image: url(../images/hero/hero-bg.jpg);
}

.page-404 {
    width: 100%;
    max-width: 400px;
}

.account-details {
    background-image: url(../images/account/user-cover-img.jpg);
}

.alert-img {
    background-image: url(../images/banners/alert-bg.jpg);
}

.alert-fa {
    font-size: 33px;
    margin-top: -5px;
}

.blog-promo {
    background-image: url(../images/banners/01.jpg);
}

.site-promo {
    background-image: url(../images/banners/03.jpg);
}

.site-promo-span {
    opacity: 0.4;
}

.site-coming {
    background-image: url(../images/coming-soon-bg.jpg);
}

.site-coming-op {
    background-color: #374250;
    opacity: .85;
}

.site-coming-in {
    width: 129px;
}

.site-coming-subs {
    max-width: 500px;
}

.progress-bar-1 {
    width: 55%;
}

.progress-bar-2 {
    width: 33%;
}

.progress-bar-3 {
    width: 70%;
}

.progress-bar-4 {
    width: 50%;
}

.progress-bar-5 {
    width: 40%;
}

.progress-bar-01 {
    width: 55%;
    height: 2px;
}

.progress-bar-02 {
    width: 33%;
    height: 2px;
}

.progress-bar-03 {
    width: 70%;
    height: 2px;
}

.progress-bar-04 {
    width: 50%;
    height: 2px;
}

.progress-bar-05 {
    width: 40%;
    height: 2px;
}

.search-products {
    max-width: 225px;
}

.search-promo {
    background-image: url(../images/banners/01.jpg);
}

.shop-promo {
    background-image: url(../images/banners/02.jpg);
}
.review-detail .user-review {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.user-track-order .user-track-order-title::-webkit-scrollbar {
    width: 0;
}
.user-track-order .track-order-content {
    margin-top: 30px
}

    .user-track-order .track-order-content h5 {
        text-transform: uppercase;
        font-size: 15px;
        letter-spacing: 1px
    }

        .user-track-order .track-order-content h5 span {
            color: var(--theme-color)
        }

.user-track-order .track-order-info {
    margin-top: 25px
}

    .user-track-order .track-order-info a {
        background: var(--theme-bg-light);
        padding: 10px 20px;
        border-radius: 10px;
        margin: 0 5px 5px 0
    }

        .user-track-order .track-order-info a span {
            font-weight: 500
        }

.user-track-order .track-order-step {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 30px
}

    .user-track-order .track-order-step .step-item {
        text-align: center;
        padding: 10px;
        flex: 1;
        position: relative;
        z-index: 1
    }

        .user-track-order .track-order-step .step-item::before {
            content: "";
            position: absolute;
            width: 100%;
            height: 5px;
            background: var(--theme-color);
            left: 0;
            top: 50px;
            z-index: -1
        }

        .user-track-order .track-order-step .step-item:first-child:before {
            width: 50%;
            left: unset;
            right: 0
        }

        .user-track-order .track-order-step .step-item:last-child:before {
            width: 50%
        }

        .user-track-order .track-order-step .step-item.completed::before {
            background: var(--success)
        }

    .user-track-order .track-order-step .step-icon {
        width: 80px;
        height: 80px;
        line-height: 80px;
        text-align: center;
        background: var(--theme-color);
        color: var(--white);
        font-size: 35px;
        border-radius: 50%;
        margin: 0 auto
    }

    .user-track-order .track-order-step .step-item.completed .step-icon {
        background: var(--success)
    }

    .user-track-order .track-order-step .step-item h6 {
        margin-top: 10px;
        font-size: 14px
    }

.package-info .package-item {
    margin-right: 10px;
}

    .package-info .package-item .package-item-title {
        color: #999;
    }

@media all and (max-width:767px) {
    .user-track-order .track-order-step {
        flex-wrap: nowrap;
        flex-direction: column
    }

        .user-track-order .track-order-step .step-item {
            display: flex;
            text-align: left;
        }

            .user-track-order .track-order-step .step-item::before {
                width: 5px;
                height: 50%;
                left: 38px;
                top: 60px;
            }

            .user-track-order .track-order-step .step-item:first-child:before {
                width: 5px;
                left: 38px;
                right: 0;
            }

            .user-track-order .track-order-step .step-item:last-child:before {
                display: none;
            }

        .user-track-order .track-order-step .step-icon {
            width: 60px;
            height: 60px;
            line-height: 60px;
            margin: 0 10px 0 0;
        }
}


