

/* 鏁翠綋 */
#user_main {
    background-color: #ffffff;
}

#lib_user {
    width: 100%;
    margin: 0 auto;
    padding: 0 2% 50px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

    #lib_user .lib_user_container {
        display: flex;
        align-items: flex-start;
    }

        #lib_user .lib_user_container.filled {
            width: 100%;
        }

            #lib_user .lib_user_container.filled .lib_user_main {
                margin-left: 0;
                width: 100%;
            }

    #lib_user img {
        opacity: inherit;
        overflow: hidden;
    }

@media (min-width: 1220px) {
    #lib_user {
        width: 1200px;
        padding-right: 0;
        padding-left: 0;
    }
}

@media (max-width: 1000px) {
    #lib_user {
        padding: 18px 0;
        max-width: 750px;
    }
}

@media (max-width: 768px) {
    #lib_user {
        padding: 18px 2.958%;
    }
}


/* 鍏叡鏍囬 */
#user_heading {
    background-color: #fff;
}

    #user_heading h2 {
        height: 112px;
        line-height: 112px;
        font-size: 30px;
        border-bottom: 1px solid #e3e3e3;
        color: #333333;
        text-align: center;
        background-color: #fff;
        font-weight: 600;
    }

@media screen and (max-width: 1000px) {
    #user_heading {
        padding: 0 15px;
    }

        #user_heading h2 {
            height: 65px;
            line-height: 65px;
            font-size: 20px;
        }
}


/* 琛ㄥ崟 */
.lib_txt {
    border: 1px solid #ccc;
    border-radius: 3px !important;
    height: 36px;
    line-height: 36px;
    padding: 0 7px;
    color: #333;
    font-size: 16px;
    box-shadow: 0 1px 0 rgba(255,255,255,.8),inset 0 1px 2px rgba(0,0,0,.06);
}

    .lib_txt:hover {
        border-color: #aaa;
    }

    .lib_txt:focus {
        border-color: #aaa;
    }

#lib_user input, #lib_user textarea, #lib_user button {
    outline: none;
}

#lib_user button {
    cursor: pointer;
}


/* 闈㈠寘灞� */
.lib_user_crumb {
    width: 100%;
    height: 67px;
    line-height: 30px;
    padding: 18px 0 19px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

    .lib_user_crumb .crumb_box {
        font-size: 0;
    }

        .lib_user_crumb .crumb_box > li {
            display: inline-block;
            vertical-align: middle;
            font-size: 12px;
            color: #888;
        }

            .lib_user_crumb .crumb_box > li > a {
                font-size: 16px;
                color: #666666;
            }

            .lib_user_crumb .crumb_box > li::before {
                content: '|';
                display: inline-block;
                vertical-align: middle;
                height: 30px;
                line-height: 25px;
                padding: 0 8px;
                font-size: 16px;
                color: #666666;
            }

            .lib_user_crumb .crumb_box > li.root a {
                color: #333;
            }

            .lib_user_crumb .crumb_box > li:first-child::before {
                display: none;
            }

@media (max-width: 1000px) {
    .lib_user_crumb {
        display: none;
    }
}

/* 宸︿晶鏍� */
.lib_user_menu {
    width: 235px;
    padding: 0 20px 24px 20px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

    .lib_user_menu .title {
        height: 68px;
        line-height: 68px;
        font-size: 14px;
        border-bottom: 1px #e3e3e3 solid;
    }

.lib_user_men2u .box_menu {
    padding: 7px 0;
}

.lib_user_menu .box_menu > li {
    line-height: 36px;
    margin-top: 11px;
    font-size: 14px;
    justify-content: space-between;
}

    .lib_user_menu .box_menu > li > a {
        float: left;
        width: calc( 100% - 12px );
        display: block;
        font-weight: 600;
        color: #333;
        text-decoration: none;
        transition: all 0.3s;
        -webkit-transition: all 0.3s;
        -moz-transition: all 0.3s;
    }

    .lib_user_menu .box_menu > li > i {
        float: right;
        display: block;
        width: 12px;
        height: 36px;
        text-align: center;
        font-size: 10px;
        cursor: pointer;
        font-weight: bold;
        color: #555;
    }

.lib_user_menu .box_menu .drop_list {
    height: 0;
    overflow: hidden;
}

    .lib_user_menu .box_menu .drop_list .drop_height a {
        display: block;
        line-height: 34px;
        text-indent: 10px;
        color: #666666;
    }

.lib_user_menu .box_menu > li.cur .drop_list {
    height: auto;
}

.lib_user_menu .box_menu > li:before {
    content: '';
    display: block;
    clear: both;
    float: none;
}

.lib_user_menu .box_menu > li:after {
    content: '';
    display: block;
    clear: both;
    float: none;
}

@media (max-width: 1000px) {
    .lib_user_menu {
        display: none;
    }
}

/* 鍙充晶鍐呭鍖� */
#lib_user.favorite .lib_user_menu {
    display: none;
}

.lib_user_main {
    width: 78.42%;
    margin-left: 2%;
    min-height: 516px;
    border-radius: 5px;
    background-color: #fff;
}

    .lib_user_main.index {
        background: transparent;
    }

    .lib_user_main.favorite {
        width: 100%;
        margin-left: 0;
    }

@media (max-width: 1000px) {
    .lib_user_main {
        width: 100%;
        min-height: 100%;
        margin-left: 0;
    }
}

/* 绌虹櫧涓讳綋 */
.box_white {
    background-color: #fff;
    border-radius: 4px;
}

/* 鍦板潃缂栬緫妗� */

#lib_user_address .add_item {
    float: left;
    margin: 0 0 19px 35px;
    padding: 0 16px;
    width: 423px;
    height: 214px;
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    box-sizing: border-box;
    position: relative;
    cursor: pointer;
}

    #lib_user_address .add_item.default {
        border-color: #eb3e3e;
    }

    #lib_user_address .add_item .rows.top {
        height: 64px;
        line-height: 64px;
        border-bottom: 1px dashed #e0e0e0;
        font-size: 14px;
        color: #333333;
        font-weight: 600;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

        #lib_user_address .add_item .rows.top .phone {
            margin-left: 12px;
            font-size: 14px;
            color: #666666;
        }

    #lib_user_address .add_item .addr_detail {
        padding-top: 10px;
        line-height: 24px;
        font-size: 14px;
        color: #666666;
        font-family: "HarmonyOSHans-Regular";
        max-height: 96px;
        overflow: hidden;
    }

    #lib_user_address .add_item .options {
        padding: 0 12px 0 20px;
        width: 100%;
        height: 18px;
        position: absolute;
        left: 0;
        bottom: 15px;
        box-sizing: border-box;
    }

        #lib_user_address .add_item .options .default_edit {
            font-size: 0;
        }

            #lib_user_address .add_item .options .default_edit i {
                display: inline-block;
                vertical-align: middle;
                width: 18px;
                height: 18px;
                box-sizing: border-box;
                border: 1px solid #dadada;
                border-radius: 50px;
                position: relative;
            }

            #lib_user_address .add_item .options .default_edit a {
                margin-left: 8px;
                display: inline-block;
                vertical-align: middle;
                font-size: 13px;
                color: #333333;
            }

        #lib_user_address .add_item .options ul {
            width: auto;
            font-size: 0;
            text-align: right;
        }

        #lib_user_address .add_item .options .user_action_down {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

    #lib_user_address .add_item.default .options .default_edit i {
        border-color: #eb3e3e;
        background-color: #eb3e3e;
    }

        #lib_user_address .add_item.default .options .default_edit i:after {
            content: '';
            width: 8px;
            height: 8px;
            background-color: #fff;
            border-radius: 50px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
        }

    #lib_user_address .add_item .options ul li {
        display: inline-block;
        font-size: 14px;
    }

        #lib_user_address .add_item .options ul li a {
            display: block;
            font-size: 14px;
            text-decoration: underline;
        }

            #lib_user_address .add_item .options ul li a[name=del] {
                margin-right: 15px;
                color: #f16056;
            }

            #lib_user_address .add_item .options ul li a[name=edit] {
                color: #333333;
            }

@media screen and (max-width:750px) {
    #lib_user_address .add_item .options ul li a[name=del] {
        color: #333;
    }
}


#addressForm {
    margin-top: 30px;
    padding: 30px 0;
    background-color: #fff;
    display: none;
    position: relative;
    z-index: 2;
}

    #addressForm .shipping_address {
        margin-right: 96px;
        width: 100%;
    }

        #addressForm .shipping_address .rows {
            position: relative;
            margin-bottom: 16px;
            padding: 0 8px;
            width: 100%;
            box-sizing: border-box;
        }

        #addressForm .shipping_address .col-2 {
            width: 50%;
        }

        #addressForm .shipping_address .col-3 {
            width: 33.3333%;
        }

        #addressForm .shipping_address .order-1 {
            order: 1;
        }

        #addressForm .shipping_address .order-2 {
            order: 2;
        }

        #addressForm .shipping_address .order-3 {
            order: 3;
        }

        #addressForm .shipping_address .order-4 {
            order: 4;
        }

        #addressForm .shipping_address .order-5 {
            order: 5;
        }

        #addressForm .shipping_address .order-6 {
            order: 6;
        }

        #addressForm .shipping_address .order-7 {
            order: 7;
        }

        #addressForm .shipping_address .order-8 {
            order: 8;
        }

        #addressForm .shipping_address .order-9 {
            order: 9;
        }

        #addressForm .shipping_address .order-10 {
            order: 10;
        }

        #addressForm .shipping_address .order-11 {
            order: 11;
        }

        #addressForm .shipping_address .order-12 {
            order: 12;
        }

        #addressForm .shipping_address .order-13 {
            order: 13;
        }

        #addressForm .shipping_address .order-14 {
            order: 14;
        }

        #addressForm .shipping_address .rows .input_box {
            display: flex;
            flex: 1;
        }

    #addressForm .shipping_address,
    #addressForm .billing_address {
        display: flex;
        flex-wrap: wrap;
    }

        #addressForm .shipping_address .rows > .input_box_label {
            display: block;
            font-size: 14px;
            margin-bottom: 5px;
        }

        #addressForm .shipping_address .rows .box .input_box_txt {
            padding: 0 15px;
            width: 100%;
            height: 49px;
            line-height: 49px;
            border: 1px solid #d9d9d9;
            border-radius: 5px;
            box-sizing: border-box;
        }

        #addressForm .shipping_address .rows .left_tit {
            width: 172px;
            text-align: right;
            font-size: 14px;
            color: #333333;
            position: absolute;
            top: 14px;
            right: calc( 100% + 30px );
            font-family: "HarmonyOSHans-Regular";
        }

            #addressForm .shipping_address .rows .left_tit .red_color {
                font-size: 14px;
                color: #f16056;
            }

        #addressForm .user_address_form .rows .input_box .input_box_txt,
        #addressForm .shipping_address .rows .box .input_box_txt {
            transition: .4s;
            font-size: 14px;
            color: #333;
        }

            #addressForm .user_address_form .rows .input_box .input_box_txt:focus,
            #addressForm .shipping_address .rows .box .input_box_txt:focus,
            #addressForm .user_address_form .rows .box.phone .input_box_txt:focus {
                border: 1px solid #666666;
                background-color: #fff;
                box-shadow: 0 0 0px 2px #ebebeb;
            }

    #addressForm .user_address_form .rows .box_select > select {
        font-size: 14px;
        color: #333;
    }

    #addressForm .user_address_form .rows .box_input_group .input_group_addon {
        width: 62px;
        height: 40px;
        line-height: 40px;
    }

    #addressForm .user_address_form .rows .box.phone {
        width: 100%;
    }

        #addressForm .user_address_form .rows .box.phone .input_box_txt {
            border-left: 0;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

    #addressForm #save_address {
        padding: 0;
        width: 142px;
        height: 46px;
        line-height: 46px;
        background-color: #f16056;
        color: #fff;
        border: 0;
        border-radius: 5px;
        text-align: center;
        font-size: 16px;
        font-weight: 600;
    }

    #addressForm .user_address_form .button {
        order: 100;
        width: 100%;
    }

        #addressForm .user_address_form .button .btn_cancel {
            padding: 0;
            width: 142px;
            height: 46px;
            line-height: 46px;
            background-color: #999999;
            border: 0;
            border-radius: 5px;
            text-align: center;
            font-size: 16px;
            color: #fff;
            font-weight: 600;
        }

    #addressForm .shipping_address .rows .box .input_box_txt::-webkit-input-placeholder {
        font-size: 14px;
        color: #aaaaaa;
    }

    #addressForm .shipping_address .rows .box .input_box_txt:-moz-placeholder {
        font-size: 14px;
        color: #aaaaaa;
    }

    #addressForm .shipping_address .rows .box .input_box_txt::-moz-placeholder {
        font-size: 14px;
        color: #aaaaaa;
    }

    #addressForm .shipping_address .rows .box .input_box_txt:-ms-input-placeholder {
        font-size: 14px;
        color: #aaaaaa;
    }

    #addressForm .shipping_address .rows .filled .chzn-container-single .chzn-single {
        padding-top: 11px;
        padding-bottom: 11px;
    }

.user_address_form {
    margin: 0;
}

    .user_address_form .cancel {
        padding-left: 18px;
        text-decoration: underline;
        background: url(../images/cart/chosen-sprite.png) no-repeat -45px 2px;
        float: right;
    }

    .user_address_form .rows {
        margin-bottom: 16px;
    }

        .user_address_form .rows .input_box {
            display: flex;
            flex: 1;
        }

            .user_address_form .rows .input_box .input_box_txt {
                height: 22px;
                font-size: 13px;
            }

        .user_address_form .rows .input_box_txt.null,
        #addressForm .shipping_address .rows .box .input_box_txt.null {
            border-color: #f00;
        }

#addressForm .user_address_form .rows .box.phone .input_box_txt.null {
    border: 1px solid #f00;
}

.user_address_form .rows .box_input_group {
    display: flex;
}

    .user_address_form .rows .box_input_group .input_group_addon {
        width: 55px;
        height: 44px;
        line-height: 44px;
        padding: 0;
        text-align: center;
        white-space: nowrap;
        border: 1px #d9d9d9 solid;
        border-right: 0;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        background-color: #f9f9f9;
    }

    .user_address_form .rows .box_input_group .input_group {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

.user_address_form .rows .box_select {
    height: 44px;
    overflow: hidden;
    position: relative;
    background-image: unset;
    border: 1px #d9d9d9 solid;
    border-radius: 5px;
}

    .user_address_form .rows .box_select::before {
        content: '\e6bd';
        width: 30px;
        line-height: 44px;
        text-align: center;
        font-size: 12px;
        font-family: 'iconfont';
        color: #919191;
        transform: scale(0.75);
        position: absolute;
        right: 0;
    }

    .user_address_form .rows .box_select > select {
        height: 44px;
        padding-left: 12px;
        font-size: 12px;
        color: #333;
        background: transparent;
        border: 0;
        outline: 0;
    }

.user_address_form .rows p.error {
    width: 100%;
    line-height: 18px;
    color: #e22120;
    font-size: 13px;
    display: none;
    margin-top: 6px;
}

.user_address_form .form_box {
    display: flex;
    flex-wrap: wrap;
}

    .user_address_form .form_box .box {
        flex: 1;
    }

        .user_address_form .form_box .box:first-child {
            margin-right: 16px;
        }

.user_address_form .button {
    margin: 10px 0;
}

    .user_address_form .button .btn_global {
        height: 32px;
        line-height: 32px;
        margin-right: 20px;
        padding: 0 33px;
        font-size: 14px;
    }

    .user_address_form .button .btn_cancel {
        color: #898989;
        background-color: #eee;
        border: 0;
    }

    .user_address_form .button .btn_save {
        background-color: #555;
    }

.user_address_form .rows .filled .input_box_label {
    top: 4px;
    margin-bottom: 0;
}

.user_address_form .rows .filled .input_box_txt {
    padding-top: 11px;
    height: 18px;
    line-height: 18px;
    padding-bottom: 11px;
}

@media screen and (max-width: 750px) {
    #addressForm .shipping_address .rows {
        margin-bottom: 20px;
    }

        #addressForm .shipping_address .rows .left_tit {
            margin-bottom: 5px;
            position: static;
            width: 100%;
            text-align: left;
            font-size: 14px;
        }

    #addressForm .user_address_form .button {
        font-size: 0;
    }

    #addressForm #save_address {
        margin-right: 4%;
        width: 48%;
    }

    #addressForm .user_address_form .rows .box_input_group .input_group_addon {
        height: 42px;
        line-height: 42px;
    }

    #addressForm .shipping_address .rows .box .input_box_txt {
        height: 44px;
        line-height: 44px;
    }

    #addressForm .user_address_form .button .btn_cancel {
        margin-right: 0;
        width: 48%;
    }

    #addressForm .shipping_address .col-3 {
        width: 100%;
    }
}

.user_form .rows > label, .user_form .rows .input {
    padding: 10px 0;
    overflow: hidden;
    line-height: 28px;
}

.user_form .rows > label {
    display: none;
    width: 15%;
    height: 28px;
    text-align: right;
    padding-right: 10px;
}

.user_form .reply_tips {
    font-size: 16px;
}

.user_form .rows .input {
    min-height: 28px;
    display: block;
}

.user_form .rows .form_input {
    width: 100%;
    height: 38px;
    line-height: 38px;
    border: 1px solid #e9e9e9;
    background: #fff;
    border-radius: 3px;
    padding: 5px 12px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
}

.user_form .rows .form_text {
    width: 100%;
    height: 164px;
    padding: 12px;
    line-height: 150%;
    border: 1px solid #e9e9e9;
    background: #fff;
    border-radius: 3px;
    vertical-align: top;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
}

#lib_user_products .reply_form {
    margin-top: 30px;
    padding: 0 30px 30px;
}

    #lib_user_products .reply_form .btn_box {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
    }

        #lib_user_products .reply_form .btn_box .upload_box {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 53px;
            height: 53px;
            padding: 0;
            margin: 0 10px 0 0;
            background: #f3f3f5;
            border-radius: 5px;
        }

            #lib_user_products .reply_form .btn_box .upload_box .icon-image1 {
                font-size: 25px;
            }

        #lib_user_products .reply_form .btn_box .video_box {
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 53px;
            height: 53px;
            padding: 0;
            margin: 0 32px 0 0;
            background: #f3f3f5;
            border-radius: 5px;
        }

            #lib_user_products .reply_form .btn_box .video_box .icon-video1 {
                font-size: 25px;
            }

            #lib_user_products .reply_form .btn_box .video_box input {
                width: 70px;
                height: 70px;
                position: absolute;
                top: 0px;
                bottom: 0;
                right: 0;
                padding: 0;
                padding-right: 300px;
                filter: alpha(opacity=0);
                -moz-opacity: 0;
                -webkit-opacity: 0;
                opacity: 0;
                cursor: pointer;
                font-size: 70px;
            }

        #lib_user_products .reply_form .btn_box .btn_submit {
            width: 110px;
            height: 51px;
            line-height: 51px;
            color: #fff;
            background-color: #f16056;
            font-size: 16px;
            font-weight: 600;
            border-radius: 5px;
            border: 0;
            float: right;
        }

        #lib_user_products .reply_form .btn_box .upload_file {
            width: 70px;
            height: 70px;
            position: absolute;
            top: 0px;
            bottom: 0;
            right: 0;
            padding: 0;
            padding-right: 300px;
            filter: alpha(opacity=0);
            -moz-opacity: 0;
            -webkit-opacity: 0;
            opacity: 0;
            cursor: pointer;
            font-size: 70px;
        }
/* 绗笁鏂� */
.facebook_button {
    display: block;
    width: 186px;
    height: 32px;
    line-height: 32px;
    font-size: 0;
    background: #1877f2;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    margin-bottom: 12px;
}

    .facebook_button:hover {
        background: #1877f2;
        cursor: pointer;
    }

    .facebook_button span.icon {
        display: inline-block;
        vertical-align: middle;
        width: 22px;
        height: 32px;
        background: transparent url(../images/user/facebook_icon.png) no-repeat center center;
        margin-left: 7px;
    }

    .facebook_button .text {
        display: inline-block;
        vertical-align: middle;
        line-height: 32px;
        font-size: 12px;
        margin-left: 6px;
        background-color: transparent;
        color: #fff;
    }

.google_button {
    display: block;
    overflow: hidden;
    position: relative;
    border-radius: 5px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    margin-bottom: 12px;
}

    .google_button .g_id_signin {
        position: absolute;
        z-index: 1;
        top: 5px;
    }

    .google_button .replace_text {
        height: 100%;
        box-sizing: border-box;
        position: relative;
        z-index: 2;
        background: #fff;
        border: 1px solid #333333;
        color: #333333;
        pointer-events: none;
        border-radius: 5px;
    }

        .google_button .replace_text span.icon {
            background: url(../images/global/google.svg) no-repeat 100%/contain;
            display: inline-block;
            vertical-align: middle;
            width: 22px;
            height: 22px;
            margin-left: 5px;
        }

        .google_button .replace_text span.button_text {
            line-height: 28px;
            font-size: 16px;
            margin-left: 12px;
        }

    .google_button iframe {
        height: 50px !important;
    }

@media screen and (max-width: 1000px) {
    .google_button::after {
        display: block;
        pointer-events: none;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 2;
        content: "\e6da";
        background: #fff;
        color: #e24220;
        font-family: "iconfont";
        font-size: 50px;
        align-items: center;
        justify-content: center;
    }

    .google_button:hover::after {
        color: #e74b37;
    }
}

.twitter_button {
    display: block;
    height: 32px;
    line-height: 32px;
    border: 1px solid #3894da;
    background: #3498e4;
    color: #fff;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    margin-bottom: 12px;
    box-sizing: border-box;
}

    .twitter_button:hover {
        background: #55ACEE;
        cursor: pointer;
    }

    .twitter_button span.icon {
        background: url(../images/user/twitter_icon.png) transparent 0 50% no-repeat;
        display: inline-block;
        vertical-align: middle;
        width: 22px;
        height: 22px;
        margin-left: 5px;
    }

    .twitter_button .text {
        line-height: 28px;
        font-size: 10px;
        margin-left: 6px;
        color: #fff;
        text-decoration: none;
    }

#paypalLogin {
    margin-bottom: 12px;
    width: 100%;
}

    #paypalLogin .PPBlue_V2 {
        height: 32px;
        padding: 0;
        border-radius: 5px;
        width: 100%;
        text-align: center;
        line-height: 32px;
    }

        #paypalLogin .PPBlue_V2 svg {
            width: 18px;
            margin: 4px 0 0 10px;
        }

        #paypalLogin .PPBlue_V2 b {
            padding: 0;
            padding-left: 10px;
            font-size: 10px;
            text-decoration: none;
            font-family: arial;
        }

#paypalLogin2 {
    margin-bottom: 12px;
    width: 100%;
}

    #paypalLogin2 .PPBlue_V2 {
        height: 32px;
        padding: 0;
        border-radius: 5px;
        width: 100%;
        text-align: center;
        line-height: 32px;
    }

        #paypalLogin2 .PPBlue_V2 svg {
            width: 18px;
            margin: 4px 0 0 10px;
        }

        #paypalLogin2 .PPBlue_V2 b {
            padding: 0;
            padding-left: 10px;
            font-size: 10px;
            text-decoration: none;
            font-family: arial;
        }

@media screen and (max-width: 750px) {
    #paypalLogin2 .PPBlue_V2 {
        border-radius: 50%;
        width: 50px;
        max-width: 50px;
        min-width: 50px;
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        flex-direction: row;
    }

        #paypalLogin2 .PPBlue_V2 svg {
            width: 30px;
            height: 30px;
            margin: 0 0px 0 13px;
        }

        #paypalLogin2 .PPBlue_V2 b {
            display: none;
        }
}

.vk_button {
    display: block;
    height: 32px;
    line-height: 30px;
    background: #507299;
    color: #fff;
    border-radius: 5px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    margin-bottom: 12px;
}

    .vk_button:hover {
        background: #5f86b4;
        cursor: pointer;
    }

    .vk_button span.icon {
        background: url(../images/user/vk_icon.png) transparent 0 50% no-repeat;
        display: inline-block;
        vertical-align: middle;
        width: 22px;
        height: 22px;
        margin-left: 5px;
    }

    .vk_button span.button_text {
        line-height: 28px;
        font-size: 16px;
        margin-left: 8px;
    }

.instagram_button {
    display: block;
    height: 32px;
    line-height: 30px;
    background: #8e6151;
    color: #fff;
    border-radius: 5px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
}

    .instagram_button:hover {
        background: #987061;
        cursor: pointer;
    }

    .instagram_button span.icon {
        background: url(../images/user/instagram_icon.png) transparent 4px 50% no-repeat;
        display: inline-block;
        vertical-align: middle;
        width: 22px;
        height: 22px;
        margin-left: 5px;
    }

    .instagram_button span.button_text {
        line-height: 28px;
        font-size: 10px;
        margin-left: 8px;
    }
/*************************** 鍏ㄥ眬 End ***************************/

.pre_sales_info .tag {
    margin-bottom: 5px;
    display: inline-block;
    padding: 0 8px;
    line-height: 24px;
    border-radius: 5px;
    background-color: #ffe7e7;
    color: #f16056;
    font-size: 12px;
}

/*************************** 浼氬憳鐧诲綍 End ***************************/

/*************************** 浼氬憳娉ㄥ唽銆佹壘鍥炲瘑鐮併€侀偖浠堕獙璇� Start ***************************/
#customer {
    width: 100%;
    margin: 0 auto;
    padding: 50px 30px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

@media (min-width: 1200px) {
    #customer {
        width: 615px;
    }
}

@media (max-width: 768px) {
    #customer {
        width: 92%;
    }

    #customer {
        padding: 18px 0;
        background-color: #fff;
    }
}

.user_back {
    margin-left: 30px;
    display: inline-block;
    font-size: 22px;
    color: #000;
    padding: 17px 0 17px 20px;
    background: url(../images/user/icon_user_back.png) no-repeat left 25px;
}

@media screen and (max-width: 750px) {
    .lib_mobile_user_crumb .user_back {
        margin-left: 3px;
        padding: 12px 0 12px 16px;
        font-size: 12px;
        background-position: center left;
        background-size: 6px 12px;
        color: #333;
    }
}

.user_back:hover {
    color: #333;
    text-decoration: none;
}


/* 鍏叡 */
#customer .sign_btn,
#customer .facebook_button,
#customer .google_button,
#customer .vk_button,
#customer .instagram_button,
#customer .twitter_button,
#customer .paypal_button {
    width: 100%;
    height: 50px;
    margin: 0;
    line-height: 50px;
}

#customer #paypalLogin .PPBlue_V2 {
    height: 50px;
    line-height: 50px;
}

#customer #paypalLogin2 .PPBlue_V2 {
    height: 50px;
    line-height: 50px;
}

/* 娉ㄥ唽椤甸潰 or 鐧诲綍椤甸潰 */
#signup {
    width: 59.182%;
    padding-right: 9.781%;
    border-right: 1px #e8e8e8 solid;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

    #signup .content {
        padding-top: 37px;
    }

@media (max-width: 1000px) {
    #signup {
        width: 100%;
        padding-right: 0;
        border: 0;
        border-bottom: 1px #e8e8e8 solid;
    }

        #signup .content {
            padding-top: 18px;
        }
}

/* 娉ㄥ唽椤甸潰宸︿晶 */
/* 鏍囬 */
#signup .top_tab .title {
    line-height: 40px;
    font-size: 40px;
    font-weight: bold;
    color: #333;
}

@media (max-width: 1000px) {
    #signup .top_tab .title {
        font-size: 30px;
    }
}

@media (max-width: 768px) {
    #signup .top_tab .title {
        font-size: 20px;
    }
}
/* 娉ㄥ唽琛ㄥ崟 */
#signup .register {
    width: 100%;
}

    #signup .register .error_note_box {
        display: none;
        margin-top: 20px;
        padding: 10px;
        color: #f00;
        background: #fff9e1;
        border: 1px #ffdb83 solid;
    }

    #signup .register .on_error {
        display: none;
        margin: 0;
        color: #f00;
    }

    #signup .register .row {
        margin: 0 0 16px 0;
    }

        #signup .register .row.mb0 {
            margin-bottom: 0;
        }

        #signup .register .row .input_box {
            display: block;
            width: 100%;
        }

            #signup .register .row .input_box .textarea_box {
                width: 100%;
                height: 120px;
                border: 1px solid #d2d2d2;
                border-radius: 8px;
                box-sizing: border-box;
                padding: 20px;
            }

        #signup .register .row label {
            display: block;
            line-height: 26px;
            font-size: 14px;
            color: #333;
        }

            #signup .register .row label em {
                color: #f00;
            }

            #signup .register .row label.tips {
                margin-bottom: 6px;
                line-height: 18px;
                font-size: 12px;
                color: #888;
                margin-top: 2px;
            }

            #signup .register .row label > i {
                margin-left: 5px;
                color: #f00;
            }

    #signup .register .input_box .input_box_txt {
        width: calc( 100% - 32px );
        margin: 8px 0;
        padding: 11px 15px;
        color: #333;
        border-color: #d2d2d2;
    }

    #signup .register .input_box.filled .input_box_txt {
        height: 22px;
    }

    #signup .register .box_side {
        display: flex;
        box-sizing: border-box;
        -webkit-box-sizing: border-box;
    }

        #signup .register .box_side .input_box {
            display: block;
            ;
            width: 50%;
            padding-left: 8px;
        }

            #signup .register .box_side .input_box:first-child {
                padding-right: 8px;
                padding-left: 0;
            }

#customer .row .input_box .global_select_box {
    height: auto;
    line-height: 56px;
    position: relative;
}

    #customer .row .input_box .global_select_box .input_case {
        position: relative;
        width: 100%;
        background-color: #fff;
        height: 56px;
        box-sizing: border-box;
        border-radius: 8px;
        padding: 0 40px 0 20px;
        cursor: pointer;
        border: 1px solid #d2d2d2;
    }

        #customer .row .input_box .global_select_box .input_case i {
            width: 12px;
            height: 12px;
            line-height: 12px;
            position: absolute;
            right: 18px;
            top: 0;
            bottom: 0;
            margin: auto;
            transform: scale(0.8);
            font-size: 16px;
            color: #7d8d9e;
            transition: all .5s ease 0s;
        }

            #customer .row .input_box .global_select_box .input_case i::before {
                content: "\e62b";
                font-family: "iconfont";
            }

        #customer .row .input_box .global_select_box .input_case input.imitation_select {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            border: 0;
            cursor: pointer;
            background-color: transparent;
            border: 0;
            box-shadow: unset;
        }

    #customer .row .input_box .global_select_box .select_ul {
        display: none;
        border-radius: 5px;
        width: 100%;
        box-sizing: border-box;
        background-color: #fff;
        position: absolute;
        left: 0;
        top: calc(100% + 2px);
        box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.1);
        overflow: hidden;
        z-index: 3;
    }

        #customer .row .input_box .global_select_box .select_ul::-webkit-scrollbar {
            width: 10px;
            background: #22191a;
        }

        #customer .row .input_box .global_select_box .select_ul::-webkit-scrollbar-thumb {
            background: rgba(193, 193, 193, 0.8);
            border-radius: 5px;
        }

        #customer .row .input_box .global_select_box .select_ul li {
            line-height: 30px;
            transition: all .5s ease 0s;
            padding: 5px 10px 5px 45px;
            color: #555;
            cursor: pointer;
            font-size: 14px;
            position: relative;
        }

            #customer .row .input_box .global_select_box .select_ul li:hover,
            #customer .row .input_box .global_select_box .select_ul li.selected {
                background-color: #eee;
                color: #555;
            }

                #customer .row .input_box .global_select_box .select_ul li.selected.leave {
                    background-color: unset;
                    color: #555;
                }

                #customer .row .input_box .global_select_box .select_ul li:hover i,
                #customer .row .input_box .global_select_box .select_ul li.selected i {
                    border: 5px solid #22191a;
                    background-color: #fff;
                }

            #customer .row .input_box .global_select_box .select_ul li i {
                width: 15px;
                height: 15px;
                position: absolute;
                left: 20px;
                top: 50%;
                transform: translateY(-50%);
                border: 1px solid #ccdced;
                border-radius: 15px;
                box-sizing: border-box;
            }

    #customer .row .input_box .global_select_box.focus .select_ul {
        display: block;
    }

    #customer .row .input_box .global_select_box.focus .input_case i {
        color: #888;
        transform: scale(0.8) rotate(-180deg);
    }

#customer .row .input_box .field_checked_box {
    margin-top: 6px;
    padding: 0;
    width: 100%;
    background-color: #fff;
}

#customer .row .form_tool_img_box {
    background-color: #fff;
    border-radius: 8px;
}

    #customer .row .form_tool_img_box .upload_box {
        display: none;
        float: left;
        width: 86px;
        height: 86px;
        position: relative;
        box-sizing: border-box;
        border: 2px dashed #d9d9d9;
        border-radius: 5px;
        margin-right: 15px;
        margin-bottom: 15px;
        border: solid 1px #dfdfdf;
        background: #fafafa;
    }

        #customer .row .form_tool_img_box .upload_box .iconfont {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
            color: #757575;
            font-size: 30px;
        }

        #customer .row .form_tool_img_box .upload_box label {
            margin: 0;
            padding-top: 0;
        }

        #customer .row .form_tool_img_box .upload_box:first-child {
            display: block;
        }

        #customer .row .form_tool_img_box .upload_box.on:after,
        #customer .row .form_tool_img_box .upload_box.on:before {
            background: none;
        }

        #customer .row .form_tool_img_box .upload_box.on .num_tips {
            font-size: 0;
        }

        #customer .row .form_tool_img_box .upload_box .num_tips {
            position: absolute;
            left: 0;
            top: 65%;
            width: 100%;
            text-align: center;
            color: #dddddd;
            display: none;
        }

    #customer .row .form_tool_img_box .upload_box {
        width: 120px;
        height: 120px;
    }

        #customer .row .form_tool_img_box .upload_box .pic_box {
            position: relative;
            width: 120px;
            height: 120px;
            vertical-align: middle;
            font-size: 0;
            text-align: center;
            cursor: pointer;
            box-sizing: border-box;
            z-index: 1;
        }

            #customer .row .form_tool_img_box .upload_box .pic_box img {
                opacity: 1;
            }

        #customer .row .form_tool_img_box .upload_box .close {
            position: absolute;
            top: -7px;
            right: -7px;
            display: none;
            width: 24px;
            height: 24px;
            background: #000;
            border-radius: 50%;
            cursor: pointer;
            z-index: 2;
            transform: rotate(45deg);
        }

            #customer .row .form_tool_img_box .upload_box .close::before {
                content: '';
                width: 12px;
                height: 1px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: #fff;
            }

            #customer .row .form_tool_img_box .upload_box .close::after {
                content: '';
                width: 1px;
                height: 12px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: #fff;
            }

    #customer .row .form_tool_img_box .upload_file {
        width: 82px;
        height: 82px;
        position: absolute;
        left: 0px;
        top: 0px;
        bottom: 0;
        right: 0;
        padding: 0;
        filter: alpha(opacity=0);
        -moz-opacity: 0;
        -webkit-opacity: 0;
        opacity: 0;
        cursor: pointer;
        font-size: 70px;
        z-index: 1;
    }

    #customer .row .form_tool_img_box .upload_file {
        width: 120px;
        height: 120px;
    }



@media (max-width: 1000px) {
    #signup .register .row {
        margin: 0 0 9px 0;
    }

        #signup .register .row label {
            line-height: 20px;
        }

    #signup .register .input_box .input_box_txt {
        margin: 5px 0;
    }
}

@media (max-width: 550px) {
    #signup .register .box_side {
        display: block;
    }

        #signup .register .box_side .input_box {
            width: 100%;
            padding-left: 0;
        }

            #signup .register .box_side .input_box:first-child {
                padding-right: 0;
                margin-bottom: 16px;
            }
}
/* 娉ㄦ剰浜嬮」 and 鎸夐挳 */
#signup .register .intro {
    margin-bottom: 12px;
    line-height: 24px;
    color: #555;
}

    #signup .register .intro a {
        color: #0654ba;
        text-decoration: underline;
    }

    #signup .register .intro dt {
        margin-bottom: 10px;
        font-size: 16px;
        color: #333;
    }

    #signup .register .intro dd {
        padding-left: 20px;
        line-height: 24px;
        font-size: 14px;
        color: #757575;
        background: url(../images/user/point2.gif) no-repeat 5px 7px;
        letter-spacing: -0.5px;
    }

        #signup .register .intro dd:last-child {
            margin-bottom: 0;
        }

#signup .register .btn_signup {
    width: 100%;
    height: 45px;
    line-height: 45px;
    padding: 0;
    margin-top: 20px;
    background-color: #fed925;
    border-color: #fed925;
    color: #fff;
    font-size: 16px;
    text-align: center;
    cursor: pointer;
    text-decoration: none;
    border-radius: 4px;
    -webkit-border-radius: 4px;
}

@media (max-width: 1000px) {
    #signup .register .intro {
        line-height: 22px;
    }

    #signup .register .btn_signup {
        margin: 10px 0;
    }
}

#signup .register .reward-points {
    line-height: 30px;
    margin-top: 10px;
    font-size: 14px;
    text-align: center;
}

    #signup .register .reward-points .points-num {
        color: #ed3f36;
        font-weight: bold;
    }

#signup ::-webkit-input-placeholder {
    color: #999;
    font-size: 14px;
}

#signup ::-moz-placeholder {
    color: #999;
    font-size: 14px;
}

#signup :-ms-input-placeholder {
    color: #999;
    font-size: 14px;
}

/* 鐧诲綍椤甸潰 */
/* 鏍囬 */
#login {
    width: 100%;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

    #login .top_tab {
        text-align: center;
    }

        #login .top_tab .title {
            line-height: 34px;
            font-size: 34px;
            color: #333;
        }

        #login .top_tab .brief {
            padding: 12px 0;
            line-height: 30px;
            font-size: 14px;
            color: #666;
        }

    #login .content {
        padding-top: 0;
    }

@media (max-width: 1000px) {
    #login {
        width: 100%;
        padding-right: 0;
        border: 0;
    }

        #login .content {
            padding-top: 0;
        }
}

@media (max-width: 768px) {
    #login .top_tab .title {
        line-height: 23px;
        font-size: 23px;
    }

    #login .top_tab .brief {
        padding: 6px 0;
        line-height: 18px;
        font-size: 14px;
    }
}
/* 鐧诲綍琛ㄥ崟 */
#login .login_box {
    width: 100%;
}

    #login .login_box .row {
        margin: 16px 0;
    }

        #login .login_box .row.mb0 {
            margin-bottom: 0;
        }

        #login .login_box .row .input_box {
            width: 100%;
        }

            #login .login_box .row .input_box.filled .input_box_txt {
                height: 22px;
            }

        #login .login_box .row label {
            display: block;
            line-height: 26px;
            font-size: 14px;
            color: #333;
        }

            #login .login_box .row label > i {
                margin-left: 5px;
                color: #f00;
            }

        #login .login_box .row:nth-child(2) {
            margin-bottom: 3px;
        }

    #login .login_box .input_box .input_box_txt {
        width: calc( 100% - 32px );
        margin: 8px 0 2px;
        padding: 11px 15px;
        color: #333;
        border-color: #a0a09f;
    }

@media (max-width: 768px) {
    #login .login_box .input_box .input_box_txt {
        margin: 5px 0;
        padding: 16px 15px;
    }
}
/* 璁颁綇璐﹀彿 and 鎸夐挳 */
#login .login_box .protect {
    float: left;
    width: 45%;
    height: 29px;
    line-height: 29px;
    font-size: 14px;
    color: #555;
}

#login .login_box .forget {
    float: right;
    width: 45%;
    height: 29px;
    line-height: 29px;
    text-align: right;
    font-size: 14px;
}

    #login .login_box .forget a {
        color: #555;
    }

#login .login_box .signin {
    width: 100%;
    height: 45px;
    line-height: 45px;
    text-align: center;
    font-size: 16px;
    color: #fff;
    border-radius: 4px;
    -webkit-border-radius: 4px;
}

#login .login_box .creat {
    text-align: center;
    margin: 16px 0;
    line-height: normal;
    font-size: 16px;
    color: #7f7f7f;
}

    #login .login_box .creat a {
        text-decoration: underline;
    }

#login input[type=checkbox] {
    position: relative;
    text-indent: 0;
    margin: 0 8px 0 0;
    width: 16px;
    height: 16px;
    text-align: center;
    display: inline-block;
    vertical-align: middle;
    line-height: 20px;
}

    #login input[type=checkbox]::before {
        content: "";
        position: absolute;
        top: -1px;
        left: -1px;
        background: #fff;
        width: 100%;
        height: 100%;
        border: 1px solid #bdbfc2;
        border-radius: 5px;
    }

    #login input[type=checkbox]:checked::before {
        content: "\2713";
        background-color: #1851c5;
        color: #fff;
        position: absolute;
        top: -1px;
        left: -1px;
        width: 100%;
        font-size: 16px;
        font-weight: bold;
        outline: none;
        border-color: #ccc;
        border: 1px solid #1851c5;
    }

@media (max-width: 1000px) {
    #login .login_box .creat {
        text-align: center;
    }
}

#login ::-webkit-input-placeholder {
    color: #999;
    font-size: 14px;
}

#login ::-moz-placeholder {
    color: #999;
    font-size: 14px;
}

#login :-ms-input-placeholder {
    color: #999;
    font-size: 14px;
}

/* 蹇樿瀵嗙爜 */
#signup.forget {
    padding: 75px 0 155px;
    margin: 0 auto;
    width: 494px;
    border-right: 0;
    padding-right: 0;
}

    #signup.forget .top_tab .title {
        font-size: 34px;
        text-align: center;
    }

    #signup.forget .register {
        margin-top: 60px;
    }

        #signup.forget .register .center {
            text-align: center;
        }

        #signup.forget .register .signbtn {
            display: inline-block;
            padding: 0 50px;
            margin: 20px auto 0;
            height: 55px;
            line-height: 55px;
            font-size: 16px;
            text-align: center;
            border-radius: 30px;
            background-color: #333;
            border-color: #333;
            color: #fff;
            box-sizing: border-box;
            text-decoration: none;
        }

        #signup.forget .register .input_box .input_box_txt {
            padding: 11px 27px;
            width: 100%;
            height: 49px;
            font-size: 14px;
            color: #333;
            box-sizing: border-box;
        }

            #signup.forget .register .input_box .input_box_txt::-webkit-input-placeholder {
                font-size: 14px;
                color: #979797;
            }

            #signup.forget .register .input_box .input_box_txt::-moz-placeholder {
                font-size: 14px;
                color: #979797;
            }

            #signup.forget .register .input_box .input_box_txt::-moz-placeholder {
                font-size: 14px;
                color: #979797;
            }

            #signup.forget .register .input_box .input_box_txt::-ms-input-placeholder {
                font-size: 14px;
                color: #979797;
            }

    #signup.forget .btn_signup {
        height: 55px;
        line-height: 55px;
    }

    #signup.forget .return {
        margin-top: 30px;
        text-align: center;
    }

        #signup.forget .return a {
            font-size: 14px;
            color: #333333;
            text-decoration: underline;
        }

@media screen and (max-width: 1000px) {
    #signup.forget {
        width: 100%;
    }

        #signup.forget .register .signbtn {
            margin: 10px auto;
        }

        #signup.forget .top_tab .title {
            text-align: left;
        }

        #signup.forget .register {
            margin-top: 30px;
        }

            #signup.forget .register .input_box .input_box_txt {
                height: 50px;
                line-height: 50px;
            }

    #signup.forget {
        padding: 30px 0 65px;
        border-bottom: 0;
    }
}

/* 绔欏唴淇� */
#pic_show {
    position: relative;
    margin: 10px 0 0;
    max-width: 60px;
    max-height: 60px;
}

    #pic_show .icon-camera1 {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        width: 20px;
        height: 20px;
        margin: auto;
        z-index: 1;
        ;
    }

    #pic_show .icon-video2 {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        width: 20px;
        height: 20px;
        margin: auto;
        z-index: 1;
        color: #fff;
        pointer-events: none;
    }

@media screen and (min-width:1000px) {
    .inbox_container .message_dialogue::-webkit-scrollbar {
        width: 10px;
        height: 1px;
    }

    .inbox_container .message_dialogue::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: #c6c6c6;
    }

    .inbox_container .message_dialogue::-webkit-scrollbar-track {
        border-radius: 10px;
        background: #fff;
    }
}

/* 鍙充晶鏍忕洰(鐢ㄤ簬鐧诲綍椤点€佹敞鍐岄〉銆佸繕璁板瘑鐮�) */
#customer .info {
    width: 100%;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    text-align: center;
}

    #customer .info .member {
        width: 100%;
    }

        #customer .info .member .box_third_login {
            padding: 6px 0;
            margin: 0 17px;
        }

        #customer .info .member p {
            padding-top: 45px;
            line-height: 26px;
            font-size: 18px;
            color: #333;
        }

            #customer .info .member p.tit {
                margin-bottom: 14px;
            }

    #customer .info .sign_btn a {
        display: block;
        width: 100%;
        height: 50px;
        line-height: 50px;
        background-color: #fed925;
        border-color: #fed925;
        border-radius: 5px;
        margin: 29px 0;
        text-align: center;
        font-size: 14px;
        color: #fff;
        text-decoration: none;
    }

    #customer .info .return {
        padding-top: 20px;
        line-height: 26px;
        text-decoration: underline;
        font-size: 14px;
        color: #333;
    }

@media (max-width: 1000px) {
    #customer .info {
        width: 100%;
        padding: 30px 0;
    }

        #customer .info .member p {
            padding-top: 25px;
        }

            #customer .info .member p.tit {
                padding-top: 11px;
            }

        #customer .info .member .box_third_login {
            display: inline-block;
            vertical-align: middle;
            margin-right: 8px;
        }

            #customer .info .member .box_third_login > div {
                width: 50px;
                border-radius: 50px;
            }

                #customer .info .member .box_third_login > div > span {
                    font-size: 0;
                }

                #customer .info .member .box_third_login > div .icon {
                    width: 50%;
                    height: 50%;
                    margin-top: 25%;
                    vertical-align: top;
                    background-size: 80%;
                }

                #customer .info .member .box_third_login > div .button_text {
                    display: none;
                }

        #customer .info .sign_btn a {
            margin: 22px 0;
        }

        #customer .info .return {
            padding-top: 25px;
        }
}
/*************************** 浼氬憳娉ㄥ唽銆佹壘鍥炲瘑鐮併€侀偖浠堕獙璇� End ***************************/

/*************************** 浼氬憳棣栭〉 Start ***************************/
/* 浼氬憳淇℃伅 */
.user_heading {
    display: flex;
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

    .user_heading .ind_icon {
        display: block;
        width: 88px;
        height: 88px;
        line-height: 88px;
        margin: 8px;
        text-align: center;
        font-size: 46px;
        color: #9f9fa1;
        background-color: #e1e1e3;
        border-radius: 50px;
    }

    .user_heading .ind_head {
        padding: 10px;
        flex: 1;
    }

        .user_heading .ind_head .welcome {
            line-height: 29px;
            font-size: 20px;
            font-weight: 600;
            color: #000;
        }

        .user_heading .ind_head .level {
            padding-left: 24px;
            line-height: 29px;
            font-size: 12px;
            color: #666;
            background: url(../images/user/icon_level_new.png) no-repeat 0 7px;
        }

        .user_heading .ind_head .discount {
            line-height: 29px;
            font-size: 14px;
            font-weight: 600;
            color: #f94827;
        }

@media (max-width: 768px) {
    .user_heading {
        padding: 15px;
    }
}
/* 璁㈠崟 */
.user_order {
    margin-top: 20px;
    padding: 20px;
}

    .user_order .title {
        display: flex;
        height: 44px;
        line-height: 44px;
        justify-content: space-between;
    }

        .user_order .title > strong {
            font-size: 16px;
            font-weight: 600;
            color: #000;
        }

        .user_order .title .view_all {
            display: flex;
            font-size: 14px;
            transition: all .3s ease-out;
            -webkit-transition: all .3s ease-out;
            text-decoration: none;
            color: #555;
        }

            .user_order .title .view_all > i {
                display: block;
                margin-top: 2px;
                width: 8px;
                height: 44px;
                line-height: 44px;
                margin-left: 6px;
                font-size: 8px;
                transform: rotate(-90deg);
            }

            .user_order .title .view_all:hover {
                color: #fb6360;
            }
/* 璁㈠崟鏍忕洰 */
.user_order_menu {
    display: flex;
    padding: 14px 0 9px;
    justify-content: space-between;
}

    .user_order_menu a {
        position: relative;
        display: block;
        width: 33.33%;
        padding: 63px 2% 0;
        line-height: 22px;
        text-align: center;
        font-size: 14px;
        color: #555;
        text-decoration: none;
        border-left: 1px #e9e9e9 solid;
        position: relative;
    }

        .user_order_menu a span {
            position: absolute;
            top: 14px;
            left: 53%;
            min-width: 10px;
            width: auto;
            height: 16px;
            line-height: 16px;
            text-align: center;
            padding: 0 3px;
            border-radius: 50px;
            background: #f86262;
            color: #fff;
            font-size: 12px;
            z-index: 2;
        }

        .user_order_menu a:first-child {
            border: 0;
        }

    .user_order_menu span {
        display: inline-block;
        width: 100%;
    }

    .user_order_menu a i {
        position: absolute;
        top: 24px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 36px
    }

    .user_order_menu a:hover {
        color: #f86262;
    }

@media (max-width: 1000px) {
    .user_order_menu a {
        font-size: 12px;
        border: 0;
    }
}

@media (max-width: 768px) {
    .user_order {
        margin-top: 15px;
        padding: 15px;
    }
}
/* 璁㈠崟淇℃伅 */
.user_order_info {
    margin: 12px 0;
    padding: 20px;
    background-color: #f9f9f9;
}

    .user_order_info .order_title {
        height: 31px;
        line-height: 31px;
    }

        .user_order_info .order_title > strong {
            color: #aaa;
        }

            .user_order_info .order_title > strong.second {
                margin-left: 30px;
            }

        .user_order_info .order_title > span {
            margin-left: 5px;
            color: #555;
        }

    .user_order_info .order_container {
        display: flex;
        justify-content: space-between;
    }

    .user_order_info .order_products {
        display: flex;
        width: 35%;
        padding: 10px;
        flex-wrap: wrap;
    }

        .user_order_info .order_products .item {
            width: 67px;
            height: 67px;
            overflow: hidden;
            margin-top: 8px;
            margin-right: 8px;
        }

            .user_order_info .order_products .item > a {
                display: block;
                width: 100%;
                height: 100%;
            }

            .user_order_info .order_products .item:nth-child(-n+4) {
                margin-top: 0;
            }

            .user_order_info .order_products .item:nth-child(4n) {
                margin-right: 0;
            }

    .user_order_info .order_detail {
        width: 50%;
        padding: 25px 10px 0;
    }

        .user_order_info .order_detail > ul {
            display: flex;
        }

            .user_order_info .order_detail > ul > li {
                width: 33.33%;
                line-height: 36px;
                font-size: 14px;
            }

                .user_order_info .order_detail > ul > li > a {
                    text-decoration: underline;
                    color: #f86262;
                }

                .user_order_info .order_detail > ul > li:nth-child(1) {
                    font-weight: 600;
                    color: #222;
                }

                .user_order_info .order_detail > ul > li:nth-child(2) {
                    color: #333;
                }

                .user_order_info .order_detail > ul > li:nth-child(3) {
                    text-align: right;
                }

    .user_order_info .box_empty {
        width: 100%;
        padding: 123px 0 50px;
        line-height: 20px;
        text-align: center;
        color: #999;
        background: url(../images/user/icon_order_empty.png) no-repeat center 28px;
        box-sizing: border-box;
        -webkit-box-sizing: border-box;
    }

@media (max-width: 1000px) {
    .user_order_info {
        display: none;
    }
}

@media (max-width: 768px) {
    .user_order_info {
        padding: 15px;
    }
}

#user_heading.orders_list h2 {
    border-bottom: 0;
}

.order_status_list {
    padding: 0 20px;
}

    .order_status_list > a {
        float: left;
        margin-left: 1%;
        padding: 30px 0 25px;
        display: block;
        width: 24%;
        height: 110px;
        line-height: 100px;
        background-color: #f9f9f9;
        position: relative;
        font-size: 14px;
        transition: .4s;
        text-align: center;
        box-sizing: border-box;
    }

        .order_status_list > a i span {
            display: block;
            width: 15px;
            height: 15px;
            line-height: 15px;
            border-radius: 50px;
            background: #f86262;
            font-size: 12px;
            color: #fff;
            text-align: center;
            text-indent: 0;
            position: absolute;
            top: -10px;
            right: -7px;
        }

        .order_status_list > a:first-child {
            margin-left: 0;
        }

        .order_status_list > a i {
            margin-right: 15px;
            display: inline-block;
            vertical-align: middle;
            width: 35px;
            height: 25px;
            line-height: 25px;
            position: relative;
            transition: .4s;
            font-size: 36px;
        }

        .order_status_list > a span {
            display: block;
            line-height: 20px;
            margin-top: 10px;
        }

@media screen and (min-width: 1000px) {
}

.account_search_box {
    margin-top: 20px;
}

    .account_search_box .form {
        display: flex;
        border: 1px #999 solid;
        border-radius: 3px;
        overflow: hidden;
        width: 240px;
    }

        .account_search_box .form .text {
            flex: 1;
            border: 0;
            border-top-left-radius: 3px;
            border-bottom-left-radius: 3px;
            height: 30px;
            line-height: 30px;
            padding: 0 10px;
            font-size: 12px;
        }

        .account_search_box .form .button {
            border: 0;
            border-top-right-radius: 3px;
            border-bottom-right-radius: 3px;
            background: none;
            width: 30px;
            height: 30px;
        }

            .account_search_box .form .button::before {
                content: "\e602";
                display: inline-block;
                font-family: "iconfont" !important;
                color: #222;
                font-size: 18px;
                font-style: normal;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

    .account_search_box[data-type="orders"] {
        margin-right: 31px;
        margin-left: 31px;
    }

@media (max-width: 768px) {
    .account_search_box {
        margin-bottom: 15px;
    }

        .account_search_box .form {
            width: auto;
        }
}

.orders_status_table {
    margin-top: 10px;
    margin: 0 31px;
}

    .orders_status_table .item {
        float: left;
        padding-right: 15px;
        width: 16%;
        height: 52px;
        line-height: 52px;
        font-size: 14px;
        color: #999999;
        box-sizing: border-box;
    }

        .orders_status_table .item.order_action {
            width: 16%;
            text-indent: 20px;
        }

        .orders_status_table .item:first-child {
            width: 52%;
        }

        .orders_status_table .item.order_status {
            position: relative;
        }

            .orders_status_table .item.order_status i {
                display: inline-block;
                vertical-align: middle;
                margin-left: 5px;
                width: 9px;
                height: 5px;
                line-height: 5px;
                font-size: 12px;
                transform: scale(0.8);
            }

            .orders_status_table .item.order_status .user_action_down {
                padding: 0 15px;
                position: absolute;
                width: 200px;
                line-height: 40px;
                background-color: #fff;
                box-shadow: 0 0 8px #eee;
                top: 100%;
                left: 50%;
                transform: translateX(-50%);
                opacity: 0;
                pointer-events: none;
                transition: .4s;
                text-align: center;
                z-index: 1;
            }

            .orders_status_table .item.order_status:hover .user_action_down {
                opacity: 1;
                pointer-events: all;
            }

#orders_list_table {
    padding: 0 30px
}

    #orders_list_table .orders_item {
        padding: 32px 0;
        border-top: 1px solid #e3e3e3;
    }

        #orders_list_table .orders_item .topStatus {
            display: none;
        }

        #orders_list_table .orders_item .list_oid .dateTime {
            color: #000000;
            font-size: 13px;
        }

        #orders_list_table .orders_item .list_oid span {
            font-size: 13px;
            color: #777777;
        }

            #orders_list_table .orders_item .list_oid span.number {
                margin-left: 40px;
            }

        #orders_list_table .orders_item .list_oid .orderNumber a {
            color: #000;
        }

        #orders_list_table .orders_item .list_opl {
            margin-top: 40px
        }

            #orders_list_table .orders_item .list_opl .opl_item {
                float: left;
                padding: 0;
                width: 16%;
                box-sizing: border-box;
            }

                #orders_list_table .orders_item .list_opl .opl_item.o_price {
                    font-size: 14px;
                    color: #222222;
                    font-weight: 600;
                }

                #orders_list_table .orders_item .list_opl .opl_item.o_status {
                    font-size: 14px;
                }

                #orders_list_table .orders_item .list_opl .opl_item.options {
                    width: 16%;
                }

                    #orders_list_table .orders_item .list_opl .opl_item.options .pay_now {
                        margin: 0 auto 15px;
                        display: block;
                        margin-top: 0;
                        width: 100px;
                        height: 36px;
                        line-height: 36px;
                        border-radius: 5px;
                        background-color: #f16056;
                        font-size: 14px;
                        color: #fff;
                        text-align: center;
                        font-weight: 600;
                        text-align: center;
                    }

                    #orders_list_table .orders_item .list_opl .opl_item.options .user_action_down {
                        text-align: center;
                        position: relative;
                    }

                        #orders_list_table .orders_item .list_opl .opl_item.options .user_action_down span {
                            line-height: 28px;
                        }

                        #orders_list_table .orders_item .list_opl .opl_item.options .user_action_down i {
                            display: inline-block;
                            vertical-align: middle;
                            margin-left: 5px;
                            width: 9px;
                            height: 5px;
                            background: url(../images/user/icon_orders_status_down.png) no-repeat center center;
                            position: relative;
                            transition: .4s;
                        }

                        #orders_list_table .orders_item .list_opl .opl_item.options .user_action_down > a {
                            display: block;
                            line-height: 28px;
                        }

                        #orders_list_table .orders_item .list_opl .opl_item.options .user_action_down ul {
                            width: 100%;
                            height: 0;
                            opacity: 0;
                            background-color: #fff;
                            pointer-events: none;
                            border-radius: 5px;
                            overflow: hidden;
                            transition: .4s;
                            position: absolute;
                            top: 100%;
                            left: 0;
                        }

                            #orders_list_table .orders_item .list_opl .opl_item.options .user_action_down ul li {
                                line-height: 28px;
                            }

                                #orders_list_table .orders_item .list_opl .opl_item.options .user_action_down ul li b {
                                    display: inline-block;
                                    width: 16px;
                                    height: 16px;
                                    line-height: 16px;
                                    margin-left: 5px;
                                    font-size: 12px;
                                    color: #fff;
                                    background-color: #f16056;
                                    border-radius: 50px;
                                }

                        #orders_list_table .orders_item .list_opl .opl_item.options .user_action_down:hover {
                            box-shadow: 0 0 7px 0px #eeeeee;
                        }

                            #orders_list_table .orders_item .list_opl .opl_item.options .user_action_down:hover > span a {
                                color: #f16056;
                            }

                            #orders_list_table .orders_item .list_opl .opl_item.options .user_action_down:hover i {
                                background: url(../images/user/icon_red_orders_status_down.png) no-repeat center center;
                            }

                            #orders_list_table .orders_item .list_opl .opl_item.options .user_action_down:hover ul {
                                opacity: 1;
                                height: auto;
                                pointer-events: all;
                                box-shadow: 0 7px 10px 0px #eeeeee;
                            }

                        #orders_list_table .orders_item .list_opl .opl_item.options .user_action_down ul li:hover a {
                            text-decoration: underline;
                        }

        #orders_list_table .orders_item .see_more {
            padding: 5px;
            display: inline-block;
            margin-top: 5px;
            margin-left: 0;
        }

        #orders_list_table .orders_item .list_opl .detail {
            width: 52%;
        }

            #orders_list_table .orders_item .list_opl .detail .list {
                margin-top: 40px;
            }

                #orders_list_table .orders_item .list_opl .detail .list:first-child {
                    margin-top: 0;
                }

            #orders_list_table .orders_item .list_opl .detail .pic {
                float: left;
                margin-right: 28px;
                display: block;
                width: 78px;
                height: 78px;
                border-radius: 5px;
                text-align: center;
                position: relative;
            }

                #orders_list_table .orders_item .list_opl .detail .pic img,
                #orders_list_table .orders_item .list_opl .detail .pic span {
                    display: inline-block;
                    vertical-align: middle;
                }

                #orders_list_table .orders_item .list_opl .detail .pic .p_qty {
                    position: absolute;
                    width: 22px;
                    height: 22px;
                    line-height: 22px;
                    border-radius: 50px;
                    background-color: #808080;
                    color: #fff;
                    font-size: 14px;
                    top: -10px;
                    right: -10px;
                }

            #orders_list_table .orders_item .list_opl .detail .desc {
                float: left;
                width: calc( 100% - 106px );
                font-size: 13px;
                color: #999999;
                box-sizing: border-box;
                padding-right: 20px;
            }

                #orders_list_table .orders_item .list_opl .detail .desc .global_pro_info_text {
                    line-height: 24px;
                    color: #333333;
                    text-align: left;
                }

                #orders_list_table .orders_item .list_opl .detail .desc ul {
                    text-align: left;
                }

                    #orders_list_table .orders_item .list_opl .detail .desc ul li {
                        display: inline-block;
                        margin-right: 10px;
                        line-height: 24px;
                        font-size: 13px;
                        color: #333333;
                    }

                        #orders_list_table .orders_item .list_opl .detail .desc ul li.shipfrom {
                            display: block;
                        }

                        #orders_list_table .orders_item .list_opl .detail .desc ul li .attr_name {
                            color: #999999;
                        }

                #orders_list_table .orders_item .list_opl .detail .desc p .extent_option_item {
                    display: inline-block;
                    width: 100%;
                }

            #orders_list_table .orders_item .list_opl .detail .p_price {
                float: left;
                display: block;
                margin-top: 10px;
                font-size: 14px;
                color: #222222;
                font-weight: 600;
            }

        #orders_list_table .orders_item .list_opl .opl_item.o_price .total {
            display: none;
        }

#orders_list_page #turn_page {
    margin: 30px 0;
}

#changePaymentBtn {
    display: inline-block;
    vertical-align: middle;
    margin-left: 30px;
    text-decoration: none;
}

@media screen and (max-width: 1000px) {
    .order_status_list {
        margin-top: 14px;
        padding-bottom: 25px;
    }

    .orders_search_box {
        margin: 20px 15px 0;
    }

    .orders_status_table {
        display: none;
    }

    .order_status_list > a {
        padding: 10px 0 0 0;
        height: 75px;
        background: #fff
    }

        .order_status_list > a i {
            display: block;
            margin: 0 auto;
        }

        .order_status_list > a .status_name {
            margin-top: 14px;
            display: block;
            line-height: 12px;
            font-size: 13px;
        }

        .order_status_list > a i span {
            width: 14px;
            height: 14px;
            line-height: 14px;
        }

    .grey_mobile_blank {
        width: 100%;
        height: 10px;
        background-color: #f5f5f5;
    }

    #orders_list_table {
        padding: 0
    }

        #orders_list_table .orders_item {
            padding: 0;
            border: none;
        }

            #orders_list_table .orders_item .list_oid {
                margin-top: 24px;
                padding: 0 15px
            }

                #orders_list_table .orders_item .list_oid .dateTime {
                    display: none;
                }

                #orders_list_table .orders_item .list_oid span.number {
                    margin-left: 0;
                    text-align: left;
                }

                #orders_list_table .orders_item .list_oid span {
                    font-size: 14px;
                }

            #orders_list_table .orders_item .list_opl {
                margin-top: 30px;
                padding: 0 15px 30px;
            }

            #orders_list_table .orders_item .topStatus {
                display: block;
                padding: 0 15px;
                height: 64px;
                line-height: 64px;
                border-bottom: 1px solid #f9f9f9;
                font-size: 15px;
                color: #333333;
                font-weight: 600;
            }

            #orders_list_table .orders_item .list_opl .detail {
                padding-right: 0;
                padding-bottom: 25px;
                width: 100%;
                border-bottom: 1px solid #f9f9f9;
            }

            #orders_list_table .orders_item .list_opl .opl_item {
                float: none;
                margin-top: 25px;
                width: 100%;
                text-align: right;
            }

            #orders_list_table .orders_item .list_opl .detail .pic {
                margin-right: 16px;
                width: 63px;
                height: 63px;
            }

                #orders_list_table .orders_item .list_opl .detail .pic .p_qty {
                    width: 21px;
                    height: 21px;
                    line-height: 21px;
                    font-size: 12px;
                }

            #orders_list_table .orders_item .list_opl .detail .desc {
                line-height: 23px;
                font-size: 13px;
            }

                #orders_list_table .orders_item .list_opl .detail .desc .name {
                    text-align: left;
                }

            #orders_list_table .orders_item .list_opl .detail .p_price {
                margin-top: 10px;
                font-size: 13px;
            }

            #orders_list_table .orders_item .list_opl .opl_item.o_status {
                display: none;
            }

            #orders_list_table .orders_item .list_opl .opl_item.o_price .total {
                display: inline-block;
            }

            #orders_list_table .orders_item .list_opl .opl_item.options {
                float: none;
                width: 100%;
            }

                #orders_list_table .orders_item .list_opl .opl_item.options:before {
                    content: '';
                    display: block;
                    width: 100%;
                    clear: both;
                }

                #orders_list_table .orders_item .list_opl .opl_item.options:after {
                    content: '';
                    display: block;
                    width: 100%;
                    clear: both;
                }

                #orders_list_table .orders_item .list_opl .opl_item.options .pay_now {
                    float: right;
                    margin-left: 20px;
                    margin-bottom: 0;
                    width: 80px;
                    height: 46px;
                    line-height: 46px;
                    font-size: 14px;
                }

                #orders_list_table .orders_item .list_opl .opl_item.options .user_action_down {
                    float: right;
                    margin-top: 9px;
                    margin-right: 0;
                }

                    #orders_list_table .orders_item .list_opl .opl_item.options .user_action_down > a {
                        font-size: 14px;
                    }
}


/* 骞舵帓 */
.box_between {
    display: flex;
    margin-top: 20px;
    justify-content: space-between;
}

    .box_between > div {
        padding: 20px;
        box-sizing: border-box;
        -webkit-box-sizing: border-box;
    }

@media (max-width: 1000px) {
    .box_between {
        display: block;
        margin: 0;
    }
}

@media (max-width: 768px) {
    .box_between > div {
        padding: 15px;
    }
}
/* 鏈嶅姟 */
.user_services {
    width: 40.384%;
}

    .user_services .title {
        display: flex;
        height: 28px;
        line-height: 28px;
        justify-content: space-between;
    }

        .user_services .title > strong {
            font-size: 16px;
            font-weight: 600;
            color: #000;
        }

    .user_services .services_container {
        display: flex;
        padding: 11px 0;
    }

        .user_services .services_container a {
            position: relative;
            display: block;
            width: 50%;
            padding: 62px 2% 9px;
            line-height: 22px;
            text-align: center;
            font-size: 14px;
            color: #555;
            text-decoration: none;
            background: no-repeat center 24px;
            border-left: 1px #e9e9e9 solid;
        }

            .user_services .services_container a span {
                position: absolute;
                top: 14px;
                left: 54%;
                min-width: 10px;
                width: auto;
                height: 16px;
                line-height: 16px;
                text-align: center;
                padding: 0 3px;
                border-radius: 50px;
                background: #f86262;
                color: #fff;
                font-size: 12px;
                z-index: 1;
            }

            .user_services .services_container a:first-child {
                border: 0;
            }

            .user_services .services_container a i {
                position: absolute;
                top: 24px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 42px;
            }

@media (min-width: 1000px) {
    .user_services .services_container a.address,
    .user_services .services_container a.money,
    .user_services .services_container a.password {
        display: none;
    }
}

@media (max-width: 1000px) {
    .user_services {
        width: 100%;
        margin-top: 20px;
    }

        .user_services .services_container a {
            width: calc(100% / 5);
            font-size: 12px;
            border: 0;
        }
}

@media (max-width: 768px) {
    .user_services {
        margin-top: 15px;
    }

        .user_services .services_container {
            flex-wrap: wrap;
        }

            .user_services .services_container a {
                width: calc(96% / 3 - 3%);
            }
}
/* 鏀惰棌 */
.user_favorites {
    width: 57.494%;
    margin-left: 20px;
}

    .user_favorites .title {
        display: flex;
        height: 28px;
        line-height: 28px;
        justify-content: space-between;
    }

        .user_favorites .title > strong {
            font-size: 16px;
            font-weight: 600;
            color: #000;
        }

        .user_favorites .title .more {
            display: flex;
            font-size: 14px;
            transition: all .3s ease-out;
            -webkit-transition: all .3s ease-out;
            color: #555;
            text-decoration: none;
        }

            .user_favorites .title .more > i {
                display: block;
                margin-top: 2px;
                width: 8px;
                height: 28px;
                line-height: 28px;
                margin-left: 6px;
                font-size: 8px;
                transform: rotate(-90deg);
            }

            .user_favorites .title .more:hover {
                color: #fb6360;
            }

    .user_favorites .favorites_container {
        padding-top: 10px;
    }

        .user_favorites .favorites_container .srcoll_list {
            position: relative;
            height: 80px;
            padding: 12px 24px;
        }

        .user_favorites .favorites_container .srcoll_btn {
            position: absolute;
            top: 42px;
            left: 1px;
            display: block;
            width: 8px;
            height: 15px;
            background: url(../images/user/icon_user_back.png) no-repeat;
        }

            .user_favorites .favorites_container .srcoll_btn.srcoll_btn_next {
                right: 1px;
                left: auto;
                transform: rotate(180deg);
            }

        .user_favorites .favorites_container .item {
            float: left;
            width: 80px;
            height: 80px;
            margin: 0 5px;
            text-align: center;
        }

        .user_favorites .favorites_container .box_empty {
            width: 100%;
            padding: 78px 0 7px;
            line-height: 20px;
            text-align: center;
            color: #999;
            background: url(../images/user/icon_favorites_empty.png) no-repeat center 7px;
            box-sizing: border-box;
            -webkit-box-sizing: border-box;
        }

@media (max-width: 1000px) {
    .user_favorites {
        width: 100%;
        margin-left: 0;
        margin-top: 20px;
    }
}

@media (max-width: 768px) {
    .user_favorites {
        margin-top: 15px;
    }

        .user_favorites .favorites_container .srcoll_list {
            height: 150px;
        }

        .user_favorites .favorites_container .srcoll_btn {
            top: 77px;
        }

        .user_favorites .favorites_container .item {
            width: 150px;
            height: 150px;
        }
}
/* 浜у搧 */
.user_products {
    margin-top: 20px;
    padding: 20px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

    .user_products .user_products_menu {
        line-height: 32px;
    }

        .user_products .user_products_menu a {
            display: inline-block;
            vertical-align: middle;
            margin-right: 15px;
            text-transform: capitalize;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            color: #888;
            transition: all .3s ease-out;
            -webkit-transition: all .3s ease-out;
        }

            .user_products .user_products_menu a:nth-child(2) {
                margin-left: 15px;
            }

            .user_products .user_products_menu a.current {
                color: #000;
            }

    .user_products .user_products_container .box_pro {
        display: none;
    }

    .user_products .user_products_container .pro_list {
        display: flex;
        padding-top: 25px;
        flex-wrap: wrap;
    }

    .user_products .user_products_container .pro_item {
        width: calc(100% / 4);
        padding: 0 2%;
        text-align: center;
        box-sizing: border-box;
        -webkit-box-sizing: border-box;
    }

        .user_products .user_products_container .pro_item > dt {
            width: 100%;
            height: calc(100% - 81px);
            text-align: center;
            background: #fff;
            vertical-align: middle;
        }

        .user_products .user_products_container .pro_item > dd {
            padding: 0 2px;
        }

        .user_products .user_products_container .pro_item .name {
            height: 30px;
            line-height: 30px;
            overflow: hidden;
            margin-top: 19px;
            text-overflow: ellipsis;
            font-size: 14px;
            color: #666;
            white-space: nowrap;
        }

        .user_products .user_products_container .pro_item .price {
            height: 32px;
            line-height: 32px;
            font-size: 14px;
            font-weight: 600;
        }

            .user_products .user_products_container .pro_item .price .PriceColor {
                color: #222;
            }

        .user_products .user_products_container .pro_item:first-child {
            margin-left: 0;
        }

@media (max-width: 768px) {
    .user_products {
        margin-top: 15px;
        padding: 15px;
    }

        .user_products .user_products_menu {
            text-align: center;
            font-size: 0;
        }

            .user_products .user_products_menu a {
                margin-right: 0;
                width: 50%;
            }

                .user_products .user_products_menu a:nth-child(2) {
                    margin-left: 0;
                }

        .user_products .user_products_container .pro_list {
            flex-wrap: wrap;
        }

        .user_products .user_products_container .pro_item {
            width: calc(100% / 2);
        }

            .user_products .user_products_container .pro_item:nth-child(n+3) {
                margin-top: 25px;
            }
}
/* 鐧诲嚭 */
.user_button {
    display: none;
    margin-top: 15px;
    padding: 0;
}

    .user_button > a {
        display: block;
        width: 100%;
        height: 60px;
        line-height: 60px;
        text-align: center;
        text-decoration: none;
        font-size: 14px;
        font-weight: 600;
        color: #666;
    }

@media (max-width: 1000px) {
    .user_button {
        display: block;
    }
}

@media (max-width: 768px) {
    .user_button > a {
        height: 50px;
        line-height: 50px;
        font-size: 16px;
    }
}

/*************************** 浼氬憳棣栭〉 End ***************************/

/*************************** 浼樻儬鍒� Start ***************************/

.w_1200 .user_coupons .cou_list .itl {
    font-size: 48px;
}

.w_1200 .user_coupons .cou_list .code {
    font-size: 18px;
}

.w_1200 .user_coupons .cou_list .date {
    margin-top: 10px;
}

.user_coupons {
    background-color: #fff;
}

    .user_coupons .menu_title {
        margin-top: 40px;
        padding: 0 30px;
        background-color: #fff;
        text-align: center;
    }

        .user_coupons .menu_title a {
            padding: 0 34px;
            color: #888888;
            font-size: 18px;
            font-weight: 600;
        }

        .user_coupons .menu_title .current {
            border: none;
            color: #333333;
        }

        .user_coupons .menu_title li {
            position: relative;
            display: inline-block;
        }

            .user_coupons .menu_title li:first-child a {
                padding-left: 0;
            }

            .user_coupons .menu_title li:last-child::after {
                display: none;
            }

    .user_coupons .cou_list {
        display: flex;
        flex-wrap: wrap;
        margin: 30px 30px 0;
    }

        .user_coupons .cou_list .item {
            position: relative;
            width: calc((100% / 3) - (44px / 3));
            margin-top: 3px;
            margin-right: 22px;
            margin-bottom: 24px;
            padding: 15px 23px 62px;
            background-color: #fff6f3;
            border: 1px solid #ffe9da;
            box-sizing: border-box;
            -webkit-box-sizing: border-box;
        }

            .user_coupons .cou_list .item:nth-child(3n) {
                margin-right: 0;
            }

            .user_coupons .cou_list .item .coupon_price {
                line-height: 29px;
                font-size: 24px;
                color: #f6490d;
                font-weight: 600;
            }

            .user_coupons .cou_list .item .code, .user_coupons .cou_list .item .max {
                line-height: 20px;
                margin: 2px 0;
                font-family: "HarmonyOSHans-Regular";
                font-size: 12px;
                color: #f6490d;
            }

            .user_coupons .cou_list .item .coupon_discount {
                line-height: 20px;
                font-family: "HarmonyOSHans-Regular";
                font-size: 12px;
                color: #666;
            }

            .user_coupons .cou_list .item .coupon_date {
                line-height: 20px;
                font-family: "HarmonyOSHans-Regular";
                font-size: 12px;
                color: #666;
            }

            .user_coupons .cou_list .item::before {
                content: "";
                position: absolute;
                top: 0;
                left: -1px;
                width: calc(100% + 2px);
                height: 3px;
                background-color: #f6490d;
            }

            .user_coupons .cou_list .item.old {
                background-color: #f5f5f5;
                border-color: #f5f5f5;
                padding-bottom: 17px;
            }

                .user_coupons .cou_list .item.old::before {
                    background-color: #bbb;
                }

                .user_coupons .cou_list .item.old .coupon_price,
                .user_coupons .cou_list .item.old .code,
                .user_coupons .cou_list .item.old .coupon_discount,
                .user_coupons .cou_list .item.old .coupon_date {
                    color: #bbb;
                }

            .user_coupons .cou_list .item .use {
                position: absolute;
                bottom: 17px;
                left: 23px;
                line-height: 30px;
                font-size: 14px;
                padding: 0 28px;
                border-radius: 3px;
                color: #fff;
                background-color: #f6490d;
                text-decoration: unset;
            }

@media screen and (max-width: 1120px) {
    .user_coupons .cou_list .item {
        width: calc((100% / 2) - 11px);
    }

        .user_coupons .cou_list .item:nth-child(2n) {
            margin-right: 0;
        }

        .user_coupons .cou_list .item:nth-child(3n) {
            margin-right: 22px;
        }
}

@media screen and (max-width: 1000px) {
    .user_coupons .menu_title {
        margin-top: 20px;
        text-align: center;
    }

        .user_coupons .menu_title a {
            padding: 0 20px;
            font-size: 16px;
        }

        .user_coupons .menu_title li {
            float: none;
            display: inline-block;
        }
}

@media screen and (max-width: 540px) {
    .user_coupons .cou_list {
        margin: 25px 25px 0;
    }

        .user_coupons .cou_list .item {
            width: 100%;
            margin-right: 0;
            margin-bottom: 20px;
        }

            .user_coupons .cou_list .item .code,
            .user_coupons .cou_list .item .coupon_discount,
            .user_coupons .cou_list .item .coupon_date {
                font-size: 14px;
            }

            .user_coupons .cou_list .item:nth-child(3n) {
                margin-right: 0;
            }
}

/*************************** 浼樻儬鍒� End ***************************/

/*************************** 浼氬憳璁㈠崟 Start ***************************/
.order_btn {
    margin-top: 5px;
    height: auto;
    line-height: 36px;
    background-color: #f16056;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    display: inline-block;
    text-decoration: none;
    color: #fff;
    margin-right: 6px;
    font-weight: 600;
    text-align: center;
    padding: 0 20px;
}

    .order_btn:hover {
        color: #fff;
        text-decoration: none;
    }

.order_body h3.title {
    margin: 0 30px;
    height: 85px;
    line-height: 85px;
    border-bottom: 1px solid #e3e3e3;
    font-size: 20px;
    font-size: 200;
    font-weight: 600;
    text-transform: capitalize;
}

.order_body h3 .fr {
    display: none;
}

.order_body .order_box {
    padding-bottom: 38px;
}

.order_body .status_box {
    margin-top: 38px;
    padding: 0 30px;
}

    .order_body .status_box .item {
        position: relative;
        float: left;
        text-align: center;
        font-size: 12px;
        color: #999;
        text-align: center;
    }

        .order_body .status_box .item .line {
            position: absolute;
            top: 11px;
            left: 0;
            height: 2px;
            width: 100%;
            background: #cccccc;
            line-height: 22px;
        }

        .order_body .status_box .item .fir {
            left: auto;
            right: 0;
            border-top-left-radius: 11px;
            border-bottom-left-radius: 11px;
        }

        .order_body .status_box .item .last {
            border-top-right-radius: 11px;
            border-bottom-right-radius: 11px;
        }

        .order_body .status_box .item.cur {
            color: #333;
        }

            .order_body .status_box .item.cur .line.error {
                display: none;
            }

            .order_body .status_box .item.cur .line {
                background: #00a850;
            }

                .order_body .status_box .item.cur .line:after {
                    content: '';
                    display: inline-block;
                    width: 0;
                    height: 0;
                    border-width: 5px 0 5px 5px;
                    border-style: solid;
                    border-color: transparent transparent transparent #00a850;
                    position: absolute;
                    top: -4px;
                    right: -4px;
                    z-index: 1;
                }

        .order_body .status_box .item .status {
            margin-top: 5px;
            line-height: 24px;
            font-size: 14px;
            color: #999999;
        }

        .order_body .status_box .item.cur .status {
            color: #00a84f;
            font-weight: 600;
        }

        .order_body .status_box .item .time {
            line-height: 20px;
            font-size: 12px;
            color: #999999;
        }

        .order_body .status_box .item.cur .time {
            color: #999999;
            font-weight: 600;
        }

        .order_body .status_box .item i {
            display: block;
            margin: 0 auto;
            width: 72px;
            height: 28px;
            line-height: 28px;
            background-color: #fff;
            background-position: center center;
            background-repeat: no-repeat;
            position: relative;
            z-index: 2;
            font-size: 32px;
        }

        .order_body .status_box .item.cur i {
            color: #00a84f;
        }

        .order_body .status_box .item .fir,
        .order_body .status_box .item .last {
            max-width: 50%;
        }

    .order_body .status_box .tips_box {
        margin: 35px 0 0;
        padding: 26px 38px;
        width: 100%;
        background-color: #f7f7f7;
        box-sizing: border-box;
        display: flex;
        align-items: center;
    }

        .order_body .status_box .tips_box .flex {
            flex: 1;
        }

        .order_body .status_box .tips_box .TopStatus {
            line-height: 28px;
            font-size: 18px;
            font-weight: 600;
            color: #333333;
        }

        .order_body .status_box .tips_box .message {
            margin-top: 5px;
            line-height: 24px;
            font-size: 14px;
            color: #555555;
        }

        .order_body .status_box .tips_box .pay_now {
            display: block;
            margin-top: 0;
            padding: 0 10px;
            min-width: 156px;
            height: 46px;
            line-height: 46px;
            border-radius: 5px;
            background-color: #f16056;
            text-align: center;
            font-size: 16px;
            color: #fff;
            font-weight: 600;
        }

    .order_body .status_box.error .item {
        text-align: left;
    }

    .order_body .status_box.error .list {
        padding: 0 65px
    }

    .order_body .status_box.error .item i.status_1 {
        margin-left: 22px;
        text-align: center;
    }

    .order_body .status_box.error .item .fir, .order_body .status_box.error .item .last {
        max-width: 90%;
    }

    .order_body .status_box.error .item.cur .line:after {
        display: none;
    }

    .order_body .status_box .item.cur i.status_7 {
        margin-left: 85.5%;
        text-align: center;
    }

    .order_body .status_box.error .item:first-child .time {
        text-indent: 22px;
    }

    .order_body .status_box.error .item .status {
        text-indent: 22px;
    }

    .order_body .status_box.error .item.columns_2:nth-child(2) {
        text-align: right;
    }

    .order_body .status_box.error .item:nth-child(2) {
        text-align: center;
    }

        .order_body .status_box.error .item:nth-child(2) .time {
            transform: translateX(3px);
        }

    .order_body .status_box.error .item:nth-child(3) {
        text-align: right;
    }

.grey_blank20 {
    width: 100%;
    height: 20px;
    background-color: #f5f5f5;
}

.box_payment_offline .hide_payment_list {
    height: 0;
    overflow: hidden;
    padding: 0;
}

.box_payment_offline {
    margin: 20px 30px 0;
    padding: 26px 38px;
    background-color: #f7f7f7;
    box-sizing: border-box;
}

    .box_payment_offline .title {
        line-height: 28px;
        font-size: 18px;
        font-weight: 600;
        color: #333333;
    }

    .box_payment_offline .payment_info {
        margin-top: 5px;
        line-height: 24px;
        font-size: 14px;
        color: #555555;
    }

    .box_payment_offline .payment_form {
        padding-top: 10px;
    }

        .box_payment_offline .payment_form .rows {
            margin-bottom: 16px;
        }

            .box_payment_offline .payment_form .rows p.error {
                line-height: 16px;
                margin-top: 5px;
                color: #e22120;
                display: none;
                font-size: 12px;
            }

        .box_payment_offline .payment_form .form_box {
            display: flex;
        }

            .box_payment_offline .payment_form .form_box .box {
                flex: 1;
            }

                .box_payment_offline .payment_form .form_box .box:first-child {
                    margin-right: 10px;
                }

        .box_payment_offline .payment_form .button .btn_global {
            margin-top: 40px;
            min-width: 205px;
            height: 46px;
            line-height: 46px;
            margin-right: 20px;
            padding: 0 40px;
            font-size: 16px;
            color: #fff;
            background-color: #000000;
            border-radius: 5px;
            font-weight: bold;
            box-sizing: border-box;
        }

        .box_payment_offline .payment_form .button .btn_cancel {
            display: none;
        }

    .box_payment_offline #add_proof {
        margin-top: 15px;
        display: inline-block;
        min-width: 205px;
        padding: 17px 25px;
        box-sizing: border-box;
        font-size: 16px;
        color: #ffffff;
        background-color: #000000;
        border-radius: 5px;
        text-align: center;
        cursor: pointer;
    }

    .box_payment_offline .payment_form .proof_title {
        display: flex;
        align-items: center;
        cursor: pointer;
    }

        .box_payment_offline .payment_form .proof_title .name {
            margin-left: 12px;
            font-size: 14px;
            color: #000000;
        }

        .box_payment_offline .payment_form .proof_title .checked {
            position: relative;
        }

        .box_payment_offline .payment_form .proof_title input {
            width: 16px;
            height: 16px;
            position: relative;
            border: none;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
        }

            .box_payment_offline .payment_form .proof_title input:after {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                width: 16px;
                height: 16px;
                border: 1px solid #dadada;
                background-color: #fff;
                transition: all 0.1s ease-in-out;
                border-radius: 18px;
            }

            .box_payment_offline .payment_form .proof_title input:checked:after {
                width: 8px;
                height: 8px;
                border: 5px solid #222222;
            }

    .box_payment_offline .payment_form .item {
        margin-top: 30px;
    }

        .box_payment_offline .payment_form .item:first-child {
            margin-top: 0;
        }

        .box_payment_offline .payment_form .item.current .list {
            margin-top: 15px;
            display: block;
        }

        .box_payment_offline .payment_form .item .list {
            display: none;
        }

        .box_payment_offline .payment_form .item .upload_box {
            width: 120px;
            height: 120px;
            box-sizing: border-box;
            border: 2px dashed #d9d9d9;
            border-radius: 5px;
            margin: 18px 20px 0 30px;
            border: dashed 1px #aaaaaa;
            background: #fafafa;
            box-sizing: border-box;
            position: relative;
        }

            .box_payment_offline .payment_form .item .upload_box .pic_box {
                position: relative;
                margin: 0 !important;
                max-width: 100% !important;
                max-height: 100% !important;
                width: 120px;
                height: 120px;
                vertical-align: middle;
                font-size: 0;
                text-align: center;
                cursor: pointer;
                box-sizing: border-box;
                z-index: 1;
            }

            .box_payment_offline .payment_form .item .upload_box .upload_file {
                width: 100%;
                height: 100%;
                position: absolute;
                left: 0px;
                top: 0px;
                bottom: 0;
                right: 0;
                padding: 0;
                filter: alpha(opacity=0);
                -moz-opacity: 0;
                -webkit-opacity: 0;
                opacity: 0;
                cursor: pointer;
                font-size: 70px;
                z-index: 3;
                cursor: pointer;
                font-size: 0;
            }

            .box_payment_offline .payment_form .item .upload_box i {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%,-50%);
                color: #757575;
                font-size: 30px;
            }

            .box_payment_offline .payment_form .item .upload_box .close {
                position: absolute;
                top: -7px;
                right: -7px;
                display: none;
                width: 24px;
                height: 24px;
                line-height: 24px;
                text-align: center;
                color: white;
                background: rgba(0,0,0,0.5);
                border-radius: 50%;
                cursor: pointer;
                z-index: 4;
            }


.order_base_div {
    padding: 20px 30px;
}

.order_base_table {
    width: 100%;
    background: #fff;
}

    .order_base_table .tr > th {
        line-height: 48px;
        font-size: 14px;
        color: #999;
        vertical-align: top;
    }

    .order_base_table .tr > td {
        line-height: 30px;
        max-width: 80%;
        padding: 0 15px 20px 0;
        font-size: 14px;
        color: #333333;
        vertical-align: top;
    }

        .order_base_table .tr > td > strong {
            margin-right: 5px;
        }

        .order_base_table .tr > td .query {
            text-decoration: underline;
            cursor: pointer;
        }

    .order_base_table .edit_pay {
        display: inline-block;
        vertical-align: top;
        width: 255px;
        height: 36px;
        border: 1px solid #eeeeee;
        line-height: 36px;
        text-indent: 10px;
    }

    .order_base_table .edit_pay_form button {
        display: inline-block;
        width: 128px;
        height: 36px;
        margin-left: 15px;
        background: #e53935;
        border-radius: 5px;
        color: #fff;
        border: none;
        vertical-align: top;
    }

    .order_base_table .pay_box {
        display: none;
        padding: 15px;
    }

.order_base .img_box {
    width: 110px;
    height: 110px;
    box-sizing: border-box;
    border: 1px #aaaaaa dashed;
    background-color: #f7f7f7;
    border-radius: 5px;
    position: relative;
    ;
    margin: 5px 0;
}

    .order_base .img_box img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
    }

.order_base .proof_box {
    background-color: #f7f7f7;
    padding: 25px;
    margin-top: 10px;
}

.order_base .order_view {
    padding: 0 10px;
}

    .order_base .order_view .order_btn {
        margin-bottom: 10px;
    }

    .order_base .order_view .payment_info {
        line-height: 180%;
    }

        .order_base .order_view .payment_info h3 {
            font-weight: bold;
            color: #333;
        }

        .order_base .order_view .payment_info .rows {
            padding: 2px;
            padding-left: 5px;
        }

            .order_base .order_view .payment_info .rows strong {
                display: inline-block;
                padding-right: 3px;
            }

            .order_base .order_view .payment_info .rows span {
                display: inline-block;
            }

.waybill_products_list .products_list_item, .delete_products_list .products_list_item {
    margin: 0 30px;
    border-bottom: 1px solid #e3e3e3;
}

    .delete_products_list .products_list_item .top {
        text-transform: capitalize
    }

.order_menu {
    padding-bottom: 38px;
}

    .order_menu .row_hd {
        padding: 40px 0 25px;
        font-size: 12px;
    }

        .order_menu .row_hd > span {
            margin-left: 15px;
        }

        .order_menu .row_hd .top {
            line-height: 18px;
            margin-bottom: 5px;
            font-size: 14px;
            color: #333333;
            font-weight: 600;
        }

        .order_menu .row_hd .bot i {
            font-size: 14px;
            color: #333;
        }

        .order_menu .row_hd .bot > span {
            line-height: 30px;
            padding-right: 30px;
            font-size: 14px;
            color: #999999;
        }

            .order_menu .row_hd .bot > span strong {
                color: #333333;
            }

            .order_menu .row_hd .bot > span.detail_track {
                text-decoration: underline;
                color: #999;
                cursor: pointer;
            }

            .order_menu .row_hd .bot > span > span {
                padding: 0 3px;
                text-decoration: underline;
                cursor: pointer;
            }

        .order_menu .row_hd .bot .not-enabled-api {
            text-decoration: unset;
            cursor: auto;
        }

        .order_menu .row_hd .bot {
            line-height: 24px;
        }

    .order_menu .row_table {
        padding: 0;
        font-size: 12px;
    }

        .order_menu .row_table .thead {
            height: 50px;
            line-height: 50px;
            background-color: #f9f9f9;
        }

            .order_menu .row_table .thead .tr > div {
                padding-left: 13px;
                box-sizing: border-box;
                background-color: #f9f9f9;
                font-size: 14px;
                color: #999999;
            }

        .order_menu .row_table .tbody .tr {
            padding: 30px 0;
        }

            .order_menu .row_table .tbody .tr > div {
                padding-left: 13px;
                line-height: 24px;
                box-sizing: border-box;
                font-size: 14px;
                color: #222222;
            }

        .order_menu .row_table .pro_list .flex {
            display: flex;
        }

        .order_menu .row_table .pro_list {
            width: 49.2%;
        }

        .order_menu .row_table .pro_price {
            width: 22.8%;
        }

        .order_menu .row_table .pro_qty {
            width: 18.1%;
        }

        .order_menu .row_table .pro_amount {
            width: 9.9%;
        }

        .order_menu .row_table .pro_list .img {
            margin-right: 28px;
            width: 80px;
            height: 80px;
        }

            .order_menu .row_table .pro_list .img a {
                display: block;
                width: 100%;
                height: 100%;
            }

            .order_menu .row_table .pro_list .img .num {
                display: none;
            }

        .order_menu .row_table .pro_list .pro_info {
            flex: 1;
        }

            .order_menu .row_table .pro_list .pro_info h4 {
                padding-right: 20px;
            }

                .order_menu .row_table .pro_list .pro_info h4 a {
                    font-size: 13px;
                    color: #999999;
                }

            .order_menu .row_table .pro_list .pro_info .pro_attr {
                padding-right: 7px;
                display: inline-block;
                font-size: 13px;
            }

                .order_menu .row_table .pro_list .pro_info .pro_attr span {
                    color: #333333;
                }

            .order_menu .row_table .pro_list .pro_info .o_price {
                display: none;
            }

.mobile_top_box {
    display: none;
}

@media screen and (max-width: 1000px) {
    .grey_blank20 {
        height: 10px;
    }

    .order_body h3.title {
        margin: 0 15px;
        height: 64px;
        line-height: 64px;
        font-size: 16px;
        color: #333333;
    }

    .order_body .order_box > .title, .order_body .order_box .status_box {
        display: none;
    }

    .order_body .order_box {
        padding-bottom: 0;
        margin-bottom: 20px;
    }

    .box_payment_offline {
        margin: 10px;
        padding: 10px;
        position: relative;
    }

        .box_payment_offline::after {
            content: '';
            height: 10px;
            background-color: #f5f5f5;
            bottom: -20px;
            left: -10px;
            right: -10px;
            position: absolute;
        }

        .box_payment_offline .payment_form .button .btn_global {
            margin-top: 20px;
        }

    .order_base .img_box {
        margin-top: 10px;
    }

    .order_base .proof_box {
        padding: 0;
        margin-top: 0;
        line-height: 30px;
        background-color: #fff;
        font-size: 13px;
    }

        .order_base .proof_box .pbtit {
            color: #999;
        }

    .order_address {
        display: none;
    }

    .mobile_top_box {
        display: block;
    }

        .mobile_top_box .scroll_height {
            height: 58px;
        }

        .mobile_top_box .mobile_order_info {
            padding: 15px;
            border-radius: 5px;
        }

            .mobile_top_box .mobile_order_info .info_item {
                line-height: 33px;
                font-size: 13px;
                color: #333333;
            }

                .mobile_top_box .mobile_order_info .info_item .title {
                    width: 50%;
                    color: #999999;
                    ;
                }

        .mobile_top_box .mobile_order_status {
            display: block;
            padding: 22px 25px;
            background: url(../images/user/order_box_bg.png) no-repeat center center / cover;
        }

            .mobile_top_box .mobile_order_status .icon {
                margin-right: 15px;
                width: 30px;
                height: 30px;
                line-height: 30px;
                color: #fff;
                font-size: 36px;
            }

            .mobile_top_box .mobile_order_status .info {
                width: calc( 100% - 45px );
            }

            .mobile_top_box .mobile_order_status .pay_now {
                display: inline-block;
                margin-top: 15px;
                padding: 0 28px;
                height: 32px;
                line-height: 32px;
                border-radius: 5px;
                background-color: #ffffff;
                text-align: center;
                font-size: 12px;
                color: #f16056;
                font-weight: 600;
            }

            .mobile_top_box .mobile_order_status .info .status {
                font-size: 15px;
                color: #fff;
                font-weight: 600;
            }

            .mobile_top_box .mobile_order_status .info .message {
                font-size: 12px;
                color: #fff;
            }

        .mobile_top_box .mobile_shipped_address {
            border-radius: 5px;
        }

            .mobile_top_box .mobile_shipped_address .shipped_info {
                padding: 20px 15px;
                line-height: 30px;
                font-size: 13px;
            }

        .mobile_top_box .mobile_billing_address {
            border-radius: 5px;
        }

            .mobile_top_box .mobile_billing_address .billing_info {
                padding: 20px 15px;
                line-height: 30px;
                font-size: 13px;
            }

        .mobile_top_box .fixed_btn {
            position: fixed;
            padding: 10px 10px 14px;
            width: 100%;
            background-color: #f5f5f5;
            left: 0;
            bottom: 0;
            box-sizing: border-box;
            z-index: 10;
        }

            .mobile_top_box .fixed_btn.static {
                position: static;
                padding: 10px 10px 0;
                background-color: #fff;
            }

            .mobile_top_box .fixed_btn a {
                display: block;
                width: 100%;
                height: 48px;
                line-height: 48px;
                border-radius: 5px;
                background-color: #f16056;
                font-size: 14px;
                font-weight: 600;
                color: #fff;
                text-align: center;
            }

    .waybill_products_list .products_list_item, .delete_products_list .products_list_item {
        margin: 0 15px
    }

    .order_menu .row_hd .top {
        margin-bottom: 10px;
        font-size: 14px;
        color: #333333;
    }

    .order_menu .row_table .thead {
        display: none;
    }

    .order_menu .row_hd .bot > span {
        display: block;
        padding-right: 0;
        line-height: 24px;
        font-size: 12px;
    }

    .order_menu .row_hd {
        padding: 25px 0 0;
    }

    .order_menu .row_table .tbody .tr {
        padding: 10px 0 20px;
    }

    .order_menu .row_table .pro_list .img {
        width: 65px;
        height: 65px;
        line-height: 14px;
        margin-right: 20px;
        font-size: 12px;
    }

    .order_menu .row_table .pro_list .pro_info h4 {
        line-height: 23px;
    }

        .order_menu .row_table .pro_list .pro_info h4 a {
            font-size: 13px;
        }

    .order_menu .row_table .pro_list .pro_info .pro_attr {
        padding-right: 7px;
        line-height: 23px;
        font-size: 13px;
    }

    .order_menu .row_table .tbody .tr .pro_qty,
    .order_menu .row_table .tbody .tr .pro_amount,
    .order_menu .row_table .tbody .tr .pro_price {
        display: none;
    }

    .order_menu .row_table .pro_list .pro_info {
        width: calc( 100% - 85px );
    }

        .order_menu .row_table .pro_list .pro_info .o_price {
            display: block;
            line-height: 23px;
            font-size: 13px;
            color: #222222;
            font-weight: 600;
        }

    .order_menu .row_table .tbody .tr > div {
        float: none;
        padding-left: 0;
        width: 100%;
    }
}

.grand_total > table {
    background: #f8f8f8;
    line-height: 24px;
    font-size: 12px;
    border-bottom: 1px solid #dfdfdf;
}

    .grand_total > table tr:first-child {
        height: 15px;
        line-height: 15px;
    }

    .grand_total > table th em {
        display: none;
        padding: 0 15px;
        font-size: 12px;
        font-weight: normal;
    }

    .grand_total > table td {
        text-align: right;
        font-size: 14px;
        color: #333333;
    }

    .grand_total > table tfoot th, .grand_total > table tfoot td {
        font-size: 14px;
        line-height: 62px;
    }

    .grand_total > table tfoot td {
        padding-right: 10px;
    }

    .grand_total > table .totalPrice {
        padding-right: 0;
        font-size: 20px;
        color: #e83e3e;
        font-weight: 600;
    }

    .grand_total > table a.pay_now {
        display: block;
        width: 100%;
        height: 36px;
        margin-bottom: 26px;
        line-height: 36px;
        color: #fff;
        font-size: 16px;
        text-align: center;
        background: #e53935;
        text-decoration: none;
        border-radius: 3px;
    }

.grand_total_chang_pay {
    float: right;
    margin-right: 30px;
    width: 35%;
}

    .grand_total_chang_pay > table {
        width: 100%;
        border-bottom: none;
        background: none;
    }

        .grand_total_chang_pay > table th {
            width: 61.5%;
            line-height: 32px;
            font-size: 14px;
            text-align: right;
            color: #999999;
        }

        .grand_total_chang_pay > table tfoot th, .grand_total_chang_pay > table tfoot td {
            line-height: 32px;
        }

        .grand_total_chang_pay > table a.pay_now {
            width: auto;
            margin-top: 10px;
            margin-left: 10px;
            margin-bottom: 10px;
        }

.order_cancel_info {
    padding: 10px;
    margin-bottom: 10px;
}

    .order_cancel_info .back_click {
        color: #c00;
        text-decoration: underline;
    }

    .order_cancel_info > table {
        margin-top: 20px;
    }

        .order_cancel_info > table th {
            text-align: left;
            width: 20%;
            line-height: 30px;
            color: #333;
        }

        .order_cancel_info > table td textarea {
            margin-top: 10px;
        }

.order_cancel_view {
    margin-bottom: 10px;
}

    .order_cancel_view h3 {
        font-size: 14px;
        padding: 6px 0;
        font-weight: bold;
        color: #666;
    }

    .order_cancel_view p {
        margin: 0;
    }

        .order_cancel_view p a {
            color: #c00;
        }

#cancelForm .form_button {
    width: auto;
    height: auto;
    line-height: auto;
    display: inline-block;
    padding: 5px 20px;
    border-radius: 3px;
}

@media screen and (max-width:1000px) {
    .grand_total_chang_pay {
        margin-right: 0;
        padding: 0 15px;
        width: calc( 100% - 30px );
    }

        .grand_total_chang_pay > table th {
            width: 50%;
            line-height: 30px;
            font-size: 13px;
            text-align: left;
        }

        .grand_total_chang_pay > table tfoot th, .grand_total_chang_pay > table tfoot td {
            line-height: 30px;
            font-size: 13px;
        }

    .grand_total > table .totalPrice {
        font-size: 16px;
    }

    .grand_total > table td {
        line-height: 30px;
        font-size: 13px;
    }

    .order_menu {
        padding-bottom: 17px;
    }
}

.box_drop_down_buy_again {
    display: none;
    position: fixed;
    top: 150px;
    left: calc(100% / 2 - 300px);
    z-index: 100001;
    overflow: hidden;
    border-radius: 8px;
    width: 600px;
    background-color: #fff;
    box-shadow: 0 0 20px 5px rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
}

    .box_drop_down_buy_again .close {
        position: absolute;
        top: 0;
        right: 0;
        width: 56px;
        height: 56px;
        text-align: center;
        cursor: pointer;
        line-height: 56px;
    }

        .box_drop_down_buy_again .close .icon {
            font-size: 16px;
            color: #4d4d4d;
        }

    .box_drop_down_buy_again .drop_head {
        padding: 40px 0 25px;
        text-align: center;
    }

        .box_drop_down_buy_again .drop_head .icon_success_status {
            display: inline-block;
            position: relative;
            top: 0;
            left: 0;
            margin: 10px 0;
        }

            .box_drop_down_buy_again .drop_head .icon_success_status::before {
                content: "\e647";
                position: absolute;
                top: 0;
                left: 0;
                border-radius: 50px;
                font-family: "iconfont";
                font-size: 43px;
                color: #fff;
                background: #0baf4d;
            }

        .box_drop_down_buy_again .drop_head .note {
            font-size: 18px;
            color: #222;
            line-height: 1.5;
        }

    .box_drop_down_buy_again .drop_products {
        padding: 20px 30px;
        background-color: #f7f7f7;
    }

        .box_drop_down_buy_again .drop_products .pro_title {
            color: #222;
            line-height: 2;
        }

        .box_drop_down_buy_again .drop_products .pro_list {
            overflow-y: auto;
            max-height: 350px;
            margin-top: 10px;
        }

            .box_drop_down_buy_again .drop_products .pro_list::-webkit-scrollbar {
                width: 5px;
                border-radius: 5px;
                background-color: #f7f7f7;
            }

            .box_drop_down_buy_again .drop_products .pro_list::-webkit-scrollbar-thumb {
                border-radius: 5px;
                background-color: rgba(0, 0, 0, .1);
            }

                .box_drop_down_buy_again .drop_products .pro_list::-webkit-scrollbar-thumb:hover {
                    background-color: rgba(0, 0, 0, .3);
                }

        .box_drop_down_buy_again .drop_products .pro_item {
            display: flex;
            margin-top: 10px;
            padding: 22px;
            background-color: #fff;
        }

            .box_drop_down_buy_again .drop_products .pro_item .img {
                position: relative;
                width: 70px;
                height: 70px;
                text-align: center;
            }

                .box_drop_down_buy_again .drop_products .pro_item .img > a {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    height: 100%;
                }

            .box_drop_down_buy_again .drop_products .pro_item .qty {
                position: absolute;
                top: -11px;
                right: -11px;
                z-index: 1;
                border-radius: 22px;
                min-width: 12px;
                height: 22px;
                padding: 0 5px;
                text-align: center;
                font-size: 14px;
                color: #fff;
                line-height: 1.75;
                background-color: #808080;
            }

            .box_drop_down_buy_again .drop_products .pro_item .info {
                flex: 1;
                margin-left: 15px;
                line-height: 20px;
                font-size: 14px;
            }

            .box_drop_down_buy_again .drop_products .pro_item .name {
                display: block;
                margin-bottom: 5px;
                font-size: 14px;
                color: #222;
            }

            .box_drop_down_buy_again .drop_products .pro_item .attribute {
                font-size: 12px;
                color: #666;
            }

                .box_drop_down_buy_again .drop_products .pro_item .attribute > span {
                    margin-left: 15px;
                }

                    .box_drop_down_buy_again .drop_products .pro_item .attribute > span:first-child {
                        margin-left: 0;
                    }

            .box_drop_down_buy_again .drop_products .pro_item .error {
                margin-top: 15px;
                color: #c00;
            }

            .box_drop_down_buy_again .drop_products .pro_item:first-child {
                margin-top: 0;
            }

@media screen and (max-width: 1000px) {
    .box_drop_down_buy_again {
        left: calc(100% / 2 - 45%);
        width: 90%;
    }

        .box_drop_down_buy_again .drop_products {
            padding: 15px;
        }

            .box_drop_down_buy_again .drop_products .pro_item {
                padding: 15px;
            }

                .box_drop_down_buy_again .drop_products .pro_item .img {
                    width: 50px;
                    height: 50px;
                }
}

/*************************** 浼氬憳璁㈠崟 End ***************************/

/*************************** 浼氬憳璇勮 & Q&A Start ***************************/

#lib_user_review {
    padding-bottom: 30px;
    background-color: #fff;
}

    #lib_user_review h2 {
        border-bottom: 0;
    }

    #lib_user_review .review_table {
        width: 100%;
        background-color: #fff;
    }

        #lib_user_review .review_table .top_title .top_item {
            float: left;
            padding: 0 30px;
            height: 50px;
            line-height: 50px;
            background-color: #f9f9f9;
            font-size: 14px;
            font-family: "HarmonyOSHans-Regular";
            box-sizing: border-box;
        }

            #lib_user_review .review_table .top_title .top_item:first-child {
                width: 52%;
            }

            #lib_user_review .review_table .top_title .top_item:nth-child(2) {
                width: 30.2%;
            }

            #lib_user_review .review_table .top_title .top_item:nth-child(3) {
                width: 17.8%;
            }

        #lib_user_review .review_table .item > div:first-child {
            width: 52%;
        }

        #lib_user_review .review_table .item > div:nth-child(2) {
            width: 30.2%;
        }

        #lib_user_review .review_table .item > div:nth-child(3) {
            width: 17.8%;
        }

        #lib_user_review .review_table .item .title .name a,
        #lib_user_review .review_table .item .title .sku {
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden;
        }

        #lib_user_review .review_table .item:first-child {
            border-top: 0;
        }

        #lib_user_review .review_table .item {
            border-top: 1px solid #e3e3e3;
            font-size: 0;
        }

            #lib_user_review .review_table .item > div {
                display: inline-block;
                padding: 30px;
                vertical-align: top;
                box-sizing: border-box;
            }

            #lib_user_review .review_table .item .pic_box {
                display: inline-block;
                vertical-align: middle;
                margin-right: 28px;
                width: 78px;
                height: 78px;
                border: 1px solid #e5e5e5;
                border-radius: 5px;
            }

            #lib_user_review .review_table .item .title {
                display: inline-block;
                vertical-align: top;
            }

                #lib_user_review .review_table .item .title .name a {
                    font-size: 14px;
                    color: #999999;
                }

                #lib_user_review .review_table .item .title .sku {
                    margin-top: 10px;
                    font-size: 14px;
                }

            #lib_user_review .review_table .item .rating {
                color: #ffc322;
            }

                #lib_user_review .review_table .item .rating .star_0,
                #lib_user_review.detail .detail_item .writer .review_star .star_0 {
                    display: inline-block;
                    font-family: "iconfont" !important;
                    color: #cccccc;
                    font-size: 16px;
                    font-style: normal;
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                }

    #lib_user_review.detail .detail_item .writer .review_star .star_0 {
        font-size: 20px;
    }

    #lib_user_review .review_table .item .date {
        margin-top: 8px;
        font-size: 14px;
        color: #999999;
    }

    #lib_user_review .review_table .item .user_action_down a {
        display: block;
        width: 100px;
        height: 36px;
        line-height: 36px;
        border-radius: 5px;
        background-color: #f16056;
        color: #fff;
        font-size: 14px;
        text-align: center;
        font-weight: 600;
        text-decoration: none;
    }

    #lib_user_review.detail h2 {
        margin: 0 30px;
        height: 86px;
        line-height: 86px;
        text-align: left;
        color: #333333;
        border-bottom: 1px solid #e3e3e3;
        font-size: 20px;
    }

    #lib_user_review.detail .detail_item {
        padding: 30px;
    }

        #lib_user_review.detail .detail_item .img {
            padding: 19px;
            width: 100%;
            min-height: 120px;
            background-color: #f9f9f9;
            box-sizing: border-box;
        }

            #lib_user_review.detail .detail_item .img .pic_box {
                display: block;
                margin-right: 19px;
                width: 80px;
                border: 1px solid #e5e5e5;
                border-radius: 5px;
                overflow: hidden;
            }

        #lib_user_review.detail .detail_item .name {
            padding: 13px 0;
            width: 435px;
            overflow: hidden;
            line-height: 24px;
            font-size: 14px;
            color: #999999;
        }

        #lib_user_review.detail .detail_item .writer {
            margin-top: 30px;
        }

            #lib_user_review.detail .detail_item .writer .review_star {
                float: left;
                font-size: 20px;
            }

                #lib_user_review.detail .detail_item .writer .review_star span {
                    font-size: 20px;
                    color: #ffc322;
                }

        #lib_user_review.detail .detail_item .time {
            float: right;
            font-size: 14px;
            color: #999999;
            font-family: "HarmonyOSHans-Regular";
        }

        #lib_user_review.detail .detail_item .content {
            margin-top: 15px;
            line-height: 28px;
            font-size: 14px;
        }

        #lib_user_review.detail .detail_item .pic_content {
            display: flex;
            align-items: center;
            margin-top: 50px;
        }

            #lib_user_review.detail .detail_item .pic_content .item_video {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 80px;
                height: 80px;
                margin-right: 10px;
                cursor: pointer;
                background: #000;
            }

                #lib_user_review.detail .detail_item .pic_content .item_video::before {
                    content: '';
                    position: absolute;
                    right: 0;
                    top: 0;
                    left: 0;
                    bottom: 0;
                    margin: auto;
                    display: flex;
                    background: url(../images/ico/icon_video2.svg) no-repeat center / 32px auto;
                    z-index: 1;
                }

                #lib_user_review.detail .detail_item .pic_content .item_video .pic_box {
                    width: 100%;
                    height: 100%;
                }

            #lib_user_review.detail .detail_item .pic_content a {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 80px;
                height: 80px;
                margin-right: 10px;
            }

@media screen and (max-width:1000px) {
    #lib_user_review .review_table .top_title {
        display: none;
    }

    #lib_user_review .review_table .item > div:first-child,
    #lib_user_review .review_table .item > div:nth-child(2),
    #lib_user_review .review_table .item > div:nth-child(3) {
        padding: 0;
        width: 100%;
    }

    #lib_user_review .review_table .list {
        padding: 0 15px;
    }

    #lib_user_review .review_table .item {
        float: left;
        margin-right: 4.4%;
        margin-bottom: 20px;
        width: 47.8%;
    }

        #lib_user_review .review_table .item:nth-child(2n) {
            margin-right: 0;
        }

        #lib_user_review .review_table .item .pic_box {
            display: block;
            padding-bottom: 100%;
            width: 100%;
            height: 0;
            position: relative;
        }

            #lib_user_review .review_table .item .pic_box img {
                display: block;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%,-50%);
            }

        #lib_user_review .review_table .item .title {
            display: block;
            margin-top: 12px;
            padding-bottom: 10px;
            line-height: 24px;
            border-bottom: 1px solid #f9f9f9;
        }

            #lib_user_review .review_table .item .title .name a {
                display: block;
                font-size: 14px;
                color: #999999;
            }

            #lib_user_review .review_table .item .title .sku {
                margin-top: 0;
                font-size: 14px;
                color: #333333;
            }

        #lib_user_review .review_table .item .rating {
            margin-top: 10px;
            color: #000000;
            font-size: 14px;
        }

        #lib_user_review .review_table .item .date {
            font-size: 12px;
        }

        #lib_user_review .review_table .item .user_action_down a {
            margin-top: 20px;
            width: 100%;
            height: 46px;
            line-height: 46px;
            font-size: 14px;
        }


    #lib_user_review.detail h2 {
        height: 64px;
        line-height: 64px;
        font-size: 16px;
    }

    #lib_user_review.detail .detail_item {
        padding: 25px 15px 0;
    }

        #lib_user_review.detail .detail_item .img {
            padding: 15px;
        }

            #lib_user_review.detail .detail_item .img .pic_box {
                margin-right: 15px;
                width: 72px;
            }

        #lib_user_review.detail .detail_item .name {
            width: calc( 100% - 90px );
            padding: 0;
            line-height: 24px;
            font-size: 14px;
            color: #999999;
        }

        #lib_user_review.detail .detail_item .writer {
            margin-top: 25px;
        }

            #lib_user_review.detail .detail_item .writer .review_star span {
                font-size: 16px;
            }

        #lib_user_review.detail .detail_item .time {
            float: none;
            margin-left: 16px;
            font-size: 12px;
        }

        #lib_user_review.detail .detail_item .content {
            line-height: 24px;
            font-size: 14px;
            word-break: break-word;
        }

        #lib_user_review.detail .detail_item .pic_content {
            margin-top: 15px;
        }

            #lib_user_review.detail .detail_item .pic_content a {
                width: 65px;
                height: 65px;
            }
}

/*************************** 浼氬憳璇勮 Q&A End ***************************/

/*************************** 浼氬憳鏀惰棌 Start ***************************/
#lib_user.favorite #user_heading h2 {
    border-bottom: none;
    text-align: left;
}

#lib_user_favorite {
    padding: 30px 0 0 0;
}

    #lib_user_favorite .tips {
        font-size: 16px;
        min-height: 150px;
    }

    #lib_user_favorite .pro_item:first-child {
        padding-top: 0;
    }

    #lib_user_favorite .pro_item {
        float: none;
        padding: 30px 0;
        width: 100%;
        border-bottom: 1px solid #e3e3e3;
    }

        #lib_user_favorite .pro_item:last-child {
            border: 0
        }

        #lib_user_favorite .pro_item .img {
            margin-right: 28px;
            width: 132px;
        }

            #lib_user_favorite .pro_item .img a {
                display: block;
                width: 100%;
                height: 0;
                padding-bottom: 100%;
                position: relative;
            }

                #lib_user_favorite .pro_item .img a img {
                    border: 1px solid #e5e5e5;
                    border-radius: 5px;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%,-50%);
                }

        #lib_user_favorite .pro_item .pro_info {
            max-width: 280px;
        }

            #lib_user_favorite .pro_item .pro_info .name {
                margin-bottom: 12px;
                line-height: 20px;
            }

                #lib_user_favorite .pro_item .pro_info .name a {
                    font-size: 14px;
                    color: #333333;
                }

            #lib_user_favorite .pro_item .pro_info .pro_review span {
                font-size: 18px;
                color: #ffc322;
            }

            #lib_user_favorite .pro_item .pro_info .pro_review .star_0 {
                display: inline-block;
                font-family: "iconfont" !important;
                font-size: 18px;
                font-style: normal;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                color: #cccccc;
            }

                #lib_user_favorite .pro_item .pro_info .pro_review .star_0:before {
                    content: "\e632";
                }

            #lib_user_favorite .pro_item .pro_info .price {
                margin-top: 12px;
                font-size: 14px;
                color: #222222;
                font-weight: 600;
            }

        #lib_user_favorite .pro_item .pro_view {
            width: 110px;
        }

            #lib_user_favorite .pro_item .pro_view .view {
                display: block;
                margin-bottom: 14px;
                width: 100%;
                height: auto;
                line-height: 36px;
                border-radius: 5px;
                background-color: #f16056;
                font-size: 14px;
                color: #fff;
                text-align: center;
                font-weight: 600;
            }

            #lib_user_favorite .pro_item .pro_view .remove {
                display: block;
                margin: 0 auto;
                width: 75px;
                height: 15px;
                line-height: 15px;
                font-size: 13px;
            }

                #lib_user_favorite .pro_item .pro_view .remove i {
                    margin-right: 5px;
                    font-size: 16px;
                }

@media screen and (max-width:750px) {
    #lib_user_favorite {
        padding: 15px;
    }

        #lib_user_favorite .pro_item {
            padding: 15px 0;
            border-bottom: 0;
        }

            #lib_user_favorite .pro_item .img {
                margin-right: 16px;
                width: 116px;
            }

        #lib_user_favorite .resize {
            float: right;
            width: calc( 100% - 132px );
        }

        #lib_user_favorite .pro_item .pro_info {
            max-width: 100%;
            width: 100%;
            float: none;
        }

        #lib_user_favorite .pro_item .pro_view {
            width: 100%;
            float: none;
        }

        #lib_user_favorite .pro_item .pro_info .price {
            font-size: 14px;
            color: #222222;
        }

        #lib_user_favorite .pro_item .pro_info .PriceColor {
            color: #222;
            font-weight: 600;
        }

        #lib_user_favorite .pro_item .pro_view .view {
            float: left;
            margin-top: 15px;
            margin-bottom: 0;
            width: 105px;
            height: 33px;
            line-height: 33px;
            font-size: 14px;
        }

        #lib_user_favorite .pro_item .pro_view .remove {
            float: left;
            margin-top: 24px;
            margin-left: 30px;
            padding-left: 0;
            font-size: 0;
            width: 20px;
            height: 20px;
        }

            #lib_user_favorite .pro_item .pro_view .remove i {
                font-size: 20px;
            }
}

@media screen and (max-width:400px) {
    #lib_user_favorite .pro_item .pro_view .remove {
        margin-left: 15px;
    }
}

#lib_user_favorite .user_page_pro {
    border-bottom: 1px solid #e3e3e3;
    margin-bottom: 27px;
}

.menu_title > li.add {
    float: right;
}

    .menu_title > li.add a {
        background: url(../images/user/icon_add.png) no-repeat left center;
        color: #2b2b2b;
    }
/*************************** 浼氬憳鏀惰棌 End ***************************/

/*************************** 鍩烘湰璧勬枡 Start ***************************/

#user_main .user_setting_container {
    padding: 50px 0 80px;
    background-color: #fff;
}

    #user_main .user_setting_container .container {
        margin: 0 auto;
        width: 648px;
    }

    #user_main .user_setting_container .setting_title {
        margin-bottom: 30px;
        font-size: 18px;
        font-weight: 600;
    }

    #user_main .user_setting_container .setting_box:first-child {
        margin-top: 0;
    }

    #user_main .user_setting_container .setting_box {
        margin-top: 60px;
    }

    #user_main .user_setting_container .input .input_box_label {
        display: block;
        margin-bottom: 8px;
        width: 100%;
        position: static;
        font-size: 14px;
        color: #333333;
    }

        #user_main .user_setting_container .input .input_box_label .reg_color {
            color: #f16056;
        }

    #user_main .user_setting_container .box .input {
        display: block;
    }

        #user_main .user_setting_container .box .input.filled {
            margin-top: 30px;
        }

    #user_main .user_setting_container .rows:first-child .box .input.filled {
        margin-top: 0;
    }

    #user_main .user_setting_container .box.half {
        float: left;
        margin-right: 20px;
        width: 313px;
    }

        #user_main .user_setting_container .box.half:nth-child(2n) {
            margin-right: 0;
        }

    #user_main .user_setting_container .box .input .input_box_txt {
        box-sizing: border-box;
        padding: 0 12px;
        width: 100%;
        height: 49px;
        border: 1px solid #d9d9d9;
        border-radius: 5px;
        transition: .4s;
        font-family: "HarmonyOSHans-Regular";
    }

        #user_main .user_setting_container .box .input .input_box_txt::-webkit-input-placeholder {
            color: #666666;
            font-family: "HarmonyOSHans-Regular";
        }

    #user_main .user_setting_container .setting_button .btn_submit {
        margin-top: 20px;
        width: 142px;
        height: 46px;
        line-height: 46px;
        border: 0;
        border-radius: 5px;
        background-color: #f16056;
        font-size: 16px;
        font-weight: 600;
        color: #fff;
        text-align: center;
    }

@media screen and (min-width:1000px) {
    #user_main .user_setting_container .box .input .input_box_txt:focus {
        border-color: #666666;
        box-shadow: 0 0 0px 1px #ebebeb;
    }

    #user_main .user_setting_container .box .input .input_box_txt::-webkit-input-placeholder {
        color: #aaaaaa;
        font-family: "HarmonyOSHans-Regular";
    }
}

@media screen and (max-width:1000px) {
    #user_main .user_setting_container {
        padding: 25px 0 25px;
    }

        #user_main .user_setting_container .container {
            width: 92%;
        }

        #user_main .user_setting_container .setting_title {
            margin-bottom: 15px;
            font-size: 16px;
        }

        #user_main .user_setting_container .box.half {
            float: none;
            width: 100%;
        }

        #user_main .user_setting_container .input .input_box_label {
            font-size: 14px;
        }

        #user_main .user_setting_container .box.half {
            margin-top: 20px;
        }

            #user_main .user_setting_container .box.half:first-child {
                margin-top: 0;
            }

        #user_main .user_setting_container .setting_box {
            margin-top: 40px;
        }

        #user_main .user_setting_container .box .input .input_box_txt {
            height: 46px;
            line-height: 46px;
            border-width: 1px;
            font-size: 14px;
        }

        #user_main .user_setting_container .setting_button .btn_submit {
            width: 116px;
            height: 40px;
            line-height: 40px;
        }
}

/*************************** 鍩烘湰璧勬枡 End ***************************/

/*************************** 绔欏唴淇� Start ***************************/

.address_menu {
    margin-top: 38px;
    padding: 0 30px;
}

    .address_menu .menu_title {
        height: 22px;
        line-height: 22px;
    }

        .address_menu .menu_title li {
            display: inline-block;
        }

            .address_menu .menu_title li a {
                text-decoration: none;
                font-size: 18px;
                color: #888888;
                font-weight: 600;
            }

                .address_menu .menu_title li a.current {
                    color: #333333;
                }

            .address_menu .menu_title li.shipping {
                margin-right: 40px;
            }

            .address_menu .menu_title li.add a {
                font-size: 14px;
                color: #333333;
                font-weight: normal;
                background: none
            }

                .address_menu .menu_title li.add a i {
                    margin-right: 8px;
                    display: inline-block;
                    vertical-align: baseline;
                    width: 10px;
                    height: 10px;
                    background: url(../images/user/icon_add_black.png) no-repeat center center;
                }

    .address_menu .menu_content {
        margin-top: 40px;
        padding-bottom: 20px;
    }

        .address_menu .menu_content .address_no_data, .user_no_data {
            display: block;
            margin: 0 auto;
            width: 92%;
            height: 254px;
            background: url(../images/user/user_no_data.png) no-repeat center center;
        }

            .address_menu .menu_content .address_no_data, .user_no_data.fill {
                width: 100%;
            }

#lib_user_address .add_item .rows.top b {
    font-weight: bold;
}

@media screen and (max-width: 1240px) {
    #lib_user_address .add_item {
        margin-left: 2%;
        width: 49%;
    }

    .address_menu .menu_title li.shipping {
        margin-right: 20px;
    }

    .address_menu .menu_title li a {
        font-size: 18px;
    }
}

@media screen and (max-width: 750px) {
    .address_menu {
        margin-top: 0;
        padding: 0;
        position: relative;
    }

        .address_menu .menu_content {
            margin: 0 auto;
            padding-bottom: 82px;
            width: 92%;
            position: relative;
        }

        .address_menu .menu_title {
            padding: 0 15px;
            height: 64px;
            line-height: 64px;
            text-align: center;
        }

            .address_menu .menu_title li.add {
                position: absolute;
                width: calc( 100% - 32px);
                height: 48px;
                line-height: 48px;
                border-radius: 5px;
                left: 50%;
                transform: translate(-50%);
                bottom: 15px;
                background: #333333;
                text-align: center;
                z-index: 1;
            }

                .address_menu .menu_title li.add a {
                    display: block;
                    padding: 0;
                    width: 100%;
                    height: 100%;
                    color: #fff;
                    *background: url(../images/user/icon_add_white.png) no-repeat center left 28%;
                    text-indent: 20px;
                    font-size: 16px;
                }

                    .address_menu .menu_title li.add a i {
                        background-image: url(../images/user/icon_add_white.png);
                    }

            .address_menu .menu_title li.shipping {
                float: left;
                width: 50%;
                text-align: center;
                margin-right: 0;
            }

            .address_menu .menu_title li.billing {
                float: right;
                width: 50%;
                text-align: center;
            }

    #lib_user_address .add_item {
        margin-left: 0;
        margin-top: 14px;
        margin-bottom: 0;
        padding: 0 15px;
        width: 100%;
        height: auto;
    }

        #lib_user_address .add_item:first-child {
            margin-top: 0;
        }

        #lib_user_address .add_item .rows.top {
            height: 50px;
            line-height: 50px;
            font-size: 16px;
        }

        #lib_user_address .add_item .addr_detail {
            max-height: 100%;
            padding-top: 16px;
            padding-bottom: 50px;
            min-height: 66px;
            line-height: 22px;
            font-size: 14px;
        }

        #lib_user_address .add_item .options {
            bottom: 20px;
        }

    #lib_user_address.show_ship_addr #user_heading,
    #lib_user_address.show_ship_addr .billing,
    #lib_user_address.show_bill_addr #user_heading,
    #lib_user_address.show_bill_addr .shipping {
        display: none;
    }


    .address_menu .menu_title li a {
        font-size: 16px;
    }

    #lib_user_address.ship_detail .address_menu .menu_title li:not(.add) {
        float: none;
        width: 100%;
        text-align: left;
    }

    #lib_user_address .shipping_address {
        float: none;
        margin-right: 0;
        width: 100%;
    }

    #lib_user_address #addressForm {
        margin-top: 0;
        padding: 15px;
    }

    #addressForm .shipping_address .rows .form_box {
        display: block;
    }

    #addressForm .user_address_form .form_box .box:first-child {
        margin-right: 0;
        margin-bottom: 10px;
    }
}

@media screen and (max-width: 550px) {
    .address_menu .menu_title {
        padding: 0 15px;
    }
}

.msg_list_box li {
    border-bottom: 1px solid #e5e5e5;
}

    .msg_list_box li a {
        display: block;
        padding: 17px 20px 17px 10px;
    }

        .msg_list_box li a:hover {
            text-decoration: none;
        }

    .msg_list_box li.is_read {
        background-image: url(../images/user/icon_msg_isread.png);
    }

    .msg_list_box li .time {
        font-size: 12px;
        line-height: 18px;
        color: #999;
    }

    .msg_list_box li .title {
        display: block;
        margin-right: 110px;
        line-height: 18px;
        font-size: 14px;
        color: #333333;
    }

    .msg_list_box li .content {
        display: block;
        font-size: 12px;
        line-height: 18px;
        margin-right: 110px;
        color: #999999;
    }

    .msg_list_box li .view {
        font-size: 14px;
        color: #fb4729;
        text-decoration: underline;
    }

.msg_view h3 {
    font-size: 14px;
}

    .msg_view h3.title {
        font-size: 18px;
        color: #333;
        line-height: 38px;
    }

.msg_view .date {
    color: #999;
    font-size: 12px;
    line-height: 26px;
}

.msg_view .content {
    font-size: 14px;
    line-height: 22px;
    color: #666;
    margin-bottom: 35px;
}

.msg_view .rows {
    clear: both;
    border-bottom: 1px solid #e5e5e5;
}

    .msg_view .rows > label, .msg_view .rows .input {
        padding: 8px 0;
        overflow: hidden;
        float: left;
        line-height: 28px;
    }

        .msg_view .rows > label, .msg_view .rows .input .pic {
            max-width: 85px;
            max-height: 85px;
        }

    .msg_view .rows > label {
        width: 15%;
        height: 28px;
        text-align: right;
        padding-right: 20px;
    }

    .msg_view .rows .input {
        width: 80%;
        min-height: 28px;
        padding-left: 10px;
        display: block;
        border-left: 1px solid #e5e5e5;
    }


#lib_user_products .item_list {
    padding: 20px 0 20px 10px;
    border-bottom: 1px dashed #ddd;
}

    #lib_user_products .item_list:hover {
        background: #f9f9f9;
    }

    #lib_user_products .item_list .pic_box {
        width: 60px;
        height: 60px;
        text-align: center;
        float: left;
    }

    #lib_user_products .item_list .item_name {
        width: 340px;
        height: 60px;
        line-height: 30px;
        overflow: hidden;
        float: left;
        margin-left: 20px;
    }

        #lib_user_products .item_list .item_name a {
            font-size: 14px;
            color: #000;
        }

    #lib_user_products .item_list .item_date {
        float: left;
        line-height: 60px;
        color: #999;
        margin-left: 60px;
    }

    #lib_user_products .item_list .item_view {
        float: left;
        margin-top: 18px;
        margin-left: 60px;
        position: relative;
    }

        #lib_user_products .item_list .item_view a {
            width: 65px;
            height: 21px;
            display: inline-block;
            border: 1px solid #c9c9c9;
            -moz-border-radius: 3px;
            -webkit-border-radius: 3px;
            border-radius: 3px;
            text-align: center;
            line-height: 21px;
            color: #999;
            text-decoration: none;
            overflow: hidden;
        }

        #lib_user_products .item_list .item_view span {
            width: 20px;
            height: 20px;
            display: block;
            position: absolute;
            right: -10px;
            top: -10px;
            background: #f00;
            border-radius: 10px;
            color: #fff;
            line-height: 20px;
            text-align: center;
        }

#lib_user_products .prod_con {
    padding: 30px 20px;
    background: #f8f8f8;
}

    #lib_user_products .prod_con .pic_box {
        width: 100px;
        height: 100px;
        text-align: center;
        float: left;
    }

    #lib_user_products .prod_con .prod_name {
        width: 610px;
        float: left;
        margin-left: 20px;
        font-size: 18px;
        line-height: 25px;
    }

#lib_user_products .order_con {
    margin: 0 30px;
    height: 69px;
    line-height: 69px;
    border-bottom: 1px solid #e3e3e3;
    font-size: 18px;
    color: #333333;
    background: #fff;
    font-size: 20px;
    font-weight: bold;
}

    #lib_user_products .order_con span.or_name {
        font-weight: bold;
    }

    #lib_user_products .order_con span.or_date {
        float: right;
        font-weight: normal;
    }

#lib_user_products .content_box {
    padding: 40px 30px 0;
    max-height: 400px;
    overflow: auto;
}

    #lib_user_products .content_box .item_date {
        margin-bottom: 30px;
        text-align: center;
        font-size: 14px;
        color: #7f7e7c;
    }

#lib_user_products .item {
    float: left;
    margin: 10px 10px 40px;
    width: 425px;
}

    #lib_user_products .item.mine {
        float: right;
        width: 425px;
    }

    #lib_user_products .item.message .item_con {
        border-top-left-radius: 0;
    }

    #lib_user_products .item.mine .item_con {
        width: 100%;
        border-top-right-radius: 0;
        background-color: #eef5ff;
    }

    #lib_user_products .item .item_date {
        color: #888888;
        font-size: 12px;
        text-align: left;
        margin-bottom: 0;
    }

    #lib_user_products .item .item_user {
        margin-right: 10px;
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }

    #lib_user_products .item .item_date, #lib_user_products .item .item_user {
        display: inline-block;
        vertical-align: middle;
    }

    #lib_user_products .item .item_img {
        width: 62px;
        height: 62px;
        margin-left: 20px;
        float: left;
    }

    #lib_user_products .item .item_con {
        margin-top: 10px;
        line-height: 30px;
        padding: 15px 21px;
        color: #111;
        background-color: #f7f7f7;
        border-radius: 35px;
        position: relative;
        font-size: 14px;
        box-sizing: border-box;
    }

        #lib_user_products .item .item_con .light_box_pic img {
            max-width: 100px;
            max-height: 100px;
            margin-top: 10px;
        }

        #lib_user_products .item .item_con .item_video {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100px;
            height: 100px;
            margin-top: 10px;
            cursor: pointer;
            background: #000;
            border-radius: 8px;
        }

            #lib_user_products .item .item_con .item_video span {
                position: absolute;
                width: 32px;
                height: 32px;
                right: 0;
                top: 0;
                left: 0;
                bottom: 0;
                margin: auto;
                background: url(../../../ico/icon_video.png) no-repeat center;
            }

#lib_user_products .mine .item_con {
    float: right;
    margin-left: 20px;
}

#lib_user_products .mine .item_img {
    float: right;
    margin-right: 20px;
}

#lib_user_products .empty_message {
    min-height: 180px;
}

#cancelForm {
    margin: 0 30px;
}

@media screen and (max-width: 1000px) {
    #cancelForm {
        margin: 0 15px
    }

    #lib_user_products .item {
        margin: 0 0 20px;
        width: 100%;
    }

        #lib_user_products .item.mine {
            width: 100%;
        }

    #lib_user_products .content_box .item_date {
        margin-bottom: 20px;
    }

    #lib_user_products .item .item_con {
        line-height: 24px;
    }

    #lib_user_products #reply_form.contact_form {
        padding: 0 15px 15px;
    }

    #lib_user_products .reply_form .btn_box .video_box {
        margin-right: 10px;
    }

    #lib_user_products #reply_form.contact_form .submit .submit_btn {
        margin-top: 0;
        width: 100%;
        font-size: 0;
        background: none;
    }
}

.inbox_container {
    width: 100%;
    margin: 0;
    overflow: hidden;
    background-color: #fff;
    border-radius: 5px;
}

    .inbox_container .inbox_left {
        width: 673px;
        float: left;
    }

    .inbox_container .inbox_right {
        width: 249px;
        border-left: 1px #e5e5e5 solid;
        float: right;
    }

    .inbox_container .inbox_full {
        width: inherit;
    }

        .inbox_container .inbox_full .unread_message {
            cursor: pointer;
        }

        .inbox_container .inbox_full .reply_form .box_textarea {
            padding: 13px 0;
            width: calc( 100% - 270px );
            box-sizing: border-box;
        }

    .inbox_container .message_dialogue {
        height: 449px;
        overflow-x: hidden;
        overflow-y: scroll;
        padding: 0 30px 45px;
        background-color: #fff;
        -webkit-overflow-scrolling: touch;
    }

    .inbox_container .dialogue_box {
        width: 425px;
        margin: 0 10px;
    }

    .inbox_container .box_item {
        margin: 40px 0 35px;
    }

        .inbox_container .box_item .date {
            margin-bottom: 40px;
            font-size: 14px;
            color: #7f7e7c;
            text-align: center
        }

    .inbox_container .dialogue_box .time {
        height: 27px;
        line-height: 27px;
        font-size: 12px;
        color: #888888;
        display: inline-block;
        vertical-align: bottom;
    }

        .inbox_container .dialogue_box .time .name {
            display: inline-block;
            vertical-align: inherit;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

    .inbox_container .dialogue_box .message {
        margin-top: 10px;
        line-height: 30px;
        padding: 15px 21px;
        color: #111;
        background-color: #f7f7f7;
        border-radius: 35px;
        ;
        position: relative;
        font-size: 14px;
    }

    .inbox_container .dialogue_box.dialogue_box_left .message {
        border-top-left-radius: 0;
    }

    .inbox_container .dialogue_box.dialogue_box_right .message {
        border-top-right-radius: 0;
    }

    .inbox_container .dialogue_box .picture {
        width: 100px;
        height: 100px;
        margin-top: 10px;
        text-align: center;
        background-color: #fff;
        border-radius: 10px;
        position: relative;
        overflow: hidden;
    }

        .inbox_container .dialogue_box .picture img {
            max-width: 100%;
            max-height: 100%;
        }

    .inbox_container .dialogue_box .item_video {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 100px;
        margin-top: 10px;
        cursor: pointer;
        background: #000;
        border-radius: 8px;
    }

        .inbox_container .dialogue_box .item_video span {
            position: absolute;
            width: 32px;
            height: 32px;
            right: 0;
            top: 0;
            left: 0;
            bottom: 0;
            margin: auto;
            background: url(../../../ico/icon_video.png) no-repeat center;
        }

    .inbox_container .dialogue_box_right {
        text-align: right;
        float: right;
    }

        .inbox_container .dialogue_box_right .message {
            text-align: left;
            background-color: #eef5ff;
        }

        .inbox_container .dialogue_box_right .picture {
            background-color: #fff;
            float: none;
        }

    .inbox_container .message_bottom {
        margin: 0 30px;
        background-color: #fff;
        border-top: 1px #e5e5e5 solid;
    }

    .inbox_container .reply_form {
        padding: 32px 0;
    }

        .inbox_container .reply_form .box_textarea {
            width: 633px;
            height: 46px;
            line-height: 18px;
            padding: 0;
            overflow-x: hidden;
            overflow-y: auto;
            border: 0;
            -webkit-overflow-scrolling: touch;
        }

        .inbox_container .reply_form .btn_box {
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
        }

            .inbox_container .reply_form .btn_box .upload_box {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 53px;
                height: 53px;
                padding: 0;
                margin: 0 10px 0 0;
                background: #f3f3f5;
                border-radius: 5px;
            }

            .inbox_container .reply_form .btn_box .video_box {
                position: relative;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 53px;
                height: 53px;
                padding: 0;
                margin: 0 32px 0 0;
                background: #f3f3f5;
                border-radius: 5px;
            }

                .inbox_container .reply_form .btn_box .video_box input {
                    width: 70px;
                    height: 70px;
                    position: absolute;
                    top: 0px;
                    bottom: 0;
                    right: 0;
                    padding: 0;
                    padding-right: 300px;
                    filter: alpha(opacity=0);
                    -moz-opacity: 0;
                    -webkit-opacity: 0;
                    opacity: 0;
                    cursor: pointer;
                    font-size: 70px;
                }

            .inbox_container .reply_form .btn_box .upload_box .icon-image1 {
                font-size: 25px;
            }

            .inbox_container .reply_form .btn_box .video_box .icon-video1 {
                font-size: 25px;
            }

            .inbox_container .reply_form .btn_box .btn_submit {
                width: 110px;
                height: 51px;
                line-height: 51px;
                color: #fff;
                background-color: #f16056;
                font-size: 16px;
                font-weight: 600;
                border-radius: 5px;
                border: 0;
                float: right;
            }

            .inbox_container .reply_form .btn_box .upload_file {
                width: 70px;
                height: 70px;
                position: absolute;
                top: 0px;
                bottom: 0;
                right: 0;
                padding: 0;
                padding-right: 300px;
                filter: alpha(opacity=0);
                -moz-opacity: 0;
                -webkit-opacity: 0;
                opacity: 0;
                cursor: pointer;
                font-size: 70px;
            }

    .inbox_container .menu_products_list {
        max-width: 100%;
        height: 568px;
        overflow-x: hidden;
        overflow-y: scroll;
        position: relative;
        -webkit-overflow-scrolling: touch;
    }

        .inbox_container .menu_products_list li {
            height: 65px;
            padding: 18px 24px 16px 19px;
            cursor: pointer;
            border-top: 1px #f1f1f1 solid;
            position: relative;
        }

            .inbox_container .menu_products_list li > i {
                min-width: 13px;
                height: 18px;
                line-height: 18px;
                padding: 0 6px;
                text-align: center;
                font-size: 12px;
                color: #fff;
                background-color: #fb4729;
                border-radius: 50px;
                position: absolute;
                top: 18px;
                left: 200px;
                display: block;
            }

            .inbox_container .menu_products_list li .img {
                width: 63px;
                height: 63px;
                text-align: center;
                background-color: #fff;
                border: 1px #fafafa solid;
                float: left;
                box-sizing: border-box;
                -moz-box-sizing: border-box;
                -webkit-box-sizing: border-box;
            }

                .inbox_container .menu_products_list li .img > img {
                    max-width: 100%;
                    max-height: 100%;
                }

            .inbox_container .menu_products_list li .name {
                width: 108px;
                height: 65px;
                line-height: 20px;
                overflow: hidden;
                margin-left: 10px;
                font-size: 12px;
                color: #999;
                float: left;
            }

            .inbox_container .menu_products_list li:hover, .inbox_container .menu_products_list li.current {
                background-color: #f3fcfb;
                border-left-color: #13b287;
            }

            .inbox_container .menu_products_list li:first-child {
                border-top: 0;
            }

            .inbox_container .menu_products_list li.more {
                height: 50px;
                padding: 0;
                text-align: center;
            }

                .inbox_container .menu_products_list li.more .btn_more {
                    height: 26px;
                    line-height: 26px;
                    margin-top: 10px;
                    text-decoration: none;
                    font-size: 14px;
                    color: #fff;
                    background-color: #0cb083;
                    border: 0;
                }

            .inbox_container .menu_products_list li.no_data {
                height: 50px;
                line-height: 50px;
                padding: 0;
                text-align: center;
                font-size: 14px;
                color: #999;
            }

    .inbox_container .message_dialogue, .inbox_container .message_bottom {
        display: none;
    }

    .inbox_container .unread_message {
        width: 100%;
        height: 568px;
        text-align: center;
        position: relative;
        display: inline-block;
    }

        .inbox_container .unread_message > p {
            width: 100%;
            height: 34px;
            line-height: 34px;
            padding-top: 80px;
            text-align: center;
            font-size: 18px;
            color: #bbb;
            background: url(../images/user/bg_unread_message.png) no-repeat center top;
            position: absolute;
            top: 188px;
            left: 0;
        }

        .inbox_container .unread_message > i {
            width: 35px;
            height: 35px;
            line-height: 35px;
            text-align: center;
            font-size: 18px;
            color: #fff;
            background-color: #fb4729;
            border-radius: 50px;
            position: absolute;
            top: 180px;
            left: 365px;
        }

#lib_user_products .contact_form {
    margin-top: 30px;
    padding: 0 30px 30px;
}

    #lib_user_products .contact_form .submit_btn {
        margin-top: 0;
        width: 110px;
        height: 48px;
        line-height: 48px;
        border: 0;
        border-radius: 5px;
        background-color: #f16056;
        font-weight: 600;
    }

    #lib_user_products .contact_form .upload_box {
        float: right;
        margin-top: 11px;
        margin-right: 25px;
        padding: 0;
        width: 21px;
        height: 21px;
        background: url(../images/user/bg_multi_small_img1.png) no-repeat center;
    }

    #lib_user_products .contact_form #pic_show {
        width: 60px;
        height: 60px;
    }

@media screen and (max-width: 1000px) {
    #lib_user_products .order_con {
        margin: 0 15px;
        height: 66px;
        line-height: 66px;
        background-color: #fff;
        font-size: 14px;
    }

    #lib_user_inbox h2 {
        margin: 0 15px 24px;
        padding-bottom: 0x;
        font-size: 20px;
    }

    .inbox_container .dialogue_box {
        margin: 0;
        width: 100%;
    }

    .inbox_container .message_dialogue {
        padding: 0 15px 25px;
    }

    .inbox_container .dialogue_box .message {
        padding: 15px;
    }

    .inbox_container .message_bottom {
        border: none;
    }

    .inbox_container .reply_form {
        position: fixed;
        width: 100%;
        padding: 15px;
        border-top: 1px #e5e5e5 solid;
        background-color: #fff;
        left: 0;
        bottom: 0;
        box-sizing: border-box;
        z-index: 10;
    }

    .inbox_container .inbox_full .reply_form .box_textarea {
        width: 100%;
        font-size: 12px;
    }

    .inbox_container .reply_form .upload_box {
        margin-right: 10px;
        width: 29px;
        height: 29px;
    }

        .inbox_container .reply_form .upload_box i {
            font-size: 28px;
        }

    .inbox_container .reply_form .btn_submit {
        width: 46px;
        height: 46px;
        font-size: 0;
        background: url(../images/user/inbox_button.png) #f16056 no-repeat 10% center;
    }

    .inbox_container .reply_form .btn_box .video_box {
        margin-right: 10px;
    }

    #pic_show {
        max-width: 30px;
        max-height: 30px;
    }

    .inbox_container .box_item {
        margin: 20px 0;
    }

    .inbox_container .dialogue_box .message {
        line-height: 24px;
    }

    .inbox_container .dialogue_box .time .name {
        font-size: 15px;
    }

    .inbox_container .dialogue_box_right {
        float: none;
    }

    #cancelForm.user_form .rows .submit {
        width: 100%;
    }

    #cancelForm.user_form .rows .submit_btn {
        margin-top: 10px;
        width: 100%;
    }
}


/*************************** 绔欏唴淇� End ***************************/

/*************************** 绯荤粺娑堟伅 Start ***************************/
#lib_user_msg .submit_btn {
    display: block;
    height: 28px;
    line-height: 28px;
    border: none;
    padding: 0 30px;
    border-radius: 5px;
    text-align: center;
    text-decoration: none;
    float: left;
    margin-right: 10px;
    background: #87AABE;
    border: 1px solid #7693A3;
    color: #fff;
    cursor: pointer;
}
/*************************** 绯荤粺娑堟伅 End ***************************/

/*************************** 浼氬憳娉ㄥ唽楠岃瘉 Start **************************/
#signup.verification {
    padding-right: 0;
    width: 100%;
    border: 0;
}

    #signup.verification .verification_box {
        padding: 50px 0;
    }

        #signup.verification .verification_box .icon_success {
            display: block;
            margin: 0 auto 30px;
            width: 68px;
            height: 68px;
            background: url(../images/user/icon_user_success.png) no-repeat center center;
        }

        #signup.verification .verification_box .go_home {
            display: block;
            margin: 40px auto 0;
            width: 154px;
            height: 54px;
            line-height: 52px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #333;
            color: #fff;
            font-size: 16px;
            box-sizing: border-box;
            text-align: center;
        }

    #signup.verification .verification_title {
        font-size: 34px;
        color: #000000;
        text-align: center;
        font-weight: bold;
    }

    #signup.verification .verification_subtitle {
        margin-top: 20px;
        font-size: 18px;
        color: #000000;
        text-align: center;
    }

        #signup.verification .verification_subtitle a {
            color: #2e9cc3;
        }

    #signup.verification .verification_button_box {
        margin-top: 35px;
        text-align: center;
        font-size: 0;
    }

        #signup.verification .verification_button_box a {
            display: inline-block;
            vertical-align: middle;
            box-sizing: border-box;
            padding: 0 20px;
            width: auto;
            height: 54px;
            line-height: 52px;
            border: 1px solid #333333;
            border-radius: 5px;
            background-color: #333333;
            color: #fff;
            text-align: center;
            font-size: 16px;
        }

            #signup.verification .verification_button_box a#send_email_btn {
                margin-left: 20px;
                background-color: #fff;
                color: #333;
            }

@media screen and (max-width: 550px) {
    #signup.verification .verification_title {
        font-size: 24px;
    }

    #signup.verification .verification_subtitle {
        font-size: 16px;
    }

    #signup.verification .verification_button_box a,
    #signup.verification .verification_box .go_home {
        display: block;
        margin: 0 auto;
        width: auto;
        height: 36px;
        line-height: 34px;
        font-size: 14px;
    }

        #signup.verification .verification_button_box a#send_email_btn {
            margin-left: 0;
            margin-top: 20px;
        }
}
/*************************** 浼氬憳娉ㄥ唽楠岃瘉  End  **************************/

/*************************** 浼氬憳楠岃瘉椤甸潰  Start  **************************/
#verify {
    padding: 70px 0 300px
}

    #verify .verify_container {
        margin: 0 auto;
        width: 494px;
    }

    #verify .verify_title {
        font-size: 34px;
        color: #262626;
        text-align: center;
        font-weight: bold;
    }

    #verify .verify_subtitle {
        font-size: 16px;
        color: #262626;
        text-align: center;
    }

    #verify .verify_form {
        margin-top: 50px;
    }

        #verify .verify_form .rows {
            margin-top: 30px;
        }

            #verify .verify_form .rows:first-child {
                margin-top: 30px;
            }

            #verify .verify_form .rows label {
                display: block;
                margin-bottom: 5px;
                font-size: 14px;
            }

                #verify .verify_form .rows label .c_reg {
                    color: #ff2800;
                }

            #verify .verify_form .rows .input .input_text {
                display: block;
                padding: 0 17px;
                width: 100%;
                height: 50px;
                line-height: 50px;
                border: 1px solid #a6a6a5;
                border-radius: 5px;
                box-sizing: border-box;
                font-size: 14px;
                color: #979797;
            }

            #verify .verify_form .rows .button {
                display: block;
                width: 100%;
                height: 55px;
                line-height: 55px;
                border-radius: 5px;
                background-color: #000;
                box-sizing: border-box;
                font-size: 16px;
                color: #ffffff;
                cursor: pointer;
            }

    #verify .expired_box {
        text-align: center;
    }

        #verify .expired_box .expired_ico {
            padding-top: 175px;
            background: url(../../../ico/expired_img.png) no-repeat center center;
        }

        #verify .expired_box .expired_title {
            font-size: 28px;
            color: #333333;
            text-align: center;
            text-align: center;
            ;
        }

        #verify .expired_box .return_btn {
            display: inline-block;
            margin-top: 40px;
            padding: 0 17px;
            height: 46px;
            line-height: 46px;
            border-radius: 5px;
            background-color: #e53935;
            color: #ffffff;
            font-size: 16px;
            text-decoration: none;
        }
/*************************** 浼氬憳楠岃瘉椤甸潰  End  **************************/

/**************************** 璁㈠崟璇︽儏鐗╂祦杩借釜 start ************************/
.bot .order_tracking_box::-webkit-scrollbar {
    width: 5px;
}

.bot .order_tracking_box::-webkit-scrollbar {
    width: 5px;
    background: #fff;
    border-radius: 5px;
}

.bot .order_tracking_box::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
}

    .bot .order_tracking_box::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.3);
    }

.bot .order_tracking_box {
    box-shadow: 0px 0px 15px rgba(0,0,0,.3);
    display: none;
    position: absolute;
    max-width: 600px;
    max-height: 485px;
    background: #fff;
    overflow: auto;
    z-index: 999;
    border: 1px solid #eaeef1;
    left: 130px;
    min-width: 45%;
}

    .bot .order_tracking_box .order_tracking_title {
        height: 60px;
        line-height: 60px;
        padding-left: 30px;
        font-size: 14px;
        color: #222222;
        border-bottom: 1px solid #f0f0f0;
        position: relative;
    }

        .bot .order_tracking_box .order_tracking_title .order_tracking_cancel {
            position: absolute;
            right: 30px;
            width: 30px;
            height: 30px;
            line-height: 30px;
            vertical-align: middle;
            margin-top: 15px;
        }

            .bot .order_tracking_box .order_tracking_title .order_tracking_cancel:before {
                content: "x";
                font-size: 16px;
                cursor: pointer;
            }

            .bot .order_tracking_box .order_tracking_title .order_tracking_cancel:hover {
                color: red;
            }

    .bot .order_tracking_box .tracking_box_content {
        padding: 30px;
        text-align: left;
    }

    .bot .order_tracking_box .tracking_box_list li {
        margin-left: 10px;
        border-left: 2px dashed #e1e5eb;
        padding-left: 10px;
        position: relative;
        padding-bottom: 20px;
    }

    .bot .order_tracking_box .tracking_box_list .track_radio {
        display: inline-block;
        width: 12px;
        ;
        height: 12px;
        background: #fff;
        position: absolute;
        top: 38px;
        left: -9px;
        border-radius: 50%;
        border: 2px solid #7d8d9e;
    }

    .bot .order_tracking_box .tracking_box_list .track_content {
        margin-left: 27px;
        background: #f7f9fb;
        min-height: 45px;
        padding: 20px 20px 20px 30px;
        border-radius: 5px;
        position: relative
    }

        .bot .order_tracking_box .tracking_box_list .track_content .arrow {
            width: 0;
            height: 0;
            border-top: 20px solid transparent;
            border-right: 24px solid #f7f9fb;
            border-bottom: 20px solid transparent;
            position: absolute;
            top: 24px;
            left: -23px
        }

        .bot .order_tracking_box .tracking_box_list .track_content .track_content_text {
            line-height: 20px;
            color: #333333;
            font-size: 14px;
        }

        .bot .order_tracking_box .tracking_box_list .track_content .track_content_time {
            line-height: 16px;
            color: #888888;
            font-size: 12px;
            margin-top: 10px;
        }

    .bot .order_tracking_box .tracking_list_hide {
        display: none;
    }

@media screen and (max-width: 1000px ) {
    .bot .order_tracking_box {
        left: 0;
    }

        .bot .order_tracking_box .order_tracking_title {
            padding-left: 15px;
            height: 45px;
            line-height: 45px;
        }

            .bot .order_tracking_box .order_tracking_title .order_tracking_cancel {
                text-align: center;
                right: 15px;
                width: 15px;
                height: 15px;
                line-height: 15px;
            }

        .bot .order_tracking_box .tracking_box_content {
            padding: 30px 15px;
        }

        .bot .order_tracking_box .tracking_box_list .track_content {
            padding: 15px 15px 15px 20px;
        }

            .bot .order_tracking_box .tracking_box_list .track_content .track_content_text {
                font-size: 12px;
            }
}
/**************************** 璁㈠崟璇︽儏鐗╂祦杩借釜 end ************************/

#lib_user .user_setting_container .settings_contianer {
    max-width: 705px;
    margin: auto;
    padding: 30px 30px 38px 30px;
    box-sizing: border-box;
}

#lib_user .user_setting_container .info_box {
    margin-top: 50px;
}

    #lib_user .user_setting_container .info_box:first-child {
        margin-top: 0;
    }

#lib_user .user_setting_container .info_title {
    font-size: 18px;
    color: #333;
}

#lib_user .user_setting_container .both_box_input,
#lib_user .user_setting_container .both_box_select {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

    #lib_user .user_setting_container .both_box_input .input_container,
    #lib_user .user_setting_container .both_box_select .input_container {
        width: 48.5%;
    }

#lib_user .user_setting_container .input_container {
    margin-top: 25px;
}

    #lib_user .user_setting_container .input_container .label {
        font-size: 14px;
        color: #333;
        margin-bottom: 10px;
    }

    #lib_user .user_setting_container .input_container .input .ly_input {
        height: 50px;
        line-height: 50px;
        box-sizing: border-box;
        font-size: 14px;
        color: #666;
        border-radius: 5px;
        border: 1px solid #d9d9d9;
        text-indent: 15px;
        width: 100%;
    }

        #lib_user .user_setting_container .input_container .input .ly_input:focus {
            border-color: #666;
            box-shadow: 0 0 0 1px #ebebeb;
        }

    #lib_user .user_setting_container .input_container .input .ly_textarea {
        box-sizing: border-box;
        width: 100%;
        height: 120px;
        font-size: 14px;
        color: #666;
        border-radius: 5px;
        border: 1px solid #d9d9d9;
        padding: 15px;
    }

        #lib_user .user_setting_container .input_container .input .ly_textarea:focus {
            border-color: #666;
            box-shadow: 0 0 0 1px #ebebeb;
        }

    #lib_user .user_setting_container .input_container .input .global_select_box {
        height: auto;
        line-height: 50px;
        position: relative;
    }

        #lib_user .user_setting_container .input_container .input .global_select_box .input_case {
            position: relative;
            width: 100%;
            background-color: #fff;
            height: 50px;
            box-sizing: border-box;
            border-radius: 5px;
            padding: 0 40px 0 15px;
            cursor: pointer;
            border: 1px solid #d9d9d9;
        }

            #lib_user .user_setting_container .input_container .input .global_select_box .input_case i {
                width: 12px;
                height: 12px;
                line-height: 12px;
                position: absolute;
                right: 18px;
                top: 0;
                bottom: 0;
                margin: auto;
                transform: scale(0.8);
                font-size: 14px;
                color: #666;
                transition: all .5s ease 0s;
            }

                #lib_user .user_setting_container .input_container .input .global_select_box .input_case i::before {
                    content: "\e62b";
                    font-family: "iconfont";
                }

            #lib_user .user_setting_container .input_container .input .global_select_box .input_case input.imitation_select {
                width: 100%;
                height: 100%;
                border: 0;
                cursor: pointer;
                background-color: transparent;
            }

        #lib_user .user_setting_container .input_container .input .global_select_box .select_ul {
            display: none;
            border-radius: 5px;
            width: 100%;
            box-sizing: border-box;
            background-color: #fff;
            position: absolute;
            left: 0;
            top: calc(100% + 2px);
            box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.1);
            overflow: hidden;
            z-index: 3;
        }

            #lib_user .user_setting_container .input_container .input .global_select_box .select_ul::-webkit-scrollbar {
                width: 10px;
                background: #22191a;
            }

            #lib_user .user_setting_container .input_container .input .global_select_box .select_ul::-webkit-scrollbar-thumb {
                background: rgba(193, 193, 193, 0.8);
                border-radius: 5px;
            }

            #lib_user .user_setting_container .input_container .input .global_select_box .select_ul li {
                line-height: 30px;
                transition: all .5s ease 0s;
                padding: 5px 10px 5px 45px;
                color: #555;
                cursor: pointer;
                font-size: 14px;
                position: relative;
            }

                #lib_user .user_setting_container .input_container .input .global_select_box .select_ul li:hover,
                #lib_user .user_setting_container .input_container .input .global_select_box .select_ul li.selected {
                    background-color: #eee;
                    color: #555;
                }

                    #lib_user .user_setting_container .input_container .input .global_select_box .select_ul li.selected.leave {
                        background-color: unset;
                        color: #555;
                    }

                    #lib_user .user_setting_container .input_container .input .global_select_box .select_ul li:hover i,
                    #lib_user .user_setting_container .input_container .input .global_select_box .select_ul li.selected i {
                        border: 5px solid #22191a;
                        background-color: #fff;
                    }

                #lib_user .user_setting_container .input_container .input .global_select_box .select_ul li i {
                    width: 15px;
                    height: 15px;
                    position: absolute;
                    left: 20px;
                    top: 50%;
                    transform: translateY(-50%);
                    border: 1px solid #ccdced;
                    border-radius: 15px;
                    box-sizing: border-box;
                }

        #lib_user .user_setting_container .input_container .input .global_select_box.focus .select_ul {
            display: block;
        }

        #lib_user .user_setting_container .input_container .input .global_select_box.focus .input_case i {
            color: #888;
            transform: scale(0.8) rotate(-180deg);
        }

    #lib_user .user_setting_container .input_container .input .field_checked_box {
        width: 100%;
        font-size: 0;
        box-sizing: border-box;
        background-color: #fff;
        border-radius: 5px;
        padding: 10px 0 0 0;
    }

        #lib_user .user_setting_container .input_container .input .field_checked_box .item {
            display: inline-block;
            margin-right: 35px;
            margin-bottom: 15px;
            font-size: 14px;
            cursor: pointer;
            color: #333;
            line-height: 16px;
        }

            #lib_user .user_setting_container .input_container .input .field_checked_box .item::before {
                display: inline-block;
                width: 14px;
                height: 14px;
                content: '';
                margin-right: 4px;
                background-color: #fff;
                border: 1px solid #ccdced;
                border-radius: 16px;
                vertical-align: bottom;
            }

            #lib_user .user_setting_container .input_container .input .field_checked_box .item.checked::before {
                width: 6px;
                height: 6px;
                border: 5px solid #222;
                border-radius: 16px;
            }

            #lib_user .user_setting_container .input_container .input .field_checked_box .item.disabled {
                cursor: no-drop;
            }

                #lib_user .user_setting_container .input_container .input .field_checked_box .item.disabled::before {
                    width: 6px;
                    height: 6px;
                    border: 5px #d2e0ee solid;
                    border-radius: 16px;
                }

        #lib_user .user_setting_container .input_container .input .field_checked_box input {
            display: none;
        }

        #lib_user .user_setting_container .input_container .input .field_checked_box .input_checkbox_box {
            display: inline-block;
            vertical-align: top;
            margin-right: 32px;
            line-height: 20px;
            cursor: pointer;
            font-size: 14px;
            color: #333
        }

            #lib_user .user_setting_container .input_container .input .field_checked_box .input_checkbox_box .input_checkbox {
                width: 13px;
                height: 13px;
                margin-top: 3px;
                margin-right: 5px;
                margin-bottom: 16px;
                background-color: #fff;
                border: 1px #bbbbbb solid;
                border-radius: 2px;
                position: relative;
                display: inline-block;
                vertical-align: top;
            }

                #lib_user .user_setting_container .input_container .input .field_checked_box .input_checkbox_box .input_checkbox input {
                    display: none;
                }

                #lib_user .user_setting_container .input_container .input .field_checked_box .input_checkbox_box .input_checkbox:before {
                    margin: auto;
                    position: absolute;
                    display: none;
                    content: "\e647";
                    font-family: "iconfont";
                }

            #lib_user .user_setting_container .input_container .input .field_checked_box .input_checkbox_box.checked .input_checkbox {
                border-color: #fff;
            }

                #lib_user .user_setting_container .input_container .input .field_checked_box .input_checkbox_box.checked .input_checkbox:before {
                    display: block;
                    width: 100%;
                    height: 100%;
                    background-color: #222;
                    border: 1px #222 solid;
                    color: #fff;
                    top: 0;
                    bottom: 0;
                    right: 0;
                    left: 0;
                    box-sizing: border-box;
                    font-size: 12px;
                    line-height: 1;
                }

            #lib_user .user_setting_container .input_container .input .field_checked_box .input_checkbox_box.indeterminate .input_checkbox:before {
                display: block;
                width: 100%;
                height: 100%;
                background-color: #222;
                border: 1px #222 solid;
                color: #fff;
                top: 0;
                bottom: 0;
                right: 0;
                left: 0;
                box-sizing: border-box;
                font-size: 12px;
                line-height: 1;
            }

    #lib_user .user_setting_container .input_container .input .form_tool_img_box {
        background-color: #fff;
        border-radius: 8px;
    }

        #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box {
            display: none;
            float: left;
            width: 86px;
            height: 86px;
            position: relative;
            box-sizing: border-box;
            border: 2px dashed #d9d9d9;
            border-radius: 5px;
            margin-right: 15px;
            border: solid 1px #dfdfdf;
            background: #fafafa;
        }

            #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box .iconfont {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%,-50%);
                color: #757575;
                font-size: 30px;
            }

            #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box label {
                margin: 0;
                padding-top: 0;
            }

            #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box:first-child {
                display: block;
            }

            #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box.on:after,
            #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box.on:before {
                background: none;
            }

            #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box.on .num_tips {
                font-size: 0;
            }

            #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box .num_tips {
                position: absolute;
                left: 0;
                top: 65%;
                width: 100%;
                text-align: center;
                color: #dddddd;
                display: none;
            }

        #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box {
            width: 120px;
            height: 120px;
        }

            #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box .pic_box {
                position: relative;
                width: 120px;
                height: 120px;
                vertical-align: middle;
                font-size: 0;
                text-align: center;
                cursor: pointer;
                box-sizing: border-box;
                z-index: 1;
            }

                #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box .pic_box img {
                    opacity: 1;
                }

            #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box .close {
                position: absolute;
                top: -7px;
                right: -7px;
                display: none;
                width: 24px;
                height: 24px;
                background: #000;
                border-radius: 50%;
                cursor: pointer;
                z-index: 2;
                transform: rotate(45deg);
            }

                #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box .close::before {
                    content: '';
                    width: 12px;
                    height: 1px;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background-color: #fff;
                }

                #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box .close::after {
                    content: '';
                    width: 1px;
                    height: 12px;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background-color: #fff;
                }

        #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_file {
            width: 82px;
            height: 82px;
            position: absolute;
            left: 0px;
            top: 0px;
            bottom: 0;
            right: 0;
            padding: 0;
            filter: alpha(opacity=0);
            -moz-opacity: 0;
            -webkit-opacity: 0;
            opacity: 0;
            cursor: pointer;
            font-size: 70px;
            z-index: 1;
        }

        #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_file {
            width: 120px;
            height: 120px;
        }

#lib_user .user_setting_container .submit_button {
    height: 46px;
    line-height: 46px;
    display: inline-block;
    padding: 0 50px;
    background-color: #000;
    font-size: 16px;
    color: #fff;
    border-radius: 5px;
    border: 0;
    margin-top: 20px;
}

@media screen and (max-width:1000px) {
    #lib_user .user_setting_container .settings_contianer {
        padding: 20px;
        width: 100%;
    }

    #lib_user .user_setting_container .both_box_input,
    #lib_user .user_setting_container .both_box_select {
        display: block;
    }

        #lib_user .user_setting_container .both_box_input .input_container,
        #lib_user .user_setting_container .both_box_select .input_container {
            width: 100%;
        }

    #lib_user .user_setting_container .account_global_flex .account_right_content .side_box {
        margin-top: 0;
    }

    #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box {
        width: 79px;
        height: 79px;
    }

        #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box:last-child {
            margin-right: 0;
        }

        #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_box .pic_box {
            width: 100%;
            height: 100%;
        }

    #lib_user .user_setting_container .input_container .input .form_tool_img_box .upload_file {
        width: 100%;
        height: 100%;
    }
}


/*************************************** 浼氬憳绉垎start ***************************************/
.lib_user_main.points .user_container {
    background-color: #fff;
    padding: 25px;
    margin-top: 20px;
}

    .lib_user_main.points .user_container:first-child {
        margin-top: 0;
        padding-top: 0;
    }

    .lib_user_main.points .user_container .title {
        font-size: 24px;
        color: #000;
        line-height: 40px;
        padding-top: 25px;
        padding-bottom: 25px;
    }

#user_points_index {
    background-color: #f5f5f5;
}

    #user_points_index #user_heading h2 {
        border-width: 0;
    }

    #user_points_index .points_box {
        background-color: #f5f5f5;
        padding: 20px 25px;
        display: flex;
    }

        #user_points_index .points_box .box_l {
            flex: 1;
        }

        #user_points_index .points_box .points_num {
            line-height: 40px;
            font-size: 30px;
            color: #f0463d;
            display: flex;
            align-items: baseline;
            font-weight: bold;
        }

            #user_points_index .points_box .points_num span {
                line-height: 20px;
                font-size: 14px;
                color: #1f2328;
                margin-left: 5px;
                font-weight: normal;
            }

        #user_points_index .points_box .desc {
            font-size: 14px;
            color: #333;
            line-height: 32px;
        }

        #user_points_index .points_box a {
            height: 36px;
            line-height: 36px;
            text-decoration: none;
            font-size: 14px;
            color: #fff;
            background-color: #f16056;
            padding: 0 25px;
            border-radius: 5px;
            margin-top: 2px;
        }

    #user_points_index .box_points_flow .flow_title {
        line-height: 42px;
        font-size: 18px;
        color: #1f2328;
        margin: 0 5px;
        font-weight: bold;
    }

    #user_points_index .box_points_flow .flow_type {
        padding-top: 5px;
        line-height: 30px;
        border-bottom: 1px solid #d7d7d7;
        display: flex;
    }

        #user_points_index .box_points_flow .flow_type a {
            line-height: 30px;
            margin: 0 28px;
            font-size: 14px;
            position: relative;
            padding: 0 2px;
        }

            #user_points_index .box_points_flow .flow_type a.cur:after {
                content: '';
                position: absolute;
                left: 0;
                right: 0;
                bottom: -1px;
                height: 2px;
                background-color: #333;
            }

            #user_points_index .box_points_flow .flow_type a:first-child {
                margin-left: 20px;
            }

    #user_points_index .flow_item .item {
        display: flex;
        align-items: center;
        padding: 25px;
        border-bottom: 1px solid #d7d7d7;
        font-size: 14px;
        line-height: 26px;
    }

        #user_points_index .flow_item .item .info {
            flex: 1;
            display: flex;
        }

        #user_points_index .flow_item .item .i_amount {
            min-width: 180px;
        }

        #user_points_index .flow_item .item.tbody .i_amount {
            font-size: 18px;
            font-weight: bold;
        }

        #user_points_index .flow_item .item .i_amount.income {
            color: #f94827;
        }

        #user_points_index .flow_item .item .i_amount.expend {
            color: #1f2328;
        }

        #user_points_index .flow_item .item .channel {
            flex: 1;
            padding-right: 20px;
        }

            #user_points_index .flow_item .item .channel a {
                font-size: 12px;
                color: #f86262;
                text-decoration: underline;
            }

        #user_points_index .flow_item .item .date {
            width: 150px;
        }

        #user_points_index .flow_item .item.thead {
            background-color: #f8f8f8;
            padding-top: 13px;
            padding-bottom: 13px;
            margin-top: 20px;
            border-bottom: unset;
            color: #7e8da0;
        }

    #user_points_index #turn_page {
        padding: 30px 0;
    }

/* 鑾峰彇鏂规硶 */
#user_points_get .glist {
    display: flex;
    border-bottom: 1px #e3e3e3 solid;
    padding: 25px 15px;
    line-height: 30px;
    font-size: 14px;
    color: #1f2328;
}

    #user_points_get .glist .tit {
        font-size: 16px;
    }

    #user_points_get .glist .type {
        flex: 1;
    }

    #user_points_get .glist .status {
        display: flex;
        align-items: center;
    }

        #user_points_get .glist .status span {
            width: 22px;
            height: 22px;
            line-height: 22px;
            text-align: center;
            font-size: 20px;
            border-radius: 50%;
            border: 2px solid #0baf4d;
            color: #0baf4d;
            margin-right: 5px;
        }
/* 浣跨敤鏂规硶 */
#user_points_used .box_flex {
    display: flex;
    flex-wrap: wrap;
}

#user_points_used .ulist {
    width: 100%;
    display: flex;
    align-items: center;
    border-bottom: 1px #e3e3e3 solid;
    padding: 25px 15px;
    line-height: 24px;
    font-size: 14px;
    color: #1f2328;
    font-size: 14px;
}

    #user_points_used .ulist.shopping {
        order: 1;
    }

    #user_points_used .ulist .type {
        flex: 1;
    }

        #user_points_used .ulist .type .tit {
            font-size: 16px;
        }

        #user_points_used .ulist .type .need {
            color: #7e8da0;
            margin-top: 5px;
        }

    #user_points_used .ulist .redeem {
        line-height: 36px;
        padding: 0 25px;
        border-radius: 5px;
        text-decoration: unset;
        background-color: #f16056;
        color: #fff;
    }
/* 鍏戞崲浼樻儬鍒稿脊绐� */
.fixed_redeem_coupon {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 100001;
    background-color: rgba(0, 0, 0, 0.6);
    display: none;
    align-items: center;
    justify-content: center;
}

    .fixed_redeem_coupon .center {
        background-color: #fff;
        border-radius: 10px;
        padding: 40px 90px;
        text-align: center;
        width: 94%;
        max-width: 750px;
        box-sizing: border-box;
        position: relative;
    }

        .fixed_redeem_coupon .center .style_close {
            width: 50px;
            height: 50px;
            line-height: 50px;
            text-align: center;
            position: absolute;
            top: 8px;
            right: 8px;
        }

            .fixed_redeem_coupon .center .style_close span {
                font-size: 20px;
                color: #666;
            }

        .fixed_redeem_coupon .center .fix_tit {
            line-height: 22px;
            padding-top: 9px;
            padding-bottom: 9px;
            font-size: 20px;
            color: #1f2328;
        }

            .fixed_redeem_coupon .center .fix_tit span {
                font-weight: bold;
            }

        .fixed_redeem_coupon .center .box {
            padding: 30px 20px;
            color: #000;
            margin-top: 19px;
            background-color: #FFE7E7;
            border-radius: 5px;
        }

        .fixed_redeem_coupon .center .discount {
            line-height: 30px;
            padding: 14px 0;
            font-size: 30px;
            font-weight: bold;
            color: #FF3939;
        }

        .fixed_redeem_coupon .center .over {
            line-height: 20px;
            margin-top: 13px;
            font-size: 18px;
        }

        .fixed_redeem_coupon .center .apply {
            display: inline-block;
            padding: 0 70px;
            line-height: 50px;
            background-color: #f8443b;
            border-radius: 5px;
            color: #fff;
            text-decoration: none;
            margin-top: 25px;
            font-size: 20px;
            font-weight: bold;
        }

        .fixed_redeem_coupon .center .default_box {
            display: block;
        }

        .fixed_redeem_coupon .center .get_box {
            display: none;
        }

        .fixed_redeem_coupon .center .get_t {
            font-size: 14px;
            color: #000;
            line-height: 28px;
            margin-top: 20px;
            margin-bottom: 8px;
        }

        .fixed_redeem_coupon .center .copy_coupon {
            line-height: 42px;
            border: 1px solid #ebeded;
            position: relative;
        }

            .fixed_redeem_coupon .center .copy_coupon span {
                position: absolute;
                width: 50px;
                height: 44px;
                top: 0;
                right: 0;
                cursor: pointer;
            }

    .fixed_redeem_coupon .get_box {
        background-color: #fff;
        text-align: center;
        padding: 0 20px;
    }

        .fixed_redeem_coupon .get_box .tit {
            line-height: 36px;
            margin-bottom: 18px;
            font-size: 32px;
            color: #000;
            width: 100%;
        }

        .fixed_redeem_coupon .get_box .sub {
            line-height: 26px;
            font-size: 14px;
            color: #555;
            width: 100%;
        }

        .fixed_redeem_coupon .get_box .code {
            width: 100%;
            line-height: 50px;
            font-size: 40px;
            color: #f06055;
            display: block;
            background: unset;
            border: none;
            text-align: center;
            padding: 0;
            margin: 0;
        }

        .fixed_redeem_coupon .get_box .btn {
            display: block;
            width: max-content;
            padding: 0 25px;
            margin: 24px auto 0;
            line-height: 46px;
            background-color: #000;
            font-size: 16px;
            color: #fff;
            text-decoration: none;
        }

    .fixed_redeem_coupon .center.current .fix_tit {
        display: none;
    }

    .fixed_redeem_coupon .center.current .apply {
        display: none;
    }

    .fixed_redeem_coupon .center.current .default_box {
        display: none;
    }

    .fixed_redeem_coupon .center.current .get_box {
        display: block;
    }

.points_mobile_menu {
    display: none;
}

@media screen and (max-width:750px) {
    .lib_user_main.points .user_container {
        padding-left: 15px;
        padding-right: 15px;
    }

    #user_points_index #user_heading {
        margin: 0 -15px;
        padding: 0;
    }

        #user_points_index #user_heading h2 {
            border-bottom: 1px solid #e3e3e3;
        }

    #user_points_index .points_box {
        margin-top: 15px;
    }

    #user_points_index .flow_item .item {
        padding: 15px;
    }

        #user_points_index .flow_item .item.thead .channel {
            display: none;
        }

        #user_points_index .flow_item .item.tbody .info {
            flex-wrap: wrap;
        }

        #user_points_index .flow_item .item.tbody .i_amount {
            width: 100%;
        }

        #user_points_index .flow_item .item .channel {
            padding-right: 10px;
        }

    #user_points_used .ulist {
        flex-wrap: wrap;
    }

        #user_points_used .ulist .type {
            flex: auto;
            width: 100%;
            margin-bottom: 20px;
        }

    .points_mobile_menu {
        margin-top: 15px;
        display: block;
    }

        .points_mobile_menu ul {
            padding: 0 25px;
        }

            .points_mobile_menu ul li {
                margin-right: 25px;
                display: inline-block;
                vertical-align: middle;
                line-height: 30px;
            }

                .points_mobile_menu ul li:last-child {
                    margin-right: 0;
                }

                .points_mobile_menu ul li a {
                    font-size: 14px;
                    color: #888;
                    font-weight: bold;
                }

    .fixed_redeem_coupon .center {
        padding: 30px;
    }

        .fixed_redeem_coupon .center .style_close {
            top: 0;
            right: 0;
        }

        .fixed_redeem_coupon .center .fix_tit {
            font-size: 14px;
        }

        .fixed_redeem_coupon .center .box {
            padding: 15px;
            margin-top: 10px;
        }

        .fixed_redeem_coupon .center .discount {
            font-size: 22px;
            padding: 4px 0 8px;
        }

        .fixed_redeem_coupon .center .over {
            margin-top: 0;
            font-size: 14px;
        }

        .fixed_redeem_coupon .center .apply {
            padding: 0 40px;
            line-height: 40px;
            margin-top: 20px;
            font-size: 14px;
        }
}
/*************************************** 浼氬憳绉垎end ***************************************/

#customer .common_box {
    padding: 20px;
    margin: 25px 0 20px;
    background: #fed925;
    border-radius: 6px;
}

#customer .retevis_box .list_box {
    display: flex;
    align-items: flex-start;
    justify-content: space-around;
    flex-wrap: wrap;
}

    #customer .retevis_box .list_box .list {
        text-align: center;
        margin: 0 10px;
    }

    #customer .retevis_box .list_box .icon {
        height: 30px;
    }

        #customer .retevis_box .list_box .icon img {
            display: inline-block;
            vertical-align: middle;
            max-width: 100%;
            max-height: 100%;
        }

        #customer .retevis_box .list_box .icon span {
            display: inline-block;
            vertical-align: middle;
            height: 100%;
        }

    #customer .retevis_box .list_box .name {
        margin: 12px 0;
        font-size: 20px;
        color: #333333;
        font-family: 'Montserrat-Bold';
    }

    #customer .retevis_box .list_box .brief {
        font-size: 14px;
        color: #333333;
    }

#customer .line_box {
    position: relative;
    margin: 20px 0 15px;
}

    #customer .line_box span {
        position: relative;
        display: inline-block;
        font-size: 16px;
        color: #999999;
        padding: 3px 15px;
        text-align: center;
        background: #ffffff;
        z-index: 2;
    }

    #customer .line_box:before {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 1px;
        background: #e3e3e3;
        content: '';
        z-index: 1;
    }

#login .login_box .row label {
    text-align: center;
}

#login .login_box .signin.form_button_bg {
    background-color: #fed925;
    border-color: #fed925;
    color: #333;
    font-size: 16px;
}

#signup {
    width: 100%;
    padding-right: 0;
    border: none;
}

    #signup .top_tab .title {
        text-align: center;
        font-size: 34px;
        font-weight: normal;
    }

    #signup .register .row label {
        text-align: center;
    }

    #signup .content {
        padding-top: 0;
    }

@media(max-width: 768px) {
    #lib_user {
        padding: 20px 4%;
    }

    #signup .top_tab .title {
        font-size: 20px;
    }

    #signup .register .intro dt {
        font-size: 14px;
    }

    #customer .common_box {
        margin: 15px 0;
        padding: 15px 10px;
    }

    #customer .retevis_box .list_box .list {
        margin: 10px;
    }

    #customer .retevis_box .list_box .icon {
        height: 25px;
    }

    #customer .retevis_box .list_box .name {
        font-size: 16px;
        margin: 6px 0;
    }

    #customer .retevis_box .list_box .brief {
        font-size: 12px;
    }

    #login .login_box .input_box .input_box_txt {
        padding: 8px 7px;
    }

    #login .login_box .forget, #login .login_box .protect {
        margin: 8px 0;
    }

    #login .login_box .row {
        margin: 8px 0;
    }

    #customer .info {
        padding: 15px 0;
    }

        #customer .info .return {
            padding-top: 0;
        }

    #signup .register .input_box .input_box_txt {
        padding: 6px 8px;
    }

    #signup .register .btn_signup {
        height: 35px;
        line-height: 35px;
    }

    #customer .info .sign_btn a {
        height: 35px;
        line-height: 35px;
    }

    #user_heading {
        padding: 0;
        margin-bottom: 10px;
    }

        #user_heading h2 {
            height: 45px;
            line-height: 45px;
        }

    #lib_user_favorite {
        padding: 0;
    }
}

@media(max-width: 600px) {
    #customer .retevis_box .list_box .list {
        margin: 10px 5px;
    }

    #customer .retevis_box .list_box .name {
        font-size: 14px;
    }
}
