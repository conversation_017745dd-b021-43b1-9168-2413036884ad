/**
 * 验证码API服务
 * 封装所有与验证码相关的API调用
 */
import { post } from './request.js';


/**
 * 验证博客评论验证码
 * @param {string} captchaVerifyParam - 验证码验证参数
 * @returns {Promise<object>} 验证结果，包含success、message等字段
 */
export function verifyBlogCommentCaptcha(captchaVerifyParam) {
  console.log('发送博客评论验证码验证请求，参数:', captchaVerifyParam);
  
  return post('/api/blog/comment/verify-captcha', { 
    captchaVerifyParam: captchaVerifyParam 
  }).catch(error => {
    console.error('博客评论验证码验证请求失败:', error);
    throw error;
  });
}
/**
 * 验证产品评论验证码
 * @param {string} captchaVerifyParam 验证码验证参数
 * @returns {Promise<Object>} 验证结果
 */
export async function verifyProductReviewCaptcha(captchaVerifyParam) {
  try {
    const response = await fetch('/api/product/review/verify-captcha', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ captchaVerifyParam }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('验证码验证失败:', errorText);
      return {
        success: false,
        message: '验证码验证失败，请重试'
      };
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('验证码验证出错:', error);
    return {
      success: false,
      message: '验证码验证出错: ' + error.message
    };
  }
}

// 默认导出所有API函数
export default {
  verifyBlogCommentCaptcha,
  verifyProductReviewCaptcha
}; 