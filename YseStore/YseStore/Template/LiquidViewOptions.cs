using Microsoft.Extensions.FileProviders;

namespace YseStore.Template
{
    public class LiquidViewOptions
    {
        /// <summary>
        /// Gets the sequence of <see cref="IFileProvider" /> instances used by <see cref="LiquidViewTemplate"/> to
        /// locate Liquid files.
        /// </summary>
        /// <remarks>
        /// At startup, this is initialized to include an instance of <see cref="PhysicalFileProvider"/> that is
        /// rooted at the application root.
        /// </remarks>
        public List<IFileProvider> FileProviders { get; } = [];
        public List<Action<LiquidViewParser>> LiquidViewParserConfiguration { get; } = [];
    }
}
