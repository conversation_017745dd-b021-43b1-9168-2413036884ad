using Aop.Api.Domain;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Fluid;
using Fluid.MvcViewEngine;
using I18Next.Net.AspNetCore;
using I18Next.Net.Backends;
using I18Next.Net.Extensions;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.ResponseCompression;
using Serilog;
using System.IO.Compression;
using YseStore;
using YseStore.Common;
using YseStore.Ext;
using YseStore.Extensions;
using YseStore.Extensions.ServiceExtensions;
using YseStore.Filter;
using YseStore.Middle;
using YseStore.Template;

var builder = WebApplication.CreateBuilder(args);

// 配置 Serilog - 从配置文件读取设置
builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));

//跨域
// 配置host与容器
builder.Host
    .UseServiceProviderFactory(new AutofacServiceProviderFactory())
    .ConfigureContainer<ContainerBuilder>(builder =>
    {
        builder.RegisterModule(new AutofacModuleRegister());
        builder.RegisterModule<AutofacPropertityModuleReg>();

        // 注册内存缓存作为ICaching的实现
        //builder.Register(c => {
        //    var memoryCache = c.Resolve<IMemoryCache>();
        //    return new MemoryCaching(memoryCache);
        //}).As<ICaching>().SingleInstance();
    })
    .ConfigureAppConfiguration((hostingContext, config) =>
    {
        hostingContext.Configuration.ConfigureApplication();
        config.Sources.Clear();
        config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: false);
    });
builder.ConfigureApplication();

// 2、配置服务
builder.Services.AddSingleton(new AppSettings(builder.Configuration));
builder.Services.AddAllOptionRegister();
builder.Services.AddHttpContextAccessor();
// 注册IMemoryCache
//builder.Services.AddMemoryCache();
builder.Services.AddCacheSetup();
builder.Services.AddSqlsugarSetup();
builder.Services.AddDetection();
// 注册应用服务
builder.Services.AddApplicationServices();
var services = builder.Services;
services.AddControllers();
services.AddEndpointsApiExplorer();
services.AddResponseCompression(options => options.EnableForHttps = true);
services.Configure<BrotliCompressionProviderOptions>(options => options.Level = CompressionLevel.Fastest);
services.Configure<GzipCompressionProviderOptions>(options => options.Level = CompressionLevel.SmallestSize);
// 添加Swagger服务
builder.Services.AddSwaggerServices("YseStore API", "v1", "YseStore API接口文档");

// 指定语言资源文件位于 "wwwroot/locales" 目录 设置默认语言为英语 ("en")
services.AddI18NextLocalization(i18N =>
    i18N.IntegrateToAspNetCore().AddBackend(new JsonFileBackend("Lang/locales")).UseDefaultLanguage("en"));



services.AddSingleton<LiquidViewParser>();
//services.AddScoped<CustomFluidViewParser>();

services.Configure<FluidMvcViewOptions>(options =>
{
    var httpContextAccessor = services.BuildServiceProvider().GetRequiredService<IHttpContextAccessor>();
    //var httpContext = httpContextAccessor.HttpContext;

    //var servicePrivoder = httpContext.RequestServices.GetService<IServiceProvider>();

   
    options.Parser =
        new CustomFluidViewParser(new FluidParserOptions() { AllowFunctions = true, AllowParentheses = true },
            httpContextAccessor);
    options.TemplateOptions.MemberAccessStrategy = UnsafeMemberAccessStrategy.Instance;

    // 注册自定义过滤器
    FluidRenderingViewAsync.RegisterFilters(options.Parser, options.TemplateOptions);
    options.RenderingViewAsync = FluidRenderingViewAsync.AddItemsToFluidContext;
});

services.AddResponseCaching();
services.AddControllersWithViews(options =>
{
    // 添加全局可视化页面数据过滤器
    options.Filters.Add<VisualPageDataFilter>();
}).AddFluid();
services.AddResponseCompression(options => options.EnableForHttps = true);
services.Configure<BrotliCompressionProviderOptions>(options => options.Level = CompressionLevel.SmallestSize);
services.Configure<GzipCompressionProviderOptions>(options => options.Level = CompressionLevel.SmallestSize);
services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.Cookie.Name = "YseStoreAuth";
        options.LoginPath = "/Account/Signin";
        options.AccessDeniedPath = "/Error/AccessDenied";
    });
services.AddScoped<PageRouteTransformer>();


//services.AddSingleton<CustomRoute>();
services.AddSingleton<CustomRouteDataBase>();

services.AddSession(options =>
{
    options.Cookie.Name = "YseStorSession"; // 设置Cookie名称
    options.IdleTimeout = TimeSpan.FromMinutes(30); // Session超时时间
    options.Cookie.HttpOnly = true; // 设置为HttpOnly以提高安全性
    options.Cookie.IsEssential = true; // 标记为必要Cookie以符合GDPR要求
});
var app = builder.Build();

app.UseSession();
//加tooken
app.UseMiddleware<RequestMiddleware>();
// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
}
else
{
    // 在开发环境显示更详细的错误
    app.UseDeveloperExceptionPage();

    // 在开发环境中启用Swagger
    app.UseSwaggerMiddleware("YseStore API", "v1");
}

//对应错误链接的/error/{0}
app.UseStatusCodePagesWithReExecute("/error/{0}");

// 初始化数据库表

// app.InitializeDatabase();
app.UseResponseCompression();
var cacheMaxAgeOneWeek = (60 * 60 * 24 * 7).ToString();
app.UseStaticFiles(new StaticFileOptions
{
    OnPrepareResponse = ctx =>
    {
        ctx.Context.Response.Headers.Append(
             "Cache-Control", $"public, max-age={cacheMaxAgeOneWeek}");
    }
});




app.UseRouting();
//301重定向
app.UseMiddleware<RedirectMiddleware>();
//404链接处理
app.UseCustomErrorPages();

//app.UseResponseCaching();
//app.UseResponseCompression();
app.UseAuthentication();
app.UseAuthorization();
app.UseMultiLanguageSupport(app.Services, new string[] { "en", "es", "fr", "de", "ru", "it", "jp", "pt", "cn" });
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.MapDynamicControllerRoute<PageRouteTransformer>("{**slug}");


try
{
    Log.Information("Starting web application");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}