
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.12.35707.178
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YseStore", "YseStore\YseStore.csproj", "{3BA60698-37F9-415D-A0D0-DEF4C9BF8752}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RefDll", "RefDll\RefDll.csproj", "{B458C7FA-E41D-4E1A-A69D-E68E3DAE7505}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "解决方案项", "解决方案项", "{80342779-0B0C-4F6F-AD29-824BC606C2E9}"
	ProjectSection(SolutionItems) = preProject
		Nuget.config = Nuget.config
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YseStore.Model", "YseStore.Model\YseStore.Model.csproj", "{729F1FAC-A5DE-4751-898C-D773CAA85FB6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YseStore.IService", "YseStore.IService\YseStore.IService.csproj", "{826A9AA1-5997-442E-A579-07BEEA48EAB0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YseStore.Service", "YseStore.Service\YseStore.Service.csproj", "{BBB13426-FBE9-4AA7-B278-4532BB180973}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YseStore.IRepo", "YseStore.IRepo\YseStore.IRepo.csproj", "{D8ACD1B5-A387-42D8-A37B-F12B9B693DC7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YseStore.Repo", "YseStore.Repo\YseStore.Repo.csproj", "{1355E4AA-C51B-471C-8703-FC1EFE1A4F0E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YseStore.Extensions", "YseStore.Extensions\YseStore.Extensions.csproj", "{6AEDA805-F1B4-4B56-9B56-B829442B8E92}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YseStore.Common", "YseStore.Common\YseStore.Common.csproj", "{738AFF5B-3A55-4406-B260-05A83AEE6299}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3BA60698-37F9-415D-A0D0-DEF4C9BF8752}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3BA60698-37F9-415D-A0D0-DEF4C9BF8752}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3BA60698-37F9-415D-A0D0-DEF4C9BF8752}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3BA60698-37F9-415D-A0D0-DEF4C9BF8752}.Release|Any CPU.Build.0 = Release|Any CPU
		{B458C7FA-E41D-4E1A-A69D-E68E3DAE7505}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B458C7FA-E41D-4E1A-A69D-E68E3DAE7505}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B458C7FA-E41D-4E1A-A69D-E68E3DAE7505}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B458C7FA-E41D-4E1A-A69D-E68E3DAE7505}.Release|Any CPU.Build.0 = Release|Any CPU
		{729F1FAC-A5DE-4751-898C-D773CAA85FB6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{729F1FAC-A5DE-4751-898C-D773CAA85FB6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{729F1FAC-A5DE-4751-898C-D773CAA85FB6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{729F1FAC-A5DE-4751-898C-D773CAA85FB6}.Release|Any CPU.Build.0 = Release|Any CPU
		{826A9AA1-5997-442E-A579-07BEEA48EAB0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{826A9AA1-5997-442E-A579-07BEEA48EAB0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{826A9AA1-5997-442E-A579-07BEEA48EAB0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{826A9AA1-5997-442E-A579-07BEEA48EAB0}.Release|Any CPU.Build.0 = Release|Any CPU
		{BBB13426-FBE9-4AA7-B278-4532BB180973}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BBB13426-FBE9-4AA7-B278-4532BB180973}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BBB13426-FBE9-4AA7-B278-4532BB180973}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BBB13426-FBE9-4AA7-B278-4532BB180973}.Release|Any CPU.Build.0 = Release|Any CPU
		{D8ACD1B5-A387-42D8-A37B-F12B9B693DC7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D8ACD1B5-A387-42D8-A37B-F12B9B693DC7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D8ACD1B5-A387-42D8-A37B-F12B9B693DC7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D8ACD1B5-A387-42D8-A37B-F12B9B693DC7}.Release|Any CPU.Build.0 = Release|Any CPU
		{1355E4AA-C51B-471C-8703-FC1EFE1A4F0E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1355E4AA-C51B-471C-8703-FC1EFE1A4F0E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1355E4AA-C51B-471C-8703-FC1EFE1A4F0E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1355E4AA-C51B-471C-8703-FC1EFE1A4F0E}.Release|Any CPU.Build.0 = Release|Any CPU
		{6AEDA805-F1B4-4B56-9B56-B829442B8E92}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6AEDA805-F1B4-4B56-9B56-B829442B8E92}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6AEDA805-F1B4-4B56-9B56-B829442B8E92}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6AEDA805-F1B4-4B56-9B56-B829442B8E92}.Release|Any CPU.Build.0 = Release|Any CPU
		{738AFF5B-3A55-4406-B260-05A83AEE6299}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{738AFF5B-3A55-4406-B260-05A83AEE6299}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{738AFF5B-3A55-4406-B260-05A83AEE6299}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{738AFF5B-3A55-4406-B260-05A83AEE6299}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 10
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://39.100.112.92:8080/tfs/yseproj
		SccLocalPath0 = .
		SccProjectUniqueName1 = RefDll\\RefDll.csproj
		SccProjectName1 = RefDll
		SccLocalPath1 = RefDll
		SccProjectUniqueName2 = YseStore.Common\\YseStore.Common.csproj
		SccProjectName2 = YseStore.Common
		SccLocalPath2 = YseStore.Common
		SccProjectUniqueName3 = YseStore.Extensions\\YseStore.Extensions.csproj
		SccProjectName3 = YseStore.Extensions
		SccLocalPath3 = YseStore.Extensions
		SccProjectUniqueName4 = YseStore.IRepo\\YseStore.IRepo.csproj
		SccProjectName4 = YseStore.IRepo
		SccLocalPath4 = YseStore.IRepo
		SccProjectUniqueName5 = YseStore.IService\\YseStore.IService.csproj
		SccProjectName5 = YseStore.IService
		SccLocalPath5 = YseStore.IService
		SccProjectUniqueName6 = YseStore.Model\\YseStore.Model.csproj
		SccProjectName6 = YseStore.Model
		SccLocalPath6 = YseStore.Model
		SccProjectUniqueName7 = YseStore.Repo\\YseStore.Repo.csproj
		SccProjectName7 = YseStore.Repo
		SccLocalPath7 = YseStore.Repo
		SccProjectUniqueName8 = YseStore.Service\\YseStore.Service.csproj
		SccProjectName8 = YseStore.Service
		SccLocalPath8 = YseStore.Service
		SccProjectUniqueName9 = YseStore\\YseStore.csproj
		SccProjectName9 = YseStore
		SccLocalPath9 = YseStore
	EndGlobalSection
EndGlobal
