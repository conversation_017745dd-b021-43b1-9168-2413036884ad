using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.Enums;

namespace YseStore.Model.VM.Sales
{
    public class VM_EqueuFlashSalesProduct
    {
        public UseProductsEnum? UseProductsType { get; set; }

        public Decimal? ConditionPrice { get; set; }

        public List<string>? CryptUseProducts { get; set; }


        public System.Int32? StartTime { get; set; }
    }

    // 产品促销信息结构
    public class PromotionInfo
    {
        public decimal Price { get; set; }
        public int StartTime { get; set; }
        public int EndTime { get; set; }
    }

    public class PromotionUpdateInfo
    {
        public decimal PromotionPrice { get; set; }
        public int StartTime { get; set; }
        public int EndTime { get; set; }

        public int ProId { get; set; }
    }

    // 限时促销活动模型
    public class FlashSaleModel
    {
        public int UseProductsType { get; set; }
        public string UseProductsData { get; set; }
        public string UseCombinationScope { get; set; }
        public string UseProductsCombination { get; set; }
        public int OfferType { get; set; }
        public decimal ConditionPrice { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string UseProducts { get; set; }
    }

    // 规格组合模型
    public class AttributeCombination
    {
        public decimal Price { get; set; }
        public string VariantsId { get; set; }
        public int CId { get; set; }
    }
}
