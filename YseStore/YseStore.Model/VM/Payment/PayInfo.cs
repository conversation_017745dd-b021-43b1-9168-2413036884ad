using Entitys;
using MessagePack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model.VM.Payment
{
    /// <summary>
    /// 支付参数信息
    /// </summary>
    [MessagePackObject(true)]
    public partial class PayInfo
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string OrderId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string payPalRequestId { get; set; }

        /// <summary>
        /// 交易币种
        /// </summary>
        public string OrderCurrency { get; set; }
        /// <summary>
        /// 订单总金额
        /// </summary>
        public string OrderAmount { get; set; }

        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }

        public string UserId { get; set; }

        public string UserName { get; set; }

        public string Phone { get; set; }
        /// <summary>
        /// 可选项：PayPal/CreditCard
        /// </summary>
        public string PaymentMethod { get; set; }
        /// <summary>
        /// 可选填
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 回调地址
        /// </summary>
        public string ReturnUrl { get; set; }


        public string PushUrl { get; set; }

        public string ShipCountry { get; set; }

        public string ShipState { get; set; }
        public string ShipCity { get; set; }
        public string ShipAddress { get; set; }
        public string ShipPost { get; set; }


        public OrderInfo orderItem { get; set; } = new OrderInfo();


        public string SettlementAmount { get; set; }

        public string SettlementCurrency { get; set; }


        //public string ShipFee { get; set; }



    }

    [MessagePackObject(true)]
    public class OrderInfo
    {
        public decimal ShipFee { get; set; }

        public decimal ShipFeeDiscount { get; set; }

        public decimal ProductAmount { get; set; }


        public decimal OrderAmount { get; set; }



        /// <summary>
        ///折扣金额
        /// </summary>
        public decimal DiscountAmount { get; set; }

        public List<OrderProduct> productList { get; set; } = new List<OrderProduct>();
    }



    /// <summary>
    /// 
    /// </summary>
    [MessagePackObject(true)]
    public class OrderProduct : orders_products_list
    {

        public decimal SettlementPrice { get; set; }

    }

}
