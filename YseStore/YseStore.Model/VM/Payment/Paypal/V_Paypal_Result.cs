using MessagePack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.VM.Payment;


namespace YseStore.Model.VM.Payment.Paypal
{

    public class Payer_info
    {
        /// <summary>
        /// 
        /// </summary>
        public string email { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string payer_id { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string last_name { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string first_name { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string country_code { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Shipping_address shipping_address { get; set; }
    }

    public class Paypal_Payer
    {
        /// <summary>
        /// 
        /// </summary>
        public string status { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Payer_info payer_info { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string payment_method { get; set; }
    }

    [MessagePackObject(true)]
    public class Paypal_Payee
    {
        /// <summary>
        /// 
        /// </summary>
        public string email { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string merchant_id { get; set; }
    }


    [MessagePackObject(true)]
    public class Details
    {
        /// <summary>
        /// 
        /// </summary>
        public string tax { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string shipping { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string subtotal { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string insurance { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string handling_fee { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string shipping_discount { get; set; }
    }



    [MessagePackObject(true)]
    public class ItemsItem
    {
        /// <summary>
        /// 
        /// </summary>
        public string sku { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string tax { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string price { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string currency { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string quantity { get; set; }
    }


    [MessagePackObject(true)]
    public class Shipping_address
    {
        /// <summary>
        /// 
        /// </summary>
        public string city { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string line1 { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string state { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string postal_code { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string country_code { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string recipient_name { get; set; }
    }


    [MessagePackObject(true)]
    public class Item_list
    {
        /// <summary>
        /// 
        /// </summary>
        public List<ItemsItem> items { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Shipping_address shipping_address { get; set; }
    }


    [MessagePackObject(true)]
    public class Paypal_Amount
    {
        /// <summary>
        /// 
        /// </summary>
        public string total { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Details details { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string currency { get; set; }
    }


    [MessagePackObject(true)]
    public class Transaction_fee
    {
        /// <summary>
        /// 
        /// </summary>
        public string value { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string currency { get; set; }
    }


    [MessagePackObject(true)]
    public class Sale
    {
        /// <summary>
        /// 
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<LinksItem> links { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string state { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Paypal_Amount amount { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string create_time { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string update_time { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string payment_mode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string parent_payment { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Transaction_fee transaction_fee { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string protection_eligibility { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string protection_eligibility_type { get; set; }
    }


    [MessagePackObject(true)]
    public class Related_resourcesItem
    {
        /// <summary>
        /// 
        /// </summary>
        public Sale sale { get; set; }
    }

    [MessagePackObject(true)]
    public class TransactionsItem
    {
        /// <summary>
        /// 
        /// </summary>
        public Paypal_Payee payee { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Paypal_Amount amount { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string custom { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Item_list item_list { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string description { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string invoice_number { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<Related_resourcesItem> related_resources { get; set; }
    }

    public class Paypal_result_details
    {
        /// <summary>
        /// 
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string cart { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<LinksItem> links { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Paypal_Payer payer { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string state { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string intent { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string create_time { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string update_time { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<TransactionsItem> transactions { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<string> failed_transactions { get; set; }
    }


    [MessagePackObject(true)]
    public class V_Paypal_Result
    {
        /// <summary>
        /// 
        /// </summary>
        public string Fee { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string OrderNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string TradeNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string RiskInfo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ErrorInfo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string OrderInfo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string PayMethod { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string OrderTotal { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string FeeCurrency { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public PayPalResult PayPalResult { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string OrderCurrency { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool PaymentStatus { get; set; }
    }

}