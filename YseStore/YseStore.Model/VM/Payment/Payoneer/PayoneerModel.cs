using MessagePack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace YseStore.Model.VM.Payment.Payoneer
{
    /// <summary>
    /// payoneer 请求数据类
    /// </summary>
    [MessagePackObject(true)]
    public class PayoneerModel
    {
        public string integration { get; set; }
        public string transactionId { get; set; }
        public string country { get; set; }
        public string channel { get; set; }
        public PayoneerSystem system { get; set; }
        public string division { get; set; }
        public Callback callback { get; set; }
        public Customer customer { get; set; }
        public Clientinfo clientInfo { get; set; }
        public Payment payment { get; set; }
        public List<Product> products { get; set; }
        public List<PayoneerShipping> shipping { get; set; }
        public bool allowDelete { get; set; }
        public Style style { get; set; }
        public Preselection preselection { get; set; }
        public string checkoutConfigurationName { get; set; }
        public Riskdata riskData { get; set; }
        public string operationType { get; set; }
        public bool registerOnce { get; set; }
    }

    /// <summary>
    /// 退款类
    /// </summary>
    [MessagePackObject(true)]
    public class PayoneerRefundModel
    {
        public string transactionId { get; set; }

        public PayoneerSystem system { get; set; }

        public Payment payment { get; set; }

        public List<Product> products { get; set; }
        public List<PayoneerShipping> shipping { get; set; }

        public bool finalOperation { get; set; }
    }

    [MessagePackObject(true)]
    public class PayoneerSystem
    {
        public string id { get; set; }
        public string type { get; set; }
        public string code { get; set; }
        public string version { get; set; }
    }
    [MessagePackObject(true)]
    public class Callback
    {
        public string returnUrl { get; set; }
        public string summaryUrl { get; set; }
        public string cancelUrl { get; set; }
        public string notificationUrl { get; set; }
        public Notificationheader[] notificationHeaders { get; set; }
    }
    [MessagePackObject(true)]

    public class Notificationheader
    {
        public string name { get; set; }
        public string value { get; set; }
    }
    [MessagePackObject(true)]
    public class Customer
    {
        public Accountinfo accountInfo { get; set; }
        public string number { get; set; }
        public string email { get; set; }
        public string deliveryEmail { get; set; }
        public string birthday { get; set; }
        public string gender { get; set; }
        public CustomerName name { get; set; }
        public Company company { get; set; }
        public Addresses addresses { get; set; }
        public Phones phones { get; set; }
        public Registration registration { get; set; }
    }
    [MessagePackObject(true)]
    public class Accountinfo
    {
        public string method { get; set; }
        public DateTime timestamp { get; set; }
        public DateTime creationDate { get; set; }
        public DateTime updateDate { get; set; }
        public DateTime passwordChangeDate { get; set; }
    }
    [MessagePackObject(true)]
    public class CustomerName
    {
        public string title { get; set; }
        public string firstName { get; set; }
        public string middleName { get; set; }
        public string lastName { get; set; }
        public string maidenName { get; set; }
    }
    [MessagePackObject(true)]
    public class Company
    {
        public string name { get; set; }
        public Address address { get; set; }
        public Identities identities { get; set; }
    }
    [MessagePackObject(true)]
    public class Address
    {
        public string street { get; set; }
        public string houseNumber { get; set; }
        public string zip { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string country { get; set; }
        public CustomerName name { get; set; }
        public string companyName { get; set; }
        public bool verified { get; set; }
        public string id { get; set; }
        public bool merchantAddress { get; set; }
        // public DateTime firstTimeUseDate { get; set; }
    }


    [MessagePackObject(true)]
    public class Identities
    {
        public Registrationnumber registrationNumber { get; set; }
        public Taxnumber taxNumber { get; set; }
    }
    [MessagePackObject(true)]
    public class Registrationnumber
    {
        public string date { get; set; }
        public string identifier { get; set; }
    }
    [MessagePackObject(true)]
    public class Taxnumber
    {
        public string date { get; set; }
        public string identifier { get; set; }
    }
    [MessagePackObject(true)]
    public class Addresses
    {
        public Shipping shipping { get; set; }
        public Billing billing { get; set; }
        public bool useBillingAsShippingAddress { get; set; }
    }
    [MessagePackObject(true)]
    public class Shipping
    {
        public string street { get; set; }
        public string houseNumber { get; set; }
        public string zip { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string country { get; set; }
        public CustomerName name { get; set; }
        public string companyName { get; set; }
        public bool verified { get; set; }
        public string id { get; set; }
        public bool merchantAddress { get; set; }
        //public DateTime firstTimeUseDate { get; set; }



    }

    [MessagePackObject(true)]
    public class Billing
    {
        public string street { get; set; }
        public string houseNumber { get; set; }
        public string zip { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string country { get; set; }
        public CustomerName name { get; set; }
        public string companyName { get; set; }
        public bool verified { get; set; }
        public string id { get; set; }
        public bool merchantAddress { get; set; }
        // public DateTime firstTimeUseDate { get; set; }
    }


    [MessagePackObject(true)]
    public class Phones
    {
        public Number home { get; set; }
        public Number work { get; set; }
        public Number mobile { get; set; }
        public Number company { get; set; }
        public Number other { get; set; }
    }
    [MessagePackObject(true)]
    public class Number
    {
        public string unstructuredNumber { get; set; }
    }


    [MessagePackObject(true)]
    public class Registration
    {
        public string id { get; set; }
        public string password { get; set; }
    }
    [MessagePackObject(true)]
    public class Clientinfo
    {
        public string ip { get; set; }
        public string ipv6 { get; set; }
        public string userAgent { get; set; }
        public string acceptHeader { get; set; }
        public string deviceId { get; set; }
        public Header[] headers { get; set; }
        public bool javaEnabled { get; set; }
        public string language { get; set; }
        public int colorDepth { get; set; }
        public string timezone { get; set; }
        public int browserScreenWidth { get; set; }
        public int browserScreenHeight { get; set; }
    }

    [MessagePackObject(true)]
    public class Header
    {
        public string name { get; set; }
        public string value { get; set; }
    }

    [MessagePackObject(true)]
    public class Payment
    {
        public string reference { get; set; }
        public decimal amount { get; set; }

        //public decimal? price { get; set; }  
        public string currency { get; set; }
        public string invoiceId { get; set; }
        public Longreference longReference { get; set; }
        // public DateTime plannedShippingDate { get; set; }
        // public DateTime dueDate { get; set; }
    }

    [MessagePackObject(true)]
    public class Longreference
    {
        public string essential { get; set; }
        public string extended { get; set; }
        public string verbose { get; set; }
        public string city { get; set; }
    }
    [MessagePackObject(true)]
    public class Style
    {
        public string language { get; set; }
        public string cssOverride { get; set; }
        public string resolution { get; set; }
        public string hostedVersion { get; set; }
        public string challengeWindowSize { get; set; }
        public string displayName { get; set; }
        public string primaryColor { get; set; }
        public string logoUrl { get; set; }
        public string backgroundType { get; set; }
        public string backgroundColor { get; set; }
        public string backgroundImageUrl { get; set; }
    }
    [MessagePackObject(true)]
    public class Preselection
    {
        public string deferral { get; set; }
        public string networks { get; set; }
        public string[] networkCodes { get; set; }
        public string challengeIndicator { get; set; }
    }
    [MessagePackObject(true)]
    public class Riskdata
    {
        public RiskdataCustomer customer { get; set; }
        public RiskdataShipping shipping { get; set; }
        public RiskdataPayment payment { get; set; }
        public Gift gift { get; set; }
    }
    [MessagePackObject(true)]
    public class RiskdataCustomer
    {
        public int paymentAttemptsLastDay { get; set; }
        public int paymentAttemptsLastYear { get; set; }
        public int cardRegistrationAttemptsLastDay { get; set; }
        public int purchasesLastSixMonths { get; set; }
        public bool suspiciousActivity { get; set; }
        public Account account { get; set; }
    }
    [MessagePackObject(true)]
    public class Account
    {
        public DateTime registrationDate { get; set; }
        public bool changedDuringCheckout { get; set; }
    }
    [MessagePackObject(true)]
    public class RiskdataShipping
    {
        public string type { get; set; }
        public bool itemsAvailable { get; set; }
        public string itemsAvailableDate { get; set; }
    }
    [MessagePackObject(true)]
    public class RiskdataPayment
    {
        public bool reorderItems { get; set; }
        public Recurring recurring { get; set; }
    }
    [MessagePackObject(true)]
    public class Recurring
    {
        public DateTime expireDate { get; set; }
        public int frequency { get; set; }
    }
    [MessagePackObject(true)]
    public class Gift
    {
        public int amount { get; set; }
        public string currency { get; set; }
        public int cardCount { get; set; }
    }
    [MessagePackObject(true)]
    public class Product
    {
        public string code { get; set; }
        public string name { get; set; }
        public decimal amount { get; set; }
        public decimal netAmount { get; set; }
        public decimal taxAmount { get; set; }
        public int taxRatePercentage { get; set; }
        public string currency { get; set; }
        public int quantity { get; set; }
        //public DateTime plannedShippingDate { get; set; }
        public string productDescriptionUrl { get; set; }
        public string productImageUrl { get; set; }
        public string description { get; set; }
        public string type { get; set; }
    }
    [MessagePackObject(true)]
    public class PayoneerShipping
    {
        public string shippingCompany { get; set; }
        public string method { get; set; }
        public int addressId { get; set; }
        public string trackingNumber { get; set; }
        public string trackingUri { get; set; }
        public string returnShippingCompany { get; set; }
        public string returnTrackingNumber { get; set; }
        public string returnTrackingUri { get; set; }
    }

    /// <summary>
    /// 返回结果对象
    /// </summary>
    [MessagePackObject(true)]
    public class PayoneerResp
    {
        public Links links { get; set; }
        public DateTime timestamp { get; set; }
        public string operation { get; set; }
        public Payment payment { get; set; }
        public List<Product> products { get; set; }
        public Shipping1[] shipping { get; set; }
        public string resultCode { get; set; }
        public string resultInfo { get; set; }
        public Returncode returnCode { get; set; }
        public Status status { get; set; }
        public Interaction interaction { get; set; }
        public Identification identification { get; set; }
        public Pspreference pspReference { get; set; }
        public Account1[] accounts { get; set; }
        public Networks networks { get; set; }
        public Redirect redirect { get; set; }
        public Presetaccount presetAccount { get; set; }
        public string operationType { get; set; }
        public string integrationType { get; set; }
        public bool allowDelete { get; set; }
        public Style style { get; set; }
        public Clientinfo clientInfo { get; set; }
        public Riskprovider[] riskProviders { get; set; }
    }

    [MessagePackObject(true)]
    public class Links
    {
        public string self { get; set; }
        public string customer { get; set; }
        public string lang { get; set; }
    }

    [MessagePackObject(true)]
    public class Returncode
    {
        public string name { get; set; }
        public string source { get; set; }
    }

    [MessagePackObject(true)]
    public class Status
    {
        public string code { get; set; }
        public string reason { get; set; }
    }

    [MessagePackObject(true)]
    public class Interaction
    {
        public string code { get; set; }
        public string reason { get; set; }
        public Customermessage customerMessage { get; set; }
    }

    [MessagePackObject(true)]
    public class Customermessage
    {
        public string title { get; set; }
        public string text { get; set; }
    }



    [MessagePackObject(true)]
    public class Identification
    {
        public string longId { get; set; }
        public string shortId { get; set; }
        public string transactionId { get; set; }
        public string pspId { get; set; }
        public string institutionId { get; set; }
        public string systemId { get; set; }
    }
    [MessagePackObject(true)]
    public class Pspreference
    {
        public string id { get; set; }
        public Datum[] data { get; set; }
    }
    [MessagePackObject(true)]
    public class Datum
    {
        public string name { get; set; }
        public string value { get; set; }
    }
    [MessagePackObject(true)]
    public class Networks
    {
        public Applicable[] applicable { get; set; }
    }
    [MessagePackObject(true)]
    public class Applicable
    {
        public string code { get; set; }
        public string label { get; set; }
        public string method { get; set; }
        public string grouping { get; set; }
        public string registration { get; set; }
        public string recurrence { get; set; }
        public bool redirect { get; set; }
        public string operationType { get; set; }
        public string button { get; set; }
        public bool selected { get; set; }
        public Contractdata contractData { get; set; }
        public Formdata formData { get; set; }
        public int iFrameHeight { get; set; }
        public bool emptyForm { get; set; }
        public Localizedinputelement[] localizedInputElements { get; set; }
        public Inputelement[] inputElements { get; set; }
        public Routing routing { get; set; }
        public Providerresponse providerResponse { get; set; }
        public Links1 links { get; set; }
        public string deferral { get; set; }
    }
    [MessagePackObject(true)]
    public class Contractdata
    {
        public string property1 { get; set; }
        public string property2 { get; set; }
    }
    [MessagePackObject(true)]
    public class Formdata
    {
        public Account2 account { get; set; }
        public Customer customer { get; set; }
        public string dataPrivacyConsentUrl { get; set; }
    }
    [MessagePackObject(true)]
    public class Account2
    {
        public string holderName { get; set; }
    }


    [MessagePackObject(true)]
    public class Routing
    {
        public Route[] routes { get; set; }
    }
    [MessagePackObject(true)]
    public class Route
    {
        public Contract contract { get; set; }
        public Costs costs { get; set; }
        public bool accountTokenized { get; set; }
        public Verificationcodeoptions verificationCodeOptions { get; set; }
    }
    [MessagePackObject(true)]
    public class Contract
    {
        public string id { get; set; }
        public string providerCode { get; set; }
        public string adapterCode { get; set; }
    }
    [MessagePackObject(true)]
    public class Costs
    {
        public float normalized { get; set; }
        public Original original { get; set; }
    }
    [MessagePackObject(true)]
    public class Original
    {
        public float amount { get; set; }
        public string currency { get; set; }
    }
    [MessagePackObject(true)]
    public class Verificationcodeoptions
    {
        public bool requiredForRegistration { get; set; }
        public bool requiredForOneClickPayment { get; set; }
        public bool requiredForFirstPayment { get; set; }
    }
    [MessagePackObject(true)]
    public class Providerresponse
    {
        public string providerCode { get; set; }
        public Parameter[] parameters { get; set; }
    }
    [MessagePackObject(true)]
    public class Parameter
    {
        public string name { get; set; }
        public string value { get; set; }
    }
    [MessagePackObject(true)]
    public class Links1
    {
        public string self { get; set; }
        public string operation { get; set; }
        public string validation { get; set; }
        public string logo { get; set; }
        public string form { get; set; }
        public string localizedForm { get; set; }
        public string preloadForm { get; set; }
        public string lang { get; set; }
        public string iFrame { get; set; }
        public string onselect { get; set; }
    }
    [MessagePackObject(true)]
    public class Localizedinputelement
    {
        public string name { get; set; }
        public string type { get; set; }
        public string label { get; set; }
        public Option[] options { get; set; }
    }
    [MessagePackObject(true)]
    public class Option
    {
        public string value { get; set; }
        public string label { get; set; }
        public bool selected { get; set; }
    }
    [MessagePackObject(true)]
    public class Inputelement
    {
        public string name { get; set; }
        public string type { get; set; }
    }
    [MessagePackObject(true)]
    public class Redirect
    {
        public string url { get; set; }
        public string method { get; set; }
        public Parameter[] parameters { get; set; }
        public bool suppressIFrame { get; set; }
        public bool displayInPopup { get; set; }
        public bool containsAccountPlaceholders { get; set; }
        public string challengeWindowSize { get; set; }
        public string type { get; set; }
    }



    [MessagePackObject(true)]
    public class Presetaccount
    {
        public Links2 links { get; set; }
        public string code { get; set; }
        public Maskedaccount maskedAccount { get; set; }
        public bool emptyForm { get; set; }
        public string button { get; set; }
        public Redirect redirect { get; set; }
        public string operationType { get; set; }
        public string method { get; set; }
        public string label { get; set; }
        public string deferral { get; set; }
        public Contractdata contractData { get; set; }
        public bool registered { get; set; }
    }
    [MessagePackObject(true)]
    public class Links2
    {
        public string operation { get; set; }
        public string logo { get; set; }
        public string form { get; set; }
        public string localizedForm { get; set; }
        public string lang { get; set; }
        public string redirect { get; set; }
    }
    [MessagePackObject(true)]
    public class Maskedaccount
    {
        public Registration registration { get; set; }
        public string displayLabel { get; set; }
        public string holderName { get; set; }
        public string number { get; set; }
        public string bankCode { get; set; }
        public string bankName { get; set; }
        public string bic { get; set; }
        public string branch { get; set; }
        public string city { get; set; }
        public int expiryMonth { get; set; }
        public int expiryYear { get; set; }
        public string iban { get; set; }
        public string login { get; set; }
    }




    [MessagePackObject(true)]
    public class Shipping1
    {
        public string shippingCompany { get; set; }
        public string method { get; set; }
        public int addressId { get; set; }
        public string trackingNumber { get; set; }
        public string trackingUri { get; set; }
        public string returnShippingCompany { get; set; }
        public string returnTrackingNumber { get; set; }
        public string returnTrackingUri { get; set; }
    }
    [MessagePackObject(true)]
    public class Account1
    {
        public Links3 links { get; set; }
        public string id { get; set; }
        public string code { get; set; }
        public string label { get; set; }
        public string method { get; set; }
        public string button { get; set; }
        public string operationType { get; set; }
        public Maskedaccount maskedAccount { get; set; }
        public bool registration { get; set; }
        public bool recurrence { get; set; }
        public DateTime lastSuccessfulChargeAt { get; set; }
        public bool selected { get; set; }
        public int iFrameHeight { get; set; }
        public DateTime preferredAt { get; set; }
        public DateTime createdAt { get; set; }
        public bool emptyForm { get; set; }
        public Localizedinputelement1[] localizedInputElements { get; set; }
        public Inputelement[] inputElements { get; set; }
        public Routing1 routing { get; set; }
        public string state { get; set; }
        public string stateReason { get; set; }
        public DateTime stateModificationDate { get; set; }
        public Contractdata contractData { get; set; }
        public Providerresponse providerResponse { get; set; }
        public string deferral { get; set; }
    }
    [MessagePackObject(true)]
    public class Links3
    {
        public string self { get; set; }
        public string operation { get; set; }
        public string validation { get; set; }
        public string logo { get; set; }
        public string form { get; set; }
        public string localizedForm { get; set; }
        public string preloadForm { get; set; }
        public string lang { get; set; }
        public string iFrame { get; set; }
        public string onselect { get; set; }
    }



    [MessagePackObject(true)]
    public class Routing1
    {
        public Route[] routes { get; set; }
    }


    [MessagePackObject(true)]
    public class Original1
    {
        public float amount { get; set; }
        public string currency { get; set; }
    }


    [MessagePackObject(true)]
    public class Localizedinputelement1
    {
        public string name { get; set; }
        public string type { get; set; }
        public string label { get; set; }
        public Option[] options { get; set; }
    }
    [MessagePackObject(true)]
    public class Riskprovider
    {
        public string providerCode { get; set; }
        public Parameter[] parameters { get; set; }
        public string providerType { get; set; }
        public Links4 links { get; set; }
    }
    [MessagePackObject(true)]
    public class Links4
    {
        public string iframe { get; set; }
    }


    /// <summary>
    /// payoneer 支付回调参数
    /// </summary>
    [MessagePackObject(true)]
    public class PayoneerCallBack
    {
        public string listUrl { get; set; }

        public string shortId { get; set; }
        public string interactionReason { get; set; }
        public string resultCode { get; set; }
        public string longId { get; set; }
        public string transactionId { get; set; }
        public string interactionCode { get; set; }
        public string amount { get; set; }
        public string currency { get; set; }

        public string reference { get; set; }

        public string PaymentMethod { get; set; }



        public string OrderInfo { get; set; }
    }



}