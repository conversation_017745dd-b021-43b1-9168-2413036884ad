using System;
using System.ComponentModel.DataAnnotations;

namespace YseStore.Model.RequestModels.Products
{
    /// <summary>
    /// 产品切换卡内容编辑请求模型
    /// </summary>
    public class ProductSwitchContentEditRequest
    {
        /// <summary>
        /// 内容ID，编辑时必填，添加时为0
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 内容名称
        /// </summary>
        [Required(ErrorMessage = "名称不能为空")]
        public string Name { get; set; }

        /// <summary>
        /// 隶属切换卡ID
        /// </summary>
        [Required(ErrorMessage = "请选择隶属切换卡")]
        public int SwitchID { get; set; }

        /// <summary>
        /// 应用值（如产品ID或分类ID）
        /// </summary>
        public string ApplyValue { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 是否使用移动端描述
        /// </summary>
        public bool UsedMobile { get; set; }

        /// <summary>
        /// 移动端描述
        /// </summary>
        public string MobileDescription { get; set; }
    }
} 