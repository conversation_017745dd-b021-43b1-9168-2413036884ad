using YseStore.Model;

namespace YseStore.Model.RequestModels.Products
{
    /// <summary>
    /// 产品分类查询请求参数
    /// </summary>
    public class ProductCategoryQueryRequest : PaginationModel
    {
        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string Keyword { get; set; } = "";

        /// <summary>
        /// 父分类ID
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 是否包含下架分类
        /// </summary>
        public bool IncludeSoldOut { get; set; } = false;
        
        /// <summary>
        /// 分类级别(0:全部 1:一级 2:二级 3:三级)
        /// </summary>
        public int? Level { get; set; }
    }
} 