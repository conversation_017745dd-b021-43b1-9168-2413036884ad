using System;

namespace YseStore.Model.RequestModels.Blog
{
    /// <summary>
    /// 回复请求模型
    /// </summary>
    public class ReplyRequest
    {
        /// <summary>
        /// 回复者姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 回复内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 是否为管理员回复
        /// </summary>
        public bool IsAdmin { get; set; } = false;
        
        /// <summary>
        /// 回复目标ID（如果回复的是另一条回复，则为该回复的ID；如果直接回复评论，则为0）
        /// </summary>
        public int? ReplyToId { get; set; }
        
        /// <summary>
        /// 回复目标用户名（被回复者的名字）
        /// </summary>
        public string ReplyToName { get; set; }
        
        /// <summary>
        /// 验证码验证参数
        /// </summary>
        public string CaptchaVerifyParam { get; set; }
    }
} 