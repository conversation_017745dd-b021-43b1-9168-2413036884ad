namespace YseStore.Model.RequestModels.Blog
{
    /// <summary>
    /// 博客评论查询请求参数
    /// </summary>
    public class BlogReviewQueryRequest : PaginationModel
    {
        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string Keyword { get; set; } = "";
        
        /// <summary>
        /// 博客文章ID
        /// </summary>
        public uint? BlogId { get; set; }
        
        /// <summary>
        /// 是否已回复
        /// </summary>
        public bool? IsReplied { get; set; }
        
        /// <summary>
        /// 排序字段，默认为AccTime
        /// </summary>
        public string OrderByField
        {
            get => OrderByFileds ?? "AccTime";
            set => OrderByFileds = value;
        }
    }
} 