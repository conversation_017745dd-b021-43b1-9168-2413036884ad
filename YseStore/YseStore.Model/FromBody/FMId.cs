using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model.FromBody
{

    public class FMLongId
    {
        /// <summary>
        ///     序列
        /// </summary>
        [Display(Name = "序列")]
        [Required(ErrorMessage = "请输入要提交的序列参数")]
        public long id { get; set; }

        public object data { get; set; } = null;


        public int userId { get; set; } = 0;

        public string userUUId { get; set; } = "";

        /// <summary>
        /// 币种
        /// </summary>
        public string currency { get; set; } = "USD";

        /// <summary>
        /// 
        /// </summary>
        public string countrySite { get; set; } = "US";
    }

    public class FMIntId
    {
        /// <summary>
        ///     序列
        /// </summary>
        [Display(Name = "序列")]
        [Required(ErrorMessage = "请输入要提交的序列参数")]
        public int id { get; set; }

        public string userUUId { get; set; } = "";


        public object data { get; set; } = null;

        public int userId { get; set; } = 0;



        /// <summary>
        /// 币种
        /// </summary>
        public string currency { get; set; } = "USD";

        /// <summary>
        /// 
        /// </summary>
        public string countrySite { get; set; } = "US";


        public string ip { get; set; }

        public short sourceType { get; set; } = 0;

    }

    public class FMIntIdByListIntData
    {
        public int id { get; set; }
        public List<int> data { get; set; } = null;

        public int userId { get; set; } = 0;
    }


    public class FMArrayIntIds
    {
        public int[] id { get; set; }
        public object data { get; set; } = null;

        public int userId { get; set; } = 0;
    }

    public class FMArrayLongIds
    {
        public long[] id { get; set; }
        public object data { get; set; } = null;

        public int userId { get; set; } = 0;
    }

    public class FMStringId
    {
        public string id { get; set; }
        public object data { get; set; } = null;

        public int userId { get; set; } = 0;

        /// <summary>
        /// 
        /// </summary>
        public string countrySite { get; set; } = "US";

        public string ip { get; set; }


        public string userUUId { get; set; }
    }

    public class FMArrayStringIds
    {
        public string[] id { get; set; }
        public object data { get; set; } = null;

        public int userId { get; set; } = 0;
    }


    public class FMGuidId
    {
        public Guid id { get; set; }
        public object data { get; set; } = null;

        public int userId { get; set; } = 0;
    }


    public class FMArrayGuidIds
    {
        public Guid[] id { get; set; }
        public object data { get; set; } = null;

        public int userId { get; set; } = 0;
    }

    //
    public class FMObj<T> where T : class
    {

        public T data { get; set; }


    }
}
