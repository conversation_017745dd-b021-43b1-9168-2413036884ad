using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model;

public class OrdersSuggestedRefundResponse
{
    public decimal ProductPrice { get; set; }
    public decimal DiscountPrice { get; set; }
    public decimal CouponPrice { get; set; }
    public decimal PointsPrice { get; set; }
    public decimal ShippingPrice { get; set; }
    public decimal FeePrice { get; set; }
    public decimal TaxPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public string Currency { get; set; }
    public decimal RefundTotalPrice { get; set; }
    public decimal Tax { get; set; }
    public decimal itemCount { get; set; }

}

public class OrderModelRefundRequest
{
    public int orderId { get; set; }
    public decimal shippingAmount { get; set; }
    public List<LineItem> lineItems { get; set; }
    public decimal totalAmount { get; set; }
    public string Reason { get; set; }
    public string refundType { get; set; }
}

public class LineItem
{
    public int itemId { get; set; }
    public int quantity { get; set; }
    public string status { get; set; }
    public int packageId { get; set; }
    public bool type { get; set; }
}


