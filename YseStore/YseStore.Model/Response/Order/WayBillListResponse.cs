using Entitys;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model;


public class WayBillListResponse
{
    public System.Int32 OrderId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String OId { get; set; }

    /// <summary>
    /// 临时订单ID
    /// </summary>
    public System.Int32 TempOrderId { get; set; }

    /// <summary>
    /// 订单类型 system-顾客下单 custom-手动创建
    /// </summary>
    public System.String Type { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean? Source { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean RefererId { get; set; }

    /// <summary>
    /// 来源地址名称简称
    /// </summary>
    public System.String RefererName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? ChannelId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean? IsNewCustom { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? UserId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? SalesId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Email { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal? Discount { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal? DiscountPrice { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.SByte? UserDiscount { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal? ProductPrice { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal? ShippingPrice { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal? ShippingInsurancePrice { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal? PayAdditionalFee { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal? PayAdditionalAffix { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Currency { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String ManageCurrency { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal Rate { get; set; }

    /// <summary>
    /// 税率
    /// </summary>
    public System.Decimal Tax { get; set; }

    /// <summary>
    /// 税费计算方式
    /// </summary>
    public System.SByte TaxType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal? TotalWeight { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal? TotalVolume { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String CouponCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal? CouponPrice { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Decimal? CouponDiscount { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String ShippingFirstName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String ShippingLastName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String ShippingAddressLine1 { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String ShippingAddressLine2 { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String ShippingCountryCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String ShippingPhoneNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String ShippingCity { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String ShippingState { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? ShippingSId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String ShippingCountry { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? ShippingCId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String ShippingZipCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String ShippingCodeOption { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean? ShippingCodeOptionId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String ShippingTaxCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String BillFirstName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String BillLastName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String BillAddressLine1 { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String BillAddressLine2 { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String BillCountryCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String BillPhoneNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String BillCity { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String BillState { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? BillSId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String BillCountry { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? BillCId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String BillZipCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String BillCodeOption { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean? BillCodeOptionId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String BillTaxCode { get; set; }

    /// <summary>
    /// 运费模板
    /// </summary>
    public System.SByte shipping_template { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Comments { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int16? PId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String PaymentMethod { get; set; }

    /// <summary>
    /// 付款ID
    /// </summary>
    public System.String PaymentId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string PayTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string OrderTime { get; set; }

    /// <summary>
    /// 订单状态 1-等待付款 2-待确认付款 3-付款失败 4-已付款 5-已发货 6-已完成 7-已取消
    /// </summary>
    //public System.Byte OrderStatus { get; set; }
    //[SugarColumn(ColumnName = "OrderStatus", IsNullable = false, ColumnDataType = "TINYINT UNSIGNED")]
    public int OrderStatus { get; set; }
    /// <summary>
    /// 付款状态 unpaid-未付款 pending-待处理 partially_paid-部分付款 paid-已付款 partially_refunded-部分退款 refunded-已退款 voided-已作废 confirm-待确认
    /// </summary>
    public System.String PaymentStatus { get; set; }

    /// <summary>
    /// 发货状态 unshipped-未发货 partial-部分发货 shipped-已发货
    /// </summary>
    public System.String ShippingStatus { get; set; }

    /// <summary>
    /// 交易状态 completed-已完成 pending-处理中 declined-拒绝
    /// </summary>
    public System.String TradeStatus { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String CancelReason { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32 UpdateTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean CutPay { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean? CutStock { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean? CutUser { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean? CutSuccess { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean CutCancel { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean CutDIST { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String Note { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String DISTInfo { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String IP { get; set; }

    /// <summary>
    /// 亚翔供应链是否已生成标签
    /// </summary>
    public System.Boolean? AsiaflyStatus { get; set; }

    /// <summary>
    /// 折扣类型(user:会员折扣 full:满减折扣 recall:弃单召回)
    /// </summary>
    public System.String DiscountType { get; set; }

    /// <summary>
    /// 是否免征税 0未免税 1已免税
    /// </summary>
    public System.Boolean IsTaxExempt { get; set; }

    /// <summary>
    /// 是否为虚拟订单
    /// </summary>
    public System.Boolean isVirtual { get; set; }

    /// <summary>
    /// 是否算入业绩
    /// </summary>
    public System.Boolean IsPerformance { get; set; }

    /// <summary>
    /// 店铺来源
    /// </summary>
    public System.String StoreSource { get; set; }

    /// <summary>
    /// 附加信息数据
    /// </summary>
    public System.String AdditionalInfoData { get; set; }

    /// <summary>
    /// 附加信息名称
    /// </summary>
    public System.String AdditionalInfoName { get; set; }

    /// <summary>
    /// 会员积分
    /// </summary>
    public System.Int32 Points { get; set; }

    /// <summary>
    /// 会员积分价格
    /// </summary>
    public System.Decimal PointsPrice { get; set; }

    /// <summary>
    /// 来源类型 (points:积分 system:系统)
    /// </summary>
    public System.String CouponSource { get; set; }

    /// <summary>
    /// 来源类型ID
    /// </summary>
    public System.Int32 CouponSourceId { get; set; }
    /// <summary>
    /// 订单包裹
    /// </summary>
    public List<orders_package> Orders_Package_List { get; set; }
    /// <summary>
    /// 订单产品列表
    /// </summary>
    public List<orders_products_list> Orders_Products_List { get; set; }
    /// <summary>
    /// 订单总额
    /// </summary>
    public decimal OrderSum { get; set; }
    /// <summary>
    /// 订单金额单位
    /// </summary>
    public string OrderSymbol { get; set; }
    /// <summary>
    /// 产品数量
    /// </summary>
    public int ProductCount { get; set; }

    public country countrys { get; set; }

    public static explicit operator WayBillListResponse(orders data)
    {
        TimeZoneInfo beijingTimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");

        var json = JsonConvert.SerializeObject(data);
        var obj = JsonConvert.DeserializeObject<WayBillListResponse>(json);
        if (data.OrderTime != null && data.OrderTime != 0)
        {
            DateTimeOffset OrderTimeOffset = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt32(data.OrderTime));
            DateTime OrderTime = TimeZoneInfo.ConvertTime(OrderTimeOffset, beijingTimeZone).DateTime;
            obj.OrderTime = OrderTime.ToString();
        }
        if (data.PayTime != null && data.PayTime != 0)
        {
            DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt32(data.PayTime));
            DateTime PayTime = TimeZoneInfo.ConvertTime(dateTimeOffset, beijingTimeZone).DateTime;
            obj.PayTime = PayTime.ToString();
        }


        if (obj == null) return null; ;
        return obj;



    }
}

