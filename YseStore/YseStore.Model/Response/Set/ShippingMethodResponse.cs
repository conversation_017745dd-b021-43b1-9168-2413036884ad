namespace YseStore.Model.Response.Set
{
    /// <summary>
    /// 运费方法查询响应
    /// </summary>
    public class ShippingMethodResponse
    {
        /// <summary>
        /// 返回状态，1表示成功
        /// </summary>
        public int ret { get; set; } = 1;

        /// <summary>
        /// 返回数据
        /// </summary>
        public ShippingMethodData msg { get; set; }
    }

    /// <summary>
    /// 运费方法数据
    /// </summary>
    public class ShippingMethodData
    {
        /// <summary>
        /// 货币符号
        /// </summary>
        public string Symbol { get; set; } = "$";

        /// <summary>
        /// 运费方法信息
        /// </summary>
        public Dictionary<string, List<ShippingMethodItem>> info { get; set; }

        /// <summary>
        /// 总重量
        /// </summary>
        public string total_weight { get; set; }

        /// <summary>
        /// 是否使用运费模板，1表示是，0表示否
        /// </summary>
        public int shipping_template { get; set; }

        /// <summary>
        /// 海外仓信息
        /// </summary>
        public Dictionary<string, string> overseas { get; set; }

        /// <summary>
        /// 会员是否免运费，1表示是，0表示否
        /// </summary>
        public int member_free { get; set; }
    }

    /// <summary>
    /// 运费方法项
    /// </summary>
    public class ShippingMethodItem
    {
        /// <summary>
        /// 运输方式ID
        /// </summary>
        public int SId { get; set; }

        /// <summary>
        /// 运输方式名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 简介（如配送时间）
        /// </summary>
        public string Brief { get; set; }

        /// <summary>
        /// 是否API方式
        /// </summary>
        public int IsAPI { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public string type { get; set; }

        /// <summary>
        /// 重量
        /// </summary>
        public string weight { get; set; }

        /// <summary>
        /// 网站显示运费价格
        /// </summary>
        public decimal webShippingPrice { get; set; }

        /// <summary>
        /// 运费价格
        /// </summary>
        public decimal ShippingPrice { get; set; }

        /// <summary>
        /// API类型
        /// </summary>
        public string ApiType { get; set; }
    }
}