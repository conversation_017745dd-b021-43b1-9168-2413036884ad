using Entitys;

namespace YseStore.Model.Response.Products
{
    /// <summary>
    /// 包含属性信息的产品详情响应类
    /// </summary>
    public class ProductDetailWithAttributesResponse
    {
        /// <summary>
        /// 产品基本信息
        /// </summary>
        public products Product { get; set; }

        /// <summary>
        /// 产品描述信息
        /// </summary>
        public products_description ProductDescription { get; set; }

        /// <summary>
        /// 基本规格信息
        /// </summary>
        public List<BasicSpecificationInformation> BasicSpecificationInformation { get; set; } = new List<BasicSpecificationInformation>();

        /// <summary>
        /// 产品图片列表
        /// </summary>
        public List<products_images> ProductImages { get; set; } = new List<products_images>();

        /// <summary>
        /// 产品属性列表
        /// </summary>
        public List<products_attribute> ProductAttributes { get; set; } = new List<products_attribute>();

        /// <summary>
        /// 产品规格列表
        /// </summary>
        public List<products_selected_attribute_combination> ProductVariants { get; set; } =
            new List<products_selected_attribute_combination>();

        /// <summary>
        /// 产品分类ID列表
        /// </summary>
        public List<int> CategoryIds { get; set; } = new List<int>();

        /// <summary>
        /// 仓库列表IDs
        /// </summary>
        public List<short> WarehouseSet { get; set; } = new List<short>();

        /// <summary>
        /// 产品SEO信息
        /// </summary>
        public products_seo ProductSeo { get; set; }

        /// <summary>
        /// 产品标签列表
        /// </summary>
        public List<products_tags> ProductTags { get; set; } = new List<products_tags>();

        /// <summary>
        /// 主要属性ID（用于关联主图）
        /// </summary>
        public int MainAttrId { get; set; }

        /// <summary>
        /// 关联主图方式
        /// </summary>
        public string RelateMethod { get; set; } = string.Empty;
    }
}