using Entitys;
using Newtonsoft.Json;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model;
public class UserAddressBookResponse
{
    public System.Int32 AId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? UserId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean? IsBillingAddress { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean IsDefault { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String FirstName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String LastName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String AddressLine1 { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String AddressLine2 { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String City { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String State { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? SId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? CId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Boolean? CodeOption { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String TaxCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String ZipCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? CountryCode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.String PhoneNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public System.Int32? AccTime { get; set; }

    /// <summary>
    /// 附加信息数据
    /// </summary>
    public System.String AdditionalInfoData { get; set; }

    /// <summary>
    /// 附加信息名称
    /// </summary>
    public System.String AdditionalInfoName { get; set; }


    public System.String CountryName { get; set; }
    public static explicit operator UserAddressBookResponse(user_address_book data)
    {
        var json = JsonConvert.SerializeObject(data);
        var obj = JsonConvert.DeserializeObject<UserAddressBookResponse>(json);
        if (obj == null) return null; ;
        return obj;

    }
}
