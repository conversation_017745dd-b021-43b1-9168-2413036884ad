using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model.Shop_V3
{
    /// <summary>
    /// sp_product_feedback
    /// </summary>
    public partial class sp_product_feedback
    {
        /// <summary>
        /// sp_product_feedback
        /// </summary>
        public sp_product_feedback()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? ProductId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? CreateTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Subject { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Content { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string UserThumbnail { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Email { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string UserNick { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? Star { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? IsApproved { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ProductName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string UserName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? UserId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int64? OrderId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ProductURLKey { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsMultiProp { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string ProductMultiProp { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ProductThumbnail { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? ProductQuantity { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? ProductUnitPrice { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string SessionId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int64? OrderGoodsId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ProductSKU { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string Scores { get; set; }


        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string IP { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string Images { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string Videos { get; set; }


        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string UserCountry { get; set; }


        /// <summary>
        /// 顶级评论id
        /// </summary>
        public System.Int32? TopFeedbackID { get; set; }

        /// <summary>
        /// 上级客户邮箱
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ParentEmail { get; set; }

        /// <summary>
        /// 上级客户名
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ParentUserNick { get; set; }

        /// <summary>
        /// 上级客户id
        /// </summary>
        public System.Int32? ParentUserId { get; set; }

        /// <summary>
        /// 是否是管理员评论
        /// </summary>
        public System.Boolean? IsAdminReply { get; set; }

        /// <summary>
        /// 上级客户评论id
        /// </summary>
        public System.Int32? ParentFeedbackID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ParentFeedbackContent { get; set; }


        /// <summary>
        /// 国家分区
        /// </summary>
        public System.Int16? CountryRegion { get; set; }
    }
}
