using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model.Shop_V3
{
    /// <summary>
    /// sp_sitepage
    /// </summary>
    public partial class sp_sitepage
    {
        /// <summary>
        /// sp_sitepage
        /// </summary>
        public sp_sitepage()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 ID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Route { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PageName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PageTitle { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string PageContent { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string SEOTitle { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string SEOKeywords { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string SEODes { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? CreateID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Creater { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? CreateTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string RelationBlocks { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Code { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string RazorContent { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? Status { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Updater { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? ParentID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ParentName { get; set; }

        /// <summary>
        /// 站点名
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string Theme { get; set; }

        /// <summary>
        /// 纯文字内容
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string ContentNoHtml { get; set; }

        /// <summary>
        /// facebook/twittermeta标签
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string FacebookTwitterMetaCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string RelationForms { get; set; }


        /// <summary>
        /// 谷歌结构化数据代码
        /// </summary>
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string GoogleStructuredData { get; set; }

        /// <summary>
        /// 不含header
        /// </summary>
        public System.Boolean? NotIncludedHeader { get; set; }

        /// <summary>
        /// 不含footer
        /// </summary>
        public System.Boolean? NotIncludedFooter { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string ShowImage { get; set; }


        /// <summary>
        /// 国家区域
        /// 分类需要到国家分区。有些是全区显示的，有些需要单独2个区显示,
        /// </summary>
        [SqlSugar.SugarColumn(IsJson = true)]
        public string CountryRegions { get; set; }
    }
}
