using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class app_distribution_config
    {
        /// <summary>
        /// 
        /// </summary>
        public app_distribution_config()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 CId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? Level_1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? Level_2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? Level_3 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Description_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String W_Description_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Data { get; set; }

        /// <summary>
        /// 佣金比例开启
        /// </summary>
        public System.Boolean? SwitchConfig { get; set; }

        /// <summary>
        /// 返佣模式(permanent永久/single单次)
        /// </summary>
        public System.String RebateMode { get; set; }
    }
}