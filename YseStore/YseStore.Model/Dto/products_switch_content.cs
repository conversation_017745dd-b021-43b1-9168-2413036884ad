using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class products_switch_content
    {
        /// <summary>
        /// 
        /// </summary>
        public products_switch_content()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 CId { get; set; }

        /// <summary>
        /// 切换卡ID
        /// </summary>
        public System.Int32 SId { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 应用范围ID
        /// </summary>
        public System.String ApplyID { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public System.String Content { get; set; }

        /// <summary>
        /// 是否启用移动端描述 0-不启用 1-启用
        /// </summary>
        public System.Boolean UsedMobile { get; set; }

        /// <summary>
        /// 移动端描述内容
        /// </summary>
        public System.String MobileDescription { get; set; }
    }
}