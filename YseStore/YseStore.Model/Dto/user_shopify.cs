using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class user_shopify
    {
        /// <summary>
        /// 
        /// </summary>
        public user_shopify()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 UserId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? TaskId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Account { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SourceId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Email { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String FirstName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String LastName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Phone { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public System.String Remark { get; set; }

        /// <summary>
        /// 标签集
        /// </summary>
        public System.String Tags { get; set; }

        /// <summary>
        /// 地址集
        /// </summary>
        public System.String Addresses { get; set; }

        /// <summary>
        /// 注册时间
        /// </summary>
        public System.Int32? RegTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? PublishStatus { get; set; }

        /// <summary>
        /// 最后登陆时间
        /// </summary>
        public System.Int32? LastLoginTime { get; set; }
    }
}