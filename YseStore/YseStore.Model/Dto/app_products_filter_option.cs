using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 筛选项表
    /// </summary>
    public class app_products_filter_option
    {
        /// <summary>
        /// 筛选项表
        /// </summary>
        public app_products_filter_option()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 OId { get; set; }

        /// <summary>
        /// 关联筛选器id
        /// </summary>
        public System.Int32? FId { get; set; }

        /// <summary>
        /// 筛选项名称
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public System.Int32? Type { get; set; }

        /// <summary>
        /// 类型关联id
        /// </summary>
        public System.Int32? KeyId { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public System.Int32? MyOrder { get; set; }
    }
}