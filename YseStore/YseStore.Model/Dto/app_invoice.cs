using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// PayPal Invoice
    /// </summary>
    public class app_invoice
    {
        /// <summary>
        /// PayPal Invoice
        /// </summary>
        public app_invoice()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 Id { get; set; }

        /// <summary>
        /// 管理员账号
        /// </summary>
        public System.Int32 ManageId { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public System.String OId { get; set; }

        /// <summary>
        /// PayPal单号
        /// </summary>
        public System.String PaypalId { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public System.String Account { get; set; }

        /// <summary>
        /// 货币
        /// </summary>
        public System.String Currency { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public System.Decimal Amount { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public System.String Status { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String DetailStatus { get; set; }

        /// <summary>
        /// 类型 0-账单 1-收款 2-支付
        /// </summary>
        public System.Boolean Type { get; set; }

        /// <summary>
        /// 用途
        /// </summary>
        public System.String Note { get; set; }

        /// <summary>
        /// 详情数据
        /// </summary>
        public System.String DetailData { get; set; }

        /// <summary>
        /// 处理时间
        /// </summary>
        public System.Int32 ProcessTime { get; set; }

        /// <summary>
        /// 详细信息请求地址
        /// </summary>
        public System.String DetailUrl { get; set; }

        /// <summary>
        /// 错误提示
        /// </summary>
        public System.String ErrorNotice { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Detail { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Address { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public System.Int32 CreateAt { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public System.Int32 ModifyAt { get; set; }
    }
}