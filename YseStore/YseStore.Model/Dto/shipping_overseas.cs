using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class shipping_overseas
    {
        /// <summary>
        /// 
        /// </summary>
        public shipping_overseas()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int16 OvId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? MyOrder { get; set; }
    }
}