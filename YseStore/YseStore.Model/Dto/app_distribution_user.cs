using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 分销会员表
    /// </summary>
    public class app_distribution_user
    {
        /// <summary>
        /// 分销会员表
        /// </summary>
        public app_distribution_user()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 DId { get; set; }

        /// <summary>
        /// 会员表id
        /// </summary>
        public System.Int32? UserId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Email { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String DISTUId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? DISTDept { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? DISTBalance { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? DISTTotalBalance { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsDistribution { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? RegTime { get; set; }

        /// <summary>
        /// 自定义佣金比例
        /// </summary>
        public System.Decimal CustomRatio { get; set; }

        /// <summary>
        /// 分销状态
        /// </summary>
        public System.String DistributionStatus { get; set; }
    }
}