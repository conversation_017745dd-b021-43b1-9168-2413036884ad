using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 收件人
    /// </summary>
    public partial class email_recipients
    {
        /// <summary>
        /// 收件人
        /// </summary>
        public email_recipients()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 RId { get; set; }

        /// <summary>
        /// 管理员ID
        /// </summary>
        public System.Int32 UserId { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        public System.String Email { get; set; }

        /// <summary>
        /// 付款通知
        /// </summary>
        public System.Boolean? OrderCreate { get; set; }

        /// <summary>
        /// 站内信通知
        /// </summary>
        public System.Boolean? UserInbox { get; set; }

        /// <summary>
        /// 下单通知
        /// </summary>
        public System.Boolean? OrderNotice { get; set; }

        /// <summary>
        /// 表单消息通知
        /// </summary>
        public System.Boolean? FormTool { get; set; }

        /// <summary>
        /// 审核分销商申请
        /// </summary>
        public System.Boolean? DistApply { get; set; }

        /// <summary>
        /// 提现申请
        /// </summary>
        public System.Boolean? DistWithdraw { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public System.Int32? CreatedAt { get; set; }

        /// <summary>
        /// 审核会员注册
        /// </summary>
        public System.Boolean ReviewMember { get; set; }
    }


    public partial class email_recipients
    {
        [SugarColumn(IsIgnore = true)]
        public manage manage { get; set; }

    }
}