using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class app_products_screening_selected
    {
        /// <summary>
        /// 
        /// </summary>
        public app_products_screening_selected()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 SelectId { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public System.Int32? ProId { get; set; }

        /// <summary>
        /// 选择的属性名称
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 选择的属性值（|分隔）
        /// </summary>
        public System.String Value { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? SId { get; set; }
    }
}