using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 下载分类管理
    /// </summary>
    public class download_category
    {
        /// <summary>
        /// 下载分类管理
        /// </summary>
        public download_category()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 CateId { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        public System.String Category { get; set; }

        /// <summary>
        /// 简短介绍
        /// </summary>
        public System.String Brief { get; set; }

        /// <summary>
        /// 添加时间
        /// </summary>
        public System.Int32 AccTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String PageUrl { get; set; }

        /// <summary>
        /// SEO标题
        /// </summary>
        public System.String SeoTitle_en { get; set; }

        /// <summary>
        /// SEO关键词
        /// </summary>
        public System.String SeoKeyword_en { get; set; }

        /// <summary>
        /// SEO描述
        /// </summary>
        public System.String SeoDescription_en { get; set; }
    }
}