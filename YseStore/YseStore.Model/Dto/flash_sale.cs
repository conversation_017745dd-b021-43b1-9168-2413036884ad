using SqlSugar;
using YseStore.Model.Enums;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class flash_sale
    {
        /// <summary>
        /// 
        /// </summary>
        public flash_sale()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 FId { get; set; }

        /// <summary>
        /// 活动名称
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 限购类型 0:不限购 1:每人限购数量
        /// </summary>
        public System.Boolean? LimitType { get; set; }

        /// <summary>
        /// 限购条件 Count:限购数量
        /// </summary>
        public System.String LimitCondition { get; set; }

        /// <summary>
        /// 最低消费金额
        /// </summary>
        public System.Decimal? ConditionPrice { get; set; }

        /// <summary>
        /// 优惠类型 0:固定折扣 1:现金减价 2:一口价
        /// </summary>
        public OfferTypeEnum? OfferType { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public System.Int32? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public System.Int32? EndTime { get; set; }

        /// <summary>
        /// 适用范围 0:全场产品 1:指定产品 2:指定分类
        /// </summary>
        public UseProductsEnum? UseProductsType { get; set; }

        /// <summary>
        /// 指定产品
        /// </summary>
        public System.String UseProducts { get; set; }

        /// <summary>
        /// 指定产品数据
        /// </summary>
        public System.String UseProductsData { get; set; }

        /// <summary>
        /// 指定产品规格数据
        /// </summary>
        public System.String UseProductsCombination { get; set; }

        /// <summary>
        /// 使用规格类型范围 (all: 全部规格可选 part: 部分规格选中)
        /// </summary>
        public System.String UseCombinationScope { get; set; }

        /// <summary>
        /// 排序方式: sales_desc 销量从高到低 time_desc 上传时间从新到旧 time_asc 上传时间从旧到新 price_desc 价格从高到低 price_asc 价格从低到高
        /// </summary>
        public System.String OrderType { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public System.Int32? CreatedAt { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public System.Int32? UpdatedAt { get; set; }

        /// <summary>
        /// 适用顾客 (all:所有人 tourist:游客 member:会员)
        /// </summary>
        public System.String ApplicableCustomers { get; set; }
    }
}