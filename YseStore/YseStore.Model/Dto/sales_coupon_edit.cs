using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class sales_coupon_edit
    {
        /// <summary>
        /// 
        /// </summary>
        public sales_coupon_edit()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 CId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Data { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 AccTime { get; set; }
    }
}