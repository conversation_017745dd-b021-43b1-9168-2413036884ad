using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class app_cod_page
    {
        /// <summary>
        /// 
        /// </summary>
        public app_cod_page()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 CId { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public System.String Tags { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public System.Int32? ProId { get; set; }

        /// <summary>
        /// 语言
        /// </summary>
        public System.String Language { get; set; }

        /// <summary>
        /// 物流方式ID
        /// </summary>
        public System.String ShippingId { get; set; }

        /// <summary>
        /// 付款方式ID
        /// </summary>
        public System.String PaymentId { get; set; }

        /// <summary>
        /// 链接
        /// </summary>
        public System.String Url { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public System.Int32? CreateAt { get; set; }

        /// <summary>
        /// 加购方式 single-单个款式单独购买 multiple-多个款式批量购买
        /// </summary>
        public System.String AddPurchase { get; set; }
    }
}