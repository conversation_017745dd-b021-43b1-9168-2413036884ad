using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class shopping_excheckout
    {
        /// <summary>
        /// 
        /// </summary>
        public shopping_excheckout()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String OId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? CId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? UserId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SessionId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? ProId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? BuyType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? KeyId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SKU { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String PicPath { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? StartFrom { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? Weight { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? Volume { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? Price { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? Qty { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Property { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal? PropertyPrice { get; set; }

        /// <summary>
        /// 定制属性价格
        /// </summary>
        public System.Decimal CustomPrice { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Attr { get; set; }

        /// <summary>
        /// 变体ID
        /// </summary>
        public System.String VariantsId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int16? OvId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? Discount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Remark { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Language { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AccTime { get; set; }

        /// <summary>
        /// 除规格属性外，其他类型的属性：例如：定制属性
        /// </summary>
        public System.String OtherAttributes { get; set; }

        /// <summary>
        /// 是否为虚拟产品
        /// </summary>
        public System.Boolean isVirtual { get; set; }

        /// <summary>
        /// 价格类型 (默认为空 promotion:促销 wholesale:批发)
        /// </summary>
        public System.String PriceType { get; set; }
    }
}