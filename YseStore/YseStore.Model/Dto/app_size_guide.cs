using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 尺码表
    /// </summary>
    public class app_size_guide
    {
        /// <summary>
        /// 尺码表
        /// </summary>
        public app_size_guide()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 SId { get; set; }

        /// <summary>
        /// 尺码表名称
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 尺码表说明
        /// </summary>
        public System.String Description { get; set; }

        /// <summary>
        /// 适用范围 all-全站产品 products-指定产品 category - 指定分类
        /// </summary>
        public System.String Scope { get; set; }

        /// <summary>
        /// 适用产品规格
        /// </summary>
        public System.String Attr { get; set; }

        /// <summary>
        /// 尺码表数据
        /// </summary>
        public System.String GuideData { get; set; }

        /// <summary>
        /// 测量说明数据
        /// </summary>
        public System.String MeasurementData { get; set; }

        /// <summary>
        /// 创建事件
        /// </summary>
        public System.Int32? AccTime { get; set; }

        /// <summary>
        /// 前台名称
        /// </summary>
        public System.String FrontTitle { get; set; }

        /// <summary>
        /// 是否发布
        /// </summary>
        public System.Boolean IsPublish { get; set; }

        /// <summary>
        /// 尺码填写方式
        /// </summary>
        public System.String FillMethod { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public System.String Unit { get; set; }
    }
}