using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class email_log
    {
        /// <summary>
        /// 
        /// </summary>
        public email_log()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 LId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Email { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Subject { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Body { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AccTime { get; set; }

        /// <summary>
        /// type 类型
        /// order
        /// </summary>
        public System.String Type { get; set; }

        /// <summary>
        /// 类型对应的id
        /// order: 订单id
        /// cart : 购物车id，逗号拼接
        /// </summary>
        public System.String TypeId { get; set; }

        /// <summary>
        /// 语言
        /// </summary>
        public System.String Lang { get; set; }
    }
}