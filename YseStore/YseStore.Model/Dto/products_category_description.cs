using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class products_category_description
    {
        /// <summary>
        /// 
        /// </summary>
        public products_category_description()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 DId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? CateId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Description_en { get; set; }
        public System.String Description_en_bottom { get; set; }
        public System.String Core_top { get; set; }
        public System.String Core_bottom { get; set; }

        /// <summary>
        /// 位置类型
        /// 0:内容-top,
        /// 1:核心-top
        /// 2:核心-bottom
        /// 3.内容-bottom
        /// </summary>
        public System.Int16 PositionType { get; set; }
         
    }
}