using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class app_products_screening
    {
        /// <summary>
        /// 
        /// </summary>
        public app_products_screening()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 SId { get; set; }

        /// <summary>
        /// 所属分类ID
        /// </summary>
        public System.String CateId { get; set; }

        /// <summary>
        /// 记录关联的产品id
        /// </summary>
        public System.String ProId { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 属性选项（JSON）
        /// </summary>
        public System.String Options { get; set; }

        /// <summary>
        /// 1下拉 2多选框 3平铺
        /// </summary>
        public System.Boolean? ShowType { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public System.Int32 Myorder { get; set; }
    }
}