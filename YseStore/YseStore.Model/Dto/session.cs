using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class session
    {
        /// <summary>
        /// 
        /// </summary>
        public session()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public System.String id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? expire { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Byte[] data { get; set; }
    }
}