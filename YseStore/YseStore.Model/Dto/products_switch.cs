using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class products_switch
    {
        /// <summary>
        /// 
        /// </summary>
        public products_switch()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 SId { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 应用范围 Website：全部产品；Products：指定产品；Categories：指定分类；
        /// </summary>
        public System.String Apply { get; set; }

        /// <summary>
        /// 应用范围ID汇总
        /// </summary>
        public System.String ApplyCollect { get; set; }
        public System.String DataId { get; set; }
        public System.String Identity { get; set; }

        /// <summary>
        /// 自定义排序
        /// </summary>
        public System.Int32 MyOrder { get; set; }
    }
}