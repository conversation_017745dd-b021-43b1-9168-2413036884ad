using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 运单注册表
    /// </summary>
    public class deliver_track
    {
        /// <summary>
        /// 运单注册表
        /// </summary>
        public deliver_track()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.UInt32 Id { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public System.String OrderNO { get; set; }

        /// <summary>
        /// 运单号
        /// </summary>
        public System.String Number { get; set; }

        /// <summary>
        /// 邮件
        /// </summary>
        public System.String Mail { get; set; }

        /// <summary>
        /// 运单注册状态1-成功
        /// </summary>
        public System.Byte ResStatus { get; set; }

        /// <summary>
        /// 注册的运输商代码
        /// </summary>
        public System.String Carrier { get; set; }

        /// <summary>
        /// 注册失败信息
        /// </summary>
        public System.String RegMsg { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public System.Byte Status { get; set; }

        /// <summary>
        /// 添加时间
        /// </summary>
        public System.UInt32 AccTime { get; set; }
    }
}