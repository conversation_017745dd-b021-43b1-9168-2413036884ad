using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class web_visual
    {
        /// <summary>
        /// 
        /// </summary>
        public web_visual()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 WId { get; set; }

        /// <summary>
        /// 页面 index PC首页 mindex 移动首页 banner 广告图 mbanner 移动广告图 global 模板公用 mglobal 移动模板公用 search 搜索(弃用)
        /// </summary>
        public System.String Pages { get; set; }

        /// <summary>
        /// 位置
        /// </summary>
        public System.SByte? Position { get; set; }

        /// <summary>
        /// 布局 0:无
        /// </summary>
        public System.SByte? Layout { get; set; }

        /// <summary>
        /// 数据
        /// </summary>
        public System.String Data { get; set; }

        /// <summary>
        /// 默认数据样式
        /// </summary>
        public System.String DataStyle { get; set; }

        /// <summary>
        /// 默认数据语言包
        /// </summary>
        public System.String DataPack { get; set; }

        /// <summary>
        /// 默认插件样式
        /// </summary>
        public System.String PluginStyle { get; set; }

        /// <summary>
        /// 默认插件配置
        /// </summary>
        public System.String PluginConfig { get; set; }

        /// <summary>
        /// 模板初始化模块排序ID
        /// </summary>
        public System.Int32? MIdStr { get; set; }

        /// <summary>
        /// 关联ID
        /// </summary>
        public System.Int32? KeyId { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public System.String Type { get; set; }

        /// <summary>
        /// 模块编号
        /// </summary>
        public System.String Mode { get; set; }
    }
}