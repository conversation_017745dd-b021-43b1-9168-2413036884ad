namespace YseStore.Model.Dto
{
    /// <summary>
    /// 固定运费数据模型
    /// </summary>
    public class FixedPriceData
    {
        /// <summary>
        /// 固定运费价格
        /// </summary>
        public decimal fixedPrice { get; set; }
    }

    /// <summary>
    /// 按数量计算运费数据模型
    /// </summary>
    public class QtyPriceData
    {
        /// <summary>
        /// 首件最小数量
        /// </summary>
        public int firstMinQty { get; set; }

        /// <summary>
        /// 首件最大数量
        /// </summary>
        public int firstMaxQty { get; set; }

        /// <summary>
        /// 首件价格
        /// </summary>
        public decimal firstQtyPrice { get; set; }

        /// <summary>
        /// 续件数量单位
        /// </summary>
        public int extQty { get; set; }

        /// <summary>
        /// 续件价格
        /// </summary>
        public decimal extQtyPrice { get; set; }
    }

    /// <summary>
    /// 首重/续重运费数据模型
    /// </summary>
    public class AdditionalPriceData
    {
        /// <summary>
        /// 首重重量
        /// </summary>
        public float firstWeight { get; set; }

        /// <summary>
        /// 首重价格
        /// </summary>
        public decimal firstPrice { get; set; }

        /// <summary>
        /// 续重重量
        /// </summary>
        public float extWeight { get; set; }

        /// <summary>
        /// 续重价格
        /// </summary>
        public decimal extPrice { get; set; }

        /// <summary>
        /// 首重重量(记录)
        /// </summary>
        public float firstSaveWeight { get; set; }

        /// <summary>
        /// 续重重量(记录)
        /// </summary>
        public float extSaveWeight { get; set; }
    }

    /// <summary>
    /// 按重量固定运费数据模型
    /// </summary>
    public class TotalPriceData
    {
        /// <summary>
        /// 固定运费价格
        /// </summary>
        public decimal totalPrice { get; set; }
    }

    /// <summary>
    /// 每KG运费数据模型
    /// </summary>
    public class EachPriceData
    {
        /// <summary>
        /// 每KG运费价格
        /// </summary>
        public decimal eachPrice { get; set; }
    }
} 