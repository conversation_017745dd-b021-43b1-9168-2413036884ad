using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class third_api_log
    {
        /// <summary>
        /// 
        /// </summary>
        public third_api_log()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 TId { get; set; }

        /// <summary>
        /// 接口类型
        /// </summary>
        public System.String Category { get; set; }

        /// <summary>
        /// 接口调用方法
        /// </summary>
        public System.String ClassName { get; set; }

        /// <summary>
        /// 接口调用状态 【0 未处理 1 连接失败 2 失败 3 成功】
        /// </summary>
        public System.Boolean Status { get; set; }

        /// <summary>
        /// 提交的数据 Json 格式
        /// </summary>
        public System.String SubmitData { get; set; }

        /// <summary>
        /// 返回的数据 Json 格式
        /// </summary>
        public System.String ReturnData { get; set; }

        /// <summary>
        /// 提交时间
        /// </summary>
        public System.Int32 SubmitTime { get; set; }

        /// <summary>
        /// 返回时间
        /// </summary>
        public System.Int32 ReturnTime { get; set; }
    }
}