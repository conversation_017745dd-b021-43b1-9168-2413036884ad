using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class products_seo
    {
        /// <summary>
        /// 
        /// </summary>
        public products_seo()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 SId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? ProId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SeoTitle_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SeoKeyword_en { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String SeoDescription_en { get; set; }
    }
}