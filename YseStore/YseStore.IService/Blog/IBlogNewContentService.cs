using YseStore.Model.Entities.Blog;

namespace YseStore.IService.Blog
{
    /// <summary>
    /// 博客内容服务接口
    /// </summary>
    public interface IBlogNewContentService : IBaseServices<BlogNewContent>
    {
        /// <summary>
        /// 根据文章ID获取博客内容
        /// </summary>
        /// <param name="articleId">文章ID</param>
        /// <returns>博客内容实体</returns>
        Task<BlogNewContent> GetContentByArticleId(int articleId);

        /// <summary>
        /// 添加博客内容
        /// </summary>
        /// <param name="content">博客内容实体</param>
        /// <returns>新增内容ID</returns>
        Task<long> AddContent(BlogNewContent content);

        /// <summary>
        /// 更新博客内容
        /// </summary>
        /// <param name="content">博客内容实体</param>
        /// <returns>是否更新成功</returns>
        Task<bool> UpdateContent(BlogNewContent content);

        /// <summary>
        /// 删除博客内容
        /// </summary>
        /// <param name="id">内容ID</param>
        /// <returns>是否删除成功</returns>
        Task<bool> DeleteContent(int id);

        /// <summary>
        /// 删除文章相关的所有内容
        /// </summary>
        /// <param name="articleId">文章ID</param>
        /// <returns>是否删除成功</returns>
        Task<bool> DeleteContentByArticleId(int articleId);
    }
} 