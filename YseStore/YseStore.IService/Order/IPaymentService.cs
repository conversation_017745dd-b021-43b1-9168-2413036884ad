using Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.IService.Order
{
   public  interface IPaymentService
    {
        /// <summary>
        /// 获取付款信息
        /// </summary>
        /// <returns></returns>
        Task<List<payment>> Getpayment();
        /// <summary>
        /// 根据邮箱获取用户信息
        /// </summary>
        /// <param name="userEmail"></param>
        /// <returns></returns>
        Task<user> GetUserByEmailAsync(string userEmail);

    }
}
