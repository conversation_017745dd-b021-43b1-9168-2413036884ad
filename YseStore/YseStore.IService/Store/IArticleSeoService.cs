namespace YseStore.IService.Store
{
    /// <summary>
    /// 文章SEO服务接口
    /// </summary>
    public interface IArticleSeoService
    {
        /// <summary>
        /// 检查文章PageUrl是否存在
        /// </summary>
        /// <param name="pageUrl">文章PageUrl</param>
        /// <param name="currentArticleId">当前文章ID（更新时使用，新增时为0）</param>
        /// <returns>是否存在</returns>
        Task<bool> IsArticlePageUrlExistAsync(string pageUrl, int currentArticleId = 0);

        /// <summary>
        /// 根据PageUrl获取文章ID
        /// </summary>
        /// <param name="pageUrl">文章PageUrl</param>
        /// <returns>文章ID</returns>
        Task<int> GetArticleIdByPageUrlAsync(string pageUrl);

        /// <summary>
        /// 生成唯一的文章PageUrl
        /// </summary>
        /// <param name="baseUrl">基础URL</param>
        /// <param name="currentArticleId">当前文章ID（更新时使用，新增时为0）</param>
        /// <returns>唯一的PageUrl</returns>
        Task<string> GenerateUniqueArticlePageUrlAsync(string baseUrl, int currentArticleId = 0);

        /// <summary>
        /// 更新文章PageUrl缓存
        /// </summary>
        /// <param name="pageUrl">文章PageUrl</param>
        /// <param name="articleId">文章ID</param>
        void UpdateArticlePageUrlCache(string pageUrl, int articleId);

        /// <summary>
        /// 移除文章PageUrl缓存
        /// </summary>
        /// <param name="pageUrl">文章PageUrl</param>
        void RemoveArticlePageUrlCache(string pageUrl);

        /// <summary>
        /// 清除所有文章PageUrl缓存
        /// </summary>
        void ClearAllArticlePageUrlCache();
    }
}
