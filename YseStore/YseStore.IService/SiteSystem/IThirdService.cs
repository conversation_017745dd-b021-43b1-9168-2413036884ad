using Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.IService.SiteSystem
{
    /// <summary>
    /// 自定义代码服务接口
    /// </summary>
    public interface IThirdService : IBaseServices<third>
    {

        /// <summary>
        /// 获取所有的自定义代码
        /// </summary>
        /// <returns></returns>
        Task<List<third>> GetAllThriedCache();

    }
}
