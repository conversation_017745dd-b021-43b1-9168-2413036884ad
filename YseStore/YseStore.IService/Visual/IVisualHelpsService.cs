using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.Entities.Blog;

namespace YseStore.IService.Visual
{
    public interface IVisualHelpsService
    {
        /// <summary>
        /// 设置宽度容器
        /// </summary>
        /// <param name="ContainerWidth"></param>
        /// <returns></returns>
        string SetContainWidth(string ContainerWidth = "");

        /// <summary>
        /// 计算图片尺寸比例--计算比率
        /// </summary>
        /// <param name="replaceAry"></param>
        /// <param name="type"></param>
        /// <param name="productsPicScale"></param>
        /// <returns></returns>
        string ComputeRatio(Dictionary<string, object> replaceAry = null, string type = "default", string productsPicScale = null);
        /// <summary>
        /// 计算宽度
        /// </summary>
        /// <param name="replaceAry"></param>
        /// <param name="picScale"></param>
        /// <param name="scrollView"></param>
        /// <returns></returns>
        string ComputeWidth(Dictionary<string, object> replaceAry = null, string picScale = "", int scrollView = 0);
        // 获取平铺方式
        string ComputeFilling(Dictionary<string, object> fillingAry = null);


        /// <summary>
        /// 店铺装修--获取博客内容
        /// </summary>
        /// <param name="valueJson"></param>
        /// <param name="normalLimit"></param>
        /// <returns></returns>
        List<BlogNew> GetBlogList(string valueJson = "", int normalLimit = 3);





    }
}
