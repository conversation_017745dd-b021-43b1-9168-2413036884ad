using Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model;
using YseStore.Model.VM;

namespace YseStore.IService
{
    public interface ISettingService
    {
        Task<IList<VM_Assignment>> GetUserSettingModuleAry();
        Task<IList<VM_Assignment>> GetUserAllModuleAry();
        /// <summary>
        /// 获取政策表
        /// </summary>
        /// <returns></returns>
        Task<IList<policies>> GetPoliciesAry();
        /// <summary>
        /// 更新政策表
        /// </summary>
        /// <param name="policies"></param>
        Task<bool> UpdetPoliciesAry(List<policies> policies);
        /// <summary>
        /// 设置-添加货币中的列表
        /// </summary>
        /// <param name="exclude"></param>
        /// <returns></returns>
        Task<string> SelectCurrency(string exclude);
        Task<IList<VM_ManageUser>> GetManagerList();
        Task<bool> SetManagerLocked(int userId, sbyte locked);
        Task<bool> DelManager(int userId);
        Task<bool> EditEditTaxType(int isUsed);
    }
}
