using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;

namespace YseStore.Service
{
    public class PluginsService : BaseServices<plugins>, IPluginsService
    {
        private readonly ILogger<UrlService> _logger;
        private readonly ICaching _cacheService;
        public PluginsService(ILogger<UrlService> logger, ICaching cacheService)
        {
            _logger = logger;
            _cacheService = cacheService;

        }

    }
}
