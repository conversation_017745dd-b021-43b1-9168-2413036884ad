using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.IService.Order;

namespace YseStore.Service.Order
{
    public class PaymentService: BaseServices<manage_operation_log>, IPaymentService
    {
        private readonly ILogger<manage_operation_log> _logger;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarClient db;
        public PaymentService(ILogger<manage_operation_log> logger, IConfiguration configuration, ISqlSugarClient db)
        {
            _logger = logger;
            _configuration = configuration;
            this.db = db;
        }
        /// <summary>
        /// 获取付款信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<payment>> Getpayment()
        {
            var ret = db.Queryable<payment>()
                .ToList();
            return ret;
        }
        /// <summary>
        /// 根据邮箱获取用户信息
        /// </summary>
        /// <param name="userEmail"></param>
        /// <returns></returns>
        public async Task<user> GetUserByEmailAsync(string userEmail)
        {
            var ret = db.Queryable<user>()
                .Where(x=>x.Email== userEmail)
                .First();
            return ret;
        }
        


    }
}
