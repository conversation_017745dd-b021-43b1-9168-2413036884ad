using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.IService.Order;

namespace YseStore.Service.Order
{
    /// <summary>
    /// 订单退款服务
    /// </summary>
    public class OrderRefundService : BaseServices<orders_refund_info>, IOrderRefundService
    {

        private readonly ILogger<OrderRefundService> _logger;
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        public OrderRefundService(ILogger<OrderRefundService> logger, ISqlSugarClient db, ICaching caching)
        {
            _logger = logger;
            this.db = db;
            _caching = caching;
        }

    }
}
