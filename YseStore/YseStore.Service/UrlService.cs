using Entitys;
using log4net.Repository.Hierarchy;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Logging;
using SnowflakeId.AutoRegister.Core;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.Common.Const;
using YseStore.Model;
using YseStore.Model.Entities;
using YseStore.Model.Entities.Blog;
using YseStore.Repo;

namespace YseStore.Service
{
    /// <summary>
    /// url重定向服务
    /// </summary>
    public class UrlService : BaseServices<url_redirect>, IUrlService
    {
        private readonly IStringLocalizer<UrlService> _localizer;

        private readonly ILogger<UrlService> _logger;
        private readonly ICaching _cacheService;
        
        /// <summary>
        /// 构造函数，通过依赖注入获取数据仓储
        /// </summary>
        /// <param name="baseDal">UrlService</param>
        public UrlService(IBaseRepository<url_redirect> baseDal, ILogger<UrlService> logger, ICaching cacheService, IStringLocalizer<UrlService> localizer) : base(baseDal)
        {
            _logger = logger;
            this._cacheService = cacheService;
            _localizer = localizer;
        }

        /// <summary>
        /// 批量删除 url重定向
        /// </summary>
        /// <param name="idList"></param>
        /// <returns></returns>
        public async Task<WebApiCallBack> DeleteUrlRedirect(List<int> idList)
        {
            WebApiCallBack result = new WebApiCallBack();
            try
            {
                if (idList.Count == 0)
                {
                    result.code = 0;
                    result.msg = "请选择要删除的数据";
                    return result;
                }
                int totalCount = idList.Count;
                int successCount = 0;
                int ignoreCount = 0;
                int errorCount = 0;
                foreach (var item in idList)
                {
                    var url = await Db.Queryable<url_redirect>().Where(it => it.Id == item).FirstAsync();
                    if (url == null)
                    {
                        ignoreCount++;
                        continue;
                    }


                    var del = await Db.Deleteable<url_redirect>(item).ExecuteCommandAsync();
                    if (del > 0)
                    {

                        _cacheService.HashRemove(GlobalConstVars.KEY_301Page, url.Url);

                        successCount++;
                    }
                    else
                    {
                        errorCount++;
                    }
                }

                result.code = 1;
                result.msg = $"共删除{totalCount}条数据,成功{successCount}条，失败{errorCount}条，忽略{ignoreCount}条";

                return result;

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                result.code = 0;
                result.msg = ex.Message;
            }
            return result;
        }

        /// <summary>
        /// url 重定向列表
        /// </summary>
        /// <param name="criteria"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PageModel<url_redirect>> GetUrlRedirect(Dictionary<string, string> criteria, int pageIndex, int pageSize)
        {
            try
            {
                using (var db = Db)
                {
                    var childq = db.Queryable<url_redirect>();
                    RefAsync<int> pageCount = 0;

                    if (criteria != null && criteria.Count > 0)
                    {
                        //搜索参数
                        if (criteria.ContainsKey("keywords"))
                        {
                            var kw = criteria["keywords"].ToString().Trim();

                            childq = childq.Where(it => it.Url.Contains(kw) || it.RedirectUrl.Contains(kw));
                        }
                        //ID
                        if (criteria.ContainsKey("minID"))
                        {
                            var minID = Convert.ToInt64(criteria["minID"]);
                            childq = childq.Where(it => it.Id >= minID);
                        }
                        if (criteria.ContainsKey("maxID"))
                        {
                            var maxID = Convert.ToInt64(criteria["maxID"]);
                            childq = childq.Where(it => it.Id <= maxID);
                        }



                    }

                    //查看到数据
                    var list = await childq.OrderBy(it => it.Id, OrderByType.Desc).ToListAsync();
                    return new PageModel<url_redirect>
                    {
                        page = pageIndex,
                        PageSize = list.Count,
                        dataCount = 1,
                        data = list
                    };


                    //var r = list.OrderBy(it => it.Id, OrderByType.Desc).ToPagedList(pageIndex, pageSize, pageCount);

                    // 构建并返回分页模型
                    //return new PageModel<url_redirect>
                    //{
                    //    page = pageIndex,
                    //    PageSize = pageSize,
                    //    dataCount = pageCount,
                    //    data = list
                    //};

                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                // 出错时返回空结果
                return new PageModel<url_redirect>();
            }
        }


        /// <summary>
        /// 添加url 重定向设置
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<WebApiCallBack> SaveUrlRedirect(url_redirect obj)
        {
            WebApiCallBack result = new WebApiCallBack();
            try
            {
                if (obj.Url.IsNullOrEmpty() || obj.Url == "/")
                {

                    result.msg = "请输入请求链接";
                    return result;
                }

                if (string.IsNullOrEmpty(obj.RedirectUrl))
                {

                    result.msg = "目标链接不能为空";
                    return result;
                }

                var count = await Db.Queryable<url_redirect>().CountAsync(it => it.RedirectUrl == obj.Url);
                if (count > 0)
                {

                    result.msg = $"{obj.Url}已作为目标链接，不能再作为请求链接";
                    return result;
                }



                if (obj.Id > 0)
                {
                    var update = await Db.Updateable<url_redirect>(obj).ExecuteCommandAsync();
                    if (update > 0)
                    {
                        result.code = 1;
                        result.msg = "保存成功";

                    }
                    else
                    {
                        result.code = 0;
                        result.msg = "保存失败";
                        return result;
                    }

                }
                else
                {


                    var insert = await Db.Insertable<url_redirect>(obj).ExecuteCommandAsync();
                    if (insert > 0)
                    {
                        result.code = 1;
                        result.msg = "保存成功";

                    }
                    else
                    {
                        result.code = 0;
                        result.msg = "保存失败";
                        return result;
                    }
                }

                _cacheService.HashSet(GlobalConstVars.KEY_301Page, obj.Url, obj.RedirectUrl);



                return result;

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                result.code = 0;
                result.msg = ex.Message;
            }
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<url_redirect> GetInfoById(int id)
        {
            try
            {
                var r = await Db.Queryable<url_redirect>()
                    .Where(it => it.Id == id).FirstAsync();
                return r;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                return new url_redirect();
            }
        }



        /// <summary>
        /// 批量导入Url
        /// </summary>
        /// <param name="list"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public async Task<WebApiCallBack> ImportUrlRedirect(List<url_redirect> list, string userName)
        {
            WebApiCallBack result = new WebApiCallBack();
            try
            {
                List<url_redirect> urlList = await Db.Queryable<url_redirect>()

                    .ToListAsync();


                List<url_redirect> rList = new List<url_redirect>();
                foreach (url_redirect obj in list)
                {

                    if (obj.Url.IsNullOrEmpty())
                    {
                        result.msg = "请输入请求链接";
                        continue;
                    }


                    if (string.IsNullOrEmpty(obj.RedirectUrl))
                    {
                        result.msg = "目标链接不能为空";
                        continue;
                    }

                    var count = urlList.Where(it => it.RedirectUrl == obj.Url).Count();
                    if (count > 0)
                    {

                        result.msg = $"{obj.Url}已作为目标链接，不能再作为请求链接";
                        continue;
                    }


                    rList.Add(obj);
                }

                var insert = await Db.Insertable<url_redirect>(rList).ExecuteCommandAsync();
                if (insert > 0)
                {
                    foreach (var obj in rList)
                    {
                        _cacheService.HashSet(GlobalConstVars.KEY_301Page, obj.Url, obj.RedirectUrl);
                    }

                    result.code = 1;
                    result.msg = "操作成功";
                    return result;

                }
                else
                {
                    result.code = 0;
                    result.msg = "操作失败";
                    return result;
                }

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                result.code = 0;
                result.msg = ex.Message;
            }
            return result;
        }

    }
}
