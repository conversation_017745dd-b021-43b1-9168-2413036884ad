using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.IService.Customer;
using YseStore.Model;

namespace YseStore.Service.Customer
{
    /// <summary>
    /// 表单工具数据服务实现类
    /// </summary>
    public class FormToolDataService : BaseServices<app_form_tool_data>, IFormToolDataService
    {
        private readonly ILogger<FormToolDataService> _logger;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        public FormToolDataService(ILogger<FormToolDataService> logger, IConfiguration configuration, ISqlSugarClient db, ICaching caching)
        {
            _logger = logger;
            _configuration = configuration;
            this.db = db;
            _caching = caching;
        }


        /// <summary>
        /// 获取表单工具列表
        /// </summary>
        /// <param name="criteria"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PageModel<app_form_tool_data>> GetFormToolData(Dictionary<string, string> criteria, int pageIndex, int pageSize)
        {
            try
            {

                var childq = db.Queryable<app_form_tool_data>();
                RefAsync<int> pageCount = 0;

                if (criteria != null && criteria.Count > 0)
                {

                    //ID
                    if (criteria.ContainsKey("FId"))
                    {
                        var minID = Convert.ToInt32(criteria["FId"]);
                        childq = childq.Where(it => it.FId == minID);
                    }

                }

                //查看到数据
                var list = await childq
                    .OrderBy(it => it.AccTime, OrderByType.Desc)
                    .ToPageListAsync(pageIndex, pageSize, pageCount);

                // 构建并返回分页模型
                return new PageModel<app_form_tool_data>
                {
                    page = pageIndex,
                    PageSize = pageSize,
                    dataCount = pageCount,
                    data = list
                };

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                // 出错时返回空结果
                return new PageModel<app_form_tool_data>();
            }

        }


    }
}
