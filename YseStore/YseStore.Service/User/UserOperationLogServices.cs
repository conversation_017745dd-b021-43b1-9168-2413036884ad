using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.IService.User;
using YseStore.Repo;

namespace YseStore.Service.User
{
    /// <summary>
    /// 用户操作日志服务
    /// </summary>
    public class UserOperationLogServices : BaseServices<user_operation_log>, IUserOperationLogServices
    {

        private readonly ILogger<UserOperationLogServices> _logger;
        private readonly ICaching _cacheService;
        /// <summary>
        /// 构造函数，通过依赖注入获取数据仓储
        /// </summary>
        /// <param name="baseDal">UrlService</param>
        public UserOperationLogServices(IBaseRepository<user_operation_log> baseDal, ILogger<UserOperationLogServices> logger, ICaching cacheService) : base(baseDal)
        {
            _logger = logger;
            this._cacheService = cacheService;
        }





    }
}
