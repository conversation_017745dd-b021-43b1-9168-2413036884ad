using Entitys;
using YseStore.IService.Set;
using YseStore.Repo;

namespace YseStore.Service.Set
{
    /// <summary>
    /// 产品服务实现类
    /// </summary>
    public class SetProductService : BaseServices<products>, ISetProductService
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="baseDal">数据访问层</param>
        public SetProductService(IBaseRepository<products> baseDal) : base(baseDal)
        {
        }

        /// <summary>
        /// 根据运费模板ID获取关联的产品列表
        /// </summary>
        /// <param name="templateId">运费模板ID</param>
        /// <returns>关联的产品列表</returns>
        public async Task<List<products>> GetProductsByTemplateIdAsync(int templateId)
        {
            return await Db.Queryable<products>()
                .Where(p => p.TId == templateId)
                .ToListAsync();
        }

        /// <summary>
        /// 更新产品的运费模板关联
        /// </summary>
        /// <param name="templateId">运费模板ID</param>
        /// <param name="productIds">产品ID列表</param>
        /// <returns>操作结果</returns>
        public async Task<bool> UpdateProductsTemplateAsync(int templateId, List<int> productIds)
        {
            // 清除之前关联的产品
            await Db.Updateable<products>()
                .SetColumns(p => p.TId == 0) // 设置为默认值
                .Where(p => p.TId == templateId)
                .ExecuteCommandAsync();

            if (productIds == null || !productIds.Any())
            {
                return true; // 如果没有新的产品ID，则直接返回成功
            }
            
            // 将选中的产品关联到当前模板
            return await Db.Updateable<products>()
                .SetColumns(p => p.TId == templateId)
                .Where(p => productIds.Contains(p.ProId))
                .ExecuteCommandAsync() > 0;
        }
        
        /// <summary>
        /// 根据产品ID列表获取产品信息
        /// </summary>
        /// <param name="productIds">产品ID列表</param>
        /// <returns>产品列表</returns>
        public async Task<List<products>> GetByIdsAsync(List<int> productIds)
        {
            if (productIds == null || !productIds.Any())
            {
                return new List<products>();
            }
            
            return await Db.Queryable<products>()
                .Where(p => productIds.Contains(p.ProId))
                .ToListAsync();
        }
    }
} 