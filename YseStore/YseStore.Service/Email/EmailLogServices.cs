using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.IService.Email;

namespace YseStore.Service.Email
{
    /// <summary>
    /// 
    /// </summary>
    internal class EmailLogServices : BaseServices<email_log>, IEmailLogServices
    {

        private readonly ILogger<email_log> _logger;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarClient db;
        public EmailLogServices(ILogger<email_log> logger, IConfiguration configuration, ISqlSugarClient db)
        {
            _logger = logger;
            _configuration = configuration;
            this.db = db;
        }


    }
}
